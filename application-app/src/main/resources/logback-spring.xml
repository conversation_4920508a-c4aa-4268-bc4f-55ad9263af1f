<?xml version="1.0" encoding="UTF-8"?>
<!--
scan：当此属性设置为true时，配置文件如果发生改变，将会被重新加载，默认值为true。
scanPeriod：设置监测配置文件是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒当scan为true时，此属性生效。默认的时间间隔为1分钟。
debug：当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。
-->
<configuration scan="false" scanPeriod="60 seconds" debug="false">
    <property name="LOG_NAME" value="application-app" />
    <!-- 定义日志的根目录 -->
    <property name="LOG_HOME" value="./zzlogs/application-app" />
    <property name="LOG_PATTERN"
        value="[%d{yyyy-MM-dd HH:mm:ss}] [%-5level]-[%thread] T:[%X{traceId}] U:[%X{userId}] [%logger{50}:%method:%line] ==> %msg%n" />


    <!-- Rolling File Appender -->
    <property
        name="LOG_PATTERN_EX"
        value="[%d{yyyy-MM-dd HH:mm:ss}] [%-5level]-[%thread] T:[%X{traceId}] U:[%X{userId}] [%logger{50}:%method:%line] ==> %msg%n" />
    <shutdownHook class="ch.qos.logback.core.hook.DelayingShutdownHook" />
    <springProperty defaultValue="qyc-app" name="appName" scope="context"
        source="spring.application.name" />

    <!-- 4. Root Logger Definition -->
    <springProperty
        defaultValue="info" name="logging.level" scope="context" source="logging.level" />
    <!-- 2. Appender Definitions -->
    <!-- Console Appender -->
    <appender
        class="ch.qos.logback.core.ConsoleAppender" name="console">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>${LOG_PATTERN_EX}</pattern>
        </layout>
    </appender>

    <!-- Loki Appender -->
    <appender class="com.github.loki4j.logback.Loki4jAppender" name="LOKI">
        <!-- 一般设置 -->
        <metricsEnabled>true</metricsEnabled>
        <batchMaxItems>1000</batchMaxItems>
        <batchTimeoutMs>5000</batchTimeoutMs>
        <!-- 连接设置 -->
        <http class="com.github.loki4j.logback.ApacheHttpSender">
            <!--dev
            需要配置白名单去腾讯云安全组-->
            <springProfile name="dev">
                <url>http://43.138.169.42:3100/loki/api/v1/push</url> <!-- Dev URL -->
            </springProfile>
            <springProfile name="test">
                <url>http://**********:3100/loki/api/v1/push</url> <!-- Test URL -->
            </springProfile>
            <springProfile name="!dev &amp; !test">
                <url>http://**********:3100/loki/api/v1/push</url> <!-- Default/Prod URL -->
            </springProfile>
            <connectionTimeoutMs>30000</connectionTimeoutMs>
            <requestTimeoutMs>5000</requestTimeoutMs>
        </http>
        <!-- 批处理设置 -->
        <format class="com.github.loki4j.logback.ProtobufEncoder">
            <label>
                <pattern>
                    app=${LOG_NAME},host=${HOSTNAME},level=%level,profile=${spring.profiles.active:-dev}
                </pattern>
            </label>
            <message>
                <pattern>[%d{yyyy-MM-dd HH:mm:ss}] [%-5level]-[%thread] T:[%X{traceId}]
                    U:[%X{userId}] [%logger{50}:%method:%line] ==> %msg %ex
                </pattern>
            </message>
            <sortByTime>true</sortByTime>
        </format>
        <!-- 编码器设置 -->
    </appender>

    <appender
        class="ch.qos.logback.core.rolling.RollingFileAppender" name="file_log">
        <append>true</append>
        <file>${LOG_HOME}/${LOG_NAME}.log</file>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>${LOG_PATTERN_EX}</pattern>
        </layout>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <MaxHistory>31</MaxHistory>
            <fileNamePattern>${LOG_HOME}/%d{yyyy-MM-dd}/${LOG_NAME}-%d{yyyy-MM-dd}-%i.log.gz</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy
                class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>500MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
    </appender>

    <!-- 1. Global Settings -->
    <logger additivity="false"
        level="error" name="springfox.documentation">
        <appender-ref ref="console" />
        <appender-ref ref="LOKI" />
    </logger>

    <logger additivity="false"
        level="info" name="com.rongchen.byh">
        <appender-ref ref="console" />
        <appender-ref ref="file_log" />
        <appender-ref ref="LOKI" />
    </logger>

    <!-- 定义日志属性 -->
    <logger additivity="false"
        level="debug" name="com.rongchen.byh.app.dao">
        <appender-ref ref="console" />
        <appender-ref ref="file_log" />
        <appender-ref ref="LOKI" />
    </logger>

    <!-- Define spring property for application name -->
    <logger additivity="false"
        level="debug" name="com.rongchen.byh.common.log.dao">
        <appender-ref ref="console" />
        <appender-ref ref="file_log" />
        <appender-ref ref="LOKI" />
    </logger>


    <root level="${logging.level}">
        <appender-ref ref="console" />
        <appender-ref ref="file_log" />
        <appender-ref ref="LOKI" />
    </root>


    <!-- 5. Status Listener -->
    <statusListener
        class="ch.qos.logback.core.status.OnConsoleStatusListener" />

</configuration>