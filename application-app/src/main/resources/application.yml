logging:
  level:
    # 这里设置的日志级别优先于logback-spring.xml文件Loggers中的日志级别。
    com.rongchen.byh: info
  config: classpath:logback-spring.xml

server:
  port: 8088
  tomcat:
    uri-encoding: UTF-8
    threads:
      max: 100
      min-spare: 10
  servlet:
    encoding:
      force: true
      charset: UTF-8
      enabled: true

# spring相关配置
spring:
  application:
    name: application-app
  profiles:
    active: localdev
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  mvc:
    converters:
      preferred-json-mapper: fastjson
  main:
    allow-circular-references: true
  groovy:
    template:
      check-template-location: false
nacos:
  config:
    bootstrap:
      #开启系统启动时预读取nacos的配置，用于满足@Value注入数据的场景
      enable: true
    #配置所属命名空间的id,此处我们配置名称为dev的id，可以在命名空间列表查看id的值
    namespace: 254a41f6-d70a-4358-8034-efc1be9bd8b7
    #配置所属分组
    group: DEFAULT_GROUP
    #配置ID
    data-id: qyc-prod
    #配置文件类型,对应nacos配置页面的配置格式，默认是properties
    type: yaml
    #nacos服务器地址
    server-addr: **********:8848
    #开启自动刷新nacos配置
    auto-refresh: true
    #针对配置项同名的情况，是否允许nacos的配置覆盖本地的配置
    remote-first: true