<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.dao.CapitalDataMapper">

    <resultMap id="BaseResultMap" type="com.rongchen.byh.app.entity.CapitalData">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="sort" column="sort" jdbcType="INTEGER"/>
            <result property="beanName" column="bean_name" jdbcType="VARCHAR"/>
            <result property="paramConfig" column="param_config" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,status,
        sort,bean_name,param_config,
        create_time,update_time
    </sql>
    <select id="selectByDisburseId" resultType="com.rongchen.byh.app.entity.CapitalData">
        SELECT
        <include refid="Base_Column_List"/>
        FROM capital_data
        WHERE id = (select capital_id from user_capital_record where id = #{capitalRecordId})
    </select>
</mapper>
