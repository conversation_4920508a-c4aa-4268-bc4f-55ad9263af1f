package com.rongchen.byh.app.v2.service.impl;

import com.rongchen.byh.app.v2.dao.UserProgressMapper;
import com.rongchen.byh.app.v2.entity.UserProgress;
import com.rongchen.byh.app.v2.service.IUserProgressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UserProgressServiceImpl implements IUserProgressService {
    @Autowired
    private UserProgressMapper userProgressMapper;
    @Override
    public UserProgress selectByUserId(Long userId) {
        UserProgress progress = userProgressMapper.selectByUserId(userId);
        return progress;
    }

    @Override
    public void updateOrInsertByUserId(UserProgress progress) {
        userProgressMapper.updateOrInsertByUserId(progress);
    }

    @Override
    public void delUserPrograss(Long userId) {
        userProgressMapper.delUserPrograss(userId);
    }
}
