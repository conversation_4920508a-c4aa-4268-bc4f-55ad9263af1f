package com.rongchen.byh.app.job;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.app.dao.RepayScheduleMapper;
import com.rongchen.byh.app.entity.DisburseData;
import com.rongchen.byh.common.core.util.MDCUtil;
import com.rongchen.byh.common.rabbitmq.constants.QueueConstant;
import com.rongchen.byh.common.rabbitmq.dto.RepayDto;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;


/**
 * 自动查询账单是否逾期，如果逾期则查询最新的账单，并更新账单逾期状态
 */
@Component
@Slf4j
public class UpdateRepayOverdueJob {

    @Resource
    RepayScheduleMapper repayScheduleMapper;
    @Resource
    RabbitTemplate rabbitTemplate;


    @XxlJob("updateRepayOverdueJobHandler")
    public void updateRepayOverdueJobHandler() {

        String traceId = UUID.fastUUID().toString();
        MDCUtil.setTraceId(traceId);

        String today = DateUtil.today();

        // 查询逾期账单
        List<DisburseData> list = repayScheduleMapper.selectListOverdueUp(today);
        if (CollectionUtil.isEmpty(list)) {
            log.info("账单逾期更新 查询账单是否逾期为空");
            return;
        }

        list.forEach(disburseData -> {
            String traceId2 = MDCUtil.generateTraceId();
            log.info("账单逾期更新 id:{}, traceId:{}", disburseData.getId(),traceId2);

            RepayDto repayDto = new RepayDto();
            repayDto.setDisburseId(disburseData.getId());
            repayDto.setTraceId(traceId2);
            rabbitTemplate.convertAndSend(QueueConstant.UPDATE_REPAY_OVERDUE_QUEUE, JSONObject.toJSONString(repayDto));
        });


    }
}
