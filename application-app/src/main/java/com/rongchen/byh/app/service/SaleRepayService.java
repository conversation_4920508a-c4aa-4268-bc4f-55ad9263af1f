package com.rongchen.byh.app.service;

import com.rongchen.byh.app.dto.app.BillRepayApplyDto;
import com.rongchen.byh.app.entity.SaleSchedule;
import com.rongchen.byh.common.core.object.ResponseResult;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 赊销还款
 * @date 2024/12/15 16:22:03
 */
public interface SaleRepayService {
    ResponseResult<Void> saleRepay(BillRepayApplyDto billRepayApplyDto);

    ResponseResult<Void> saleRepayNew(BillRepayApplyDto billRepayApplyDto);

    SaleSchedule selectLast(SaleSchedule saleSchedule);

    void updateById(SaleSchedule saleSchedule1);
}
