<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.dao.RepayScheduleApplyMapper">
  <insert id="insertSelective" keyColumn="id" keyProperty="id"
    parameterType="com.rongchen.byh.app.entity.RepayScheduleApply" useGeneratedKeys="true">
    <!--@mbg.generated--> insert
    into `repay_schedule_apply` <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="repayApplyNo != null"> `repay_apply_no`, </if>
      <if test="userId != null"> `user_id`, </if>
      <if
        test="repayScheduleId != null"> `repay_schedule_id`, </if>
      <if test="repayType != null">
    `repay_type`, </if>
      <if test="repayStatus != null"> `repay_status`, </if>
      <if
        test="reason != null"> `reason`, </if>
      <if test="createTime != null"> `create_time`, </if>
      <if
        test="updateTime != null"> `update_time`, </if>
      <if test="responseTime != null">
    `response_time`, </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="repayApplyNo != null"> #{repayApplyNo,jdbcType=VARCHAR}, </if>
      <if
        test="userId != null"> #{userId,jdbcType=BIGINT}, </if>
      <if test="repayScheduleId != null">
    #{repayScheduleId,jdbcType=BIGINT}, </if>
      <if test="repayType != null">
    #{repayType,jdbcType=INTEGER}, </if>
      <if test="repayStatus != null">
    #{repayStatus,jdbcType=INTEGER}, </if>
      <if test="reason != null"> #{reason,jdbcType=VARCHAR}, </if>
      <if
        test="createTime != null"> #{createTime,jdbcType=TIMESTAMP}, </if>
      <if
        test="updateTime != null"> #{updateTime,jdbcType=TIMESTAMP}, </if>
      <if
        test="responseTime != null"> #{responseTime,jdbcType=TIMESTAMP}, </if>
    </trim>
  </insert>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map"
    useGeneratedKeys="true">
    <!--@mbg.generated--> insert into `repay_schedule_apply` (`repay_apply_no`, `user_id`,
    `repay_schedule_id`, `repay_type`, `repay_status`, `reason`, `create_time`, `update_time`,
    `response_time`) values <foreach collection="list" item="item" separator=",">
    (#{item.repayApplyNo,jdbcType=VARCHAR}, #{item.userId,jdbcType=BIGINT},
    #{item.repayScheduleId,jdbcType=BIGINT}, #{item.repayType,jdbcType=INTEGER},
    #{item.repayStatus,jdbcType=INTEGER}, #{item.reason,jdbcType=VARCHAR},
    #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP},
    #{item.responseTime,jdbcType=TIMESTAMP} ) </foreach>
  </insert>
  <insert id="batchInsertOrUpdate" keyColumn="id" keyProperty="id" parameterType="map"
    useGeneratedKeys="true">
    <!--@mbg.generated--> insert into `repay_schedule_apply` (`repay_apply_no`, `user_id`,
    `repay_schedule_id`, `repay_type`, `repay_status`, `reason`, `create_time`, `update_time`,
    `response_time`) values <foreach collection="list" item="item" separator=",">
    (#{item.repayApplyNo,jdbcType=VARCHAR}, #{item.userId,jdbcType=BIGINT},
    #{item.repayScheduleId,jdbcType=BIGINT}, #{item.repayType,jdbcType=INTEGER},
    #{item.repayStatus,jdbcType=INTEGER}, #{item.reason,jdbcType=VARCHAR},
    #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP},
    #{item.responseTime,jdbcType=TIMESTAMP} ) </foreach> on duplicate key update
    repay_apply_no=values(repay_apply_no), user_id=values(user_id),
    repay_schedule_id=values(repay_schedule_id), repay_type=values(repay_type),
    repay_status=values(repay_status), reason=values(reason), create_time=values(create_time),
    update_time=values(update_time), response_time=values(response_time) </insert>
  <resultMap id="BaseResultMap" type="com.rongchen.byh.app.entity.RepayScheduleApply">
    <!--@mbg.generated-->
    <!--@Table
    `repay_schedule_apply`-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="repay_apply_no" jdbcType="VARCHAR" property="repayApplyNo" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="repay_schedule_id" jdbcType="BIGINT" property="repayScheduleId" />
    <result column="repay_type" jdbcType="INTEGER" property="repayType" />
    <result column="repay_status" jdbcType="INTEGER" property="repayStatus" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="response_time" jdbcType="TIMESTAMP" property="responseTime" />
  </resultMap>

  <select id="selectByRepayScheduleIdAndUserId"
    resultType="com.rongchen.byh.app.entity.RepayScheduleApply"> select <include
      refid="Base_Column_List" /> from repay_schedule_apply where repay_schedule_id =
    #{repayScheduleId} and user_id = #{userId} order by id desc limit 1 </select>
  <select id="selectByRepayApplyNo" resultType="com.rongchen.byh.app.entity.RepayScheduleApply">
    select * from repay_schedule_apply where repay_apply_no = #{repayApplyNo} </select>
  <select id="selectPendingAppliesWithCapital"
    resultType="com.rongchen.byh.app.dto.RepayApplyWithCapitalDto"> SELECT rsa.id AS
    "applyRecord.id", rsa.repay_apply_no AS "applyRecord.repayApplyNo", rsa.user_id AS
    "applyRecord.userId", rsa.repay_schedule_id AS "applyRecord.repayScheduleId", rsa.repay_type AS
    "applyRecord.repayType", rsa.repay_status AS "applyRecord.repayStatus", rsa.reason AS
    "applyRecord.reason", rsa.create_time AS "applyRecord.createTime", rsa.update_time AS
    "applyRecord.updateTime", rsa.response_time AS "applyRecord.responseTime", cd.id AS
    "capitalData.id", cd.name AS "capitalData.name", cd.bean_name AS "capitalData.beanName", cd.sort
    AS "capitalData.sort", cd.status AS "capitalData.status", cd.create_time AS
    "capitalData.createTime", cd.update_time AS "capitalData.updateTime" FROM repay_schedule_apply
    rsa JOIN repay_schedule rs ON rsa.repay_schedule_id = rs.id JOIN disburse_data dd ON
    rs.disburse_id = dd.id JOIN capital_data cd ON dd.capital_id = cd.id WHERE rsa.repay_status = 0
    AND rsa.create_time &lt; #{cutoffTime} ORDER BY rsa.create_time ASC </select>
  <sql id="Base_Column_List">
    <!--@mbg.generated--> `id`, `repay_apply_no`, `user_id`, `repay_schedule_id`, `repay_type`,
    `repay_status`, `reason`, `create_time`, `update_time`, `response_time` </sql>

  <update id="updateByPrimaryKeySelective"
    parameterType="com.rongchen.byh.app.entity.RepayScheduleApply">
    <!--@mbg.generated--> update `repay_schedule_apply` <set>
      <if test="repayApplyNo != null"> `repay_apply_no` = #{repayApplyNo,jdbcType=VARCHAR}, </if>
      <if
        test="userId != null"> `user_id` = #{userId,jdbcType=BIGINT}, </if>
      <if
        test="repayScheduleId != null"> `repay_schedule_id` = #{repayScheduleId,jdbcType=BIGINT}, </if>
      <if
        test="repayType != null"> `repay_type` = #{repayType,jdbcType=INTEGER}, </if>
      <if
        test="repayStatus != null"> `repay_status` = #{repayStatus,jdbcType=INTEGER}, </if>
      <if
        test="reason != null"> `reason` = #{reason,jdbcType=VARCHAR}, </if>
      <if
        test="createTime != null"> `create_time` = #{createTime,jdbcType=TIMESTAMP}, </if>
      <if
        test="updateTime != null"> `update_time` = #{updateTime,jdbcType=TIMESTAMP}, </if>
      <if
        test="responseTime != null"> `response_time` = #{responseTime,jdbcType=TIMESTAMP}, </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT} </update>


  <update id="updateByPrimaryKey" parameterType="com.rongchen.byh.app.entity.RepayScheduleApply">
    <!--@mbg.generated-->
    update `repay_schedule_apply` set `repay_apply_no` = #{repayApplyNo,jdbcType=VARCHAR}, `user_id`
    = #{userId,jdbcType=BIGINT}, `repay_schedule_id` = #{repayScheduleId,jdbcType=BIGINT},
    `repay_type` = #{repayType,jdbcType=INTEGER}, `repay_status` = #{repayStatus,jdbcType=INTEGER},
    `reason` = #{reason,jdbcType=VARCHAR}, `create_time` = #{createTime,jdbcType=TIMESTAMP},
    `update_time` = #{updateTime,jdbcType=TIMESTAMP}, `response_time` =
    #{responseTime,jdbcType=TIMESTAMP} where `id` = #{id,jdbcType=BIGINT} </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated--> update `repay_schedule_apply` <foreach
      close=")" collection="list" item="item" open="(" separator=", "> #{item.id,jdbcType=BIGINT} </foreach>
    where `id` in <trim
      prefix="set" suffixOverrides=",">
      <trim prefix="`repay_apply_no` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.repayApplyNo,jdbcType=VARCHAR} </foreach>
      </trim>
      <trim
        prefix="`user_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.userId,jdbcType=BIGINT} </foreach>
      </trim>
      <trim
        prefix="`repay_schedule_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.repayScheduleId,jdbcType=BIGINT} </foreach>
      </trim>
      <trim
        prefix="`repay_type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.repayType,jdbcType=INTEGER} </foreach>
      </trim>
      <trim
        prefix="`repay_status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.repayStatus,jdbcType=INTEGER} </foreach>
      </trim>
      <trim prefix="`reason` = case"
        suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.reason,jdbcType=VARCHAR} </foreach>
      </trim>
      <trim prefix="`create_time` = case"
        suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.createTime,jdbcType=TIMESTAMP} </foreach>
      </trim>
      <trim
        prefix="`update_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.updateTime,jdbcType=TIMESTAMP} </foreach>
      </trim>
      <trim
        prefix="`response_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.responseTime,jdbcType=TIMESTAMP} </foreach>
      </trim>
    </trim>
  </update>

  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated--> update `repay_schedule_apply` <foreach
      close=")" collection="list" item="item" open="(" separator=", "> #{item.id,jdbcType=BIGINT} </foreach>
    where `id` in <trim
      prefix="set" suffixOverrides=",">
      <trim prefix="`repay_apply_no` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.repayApplyNo != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.repayApplyNo,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`user_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.userId != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.userId,jdbcType=BIGINT} </if>
        </foreach>
      </trim>
      <trim
        prefix="`repay_schedule_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.repayScheduleId != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.repayScheduleId,jdbcType=BIGINT} </if>
        </foreach>
      </trim>
      <trim
        prefix="`repay_type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.repayType != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.repayType,jdbcType=INTEGER} </if>
        </foreach>
      </trim>
      <trim
        prefix="`repay_status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.repayStatus != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.repayStatus,jdbcType=INTEGER} </if>
        </foreach>
      </trim>
      <trim
        prefix="`reason` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.reason != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.reason,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`create_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.createTime,jdbcType=TIMESTAMP} </if>
        </foreach>
      </trim>
      <trim
        prefix="`update_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.updateTime,jdbcType=TIMESTAMP} </if>
        </foreach>
      </trim>
      <trim
        prefix="`response_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.responseTime != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.responseTime,jdbcType=TIMESTAMP} </if>
        </foreach>
      </trim>
    </trim>
  </update>
</mapper>