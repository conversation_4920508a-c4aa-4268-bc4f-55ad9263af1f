package com.rongchen.byh.app.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rongchen.byh.app.entity.RepaySaleApply;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【repay_sale_apply(赊销结果表)】的数据库操作Mapper
* @createDate 2024-12-15 18:48:25
* @Entity generator.domain.RepaySaleApply
*/
public interface RepaySaleApplyMapper extends BaseMapper<RepaySaleApply> {
    RepaySaleApply selectByRepaySaleScheduleIdAndUserId(@Param("repaySaleScheduleId") Long repaySaleScheduleId, @Param("userId") Long userId);

    RepaySaleApply selectByRepayApplyNo(String repayApplyNo);
}




