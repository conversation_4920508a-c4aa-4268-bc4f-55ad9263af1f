package com.rongchen.byh.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * api风控失败记录表
 * @TableName api_risk_fail_record
 */
@TableName(value ="api_risk_fail_record")
@Data
public class ApiRiskFailRecord implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * api_credit_record主键id
     */
    private Long apiId;

    /**
     * 授信申请表id
     */
    private Long applyId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 授信单号
     */
    private String creditNo;

    /**
     * 风控唯一标识
     */
    private String creditId;

    /**
     * 请求参数
     */
    private String requestParam;

    /**
     * 创建时间
     */
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}