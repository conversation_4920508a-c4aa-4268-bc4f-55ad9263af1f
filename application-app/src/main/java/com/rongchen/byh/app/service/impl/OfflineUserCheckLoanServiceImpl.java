package com.rongchen.byh.app.service.impl;
import java.util.Date;

import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rongchen.byh.app.constant.LoanType;
import com.rongchen.byh.app.constant.SourceMode;
import com.rongchen.byh.app.constant.UserAuditStatus;
import com.rongchen.byh.app.dao.*;
import com.rongchen.byh.app.dto.h5.UserCheckLoanApplyDto;
import com.rongchen.byh.app.entity.*;
import com.rongchen.byh.app.service.OfflineUserCheckLoanService;
import com.rongchen.byh.app.utils.UserTokenUtil;
import com.rongchen.byh.app.vo.h5.UserCheckLoanApplyVo;
import com.rongchen.byh.common.api.idCardVerify.service.IdCardVerifyService;
import com.rongchen.byh.common.api.idCardVerify.vo.VerifyVo;
import com.rongchen.byh.common.api.riskControl.dto.PreLoanAuditDto;
import com.rongchen.byh.common.api.riskControl.service.RiskControlService;
import com.rongchen.byh.common.api.riskControl.vo.PreLoanAuditVo;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.app.entity.StaffAuditRecord;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 线下模式用户初筛相关业务
 * @date 2024/12/11 10:26:05
 */
@Service
public class OfflineUserCheckLoanServiceImpl implements OfflineUserCheckLoanService {
    @Resource
    private IdCardVerifyService idCardVerifyService;
    @Resource
    private UserLoanApplyMapper userLoanApplyMapper;
    @Resource
    private RiskControlService riskControlService;
    @Resource
    private UserDataMapper userDataMapper;
    @Resource
    private UserDetailMapper userDetailMapper;
    @Resource
    private UserCreditDataMapper userCreditDataMapper;
    @Resource
    private StaffAuditRecordMapper staffAuditRecordMapper;
    @Resource
    private UserStaffMapper userStaffMapper;
    @Value("${spring.profiles.active}")
    private String active;

    @Override
    public synchronized ResponseResult<UserCheckLoanApplyVo> apply(UserCheckLoanApplyDto userCheckLoanApplyDto) {
        if (userCheckLoanApplyDto != null && userCheckLoanApplyDto.getIdNumber() != null){
            userCheckLoanApplyDto.setIdNumber(userCheckLoanApplyDto.getIdNumber().toUpperCase());
        }
        // 前置校验
        if (!IdcardUtil.isValidCard(userCheckLoanApplyDto.getIdNumber())) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "身份证号格式错误");
        }
        // 是否已经提交过一次申请，是则提示已经提交无需再次提交
        Long userId = UserTokenUtil.getUserId();
        UserData userData = userDataMapper.selectById(userId);
        if (ObjectUtil.isEmpty(userData)) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "用户不存在");
        }
        UserStaff userStaff = userStaffMapper.selectByUserId(userId);
        if (ObjectUtil.isEmpty(userStaff)) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "请先绑定员工");
        }
        UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userId, LoanType.CHECK, 1);
        UserCheckLoanApplyVo userCheckLoanApplyVo = new UserCheckLoanApplyVo();
        if (userLoanApply != null) {
            UserCheckLoanApplyVo result = userCheckLoanApplyVo;
            result.setUserId(userId);
            result.setAuditStatus(userLoanApply.getAuditsStatus());
            return ResponseResult.success(result);
        }
        // 根据身份证号查询用户
        UserDetail detail = userDetailMapper.selectOne(new LambdaQueryWrapper<UserDetail>().eq(UserDetail::getWebIdCard, userCheckLoanApplyDto.getIdNumber()));
        if (ObjectUtil.isNotEmpty(detail)) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "身份证号已存在，请勿重复申请");
        }
        // 二要素校验----->修改为三要素校验
//        VerifyVo verifyResult = idCardVerifyService.verify(userCheckLoanApplyDto.getIdNumber(),
//                userCheckLoanApplyDto.getUserName());
//        if (verifyResult.getCode() != 0) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL, verifyResult.getMsg());
//        }
        if ("prod".equals(active)) {
            VerifyVo verifyResult = idCardVerifyService.verifyThree(userCheckLoanApplyDto.getIdNumber(),
                    userCheckLoanApplyDto.getUserName(), userData.getMobile());
            if (verifyResult.getCode() != 1) {
                return ResponseResult.error(ErrorCodeEnum.FAIL, "您的姓名，身份证号，手机号不一致，请核对");
            }
        }
        // 记录申请
        UserLoanApply apply = new UserLoanApply();
        apply.setUserId(userId);
        apply.setApplyType(LoanType.CHECK);
        apply.setUserApplyInfo(JSONObject.toJSONString(userCheckLoanApplyDto));
        apply.setOnlineType(1);
        userLoanApplyMapper.insert(apply);

        // 记录用户身份证号和姓名
        UserDetail userDetail = new UserDetail();
        userDetail.setUserId(userId);
        userDetail.setWebName(userCheckLoanApplyDto.getUserName());
        userDetail.setWebIdCard(userCheckLoanApplyDto.getIdNumber());
        userDetail.setWebTwoElements(1);
        userDetailMapper.insert(userDetail);

        // 风控
        PreLoanAuditDto preLoanAuditDto = new PreLoanAuditDto();
        preLoanAuditDto.setApplyId(String.valueOf(apply.getId()));
        preLoanAuditDto.setMobile(userData.getMobile());
        preLoanAuditDto.setName(userCheckLoanApplyDto.getUserName());
        preLoanAuditDto.setIdNumber(userCheckLoanApplyDto.getIdNumber());
        PreLoanAuditVo preLoanAuditVo = riskControlService.offlineH5PreLoanAudit(preLoanAuditDto);
        Integer result = preLoanAuditVo.getResult();
        if (result == -1) {
            apply.setAuditsStatus(2);
            userLoanApplyMapper.updateById(apply);
            // 更新用户审核状态
            userData.setAuditStatus(UserAuditStatus.FIRST_REJECT);
            userDataMapper.updateById(userData);
            return ResponseResult.success(null);
        }else {
            if (result == 0 || result == 1) {
                apply.setAuditsStatus(1);
            } else {
                return ResponseResult.error(ErrorCodeEnum.FAIL, "请稍候再试");
            }
        }
        // 更新用户风险等级
        userDetail.setRiskLevel(preLoanAuditVo.getCreditRating());
        userDetailMapper.updateById(userDetail);

        userLoanApplyMapper.updateById(apply);
        // 更新用户审核状态
        userData.setAuditStatus(UserAuditStatus.WAIT_REVIEW);
        userData.setSourceMode(SourceMode.OFFLINE);
        userDataMapper.updateById(userData);
        // 添加待审核记录

        StaffAuditRecord staffAuditRecord = new StaffAuditRecord();
        staffAuditRecord.setUserStaffId(userStaff.getId());
        staffAuditRecord.setSubmitTime(new Date());
        staffAuditRecordMapper.insert(staffAuditRecord);


        userCheckLoanApplyVo.setUserId(userId);
        userCheckLoanApplyVo.setAuditStatus(apply.getAuditsStatus());
        return ResponseResult.success(userCheckLoanApplyVo);
    }

    @Override
    public ResponseResult<UserCheckLoanApplyVo> queryResult() {
        Long userId = UserTokenUtil.getUserId();
        UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userId, LoanType.CHECK, 1);
        UserCheckLoanApplyVo userCheckLoanApplyVo = new UserCheckLoanApplyVo();
        userCheckLoanApplyVo.setUserId(userId);
        if (ObjectUtil.isEmpty(userLoanApply)) {
            userCheckLoanApplyVo.setAuditStatus(3);
            return ResponseResult.success(userCheckLoanApplyVo);
        }
        UserData userData = userDataMapper.selectById(userId);
        if (ObjectUtil.isEmpty(userData)) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "用户不存在");
        }
        if (userData.getAuditStatus().equals(UserAuditStatus.FIRST_REJECT)) {
            userCheckLoanApplyVo.setAuditStatus(2);
            return ResponseResult.success(userCheckLoanApplyVo);
        } else if (userData.getAuditStatus().equals(UserAuditStatus.WAIT_REVIEW)) {
            userCheckLoanApplyVo.setAuditStatus(1);
            return ResponseResult.success(userCheckLoanApplyVo);
        } else if (userData.getAuditStatus().equals(UserAuditStatus.REVIEW_REJECT)) {
            userCheckLoanApplyVo.setAuditStatus(6);
            return ResponseResult.success(userCheckLoanApplyVo);
        } else if (userData.getAuditStatus() >= UserAuditStatus.WAIT_APP_CREDIT) {
            userCheckLoanApplyVo.setAuditStatus(5);
            UserCreditData userCreditData = userCreditDataMapper.queryByUserId(userId);
            if (ObjectUtil.isNotEmpty(userCreditData)){
                userCheckLoanApplyVo.setCreditAmount(userCreditData.getCreditAmount());
            }
            return ResponseResult.success(userCheckLoanApplyVo);
        } else {
            userCheckLoanApplyVo.setAuditStatus(userLoanApply.getAuditsStatus());
        }
        return ResponseResult.success(userCheckLoanApplyVo);
    }
}
