package com.rongchen.byh.app.dto.app;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * @ClassName AppLoginDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/11 10:42
 * @Version 1.0
 **/
@Data
public class AppLoginDto {

    @Schema(description = "手机号")
    @NotEmpty(message = "手机号 不能为空")
    private String mobile;

    @Schema(description = "渠道")
    private Long channelId;

    @Schema(description = "验证码")
    @NotEmpty(message = "验证码 不能为空")
    private String code;

    @Schema(description = "经度")
    private String longitude;

    @Schema(description = "纬度")
    private String latitude;


    @Schema(description = "设备品牌")
    private String deviceBrand;


    @Schema(description = "设备网络类型")
    private String networkType;


    @Schema(description = "手机型号")
    private String devAlias;


    @Schema(description = "设备id")
    private String deviceId;


    @Schema(description = "ip地址")
    private String clientIp;


    @Schema(description = "经纬度坐标系类型")
    private String coordinateType;


    @Schema(description = "设备GPS定位城市")
    private String gpsCity;


    @Schema(description = "LBS定位地址")
    private String lbsAddress;


    @Schema(description = "GPS定位地址")
    private String gpsAddress;


    @Schema(description = "操作系统")
    private String os;


    @Schema(description = "手机系统版本号")
    private String osVersion;

}
