package com.rongchen.byh.app.controller.offline;

import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.rongchen.byh.app.dto.h5.UserCheckLoanApplyDto;
import com.rongchen.byh.app.service.OfflineUserCheckLoanService;
import com.rongchen.byh.app.service.UserCheckLoanService;
import com.rongchen.byh.app.vo.h5.UserCheckLoanApplyVo;
import com.rongchen.byh.common.core.annotation.DisableDataFilter;
import com.rongchen.byh.common.core.object.ResponseResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 线下模式h5页面用户相关接口
 * @date 2025/2/8 17:06:02
 */
@ApiSupport(order = 1)
@Tag(name = "线下模式h5页面用户相关接口")
@DisableDataFilter
@Slf4j
@RestController
@RequestMapping("/userApi/offline/userCheckLoan")
public class OfflineUserCheckLoanController {
    @Resource
    private OfflineUserCheckLoanService offlineUserCheckLoanService;
    @PostMapping("/apply")
    @Operation(summary = "线下模式初筛贷款申请")
    public ResponseResult<UserCheckLoanApplyVo> apply(@RequestBody @Validated UserCheckLoanApplyDto userCheckLoanApplyDto) {
        return offlineUserCheckLoanService.apply(userCheckLoanApplyDto);
    }

    @PostMapping("/queryResult")
    @Operation(summary = "线下模式初筛贷款申请结果查询")
    public ResponseResult<UserCheckLoanApplyVo> queryResult() {
        return offlineUserCheckLoanService.queryResult();
    }
}
