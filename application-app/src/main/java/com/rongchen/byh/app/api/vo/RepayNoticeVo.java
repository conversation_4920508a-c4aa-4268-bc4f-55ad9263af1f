package com.rongchen.byh.app.api.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class RepayNoticeVo implements Serializable {
   private static final long serialVersionUID = 1L;
   /**
    *请求方产品码
    * */
   private String reqSysCode;
   /**
    *资金方编码
    * */
   private String fundCode;
   /**
    *产品代码
    * */
   private String productCode;
   /**
    *chanRepayApplyNo	渠道还款流水号
    * */
   private String chanRepayApplyNo;
   /**
    *还款流水号
    * */
   private String repayApplyNo;
   /**
    *贷款编号
    * */
   private String loanNo;
   /**
    *还款金额
    * */
   private String amt;
   /**
    *还款状态
    * */
   private String status;
   /**
    *还款结果描述
    * */
   private String result;
   /**
    *还款时间
    * */
   private String repayTime;
   /**
    *还款期数
    * */
   private String repayTerm;
   /**
    *还款方式
    * */
   private String repayMode;
   /**
    *还款类型
    * */
   private String repayType;
   /**
    *批次号
    * */
   private String batchNo;
   /**
    *申请来源
    * */
   private String applySource;
   /**
    *银行卡后4位
    * */
   private String bankNoSuf;
   /**
    *银行名称
    * */
   private String bankName;
}

