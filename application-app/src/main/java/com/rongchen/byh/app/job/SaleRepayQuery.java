package com.rongchen.byh.app.job;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.rongchen.byh.app.config.ZifangFactory;
import com.rongchen.byh.app.dao.CapitalDataMapper;
import com.rongchen.byh.app.dao.RepaySaleApplyMapper;
import com.rongchen.byh.app.dao.SaleScheduleMapper;
import com.rongchen.byh.app.entity.CapitalData;
import com.rongchen.byh.app.entity.RepaySaleApply;
import com.rongchen.byh.app.entity.SalePayVo;
import com.rongchen.byh.app.entity.SaleSchedule;
import com.rongchen.byh.common.api.zifang.dto.SaleRepayResultDto;
import com.rongchen.byh.common.api.zifang.service.OtherApi;
import com.rongchen.byh.common.api.zifang.vo.SaleRepayResultVo;
import com.rongchen.byh.common.core.constant.SettleFlagConstant;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;


/**
 * 赊销账单还款查询
 */
@Component
@Slf4j
public class SaleRepayQuery {


    @Resource
    SaleScheduleMapper saleScheduleMapper;
    @Resource
    CapitalDataMapper capitalDataMapper;
    @Resource
    ZifangFactory zifangFactory;
    @Resource
    private RepaySaleApplyMapper repaySaleApplyMapper;

    @XxlJob("saleRepayQueryHandler")
    public void saleRepayQueryHandler() {
        List<SalePayVo> repaying = saleScheduleMapper.repaying();
        if (CollectionUtil.isEmpty(repaying)) {
            log.info("赊销还款中查询 没有还款中订单");
            return;
        }
        repaying.forEach(this::queryProcess);
    }

    private void queryProcess(SalePayVo salePayVo) {
        try {
            CapitalData capitalData = capitalDataMapper.selectById(salePayVo.getCapitalId());
            if (capitalData == null) {
                log.error("赊销还款中查询 - 未找到资方信息: salePayVo={}", salePayVo);
                return;
            }
            OtherApi otherApi = zifangFactory.getApi(capitalData.getBeanName(),OtherApi.class);
            SaleRepayResultDto applyDto = new SaleRepayResultDto();
            applyDto.setSaleNo(salePayVo.getSaleNo());
            applyDto.setRepayApplyNo(salePayVo.getRepayApplyNo());
            ResponseResult<SaleRepayResultVo> saleRepayResult = otherApi.getSaleRepayResult(applyDto);
            if (saleRepayResult.isSuccess()) {

                SaleRepayResultVo data = saleRepayResult.getData();
                int repayStatus = 0;
                String reason = "";
                if ("0000".equals(data.getResponseCode())) {
                    SaleSchedule saleSchedule = new SaleSchedule();
                    saleSchedule.setId(salePayVo.getSaleScheduleId());
                    if ("SUCCESS".equals(data.getStatus())) {
                        //成功
                        saleSchedule.setSettleFlag(SettleFlagConstant.CLOSE);
                        saleSchedule.setDatePay(DateUtil.today());
                        saleSchedule.setDatePayTime(data.getRepayTime());
                        repayStatus = 1;
                    } else if ("FAIL".equals(data.getStatus())) {
                        // 失败
                        saleSchedule.setSettleFlag(SettleFlagConstant.RUNNING);
                        repayStatus = 2;
                        reason = data.getResult();
                    } else if ("CLOSE".equals(data.getStatus())) {
                        // 关闭
                        saleSchedule.setSettleFlag(SettleFlagConstant.CLOSE);
                        saleSchedule.setDatePay(DateUtil.today());
                        saleSchedule.setDatePayTime(data.getRepayTime());
                        repayStatus = 1;
                    } else {
                        log.info("赊销还款中查询 - 订单号: {}, 状态: {}", salePayVo.getRepayApplyNo(), data.getStatus());
                        return;
                    }
                    saleScheduleMapper.updateById(saleSchedule);
                    RepaySaleApply repaySaleApply = repaySaleApplyMapper.selectByRepayApplyNo(salePayVo.getRepayApplyNo());
                    if (repaySaleApply != null) {
                        repaySaleApply.setRepayStatus(repayStatus);
                        repaySaleApply.setReason(reason);
                        repaySaleApply.setResponseTime(DateUtil.date());
                        repaySaleApplyMapper.updateById(repaySaleApply);
                    } else {
                        log.info("赊销还款中查询 - 订单号: {}, 资方查询响应失败：{}", salePayVo.getRepayApplyNo(), data);
                        return;
                    }
                }
                log.info("赊销还款中查询 - 订单号: {}, 资方查询失败：{}", salePayVo.getRepayApplyNo(), saleRepayResult);
                return;
            }
        } catch (Exception e) {
            log.info("赊销还款中查询 - 订单号: {}, 异常: {}", salePayVo.getRepayApplyNo(),e);
            return;
        }
    }
}
