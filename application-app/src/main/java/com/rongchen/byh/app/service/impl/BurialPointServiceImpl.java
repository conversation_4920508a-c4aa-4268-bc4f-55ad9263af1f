package com.rongchen.byh.app.service.impl;

import cn.hutool.core.util.StrUtil;
import com.rongchen.byh.app.dao.BurialPointLogMapper;
import com.rongchen.byh.app.dao.ChannelDataMapper;
import com.rongchen.byh.app.dto.app.BurialPointDto;
import com.rongchen.byh.app.entity.BurialPointLog;
import com.rongchen.byh.app.entity.ChannelData;
import com.rongchen.byh.app.service.BurialPointService;
import com.rongchen.byh.app.utils.UserTokenUtil;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.ContextUtil;
import com.rongchen.byh.common.core.util.IpUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * @ClassName BurialPointServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/1/8 14:44
 * @Version 1.0
 **/
@Service
public class BurialPointServiceImpl implements BurialPointService {


    @Resource
    BurialPointLogMapper burialPointLogMapper;
    @Resource
    ChannelDataMapper channelDataMapper;


    @Override
    public ResponseResult<Void> burialPointAdd(BurialPointDto dto, HttpServletRequest request) {
        BurialPointLog  log = new BurialPointLog();
        if (dto.getChannelId() != null) {
            log.setChannelId(dto.getChannelId());
        }
        if (StrUtil.isNotEmpty(dto.getChannel())) {
            ChannelData channelData = channelDataMapper.selectBySecret(dto.getChannel());
            if (channelData != null) {
                log.setChannelId(channelData.getId());
            }
        }
        if (dto.getUserId() != null) {
//            Long userId = UserTokenUtil.getUserId();
//            log.setUserId(userId);
            log.setUserId(dto.getUserId());
        }
        log.setIp(IpUtil.getRemoteIpAddress(request));
        log.setPageKey(dto.getPageKey());
        log.setPageName(dto.getPageName());
        log.setButtonKey(dto.getButtonKey());
        log.setButtonName(dto.getButtonName());
        log.setOther(dto.getOther());
        int insert = burialPointLogMapper.insert(log);
        if (insert > 0){
            return ResponseResult.success();
        }else {
            return ResponseResult.error(ErrorCodeEnum.FAIL);
        }
    }
}
