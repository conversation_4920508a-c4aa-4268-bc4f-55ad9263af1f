package com.rongchen.byh.app.loveSign.strategy;

import com.ancun.netsign.model.ContractUserInput;
import com.ancun.netsign.model.UserSignStrategyInput;
import com.rongchen.byh.app.loveSign.LoveSignProperties;
import com.rongchen.byh.app.loveSign.SignDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/12/13 16:44:29
 */
@Service
public class FaceSignStrategy implements SignStrategy{
    @Resource
    private LoveSignProperties loveSignProperties;
    @Override
    public List<ContractUserInput> getInputList(SignDto dto) {
        List<ContractUserInput> inputs = new ArrayList<>(2);
        ContractUserInput inputUser = new ContractUserInput();
        inputUser.setContractNo(dto.getContractNo());
        inputUser.setAccount(dto.getAccount());
        inputUser.setSignType(2);
        List<UserSignStrategyInput> signStrategyList = new ArrayList<>(1);

        UserSignStrategyInput strategyNameInput = new UserSignStrategyInput();
        strategyNameInput.setAttachNo(1);
        strategyNameInput.setLocationMode(4);
        strategyNameInput.setCanDrag(11);
        // signDate
        strategyNameInput.setSignKey("signUserName");
        strategyNameInput.setSignType(1);
        signStrategyList.add(strategyNameInput);

        UserSignStrategyInput strategyDateInput = new UserSignStrategyInput();
        strategyDateInput.setAttachNo(1);
        strategyDateInput.setLocationMode(4);
        strategyDateInput.setCanDrag(11);
        strategyDateInput.setSignKey("signDate");
        strategyDateInput.setSignType(1);
        signStrategyList.add(strategyDateInput);


        inputUser.setSignStrategyList(signStrategyList);
        inputs.add(inputUser);
        return inputs;
    }

    @Override
    public boolean match(String templateNo) {
        return loveSignProperties.getFace().equals(templateNo);
    }
}
