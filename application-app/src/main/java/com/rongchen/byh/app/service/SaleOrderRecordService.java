package com.rongchen.byh.app.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rongchen.byh.app.dao.SaleOrderRecordMapper;
import com.rongchen.byh.app.entity.SaleOrderRecord;
import com.rongchen.byh.common.api.yunxi.vo.YunXiOrderDetailVo;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;

/**
 * 项目名称：byh_java
 * 文件名称: SaleOrderRecordService
 * 创建时间: 2025-03-11 15:37
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.app.service
 * 文件描述:
 * Copyright (c) 2025 byh_java All rights reserved.
 */
@Service
@Slf4j
public class SaleOrderRecordService extends ServiceImpl<SaleOrderRecordMapper, SaleOrderRecord> {

    public static final String SALE_CHANNEL = "YUNXI";

    /**
     * 根据订单详情构建或更新渠道记录
     *
     * @param orderDetail     云樨订单详情
     * @param existingChannel 已存在的渠道记录,如果为null则创建新记录
     * @param saleNo          内部订单号
     * @param userId          用户ID
     * @return 渠道记录
     */
    public SaleOrderRecord buildFromOrderDetail(YunXiOrderDetailVo orderDetail, SaleOrderRecord existingChannel,
        String saleNo, String userId) {
        SaleOrderRecord channel = existingChannel != null ? existingChannel : new SaleOrderRecord();

        // 设置基础信息
        if (existingChannel == null) {
            channel.setSaleChannel(SALE_CHANNEL);
            channel.setSaleNo(saleNo);
            channel.setUserId(userId);
        }

        // 设置订单信息
        channel.setOutSaleNo(orderDetail.getOrderNum());
        channel.setOrderStatus(orderDetail.getOrderStatus());
        channel.setPaymentNo(orderDetail.getPaymentNo());
        if (StrUtil.isNotBlank(orderDetail.getOrderAmount())) {
            channel.setOrderAmount(new BigDecimal(orderDetail.getOrderAmount()));
        } else {
            log.warn("云信订单详情中订单金额为空, userId: {}, 订单号: {}  orderDetail: {}", userId,
                orderDetail.getOrderNum(), orderDetail);
            channel.setOrderAmount(BigDecimal.ZERO);
        }

        channel.setUserMobile(orderDetail.getUserMobile());
        channel.setPayWay(orderDetail.getPayWay());
        channel.setCouponPackageId(orderDetail.getCouponPackageId());
        channel.setCouponPackageName(orderDetail.getCouponPackageName());

        // 设置时间信息
        if (StrUtil.isNotBlank(orderDetail.getPayTime())) {
            channel.setPayTime(DateUtil.parse(orderDetail.getPayTime()));
        }
        if (StrUtil.isNotBlank(orderDetail.getExpireTime())) {
            channel.setExpireTime(DateUtil.parse(orderDetail.getExpireTime()));
        }

        // 设置退款信息
        channel.setRefundable(orderDetail.getRefundable());
        channel.setReturnAmount(orderDetail.getReturnAmount());
        if (StrUtil.isNotBlank(orderDetail.getReturnTime())) {
            channel.setReturnTime(DateUtil.parse(orderDetail.getReturnTime()));
        }

        return channel;
    }

    public int updateByPrimaryKey(SaleOrderRecord record) {
        return baseMapper.updateByPrimaryKey(record);
    }

    public SaleOrderRecord getSaleChannel(String saleNo, Long userId) {
       return this.lambdaQuery()
       .eq(SaleOrderRecord::getSaleNo, saleNo)
       .eq(SaleOrderRecord::getUserId, userId)
       .one();
    }
}
