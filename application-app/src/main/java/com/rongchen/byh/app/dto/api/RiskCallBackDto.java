package com.rongchen.byh.app.dto.api;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 风控回调dto
 * @date 2025/3/28 19:32:35
 */
@Data
public class RiskCallBackDto {
    private String status;      // 状态标识（如 "1000"）
    private String message;     // 响应描述
    private String signature;   // 数字签名（Base64编码的RSA加密MD5摘要）
    private String content;     // AES加密后的Base64编码数据（实际解密后为CreditCallbackData对象）
}
