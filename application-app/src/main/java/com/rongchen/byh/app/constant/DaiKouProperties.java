package com.rongchen.byh.app.constant;


import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;


@Configuration
@ConfigurationProperties(prefix = "daikou")
@Data
public class DaiKouProperties {
    private String ua;
    private String uaKey;
    private String url;
    private String creditOrgId;
    private String productId;
}
