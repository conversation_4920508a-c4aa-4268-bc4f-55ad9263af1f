package com.rongchen.byh.app.v2.handle.product;

import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.app.dto.app.FaceVerifyDto;
import com.rongchen.byh.app.dto.app.IdCardVerifyDto;
import com.rongchen.byh.app.dto.app.LoanApplyDto;
import com.rongchen.byh.app.dto.app.UserFormDto;
import com.rongchen.byh.app.dto.h5.UserCheckLoanApplyDto;
import com.rongchen.byh.app.entity.UserData;
import com.rongchen.byh.app.v2.dto.LoginH5Dto;
import com.rongchen.byh.app.v2.entity.ProductPageMenu;
import com.rongchen.byh.app.vo.RegOrLoginVo;
import com.rongchen.byh.app.vo.app.SubmitUserFormVo;
import com.rongchen.byh.app.vo.h5.UserCheckLoanApplyVo;
import com.rongchen.byh.common.api.idCardVerify.vo.OcrVo;
import com.rongchen.byh.common.core.object.ResponseResult;

import javax.servlet.http.HttpServletRequest;

/**
 * 产品处理接口
 */
public interface ProductHandleFactory {

    /**
     * h5登录
     * @param loginH5Dto-
     */
    ResponseResult<RegOrLoginVo> loginByH5(LoginH5Dto loginH5Dto, UserData userData);


    /**
     * 提交身份证姓名二要素数据
     * @param
     * @param
     */
    ResponseResult<UserCheckLoanApplyVo> submitTwoElementsData(UserCheckLoanApplyDto userCheckLoanApplyDto);


    /**
     * 提交ocr数据
     * @param
     * @param
     */
    ResponseResult<OcrVo> submitOcrData(IdCardVerifyDto verifyDto, HttpServletRequest request);


    /**
     * 提交详细信息表单
     * @param
     * @param
     */
    ResponseResult<SubmitUserFormVo> submitUserForm(UserFormDto dto);


    /**
     * 查询初筛结果
     * @param
     * @param
     */
    ResponseResult<UserCheckLoanApplyVo> queryLoanResult(Object parame);




// ===================================需调整===============================================
    /**
     * 产品初筛申请
     * @param params
     * @param userCheckLoanApplyDto
     */
    ResponseResult<UserCheckLoanApplyVo> apply(UserCheckLoanApplyDto userCheckLoanApplyDto, Object params);
    /**
     * 产品借贷申请
     * @param params
     * @param loanApplyDto
     */
    void loanApply(LoanApplyDto loanApplyDto, Object params);

    /**
     * 提交人脸数据
     * @param
     * @param
     */
    ResponseResult<Void> submitFaceData(FaceVerifyDto verifyDto);

    ResponseResult<ProductPageMenu> getPage(Long userId, String curPage, Integer productId);
}