<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.dao.UserLoveLogMapper">

    <resultMap id="BaseResultMap" type="com.rongchen.byh.app.entity.UserLoveLog">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="contractName" column="contract_name" jdbcType="VARCHAR"/>
            <result property="contractNo" column="contract_no" jdbcType="INTEGER"/>
            <result property="contractUrl" column="contract_url" jdbcType="VARCHAR"/>
            <result property="contractStatus" column="contract_status" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,contract_name,
        contract_no,contract_url,contract_status,
        create_time
    </sql>
    <select id="getByUserIdAndContractName" resultType="com.rongchen.byh.app.entity.UserLoveLog">
        select
        <include refid="Base_Column_List"/>
        from user_love_log
        where user_id = #{userId}
        and contract_name = #{contractName} order by id desc limit 1
    </select>
</mapper>
