package com.rongchen.byh.app.v2.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rongchen.byh.app.exceptions.BusinessException;
import com.rongchen.byh.app.utils.UserTokenUtil;
import com.rongchen.byh.app.v2.async.AsyncMethod;
import com.rongchen.byh.app.v2.common.RedisEnum;
import com.rongchen.byh.app.v2.dao.ProductDataMapper;
import com.rongchen.byh.app.v2.dao.ProductPageMenuMapper;
import com.rongchen.byh.app.v2.dao.UserProgressMapper;
import com.rongchen.byh.app.v2.entity.ProductData;
import com.rongchen.byh.app.v2.entity.ProductPageMenu;
import com.rongchen.byh.app.v2.entity.UserProgress;
import com.rongchen.byh.app.v2.handle.product.ProductHandleCenter;
import com.rongchen.byh.app.v2.handle.product.ProductHandleFactory;
import com.rongchen.byh.app.v2.service.IProductPageMenuService;
import com.rongchen.byh.app.v2.service.IUserProgressService;
import com.rongchen.byh.app.vo.RegOrLoginVo;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
@RequiredArgsConstructor
public class ProductPageMenuServiceImpl implements IProductPageMenuService {

    @Autowired
    private ProductPageMenuMapper menuMapper;
    @Resource
    private RedisTemplate<String,Object> redisTemplate;

    @Autowired
    private AsyncMethod asyncMethod;
    private final ProductDataMapper productDataMapper;

    @Autowired
    private UserProgressMapper userProgressMapper;

    @Override
    public ProductPageMenu selectByCurPage(String curPage,Integer isError,String productCode) {
        //单独分析特殊情况的路由
        //1.用户进入h5初连接的时候
        if(productCode != null && "/login".equals(curPage)){
            ProductData productData = productDataMapper.selectOne(new LambdaQueryWrapper<ProductData>().eq(ProductData::getCode, productCode));
            if (ObjectUtil.isEmpty(productData)) {
                throw new BusinessException(ErrorCodeEnum.DATA_VALIDATED_FAILED, "产品不存在");
            }
            ProductPageMenu menu1 = new ProductPageMenu();
            menu1.setSort(1);
            menu1.setProductId(productData.getId());
            ProductPageMenu menu = menuMapper.selectNext(menu1);
            return menu;
        }

        //2.用户进入app登录页面的时候，前端和后端都不知道用户的产品id，此时随便默认一个产品，只是返回动态协议，跟路由跳转没有关系
        if("/app".equals(curPage)){
            ProductPageMenu menu1 = new ProductPageMenu();
            menu1.setCurPage("pages/login/login");
            //这里的产品id没啥意义，只为了返回唯一的页面数据，前提必须是所有模式的app登录页面都长一样，并且协议也必须保证一样，否者此逻辑需要修改
            menu1.setProductId(0);
            ProductPageMenu menu = menuMapper.selectNext(menu1);
            return menu;
        }
        //3.用户已经登录过app，但又从原连接的初h5页面进去注册登录，直接返回结果页面


        //4.无特殊情况的时候
        Long userId = UserTokenUtil.getUserId();
        Integer productId = UserTokenUtil.getProductId();
        ProductPageMenu pageMenu = menuMapper.selectByCurPage(curPage,productId);
        Integer beforeSort = pageMenu.getSort();
        ProductPageMenu returnMenu = new ProductPageMenu();
        //1.先查询redis，看是否有用户的页面位置，如果有获取缓存里面的url,如果没有就查询数据库
        if(redisTemplate.hasKey(RedisEnum.PAGE_USER_ID+userId)){
            ProductPageMenu curMenu = (ProductPageMenu)redisTemplate.opsForValue().get(RedisEnum.PAGE_USER_ID + userId);
            if(curMenu.getSort()>beforeSort){
                //说明用户之前执行过相关流程，可以跳步骤
                //直接返回redis存储的页面，回到用户上一次推出的页面，即用户还没有跑完页面程序
                returnMenu = curMenu;
            }
            else{
                //说明按照顺序在执行
                curMenu.setSort(beforeSort+1);
                curMenu.setCurPage(null);
                curMenu.setIsError(isError);
                ProductPageMenu menu = menuMapper.selectNext(curMenu);
                returnMenu= menu;
            }
        }else{
            //1.缓存过期或者没有缓存，从数据库里面查询用户上一次走到哪一个页面了
            UserProgress progress = userProgressMapper.selectByUserId(userId);
            if(progress!=null){
                ProductPageMenu pageMenu1 = progress.getPageMenu();
                if(pageMenu1.getSort()>beforeSort){
                    //说明用户之前已经走过部分流程，可以跳页面操作，直接跳转到该表存储的页面
                    pageMenu1.setCurPage(null);
                    ProductPageMenu menuTwo = menuMapper.selectNext(pageMenu1);
                    returnMenu = menuTwo;
                }
            }
            else{
                //否则用户之前没有执行后续操作，并且缓存还过期了，按照当前顺序返回给用户下一个页面
                //说明按照顺序在执行
                pageMenu.setSort(beforeSort+1);
                pageMenu.setIsError(isError);
                pageMenu.setCurPage(null);
                ProductPageMenu menuTwor = menuMapper.selectNext(pageMenu);
                returnMenu = menuTwor;
            }

        }
        //异步存储数据到redis和mysql中
        asyncMethod.updateUserProgress(returnMenu,userId);
        return returnMenu;
    }

    @Override
    public ProductPageMenu getPageNew(String curPage, Integer isError, String productCode) {
        //单独分析特殊情况的路由
        //1.用户进入h5初连接的时候
        if(productCode != null && "/login".equals(curPage)){
            ProductData productData = productDataMapper.selectOne(new LambdaQueryWrapper<ProductData>().eq(ProductData::getCode, productCode));
            if (ObjectUtil.isEmpty(productData)) {
                throw new BusinessException(ErrorCodeEnum.DATA_VALIDATED_FAILED, "产品不存在");
            }
            ProductPageMenu menu1 = new ProductPageMenu();
            menu1.setSort(1);
            menu1.setProductId(productData.getId());
            ProductPageMenu menu = menuMapper.selectNext(menu1);
            return menu;
        }

        //2.用户进入app登录页面的时候，前端和后端都不知道用户的产品id，此时随便默认一个产品，只是返回动态协议，跟路由跳转没有关系
        if("/app".equals(curPage)){
            ProductPageMenu menu1 = new ProductPageMenu();
            menu1.setCurPage("pages/login/login");
            //这里的产品id没啥意义，只为了返回唯一的页面数据，前提必须是所有模式的app登录页面都长一样，并且协议也必须保证一样，否者此逻辑需要修改
            menu1.setProductId(0);
            ProductPageMenu menu = menuMapper.selectNext(menu1);
            return menu;
        }
        //3.其他可以获取到产品id的情况，在具体的实现类里面去做区分
        Long userId = UserTokenUtil.getUserId();
        Integer productId = UserTokenUtil.getProductId();
        ProductData productData = productDataMapper.selectOne(new LambdaQueryWrapper<ProductData>().eq(ProductData::getId, productId));
        ProductHandleFactory productHandle = ProductHandleCenter.getProductHandle(productData.getBeanName());
        ResponseResult<ProductPageMenu> result = productHandle.getPage(userId,curPage,productId);
        return result.getData();
    }
}
