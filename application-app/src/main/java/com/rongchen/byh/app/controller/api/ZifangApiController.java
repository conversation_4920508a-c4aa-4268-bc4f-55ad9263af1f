package com.rongchen.byh.app.controller.api;


import cn.dev33.satoken.annotation.SaIgnore;
import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.app.dto.api.*;
import com.rongchen.byh.app.service.ZifangApiService;
import com.rongchen.byh.common.api.beiyihua.config.BeiYiHuaProperties;
import com.rongchen.byh.common.api.zifang.service.mayi.config.ZiFangProperties;
import com.rongchen.byh.common.api.zifang.utils.ZiFangUtil;
import com.rongchen.byh.common.api.zifang.vo.BaseVo;
import com.rongchen.byh.common.core.annotation.DisableDataFilter;
import com.rongchen.byh.common.core.util.HttpUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Tag(name = "资方回调api接口")
@DisableDataFilter
@Slf4j
@RestController
@RequestMapping("/notice")
@SaIgnore
public class ZifangApiController {

    @Resource
    ZiFangProperties ziFangProperties;
    @Resource
    ZifangApiService zifangApiService;
    @Resource
    BeiYiHuaProperties beiYiHuaProperties;

    /**
     * 2.1.4 授信审批结果通知
     */
    @PostMapping("/creditNotice")
    public JSONObject creditNotice(@RequestBody String request) {
        try {
            log.info("【资方】授信审批结果通知 解密前参数：{}",request);
            JSONObject bizMap = JSONObject.parseObject(request);

            JSONObject res = ZiFangUtil.parseCallResponse(bizMap, ziFangProperties.getPrivateKey(), ziFangProperties.getPublicKey());
            log.info("【资方】授信审批结果通知 解密后参数：{}",res.toJSONString());
            CreditNoticeDto creditNoticeDto = res.toJavaObject(CreditNoticeDto.class);
            if (creditNoticeDto.getCreditNo().startsWith("jw-")) {
                try {
                    String data = HttpUtil.postJson(beiYiHuaProperties.getByhOldCreditNoticeUrl(), request);
                    return JSONObject.parseObject(data);
                } catch (Exception e) {
                    log.error("【资方】授信审批结果分发 异常",e);
                }
                BaseVo baseVo = new BaseVo();
                baseVo.setResponseCode("9999");
                baseVo.setResponseMsg("成功");
                String response = ZiFangUtil.buildNoticeResponse(JSONObject.toJSONString(baseVo), ziFangProperties.getPrivateKey(), ziFangProperties.getOutPublicKey());
                return JSONObject.parseObject(response);
            }
            BaseVo baseVo = zifangApiService.creditNotice(creditNoticeDto);

            String result = ZiFangUtil.buildNoticeResponse(JSONObject.toJSONString(baseVo), ziFangProperties.getPrivateKey(), ziFangProperties.getOutPublicKey());
            return JSONObject.parseObject(result);
        } catch (Exception e) {

        }
        return null;
    }


    /**
     * 2.3.3.用信结果通知接口
     * @param request
     * @return
     */
    @PostMapping("/loanNotice")
    public JSONObject loanNotice(@RequestBody String request) {
        try {
            JSONObject bizMap = JSONObject.parseObject(request);
            JSONObject res = ZiFangUtil.parseCallResponse(bizMap, ziFangProperties.getPrivateKey(), ziFangProperties.getPublicKey());
            log.info("【资方】 用信结果通知 解密后参数：{}",res.toJSONString());
            LoanNoticeDto loanNoticeDto = res.toJavaObject(LoanNoticeDto.class);
            if (loanNoticeDto.getLoanNo().startsWith("jw-")) {
                try {
                    String data = HttpUtil.postJson(beiYiHuaProperties.getByhOldLoanNoticeUrl(), request);
                    return JSONObject.parseObject(data);
                } catch (Exception e) {
                    log.error("【资方】 用信结果通知 异常",e);
                }
                BaseVo baseVo = new BaseVo();
                baseVo.setResponseCode("9999");
                baseVo.setResponseMsg("成功");
                String response = ZiFangUtil.buildNoticeResponse(JSONObject.toJSONString(baseVo), ziFangProperties.getPrivateKey(), ziFangProperties.getOutPublicKey());
                return JSONObject.parseObject(response);
            }
            BaseVo baseVo = zifangApiService.loanNotice(loanNoticeDto);

            String result = ZiFangUtil.buildNoticeResponse(JSONObject.toJSONString(baseVo), ziFangProperties.getPrivateKey(), ziFangProperties.getOutPublicKey());
            return JSONObject.parseObject(result);
        } catch (Exception e) {
            log.error("【资方】 用信结果通知 异常",e);
        }

        return null;
    }

    /**
     * 还款结果通知
     * @return
     */
    @PostMapping("/repayNotice")
    public JSONObject repayNotice(@RequestBody String request) {
        try {
            JSONObject bizMap = JSONObject.parseObject(request);
            JSONObject res = ZiFangUtil.parseCallResponse(bizMap, ziFangProperties.getPrivateKey(), ziFangProperties.getPublicKey());
            log.info("【资方】 还款结果通知 解密后参数：{}",res.toJSONString());
            RepayNoticeDto repayNoticeDto = res.toJavaObject(RepayNoticeDto.class);
            if (repayNoticeDto.getLoanNo().startsWith("jw-")) {
                try {
                    String data = HttpUtil.postJson(beiYiHuaProperties.getByhOldRepayNoticeUrl(), request);
                    return JSONObject.parseObject(data);
                } catch (Exception e) {
                    log.error("【资方】 还款结果通知 异常",e);
                }
                BaseVo baseVo = new BaseVo();
                baseVo.setResponseCode("9999");
                baseVo.setResponseMsg("成功");
                String response = ZiFangUtil.buildNoticeResponse(JSONObject.toJSONString(baseVo), ziFangProperties.getPrivateKey(), ziFangProperties.getOutPublicKey());
                return JSONObject.parseObject(response);
            }
            BaseVo baseVo = zifangApiService.repayNotice(repayNoticeDto);

            String result = ZiFangUtil.buildNoticeResponse(JSONObject.toJSONString(baseVo), ziFangProperties.getPrivateKey(), ziFangProperties.getOutPublicKey());
            return JSONObject.parseObject(result);
        } catch (Exception e) {
            log.error("【资方】还款结果通知 异常",e);
        }
        return null;
    }

    /**
     * 赊销结果通知
     * @return
     */
    @PostMapping("/saleNotice")
    public JSONObject saleNotice(@RequestBody String request) {
        try {
            JSONObject bizMap = JSONObject.parseObject(request);
            JSONObject res = ZiFangUtil.parseCallResponse(bizMap, ziFangProperties.getPrivateKey(), ziFangProperties.getPublicKey());
            log.info("【资方】 赊销结果通知 解密后参数：{}",res.toJSONString());
            SaleNoticeDto saleNoticeDto = res.toJavaObject(SaleNoticeDto.class);
            if (saleNoticeDto.getSaleNo().startsWith("jw-")) {
                try {
                    String data = HttpUtil.postJson(beiYiHuaProperties.getByhOldSaleNoticeUrl(), request);
                    return JSONObject.parseObject(data);
                } catch (Exception e) {
                    log.error("资方】 赊销结果通知 异常",e);
                }
                BaseVo baseVo = new BaseVo();
                baseVo.setResponseCode("9999");
                baseVo.setResponseMsg("成功");
                String response = ZiFangUtil.buildNoticeResponse(JSONObject.toJSONString(baseVo), ziFangProperties.getPrivateKey(), ziFangProperties.getOutPublicKey());
                return JSONObject.parseObject(response);
            }
            BaseVo baseVo = zifangApiService.saleNotice(saleNoticeDto);

            String result = ZiFangUtil.buildNoticeResponse(JSONObject.toJSONString(baseVo), ziFangProperties.getPrivateKey(), ziFangProperties.getOutPublicKey());
            return JSONObject.parseObject(result);
        } catch (Exception e) {
            log.error("【资方】赊销结果通知 异常",e);
        }
        return null;
    }
}
