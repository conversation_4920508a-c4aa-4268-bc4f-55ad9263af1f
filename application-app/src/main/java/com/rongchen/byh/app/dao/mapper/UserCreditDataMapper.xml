<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.dao.UserCreditDataMapper">

    <resultMap id="BaseResultMap" type="com.rongchen.byh.app.entity.UserCreditData">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="creditAmount" column="credit_amount" jdbcType="DECIMAL"/>
            <result property="withdrawAmount" column="withdraw_amount" jdbcType="DECIMAL"/>
            <result property="residueAmount" column="residue_amount" jdbcType="DECIMAL"/>
            <result property="freezeAmount" column="freeze_amount" jdbcType="DECIMAL"/>
            <result property="statusFlag" column="status_flag" jdbcType="INTEGER"/>
            <result property="creditTime" column="credit_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="sourceMode" column="source_mode" jdbcType="INTEGER"/>

    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,credit_amount,
        withdraw_amount,residue_amount,freeze_amount,status_flag,
        credit_time,create_time,source_mode
    </sql>
    <select id="queryByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from user_credit_data
        where user_id = #{userId} order by id desc limit 1
    </select>
</mapper>
