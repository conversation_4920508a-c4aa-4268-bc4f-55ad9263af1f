package com.rongchen.byh.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName disburse_record
 */
@TableName(value ="disburse_record")
@Data
public class DisburseRecord implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 类型 1-放款 2-还款
     */
    private Integer type;

    /**
     * 流程单号 放款：loanNo, 还款：
     */
    private String loanNo;

    /**
     * 追踪id
     */
    private String traceId;

    /**
     * 位置
     */
    private String location;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}