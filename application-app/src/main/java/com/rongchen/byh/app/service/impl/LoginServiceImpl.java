package com.rongchen.byh.app.service.impl;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Date;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.rongchen.byh.app.constant.LoanType;
import com.rongchen.byh.app.constant.SourceMode;
import com.rongchen.byh.app.constant.UserAuditStatus;
import com.rongchen.byh.app.dao.*;
import com.rongchen.byh.app.dto.CreateTokenDto;
import com.rongchen.byh.app.dto.OfflineRegOrLoginDto;
import com.rongchen.byh.app.dto.RegOrLoginDto;
import com.rongchen.byh.app.dto.YzmCodeDto;
import com.rongchen.byh.app.entity.*;
import com.rongchen.byh.app.service.LoginService;
import com.rongchen.byh.app.utils.UserTokenUtil;
import com.rongchen.byh.app.vo.RegOrLoginVo;
import com.rongchen.byh.common.api.sms.service.SmsService;
import com.rongchen.byh.common.api.sms.vo.SendSmsVo;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.ContextUtil;
import com.rongchen.byh.common.core.util.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.TimeUnit;


@Service
@Slf4j
public class LoginServiceImpl implements LoginService {

    @Resource
    RedissonClient redissonClient;
    @Resource
    SmsService smsService;
    @Resource
    UserDataMapper userDataMapper;
    @Resource
    UserLoanApplyMapper userLoanApplyMapper;
    @Resource
    ChannelDataMapper channelDataMapper;
    @Resource
    UserStaffMapper userStaffMapper;
    @Resource
    StaffDataMapper staffDataMapper;
    @Resource
    UserCreditDataMapper userCreditDataMapper;
    @Resource
    UserDetailMapper userDetailMapper;
    @Value("${spring.profiles.active}")
    private String active;

    private static final String MOBILE_PREFIX = "mb:";
    private static final String IP_PREFIX = "ip:";
    private static final String YZM_PREFIX = "yzm:";

    @Override
    public ResponseResult<RegOrLoginVo> regOrLogin(RegOrLoginDto regOrLoginDto) {
        log.info("用户h5注册或登录请求参数：{}", regOrLoginDto);
        if(!Validator.isMobile(regOrLoginDto.getMobile())) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"手机号格式错误");
        }
        if ("prod".equals(active)) {
            RBucket<String> bucket = redissonClient.getBucket(YZM_PREFIX + regOrLoginDto.getMobile());
            String msgId = bucket.get();
            if (msgId == null) {
                return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "验证码已过期，请重新获取");
            }
            SendSmsVo vo = smsService.verifyCode(msgId, regOrLoginDto.getCode());
            if (!vo.getSuccessFlag()) {
                return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "验证码错误");
            }
        }

        ChannelData channelData = channelDataMapper.selectBySecret(regOrLoginDto.getChannel());
        if (channelData == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"渠道不存在");
        }
        if (channelData.getStatus() != 1) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"渠道已禁用");
        }

        // 用户注册
        UserData userData = userDataMapper.selectOne(new LambdaQueryWrapper<UserData>().eq(UserData::getMobile, regOrLoginDto.getMobile()));
        if (ObjectUtil.isEmpty(userData)) {
            userData = createUser(regOrLoginDto.getMobile(),channelData,SourceMode.ONLINE);
        } else {
            // 判定线下是否有申请
            UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndTypeStatus(userData.getId(), LoanType.CHECK, StrUtil.join(StrUtil.COMMA, 1, 2));
            if (ObjectUtil.isNotEmpty(userLoanApply) && (userLoanApply.getAuditsStatus() == 1 || userLoanApply.getAuditsStatus() == 4)
                    && userData.getAuditStatus() >= UserAuditStatus.WAIT_APP_CREDIT) {
                return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"您已经有额度，无法申请该产品");
            }
        }
        // 生成token
        Long userId = userData.getId();
        RegOrLoginVo regOrLoginVo = new RegOrLoginVo();
        CreateTokenDto createTokenDto = new CreateTokenDto();
        createTokenDto.setUserId(userId);
        createTokenDto.setAccount(regOrLoginDto.getMobile());

        UserTokenUtil.createToken(createTokenDto);
        UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userId, LoanType.CHECK, 0);
        regOrLoginVo.setToken(createTokenDto.getToken());
        regOrLoginVo.setUserId(userId);
        regOrLoginVo.setApplyStatus(ObjectUtil.isEmpty(userLoanApply)  ? 0 : 1);
        regOrLoginVo.setChannelId(channelData.getId());
        log.info("用户h5注册或登录响应参数：{}，请求参数：{}", regOrLoginVo, regOrLoginDto);
        return ResponseResult.success(regOrLoginVo);
    }

    private UserData createUser(String mobile,ChannelData channelData,Integer sourceMode) {
        UserData user = new UserData();
        user.setMobile(mobile);
        user.setStatusFlag(1);
        user.setAuditFlag(0);
        user.setMobileMd(DigestUtil.md5Hex(mobile, StandardCharsets.UTF_8.name()));
        user.setChannelId(channelData.getId());
        user.setCreateTime(new Date());
        user.setIp(IpUtil.getRemoteIpAddress(ContextUtil.getHttpRequest()));
        user.setSourceMode(sourceMode);
        userDataMapper.insert(user);
        return user;
    }

    @Override
    public ResponseResult<Void> sendCode(YzmCodeDto yzmCodeDto) {
        String mobile = yzmCodeDto.getMobile();
        if (!Validator.isMobile(mobile)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"手机号格式错误");
        }
        HttpServletRequest httpRequest = ContextUtil.getHttpRequest();
        String ipAddress = IpUtil.getRemoteIpAddress(httpRequest);
        // 一个手机号2分钟3条
        if (!checkMobile(mobile)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"短信发送太频繁，请稍后再试");
        }
        // 一个IP一天20条
        if (!checkIp(ipAddress)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"短信发送太频繁，请稍后再试");
        }
        // 发送短信
        if ("prod".equals(active)) {
            SendSmsVo vo = smsService.sendSms(mobile);
            if (!vo.getSuccessFlag()) {
                return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "短信发送失败，请稍后再试");
            }
            String msgId = vo.getMsgId();
            // 缓存验证码
            RBucket<String> bucket = redissonClient.getBucket(YZM_PREFIX + mobile);
            bucket.set(msgId + "", 2, TimeUnit.MINUTES);
        }

        return ResponseResult.success();
    }

    @Override
    public ResponseResult<RegOrLoginVo> offlineRegOrLogin(OfflineRegOrLoginDto dto) {
        log.info("线下模式用户h5注册或登录请求参数：{}", dto);
        if(!Validator.isMobile(dto.getMobile())) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"手机号格式错误");
        }


        ChannelData channelData = channelDataMapper.selectBySecret(dto.getChannel());
        if (channelData == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"渠道不存在");
        }
        if (channelData.getStatus() != 1) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"渠道已禁用");
        }
        if ("prod".equals(active)) {
            RBucket<String> bucket = redissonClient.getBucket(YZM_PREFIX + dto.getMobile());
            String msgId = bucket.get();
            if (msgId == null) {
                return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "验证码已过期，请重新获取");
            }
            SendSmsVo vo = smsService.verifyCode(msgId, dto.getCode());
            if (!vo.getSuccessFlag()) {
                return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "验证码错误");
            }
        }
        // 用户注册
        UserData userData = userDataMapper.selectOne(new LambdaQueryWrapper<UserData>().eq(UserData::getMobile, dto.getMobile()));
        if (ObjectUtil.isEmpty(userData)) {
            userData = createUser(dto.getMobile(),channelData,SourceMode.OFFLINE);
        } else {
            // 判定线上是否有申请
            UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndTypeStatus(userData.getId(), LoanType.CHECK, StrUtil.join(StrUtil.COMMA, 0, 2));
            if (ObjectUtil.isNotEmpty(userLoanApply) && (userLoanApply.getAuditsStatus() == 1 || userLoanApply.getAuditsStatus() == 4)) {
                return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"您已经有额度，无法申请该产品");
            }
        }
        // 绑定销售
        UserStaff userStaff = userStaffMapper.selectOne(new LambdaQueryWrapper<UserStaff>().eq(UserStaff::getUserId, userData.getId()));
        if (ObjectUtil.isEmpty(userStaff)) {
            StaffData staffData = staffDataMapper.selectByInviteCode(dto.getInviteCode());
            if (ObjectUtil.isEmpty(staffData)) {
                return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"邀请码错误");
            }
            if (staffData.getInviteFlag() == 1) {
                return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"邀请码已失效");
            }
            userStaff = new UserStaff();
            userStaff.setUserId(userData.getId());
            userStaff.setStaffId(staffData.getId());
            userStaff.setCreateTime(new Date());
            userStaffMapper.insert(userStaff);
        }
        // 生成token
        Long userId = userData.getId();
        RegOrLoginVo regOrLoginVo = new RegOrLoginVo();
        CreateTokenDto createTokenDto = new CreateTokenDto();
        createTokenDto.setUserId(userId);
        createTokenDto.setAccount(dto.getMobile());

        UserTokenUtil.createToken(createTokenDto);
        UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userId, LoanType.CHECK, 1);
        regOrLoginVo.setToken(createTokenDto.getToken());
        regOrLoginVo.setUserId(userId);
        regOrLoginVo.setApplyStatus(ObjectUtil.isEmpty(userLoanApply)  ? 0 : 1);
        regOrLoginVo.setChannelId(channelData.getId());
        log.info("线下模式用户h5注册或登录响应参数：{}，请求参数：{}", regOrLoginVo, dto);
        return ResponseResult.success(regOrLoginVo);
    }

    @Override
    public ResponseResult<Void> checkInviteCode(OfflineRegOrLoginDto dto) {
        String inviteCode = dto.getInviteCode();
        StaffData staffData = staffDataMapper.selectByInviteCode(inviteCode);
        if (ObjectUtil.isEmpty(staffData)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"邀请码错误");
        }
        if (staffData.getInviteFlag() == 1) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"邀请码已失效");
        }
        return ResponseResult.success();
    }

    private boolean checkIp(String ipAddress) {
        RBucket<Integer> bucket = redissonClient.getBucket(IP_PREFIX + ipAddress);
        Integer count = bucket.get();
        if (count == null) {
            bucket.set(1);
            bucket.expire(1, TimeUnit.DAYS);
            return true;
        }
        if (count >= 20) {
            return false;
        }
        bucket.set(count + 1,bucket.remainTimeToLive()/1000, TimeUnit.SECONDS);
        return true;
    }

    private boolean checkMobile(String mobile) {
        RBucket<Integer> bucket = redissonClient.getBucket(MOBILE_PREFIX + mobile);
        Integer count = bucket.get();
        if (count == null) {
            bucket.set(1);
            bucket.expire(60 * 2, TimeUnit.SECONDS);
            return true;
        }
        if (count >= 3) {
            return false;
        }
        bucket.set(count + 1,bucket.remainTimeToLive()/1000, TimeUnit.SECONDS);
        return true;
    }


    @Override
    public ResponseResult<RegOrLoginVo> flyRegOrLogin(RegOrLoginDto regOrLoginDto) {
        log.info("空中放款用户h5注册或登录请求参数：{}", regOrLoginDto);
        if(!Validator.isMobile(regOrLoginDto.getMobile())) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"手机号格式错误");
        }
        if (StrUtil.isEmpty(regOrLoginDto.getInviteCode())) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"邀请码不能为空");
        }
        if ("prod".equals(active)) {
            RBucket<String> bucket = redissonClient.getBucket(YZM_PREFIX + regOrLoginDto.getMobile());
            String msgId = bucket.get();
            if (msgId == null) {
                return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "验证码已过期，请重新获取");
            }

            SendSmsVo vo = smsService.verifyCode(msgId, regOrLoginDto.getCode());
            if (!vo.getSuccessFlag()) {
                return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "验证码错误");
            }
        }

        ChannelData channelData = channelDataMapper.selectBySecret(regOrLoginDto.getChannel());
        if (channelData == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"渠道不存在");
        }
        if (channelData.getStatus() != 1) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"渠道已禁用");
        }

        // 用户注册
        UserData userData = userDataMapper.selectOne(new LambdaQueryWrapper<UserData>().eq(UserData::getMobile, regOrLoginDto.getMobile()));
        if (ObjectUtil.isEmpty(userData)) {
            userData = createUser(regOrLoginDto.getMobile(),channelData,SourceMode.AIR);
        } else {
            // 判定线下是否有申请
            UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndTypeStatus(userData.getId(), LoanType.CHECK, StrUtil.join(StrUtil.COMMA, 0, 1));
            if (ObjectUtil.isNotEmpty(userLoanApply) && (userLoanApply.getAuditsStatus() == 1 || userLoanApply.getAuditsStatus() == 4)
                    && userData.getAuditStatus() >= UserAuditStatus.WAIT_APP_CREDIT) {
                return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"您已经有额度，无法申请该产品");
            }
        }

        // 绑定销售
        UserStaff userStaff = userStaffMapper.selectOne(new LambdaQueryWrapper<UserStaff>().eq(UserStaff::getUserId, userData.getId()));
        if (ObjectUtil.isEmpty(userStaff)) {
            StaffData staffData = staffDataMapper.selectByInviteCode(regOrLoginDto.getInviteCode());
            if (ObjectUtil.isEmpty(staffData)) {
                return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"邀请码错误");
            }
            if (staffData.getInviteFlag() == 1) {
                return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"邀请码已失效");
            }
            userStaff = new UserStaff();
            userStaff.setUserId(userData.getId());
            userStaff.setStaffId(staffData.getId());
            userStaff.setCreateTime(new Date());
            userStaffMapper.insert(userStaff);
        }
        // 生成token
        Long userId = userData.getId();
        RegOrLoginVo regOrLoginVo = new RegOrLoginVo();
        CreateTokenDto createTokenDto = new CreateTokenDto();
        createTokenDto.setUserId(userId);
        createTokenDto.setAccount(regOrLoginDto.getMobile());

        UserTokenUtil.createToken(createTokenDto);
        UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userId, LoanType.CHECK,  2);
        regOrLoginVo.setToken(createTokenDto.getToken());
        regOrLoginVo.setUserId(userId);
        UserDetail userDetail = userDetailMapper.queryByUserId(userId);
        regOrLoginVo.setApplyStatus(0);
        if (ObjectUtil.isNotEmpty(userDetail) && ObjectUtil.isNotEmpty(userDetail.getIdNumber())) {
            regOrLoginVo.setApplyStatus(1);
        }
        if (ObjectUtil.isNotEmpty(userLoanApply)) {
            regOrLoginVo.setApplyStatus(2);
        }
        regOrLoginVo.setChannelId(channelData.getId());
        log.info("空中放款用户h5注册或登录响应参数：{}，请求参数：{}", regOrLoginVo, regOrLoginDto);
        return ResponseResult.success(regOrLoginVo);
    }
}
