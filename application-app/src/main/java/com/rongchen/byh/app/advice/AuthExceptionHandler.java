package com.rongchen.byh.app.advice;


import cn.dev33.satoken.exception.NotLoginException;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;


/**
 * 认证异常处理
 */
@Slf4j
@RestControllerAdvice("com.rongchen.byh")
public class AuthExceptionHandler {

    /**
     * 认证异常处理
     * @param ex
     * @param request
     * @return
     */
    @ExceptionHandler(NotLoginException.class)
    public ResponseResult<Void> notLoginExceptionHandle(NotLoginException ex, HttpServletRequest request) {
        // 判断场景值，定制化异常信息
        String message = "";
        if(ex.getType().equals(NotLoginException.NOT_TOKEN)) {
            message = "未能读取到有效 token";
        }
        else if(ex.getType().equals(NotLoginException.INVALID_TOKEN)) {
            message = "token 无效";
        }
        else if(ex.getType().equals(NotLoginException.TOKEN_TIMEOUT)) {
            message = "token 已过期";
        }
        else if(ex.getType().equals(NotLoginException.BE_REPLACED)) {
            message = "token 已被顶下线";
        }
        else if(ex.getType().equals(NotLoginException.KICK_OUT)) {
            message = "token 已被踢下线";
        }
        else if(ex.getType().equals(NotLoginException.TOKEN_FREEZE)) {
            message = "token 已被冻结";
        }
        else if(ex.getType().equals(NotLoginException.NO_PREFIX)) {
            message = "未按照指定前缀提交 token";
        } else {
            message = "未登录";
        }

        return ResponseResult.error(ErrorCodeEnum.INVALID_ACCESS_TOKEN,message);
    }

}
