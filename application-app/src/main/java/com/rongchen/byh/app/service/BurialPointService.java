package com.rongchen.byh.app.service;

import com.rongchen.byh.app.dto.app.BurialPointDto;
import com.rongchen.byh.common.core.object.ResponseResult;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
public interface BurialPointService {

    /**
     * 埋点
     *
     * @param dto
     * @param request
     * @return
     */
    ResponseResult<Void> burialPointAdd(BurialPointDto dto, HttpServletRequest request);
}
