package com.rongchen.byh.app.api.vo.credit;


import com.rongchen.byh.app.api.vo.BaseResultVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreditQueryVo {

    /**
     * 响应码  0000成功 9999失败
     */
    protected String responseCode;

    /**
     * 响应信息
     */
    protected String responseMsg;

    /**
     * 审批结果
     */
    private String approvalResult;

    /**
     * 审批结果描述
     */
    private String approvalResultDesc;

    /**
     * 授信总额度
     */
    private String totalCredit;

    /**
     * 额度失效日期
     */
    private String subExpDate;

    /**
     * 利率类型
     */
    private String rateType;

    /**
     * 利率
     */
    private String quotaRate;
}
