<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.dao.UserCapitalRecordMapper">

    <resultMap id="BaseResultMap" type="com.rongchen.byh.app.entity.UserCapitalRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="capitalId" column="capital_id" jdbcType="BIGINT"/>
            <result property="creditStatus" column="credit_status" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="upcateTime" column="upcate_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,capital_id,
        credit_status,create_time,upcate_time
    </sql>
    <select id="selectListByUserId" resultType="com.rongchen.byh.app.entity.UserCapitalRecord">
        select
        <include refid="Base_Column_List"/>
        from user_capital_record
        where user_id = #{userId}
    </select>
</mapper>
