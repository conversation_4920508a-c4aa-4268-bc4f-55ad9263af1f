package com.rongchen.byh.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 销售员工表
 * @TableName staff_data
 */
@TableName(value ="staff_data")
@Data
public class StaffData implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 邀请码
     */
    private String inviteCode;

    /**
     * 邀请码使用标识 0-允许使用 1-禁止使用（禁止注册）
     */
    private Integer inviteFlag;

    /**
     * 状态 0-正常 1-禁用
     */
    private Integer staffStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}