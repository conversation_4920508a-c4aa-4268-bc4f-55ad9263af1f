package com.rongchen.byh.app.dto.api;


import lombok.Data;

@Data
public class LoanNoticeDto {


    /**
     * 请求方
     */
    private String reqSysCode;

    /**
     * 资金方编码
     */
    private String fundCode;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 贷款编号
     */
    private String loanNo;

    /**
     * 用户编号
     */
    private String userId;

    /**
     * 用信结果
     * 01	审核中
     * 04	放款失败
     * 06	放款成功-起息
     * 08	订单不存在
     * 09	结清
     */
    private String loanResult;

    /**
     * 用信结果描述
     */
    private String loanResultDesc;

    /**
     * 借款合同签约地址
     */
    private String agreementUrl;

    /**
     * 批复金额
     */
    private Double approvedAmount;

    /**
     * 放款成功时间
     */
    private String loanTime;

    /**
     * 贷款到期日
     */
    private String loanMaturity;

    /**
     * 资方订单号
     */
    private String contractId;

    /**
     * 行方贷款合同号
     */
    private String fundOrderId;

}
