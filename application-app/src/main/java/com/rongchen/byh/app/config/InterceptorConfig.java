package com.rongchen.byh.app.config;

import com.rongchen.byh.common.satoken.common.IntercepterCons;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 所有的项目拦截器都在这里集中配置
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@Configuration
public class InterceptorConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        System.out.println("Adding interceptors...");
        registry.addInterceptor(new AuthenticationInterceptor())
                .addPathPatterns("/**");
//                .excludePathPatterns(IntercepterCons.EXCLUDE_PATHS.toArray(new String[0]));
    }
}
