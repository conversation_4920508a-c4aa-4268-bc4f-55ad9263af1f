package com.rongchen.byh.app.mqListener;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.rongchen.byh.app.config.ZifangFactory;
import com.rongchen.byh.app.dao.CapitalDataMapper;
import com.rongchen.byh.app.dao.DisburseDataMapper;
import com.rongchen.byh.app.dao.RepayScheduleMapper;
import com.rongchen.byh.app.entity.CapitalData;
import com.rongchen.byh.app.entity.DisburseData;
import com.rongchen.byh.app.entity.RepaySchedule;
import com.rongchen.byh.app.service.DisburseRecordService;
import com.rongchen.byh.app.service.RepayScheduleService;
import com.rongchen.byh.common.api.zifang.dto.RepaymentPlanQueryDto;
import com.rongchen.byh.common.api.zifang.service.RepaymentApi;
import com.rongchen.byh.common.api.zifang.vo.RepaymentPlanQueryPkgVo;
import com.rongchen.byh.common.api.zifang.vo.RepaymentPlanQueryVo;
import com.rongchen.byh.common.core.constant.SettleFlagConstant;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.MDCUtil;
import com.rongchen.byh.common.rabbitmq.constants.QueueConstant;
import com.rongchen.byh.common.rabbitmq.dto.RepayDto;
import com.rongchen.byh.common.redis.util.CommonRedisUtil;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class UpdateRepayOverdueListener {

    @Resource
    private RepayScheduleMapper repayScheduleMapper;
    @Resource
    private RepaymentApi repaymentApi;
    @Resource
    private DisburseDataMapper disburseDataMapper;
    @Resource
    DisburseRecordService disburseRecordService;
    @Resource
    RepayScheduleService repayScheduleService;
    @Resource
    ZifangFactory zifangFactory;
    @Resource
    private CapitalDataMapper capitalDataMapper;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private CommonRedisUtil commonRedisUtil;

    @RabbitListener(queues = QueueConstant.UPDATE_REPAY_OVERDUE_QUEUE, ackMode = "MANUAL")
    public void receiver(String msg, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        log.info("【逾期还款更新监听器】开始处理消息，RabbitMQ原始消息: {}", msg);
        RepayDto repayDto = null;
        Long disburseIdForCatch = null;

        try {
            log.info("接收到逾期还款更新消息: {}", msg);
            try {
                repayDto = JSONObject.parseObject(msg, RepayDto.class);
            } catch (Exception e) {
                log.error("从消息解析 RepayDto 失败: {}. 错误: {}", msg, e.getMessage(), e);
                channel.basicNack(deliveryTag, false, false);
                return;
            }

            if (repayDto == null || repayDto.getDisburseId() == null) {
                log.error("收到无效的 RepayDto (null 或缺少 disburseId): {}", msg);
                channel.basicNack(deliveryTag, false, false);
                return;
            }

            disburseIdForCatch = repayDto.getDisburseId();
            String traceId = StrUtil.isBlank(repayDto.getTraceId())
                ? UUID.randomUUID().toString()
                : repayDto.getTraceId();

            String statusKey = commonRedisUtil.buildKey("mq", "processed", "status:update_repay_overdue",
                disburseIdForCatch.toString());
            RBucket<String> statusBucket = redissonClient.getBucket(statusKey);
            if ("COMPLETED".equals(statusBucket.get())) {
                log.warn("【逾期还款更新】借款ID：{} 的消息已处理完成，跳过重复处理。", disburseIdForCatch);
                channel.basicAck(deliveryTag, false);
                return;
            }

            MDCUtil.setTraceId(traceId);
            log.info("【逾期还款更新监听器】开始处理消息，RabbitMQ原始消息: {} tag: {}", msg, deliveryTag);
            try {
                DisburseData disburseData = disburseDataMapper.selectById(repayDto.getDisburseId());
                if (disburseData == null) {
                    log.error("【逾期还款更新】根据 disburseId 未找到 DisburseData 记录: {}", disburseIdForCatch);
                    channel.basicNack(deliveryTag, false, false);
                    return;
                }
                CapitalData capitalData = capitalDataMapper.selectById(disburseData.getCapitalId());
                if (capitalData == null) {
                    log.error("【逾期还款更新】根据 capitalId 未找到 CapitalData 记录: {}, disburseId: {}",
                        disburseData.getCapitalId(), disburseIdForCatch);
                    channel.basicNack(deliveryTag, false, false);
                    return;
                }

                RepaymentPlanQueryDto queryDto = new RepaymentPlanQueryDto();
                queryDto.setUserId(disburseData.getUserId() + "");
                queryDto.setLoanNo(disburseData.getLoanNo());
                RepaymentApi api = zifangFactory.getApi(capitalData.getBeanName(), RepaymentApi.class);
                ResponseResult<RepaymentPlanQueryVo> result = api.getRepaymentPlanQuery(queryDto);

                if (!result.isSuccess()) {
                    log.error("【逾期还款更新】查询还款账单 借款id：{} 查询资方还款计划接口失败", disburseData.getId());
                    disburseRecordService.saveRecord(3, "借款(disburseData)id:" + disburseData.getId(), traceId,
                        "查询资方还款计划查询接口失败");
                    channel.basicNack(deliveryTag, false, false);
                    return;
                }

                RepaymentPlanQueryVo data = result.getData();
                if (!"0000".equals(data.getResponseCode())) {
                    log.error("【逾期还款更新】查询还款账单 借款id：{} 资方返回结果错误：{}", disburseData.getId(),
                        data.getResponseMsg());
                    disburseRecordService.saveRecord(3, "借款(disburseData)id:" + disburseData.getId(), traceId,
                        "查询资方还款计划资方返回结果错误:" + data.getResponseMsg());
                    channel.basicNack(deliveryTag, false, false);
                    return;
                }

                List<RepaySchedule> list = repayScheduleMapper.selectListByDisburseId(disburseData.getId());
                if (list == null || list.isEmpty()) {
                    log.error("【逾期还款更新】借款id：{} 未查询到本地还款计划记录", disburseData.getId());
                    channel.basicNack(deliveryTag, false, false);
                    return;
                }
                Map<String, RepaySchedule> scheduleMap = list.stream()
                    .collect(Collectors.toMap(RepaySchedule::getRepayTerm, v -> v));

                List<RepaymentPlanQueryPkgVo> pkgList = data.getPkgList();
                List<RepaySchedule> saveList = new ArrayList<>(pkgList.size());
                BigDecimal totalRet = new BigDecimal("0.00");
                for (RepaymentPlanQueryPkgVo li : pkgList) {
                    RepaySchedule schedule = scheduleMap.get(li.getRepayTerm());
                    if (schedule == null) {
                        log.warn("【逾期还款更新】借款id：{} 资方返回的期数 {} 在本地未找到对应记录，跳过",
                            disburseData.getId(), li.getRepayTerm());
                        continue;
                    }
                    if (!schedule.getSettleFlag().equals(SettleFlagConstant.RUNNING)) {
                        log.info("【逾期还款更新】查询还款账单 账单id：{} 状态不为进行中，跳过：{}", schedule.getId(),
                            schedule.getSettleFlag());
                        continue;
                    }
                    RepaySchedule entity = new RepaySchedule();
                    entity.setId(schedule.getId());
                    entity.setDisburseId(disburseData.getId());
                    entity.setUserId(disburseData.getUserId());
                    buildEntity(li, entity);
                    totalRet = totalRet.add(entity.getTermRetInt()).add(entity.getTermGuarantorFee())
                        .add(entity.getTermOverdueGuarantorFee());
                    saveList.add(entity);
                }

                if (saveList.isEmpty()) {
                    log.info("【逾期还款更新】借款id：{} 没有需要更新的进行中账单", disburseData.getId());
                    channel.basicAck(deliveryTag, false);
                    return;
                }

                DisburseData disburseDataUp = new DisburseData();
                disburseDataUp.setId(disburseData.getId());
                disburseDataUp.setGrossInterest(totalRet);
                int disburseUpdateCount = disburseDataMapper.updateById(disburseDataUp);

                boolean scheduleUpdateResult = repayScheduleService.updateBatchById(saveList);

                if (disburseUpdateCount > 0 && scheduleUpdateResult) {
                    statusBucket.set("COMPLETED", 1, TimeUnit.HOURS);
                    log.info("【逾期还款更新】借款ID：{} 处理成功完成。已设置幂等性状态。", disburseIdForCatch);

                    channel.basicAck(deliveryTag, false);
                    log.info("【逾期还款更新】借款ID：{} 消息已确认 (ACK)", disburseIdForCatch);
                } else {
                    log.error(
                        "【逾期还款更新】借款ID：{} 数据库更新失败。DisburseData 更新行数: {}, RepaySchedule 批量更新结果: {}",
                        disburseIdForCatch, disburseUpdateCount, scheduleUpdateResult);
                    channel.basicNack(deliveryTag, false, false);
                }

            } catch (Exception e) {
                log.error("【逾期还款更新】借款ID：{} 内部处理期间发生意外错误: {}", disburseIdForCatch, e.getMessage(),
                    e);
                channel.basicNack(deliveryTag, false, false);
            }

        } catch (IOException e) {
            log.error("【逾期还款更新】借款ID：{} 手动确认/拒绝消息时发生 IO 错误, deliveryTag: {}. 错误: {}",
                disburseIdForCatch != null ? disburseIdForCatch : "N/A", deliveryTag, e.getMessage(), e);
        } catch (Exception e) {
            log.error("【逾期还款更新】处理消息时发生未知错误, deliveryTag: {}, 借款ID: {}. 错误: {}",
                deliveryTag, disburseIdForCatch != null ? disburseIdForCatch : "N/A", e.getMessage(), e);
            try {
                channel.basicNack(deliveryTag, false, false);
                log.warn("【逾期还款更新】外层未知错误，消息已拒绝 (NACK), deliveryTag: {}", deliveryTag);
            } catch (IOException ioEx) {
                log.error("【逾期还款更新】外层未知错误，拒绝消息时发生 IO 错误, deliveryTag: {}. 错误: {}", deliveryTag,
                    ioEx.getMessage(),
                    ioEx);
            }
        } finally {
            MDCUtil.clear();
        }

    }

    private void buildEntity(RepaymentPlanQueryPkgVo li, RepaySchedule entity) {
        entity.setRepayOwnbDate(li.getRepayOwnbDate());
        entity.setRepayOwneDate(li.getRepayOwneDate());
        entity.setRepayIntbDate(li.getRepayIntbDate());
        entity.setRepayInteDate(li.getRepayInteDate());
        entity.setTotalAmt(new BigDecimal(li.getTotalAmt()));
        entity.setTermRetPrin(new BigDecimal(li.getTermRetPrin()));
        entity.setTermRetInt(new BigDecimal(li.getTermRetInt()));
        entity.setTermGuarantorFee(new BigDecimal(li.getTermGuarantorFee()));
        entity.setTermRetFint(new BigDecimal(li.getTermRetFint()));
        entity.setTermOverdueGuarantorFee(
            StrUtil.isNotEmpty(li.getTermOverdueGuarantorFee()) ? new BigDecimal(li.getTermOverdueGuarantorFee())
                : new BigDecimal("0.00"));
        entity.setPrinAmt(
            StrUtil.isNotEmpty(li.getPrinAmt()) ? new BigDecimal(li.getPrinAmt()) : new BigDecimal("0.00"));
        entity.setNoRetAmt(
            StrUtil.isNotEmpty(li.getNoRetAmt()) ? new BigDecimal(li.getNoRetAmt()) : new BigDecimal("0.00"));
        entity.setIntAmt(StrUtil.isNotEmpty(li.getIntAmt()) ? new BigDecimal(li.getIntAmt()) : new BigDecimal("0.00"));
        entity.setNoRetInt(
            StrUtil.isNotEmpty(li.getNoRetInt()) ? new BigDecimal(li.getNoRetInt()) : new BigDecimal("0.00"));
        entity.setTermFintFinish(StrUtil.isNotEmpty(li.getTermFintFinish()) ? new BigDecimal(li.getTermFintFinish())
            : new BigDecimal("0.00"));
        entity.setNoRetFin(
            StrUtil.isNotEmpty(li.getNoRetFin()) ? new BigDecimal(li.getNoRetFin()) : new BigDecimal("0.00"));
        entity.setGuarantorFee(StrUtil.isNotEmpty(li.getGuarantorFee()) ? new BigDecimal(li.getGuarantorFee())
            : new BigDecimal("0.00"));
        entity.setNoGuarantorFee(StrUtil.isNotEmpty(li.getNoGuarantorFee()) ? new BigDecimal(li.getNoGuarantorFee())
            : new BigDecimal("0.00"));
        entity.setTermServiceFee(StrUtil.isNotEmpty(li.getTermServiceFee()) ? new BigDecimal(li.getTermServiceFee())
            : new BigDecimal("0.00"));
        entity.setServiceFee(
            StrUtil.isNotEmpty(li.getServiceFee()) ? new BigDecimal(li.getServiceFee()) : new BigDecimal("0.00"));
        entity.setNoServiceFee(StrUtil.isNotEmpty(li.getNoServiceFee()) ? new BigDecimal(li.getNoServiceFee())
            : new BigDecimal("0.00"));
        entity.setOverdueGuarantorFee(
            StrUtil.isNotEmpty(li.getOverdueGuarantorFee()) ? new BigDecimal(li.getOverdueGuarantorFee())
                : new BigDecimal("0.00"));
        entity.setNoOverdueGuarantorFee(
            StrUtil.isNotEmpty(li.getNoOverdueGuarantorFee()) ? new BigDecimal(li.getNoOverdueGuarantorFee())
                : new BigDecimal("0.00"));
        entity.setTermStatus(li.getTermStatus());
        entity.setSettleFlag(li.getSettleFlag());
        entity.setRepayTerm(li.getRepayTerm());
    }

}
