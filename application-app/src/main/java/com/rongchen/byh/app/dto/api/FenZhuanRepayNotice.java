package com.rongchen.byh.app.dto.api;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName FenZhuanRepayNotice
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/11 15:32
 * @Version 1.0
 **/
@Data
public class FenZhuanRepayNotice {

    /**
     * 还款流水号
     */
    private String thirdRepayNo;

    /**
     * 借款单号
     */
    private String loanOrderNo;

    /**
     * 还款受理单号
     */
    private String repayId;

    /**
     * 还款交易单号
     */
    private String repayTrxId;

    /**
     * 实还总金额，单位：元
     */
    private BigDecimal actualAmount;

    /**
     * 实还本金，单位：元
     */
    private BigDecimal actualPrincipal;

    /**
     * 实还利息，单位：元
     */
    private BigDecimal actualInterest;

    /**
     * 实还服务费，单位：元
     */
    private BigDecimal actualServiceFee;

    /**
     * 实还罚息，单位：元
     */
    private BigDecimal actualPenalty;

    /**
     * 实还其他费用，单位：元
     */
    private BigDecimal actualOtherFee;

    /**
     * 实还时间，格式：yyyy-MM-dd HH:mm:ss
     */
    private String repayTime;

    /**
     * 还款类型
     */
    private String repayType;

    /**
     * 还款状态
     */
    private String repayStatus;
}
