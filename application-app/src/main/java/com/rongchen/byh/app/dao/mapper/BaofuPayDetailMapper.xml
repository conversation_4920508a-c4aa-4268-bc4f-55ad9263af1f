<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.dao.BaofuPayDetailMapper">
  <resultMap id="BaseResultMap" type="com.rongchen.byh.app.entity.BaofuPayDetail">
    <!--@mbg.generated-->
    <!--@Table baofu_pay_detail-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="trans_id" jdbcType="VARCHAR" property="transId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="protocol_no" jdbcType="VARCHAR" property="protocolNo" />
    <result column="txn_amt" jdbcType="VARCHAR" property="txnAmt" />
    <result column="pay_status" jdbcType="VARCHAR" property="payStatus" />
    <result column="ou_pay_order_on" jdbcType="VARCHAR" property="ouPayOrderOn" />
    <result column="msg_code" jdbcType="VARCHAR" property="msgCode" />
    <result column="message" jdbcType="LONGVARCHAR" property="message" />
    <result column="succeed_time" jdbcType="TIMESTAMP" property="succeedTime" />
    <result column="succeed_amt" jdbcType="VARCHAR" property="succeedAmt" />
    <result column="risk_item" jdbcType="LONGVARCHAR" property="riskItem" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, trans_id, user_id, protocol_no, txn_amt, pay_status, ou_pay_order_on, msg_code, 
    message, succeed_time, succeed_amt, risk_item, remark, create_time, update_time
  </sql>
  <update id="updateByPrimaryKey" parameterType="com.rongchen.byh.app.entity.BaofuPayDetail">
    <!--@mbg.generated-->
    update baofu_pay_detail
    set trans_id = #{transId,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      protocol_no = #{protocolNo,jdbcType=VARCHAR},
      txn_amt = #{txnAmt,jdbcType=VARCHAR},
      pay_status = #{payStatus,jdbcType=VARCHAR},
      ou_pay_order_on = #{ouPayOrderOn,jdbcType=VARCHAR},
      msg_code = #{msgCode,jdbcType=VARCHAR},
      message = #{message,jdbcType=LONGVARCHAR},
      succeed_time = #{succeedTime,jdbcType=TIMESTAMP},
      succeed_amt = #{succeedAmt,jdbcType=VARCHAR},
      risk_item = #{riskItem,jdbcType=LONGVARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>