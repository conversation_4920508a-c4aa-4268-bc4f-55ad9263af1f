<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.dao.DisburseDataMapper">
  <insert id="insertSelective" keyColumn="id" keyProperty="id"
    parameterType="com.rongchen.byh.app.entity.DisburseData" useGeneratedKeys="true">
    <!--@mbg.generated--> insert into
    `disburse_data` <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null"> `user_id`, </if>
      <if test="productId != null"> `product_id`, </if>
      <if
        test="creditNo != null"> `credit_no`, </if>
      <if test="loanNo != null"> `loan_no`, </if>
      <if
        test="saleNo != null"> `sale_no`, </if>
      <if test="creditAmount != null"> `credit_amount`, </if>
      <if
        test="grossInterest != null"> `gross_interest`, </if>
      <if test="creditStatus != null">
    `credit_status`, </if>
      <if test="creditTime != null"> `credit_time`, </if>
      <if
        test="loanTime != null"> `loan_time`, </if>
      <if test="repaymentTime != null">
    `repayment_time`, </if>
      <if test="periods != null"> `periods`, </if>
      <if test="fundCode != null">
    `fund_code`, </if>
      <if test="purposeLoan != null"> `purpose_loan`, </if>
      <if
        test="yearRete != null"> `year_rete`, </if>
      <if test="repaymentMethod != null">
    `repayment_method`, </if>
      <if test="saleRepayAmount != null"> `sale_repay_amount`, </if>
      <if test="createTime != null"> `create_time`, </if>
      <if
        test="consultFee != null"> `consult_fee`, </if>
      <if test="capitalRecordId != null">
    `capital_record_id`, </if>
      <if test="capitalId != null"> `capital_id`, </if>
      <if
        test="contractId != null"> `contract_id`, </if>
      <if test="fundOrderId != null">
    `fund_order_id`, </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null"> #{userId,jdbcType=BIGINT}, </if>
      <if test="productId != null">
    #{productId,jdbcType=BIGINT}, </if>
      <if test="creditNo != null"> #{creditNo,jdbcType=VARCHAR}, </if>
      <if
        test="loanNo != null"> #{loanNo,jdbcType=VARCHAR}, </if>
      <if test="saleNo != null">
    #{saleNo,jdbcType=VARCHAR}, </if>
      <if test="creditAmount != null">
    #{creditAmount,jdbcType=DECIMAL}, </if>
      <if test="grossInterest != null">
    #{grossInterest,jdbcType=DECIMAL}, </if>
      <if test="creditStatus != null">
    #{creditStatus,jdbcType=INTEGER}, </if>
      <if test="creditTime != null">
    #{creditTime,jdbcType=TIMESTAMP}, </if>
      <if test="loanTime != null">
    #{loanTime,jdbcType=TIMESTAMP}, </if>
      <if test="repaymentTime != null">
    #{repaymentTime,jdbcType=TIMESTAMP}, </if>
      <if test="periods != null">
    #{periods,jdbcType=INTEGER}, </if>
      <if test="fundCode != null"> #{fundCode,jdbcType=VARCHAR}, </if>
      <if
        test="purposeLoan != null"> #{purposeLoan,jdbcType=VARCHAR}, </if>
      <if
        test="yearRete != null"> #{yearRete,jdbcType=VARCHAR}, </if>
      <if
        test="repaymentMethod != null"> #{repaymentMethod,jdbcType=VARCHAR}, </if>
      <if
        test="saleRepayAmount != null"> #{saleRepayAmount,jdbcType=DECIMAL}, </if>
      <if test="createTime != null">
    #{createTime,jdbcType=TIMESTAMP}, </if>
      <if test="consultFee != null">
    #{consultFee,jdbcType=INTEGER}, </if>
      <if test="capitalRecordId != null">
    #{capitalRecordId,jdbcType=BIGINT}, </if>
      <if test="capitalId != null">
    #{capitalId,jdbcType=BIGINT}, </if>
      <if test="contractId != null">
    #{contractId,jdbcType=VARCHAR}, </if>
      <if test="fundOrderId != null">
    #{fundOrderId,jdbcType=VARCHAR}, </if>
    </trim>
  </insert>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map"
    useGeneratedKeys="true">
    <!--@mbg.generated--> insert into `disburse_data` (`user_id`, `product_id`, `credit_no`,
    `loan_no`, `sale_no`, `credit_amount`, `gross_interest`, `credit_status`, `credit_time`,
    `loan_time`, `repayment_time`, `periods`, `fund_code`, `purpose_loan`, `year_rete`,
    `repayment_method`, `sale_repay_amount`, `create_time`,
    `consult_fee`, `capital_record_id`, `capital_id`, `contract_id`,
    `fund_order_id`) values <foreach collection="list" item="item" separator=",">
    (#{item.userId,jdbcType=BIGINT}, #{item.productId,jdbcType=BIGINT},
    #{item.creditNo,jdbcType=VARCHAR}, #{item.loanNo,jdbcType=VARCHAR},
    #{item.saleNo,jdbcType=VARCHAR}, #{item.creditAmount,jdbcType=DECIMAL},
    #{item.grossInterest,jdbcType=DECIMAL}, #{item.creditStatus,jdbcType=INTEGER},
    #{item.creditTime,jdbcType=TIMESTAMP}, #{item.loanTime,jdbcType=TIMESTAMP},
    #{item.repaymentTime,jdbcType=TIMESTAMP}, #{item.periods,jdbcType=INTEGER},
    #{item.fundCode,jdbcType=VARCHAR}, #{item.purposeLoan,jdbcType=VARCHAR},
    #{item.yearRete,jdbcType=VARCHAR}, #{item.repaymentMethod,jdbcType=VARCHAR},
    #{item.saleRepayAmount,jdbcType=DECIMAL},
    #{item.createTime,jdbcType=TIMESTAMP},
    #{item.consultFee,jdbcType=INTEGER},
    #{item.capitalRecordId,jdbcType=BIGINT}, #{item.capitalId,jdbcType=BIGINT},
    #{item.contractId,jdbcType=VARCHAR}, #{item.fundOrderId,jdbcType=VARCHAR} ) </foreach>
  </insert>
  <insert id="batchInsertOrUpdate" keyColumn="id" keyProperty="id" parameterType="map"
    useGeneratedKeys="true">
    <!--@mbg.generated--> insert into `disburse_data` (`user_id`, `product_id`, `credit_no`,
    `loan_no`, `sale_no`, `credit_amount`, `gross_interest`, `credit_status`, `credit_time`,
    `loan_time`, `repayment_time`, `periods`, `fund_code`, `purpose_loan`, `year_rete`,
    `repayment_method`, `sale_repay_amount`,  `create_time`,
     `consult_fee`, `capital_record_id`, `capital_id`, `contract_id`,
    `fund_order_id`) values <foreach collection="list" item="item" separator=",">
    (#{item.userId,jdbcType=BIGINT}, #{item.productId,jdbcType=BIGINT},
    #{item.creditNo,jdbcType=VARCHAR}, #{item.loanNo,jdbcType=VARCHAR},
    #{item.saleNo,jdbcType=VARCHAR}, #{item.creditAmount,jdbcType=DECIMAL},
    #{item.grossInterest,jdbcType=DECIMAL}, #{item.creditStatus,jdbcType=INTEGER},
    #{item.creditTime,jdbcType=TIMESTAMP}, #{item.loanTime,jdbcType=TIMESTAMP},
    #{item.repaymentTime,jdbcType=TIMESTAMP}, #{item.periods,jdbcType=INTEGER},
    #{item.fundCode,jdbcType=VARCHAR}, #{item.purposeLoan,jdbcType=VARCHAR},
    #{item.yearRete,jdbcType=VARCHAR}, #{item.repaymentMethod,jdbcType=VARCHAR},
    #{item.saleRepayAmount,jdbcType=DECIMAL},
    #{item.createTime,jdbcType=TIMESTAMP},
    #{item.consultFee,jdbcType=INTEGER},
    #{item.capitalRecordId,jdbcType=BIGINT}, #{item.capitalId,jdbcType=BIGINT},
    #{item.contractId,jdbcType=VARCHAR}, #{item.fundOrderId,jdbcType=VARCHAR} ) </foreach> on
    duplicate key update user_id=values(user_id), product_id=values(product_id),
    credit_no=values(credit_no), loan_no=values(loan_no), sale_no=values(sale_no),
    credit_amount=values(credit_amount), gross_interest=values(gross_interest),
    credit_status=values(credit_status), credit_time=values(credit_time),
    loan_time=values(loan_time), repayment_time=values(repayment_time), periods=values(periods),
    fund_code=values(fund_code), purpose_loan=values(purpose_loan), year_rete=values(year_rete),
    repayment_method=values(repayment_method), sale_repay_amount=values(sale_repay_amount),
    create_time=values(create_time),
     consult_fee=values(consult_fee),
    capital_record_id=values(capital_record_id), capital_id=values(capital_id),
    contract_id=values(contract_id), fund_order_id=values(fund_order_id) </insert>
  <insert id="batchInsertSelectiveUseDefaultForNull" keyColumn="id" keyProperty="id"
    parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated--> insert into `disburse_data` (`user_id`,
    `product_id`, `credit_no`, `loan_no`, `sale_no`, `credit_amount`, `gross_interest`,
    `credit_status`, `credit_time`, `loan_time`, `repayment_time`, `periods`, `fund_code`,
    `purpose_loan`, `year_rete`, `repayment_method`, `sale_repay_amount`,
    `create_time`,   `consult_fee`, `capital_record_id`, `capital_id`,
    `contract_id`, `fund_order_id`) values <foreach collection="list" item="item" separator=","> ( <choose>
        <!--@ignoreSql-->
        <when test="item.userId != null"> #{item.userId,jdbcType=BIGINT}, </when>
        <otherwise> DEFAULT, </otherwise>
      </choose>
      <choose>
        <!--@ignoreSql-->
        <when test="item.productId != null"> #{item.productId,jdbcType=BIGINT}, </when>
        <otherwise> DEFAULT, </otherwise>
      </choose>
      <choose>
        <!--@ignoreSql-->
        <when test="item.creditNo != null"> #{item.creditNo,jdbcType=VARCHAR}, </when>
        <otherwise> DEFAULT, </otherwise>
      </choose>
      <choose>
        <!--@ignoreSql-->
        <when test="item.loanNo != null"> #{item.loanNo,jdbcType=VARCHAR}, </when>
        <otherwise> DEFAULT, </otherwise>
      </choose>
      <choose>
        <!--@ignoreSql-->
        <when test="item.saleNo != null"> #{item.saleNo,jdbcType=VARCHAR}, </when>
        <otherwise> DEFAULT, </otherwise>
      </choose>
      <choose>
        <!--@ignoreSql-->
        <when test="item.creditAmount != null"> #{item.creditAmount,jdbcType=DECIMAL}, </when>
        <otherwise> DEFAULT, </otherwise>
      </choose>
      <choose>
        <!--@ignoreSql-->
        <when test="item.grossInterest != null"> #{item.grossInterest,jdbcType=DECIMAL}, </when>
        <otherwise> DEFAULT, </otherwise>
      </choose>
      <choose>
        <!--@ignoreSql-->
        <when test="item.creditStatus != null"> #{item.creditStatus,jdbcType=INTEGER}, </when>
        <otherwise> DEFAULT, </otherwise>
      </choose>
      <choose>
        <!--@ignoreSql-->
        <when test="item.creditTime != null"> #{item.creditTime,jdbcType=TIMESTAMP}, </when>
        <otherwise> DEFAULT, </otherwise>
      </choose>
      <choose>
        <!--@ignoreSql-->
        <when test="item.loanTime != null"> #{item.loanTime,jdbcType=TIMESTAMP}, </when>
        <otherwise> DEFAULT, </otherwise>
      </choose>
      <choose>
        <!--@ignoreSql-->
        <when test="item.repaymentTime != null"> #{item.repaymentTime,jdbcType=TIMESTAMP}, </when>
        <otherwise> DEFAULT, </otherwise>
      </choose>
      <choose>
        <!--@ignoreSql-->
        <when test="item.periods != null"> #{item.periods,jdbcType=INTEGER}, </when>
        <otherwise> DEFAULT, </otherwise>
      </choose>
      <choose>
        <!--@ignoreSql-->
        <when test="item.fundCode != null"> #{item.fundCode,jdbcType=VARCHAR}, </when>
        <otherwise> DEFAULT, </otherwise>
      </choose>
      <choose>
        <!--@ignoreSql-->
        <when test="item.purposeLoan != null"> #{item.purposeLoan,jdbcType=VARCHAR}, </when>
        <otherwise> DEFAULT, </otherwise>
      </choose>
      <choose>
        <!--@ignoreSql-->
        <when test="item.yearRete != null"> #{item.yearRete,jdbcType=VARCHAR}, </when>
        <otherwise> DEFAULT, </otherwise>
      </choose>
      <choose>
        <!--@ignoreSql-->
        <when test="item.repaymentMethod != null"> #{item.repaymentMethod,jdbcType=VARCHAR}, </when>
        <otherwise> DEFAULT, </otherwise>
      </choose>
      <choose>
        <!--@ignoreSql-->
        <when test="item.saleRepayAmount != null"> #{item.saleRepayAmount,jdbcType=DECIMAL}, </when>
        <otherwise> DEFAULT, </otherwise>
      </choose>
      <choose>
        <!--@ignoreSql-->
        <when test="item.createTime != null"> #{item.createTime,jdbcType=TIMESTAMP}, </when>
        <otherwise> DEFAULT, </otherwise>
      </choose>
      <choose>
        <!--@ignoreSql-->
        <when test="item.consultFee != null"> #{item.consultFee,jdbcType=INTEGER}, </when>
        <otherwise> DEFAULT, </otherwise>
      </choose>
      <choose>
        <!--@ignoreSql-->
        <when test="item.capitalRecordId != null"> #{item.capitalRecordId,jdbcType=BIGINT}, </when>
        <otherwise> DEFAULT, </otherwise>
      </choose>
      <choose>
        <!--@ignoreSql-->
        <when test="item.capitalId != null"> #{item.capitalId,jdbcType=BIGINT}, </when>
        <otherwise> DEFAULT, </otherwise>
      </choose>
      <choose>
        <!--@ignoreSql-->
        <when test="item.contractId != null"> #{item.contractId,jdbcType=VARCHAR}, </when>
        <otherwise> DEFAULT, </otherwise>
      </choose>
      <choose>
        <!--@ignoreSql-->
        <when test="item.fundOrderId != null"> #{item.fundOrderId,jdbcType=VARCHAR} </when>
        <otherwise> DEFAULT </otherwise>
      </choose> ) </foreach>
  </insert>
  <resultMap id="BaseResultMap" type="com.rongchen.byh.app.entity.DisburseData">
    <!--@mbg.generated-->
    <!--@Table
    `disburse_data`-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="credit_no" jdbcType="VARCHAR" property="creditNo" />
    <result column="loan_no" jdbcType="VARCHAR" property="loanNo" />
    <result column="sale_no" jdbcType="VARCHAR" property="saleNo" />
    <result column="credit_amount" jdbcType="DECIMAL" property="creditAmount" />
    <result column="gross_interest" jdbcType="DECIMAL" property="grossInterest" />
    <result column="credit_status" jdbcType="INTEGER" property="creditStatus" />
    <result column="credit_time" jdbcType="TIMESTAMP" property="creditTime" />
    <result column="loan_time" jdbcType="TIMESTAMP" property="loanTime" />
    <result column="repayment_time" jdbcType="TIMESTAMP" property="repaymentTime" />
    <result column="periods" jdbcType="INTEGER" property="periods" />
    <result column="fund_code" jdbcType="VARCHAR" property="fundCode" />
    <result column="purpose_loan" jdbcType="VARCHAR" property="purposeLoan" />
    <result column="year_rete" jdbcType="VARCHAR" property="yearRete" />
    <result column="repayment_method" jdbcType="VARCHAR" property="repaymentMethod" />
    <result column="sale_repay_amount" jdbcType="DECIMAL" property="saleRepayAmount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="consult_fee" jdbcType="INTEGER" property="consultFee" />
    <result column="capital_record_id" jdbcType="BIGINT" property="capitalRecordId" />
    <result column="capital_id" jdbcType="BIGINT" property="capitalId" />
    <result column="contract_id" jdbcType="VARCHAR" property="contractId" />
    <result column="fund_order_id" jdbcType="VARCHAR" property="fundOrderId" />
  </resultMap>
  <select id="selectByUserId" resultType="com.rongchen.byh.app.entity.DisburseData"> select <include
      refid="Base_Column_List" /> from disburse_data where user_id = #{userId} order by id desc
    limit 1 </select>
  <select id="selectByLoanNo" resultType="com.rongchen.byh.app.entity.DisburseData"> select <include
      refid="Base_Column_List" /> from disburse_data where loan_no = #{loanNo} order by id desc
    limit 1 </select>
  <select id="selectByCreditNo" resultType="com.rongchen.byh.app.entity.DisburseData"> select <include
      refid="Base_Column_List" /> from disburse_data where credit_no = #{creditNo} order by id desc
    limit 1 </select>
  <select id="selectNewByUser" resultType="com.rongchen.byh.app.entity.DisburseData"> select <include
      refid="Base_Column_List" /> from disburse_data <where>
      <if test="userId != null">and user_id = #{userId}</if>
            <if test="creditStatus != null">and
    credit_status = #{creditStatus}</if>
            <if test="loanNo != null">and loan_no = #{loanNo}</if>
            <if
        test="saleNo != null">and sale_no = #{saleNo}</if>
    </where> order by id desc limit 1 </select>

  <select id="selectListByStatus" resultMap="BaseResultMap"> select * from disburse_data where
    credit_status = #{status} </select>

  <sql id="Base_Column_List">
    <!--@mbg.generated--> `id`, `user_id`, `product_id`, `credit_no`, `loan_no`, `sale_no`,
    `credit_amount`, `gross_interest`, `credit_status`, `credit_time`, `loan_time`,
    `repayment_time`, `periods`, `fund_code`, `purpose_loan`, `year_rete`, `repayment_method`,
    `sale_repay_amount`, `create_time`, `consult_fee`,
    `capital_record_id`, `capital_id`, `contract_id`, `fund_order_id` </sql>


  <update id="updateByPrimaryKeySelective" parameterType="com.rongchen.byh.app.entity.DisburseData">
    <!--@mbg.generated-->
    update `disburse_data` <set>
      <if test="userId != null"> `user_id` = #{userId,jdbcType=BIGINT}, </if>
      <if
        test="productId != null"> `product_id` = #{productId,jdbcType=BIGINT}, </if>
      <if
        test="creditNo != null"> `credit_no` = #{creditNo,jdbcType=VARCHAR}, </if>
      <if
        test="loanNo != null"> `loan_no` = #{loanNo,jdbcType=VARCHAR}, </if>
      <if
        test="saleNo != null"> `sale_no` = #{saleNo,jdbcType=VARCHAR}, </if>
      <if
        test="creditAmount != null"> `credit_amount` = #{creditAmount,jdbcType=DECIMAL}, </if>
      <if
        test="grossInterest != null"> `gross_interest` = #{grossInterest,jdbcType=DECIMAL}, </if>
      <if
        test="creditStatus != null"> `credit_status` = #{creditStatus,jdbcType=INTEGER}, </if>
      <if
        test="creditTime != null"> `credit_time` = #{creditTime,jdbcType=TIMESTAMP}, </if>
      <if
        test="loanTime != null"> `loan_time` = #{loanTime,jdbcType=TIMESTAMP}, </if>
      <if
        test="repaymentTime != null"> `repayment_time` = #{repaymentTime,jdbcType=TIMESTAMP}, </if>
      <if
        test="periods != null"> `periods` = #{periods,jdbcType=INTEGER}, </if>
      <if
        test="fundCode != null"> `fund_code` = #{fundCode,jdbcType=VARCHAR}, </if>
      <if
        test="purposeLoan != null"> `purpose_loan` = #{purposeLoan,jdbcType=VARCHAR}, </if>
      <if
        test="yearRete != null"> `year_rete` = #{yearRete,jdbcType=VARCHAR}, </if>
      <if
        test="repaymentMethod != null"> `repayment_method` = #{repaymentMethod,jdbcType=VARCHAR}, </if>
      <if
        test="saleRepayAmount != null"> `sale_repay_amount` = #{saleRepayAmount,jdbcType=DECIMAL}, </if>
      <if
        test="createTime != null"> `create_time` = #{createTime,jdbcType=TIMESTAMP}, </if>
      <if
        test="consultFee != null"> `consult_fee` = #{consultFee,jdbcType=INTEGER}, </if>
      <if
        test="capitalRecordId != null"> `capital_record_id` = #{capitalRecordId,jdbcType=BIGINT}, </if>
      <if
        test="capitalId != null"> `capital_id` = #{capitalId,jdbcType=BIGINT}, </if>
      <if
        test="contractId != null"> `contract_id` = #{contractId,jdbcType=VARCHAR}, </if>
      <if
        test="fundOrderId != null"> `fund_order_id` = #{fundOrderId,jdbcType=VARCHAR}, </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT} </update>


  <update id="updateByPrimaryKey" parameterType="com.rongchen.byh.app.entity.DisburseData">
    <!--@mbg.generated--> update
    `disburse_data` set `user_id` = #{userId,jdbcType=BIGINT}, `product_id` =
    #{productId,jdbcType=BIGINT}, `credit_no` = #{creditNo,jdbcType=VARCHAR}, `loan_no` =
    #{loanNo,jdbcType=VARCHAR}, `sale_no` = #{saleNo,jdbcType=VARCHAR}, `credit_amount` =
    #{creditAmount,jdbcType=DECIMAL}, `gross_interest` = #{grossInterest,jdbcType=DECIMAL},
    `credit_status` = #{creditStatus,jdbcType=INTEGER}, `credit_time` =
    #{creditTime,jdbcType=TIMESTAMP}, `loan_time` = #{loanTime,jdbcType=TIMESTAMP}, `repayment_time`
    = #{repaymentTime,jdbcType=TIMESTAMP}, `periods` = #{periods,jdbcType=INTEGER}, `fund_code` =
    #{fundCode,jdbcType=VARCHAR}, `purpose_loan` = #{purposeLoan,jdbcType=VARCHAR}, `year_rete` =
    #{yearRete,jdbcType=VARCHAR}, `repayment_method` = #{repaymentMethod,jdbcType=VARCHAR},
    `sale_repay_amount` = #{saleRepayAmount,jdbcType=DECIMAL}, `create_time` = #{createTime,jdbcType=TIMESTAMP}, `consult_fee` =
    #{consultFee,jdbcType=INTEGER}, `capital_record_id` = #{capitalRecordId,jdbcType=BIGINT},
    `capital_id` = #{capitalId,jdbcType=BIGINT}, `contract_id` = #{contractId,jdbcType=VARCHAR},
    `fund_order_id` = #{fundOrderId,jdbcType=VARCHAR} where `id` = #{id,jdbcType=BIGINT} </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated--> update `disburse_data` <foreach close=")"
      collection="list" item="item" open="(" separator=", "> #{item.id,jdbcType=BIGINT} </foreach>
    where `id` in <trim prefix="set" suffixOverrides=",">
      <trim prefix="`user_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.userId,jdbcType=BIGINT} </foreach>
      </trim>
      <trim prefix="`product_id` = case"
        suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.productId,jdbcType=BIGINT} </foreach>
      </trim>
      <trim prefix="`credit_no` = case"
        suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.creditNo,jdbcType=VARCHAR} </foreach>
      </trim>
      <trim prefix="`loan_no` = case"
        suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.loanNo,jdbcType=VARCHAR} </foreach>
      </trim>
      <trim prefix="`sale_no` = case"
        suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.saleNo,jdbcType=VARCHAR} </foreach>
      </trim>
      <trim
        prefix="`credit_amount` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.creditAmount,jdbcType=DECIMAL} </foreach>
      </trim>
      <trim
        prefix="`gross_interest` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.grossInterest,jdbcType=DECIMAL} </foreach>
      </trim>
      <trim
        prefix="`credit_status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.creditStatus,jdbcType=INTEGER} </foreach>
      </trim>
      <trim
        prefix="`credit_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.creditTime,jdbcType=TIMESTAMP} </foreach>
      </trim>
      <trim
        prefix="`loan_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.loanTime,jdbcType=TIMESTAMP} </foreach>
      </trim>
      <trim
        prefix="`repayment_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.repaymentTime,jdbcType=TIMESTAMP} </foreach>
      </trim>
      <trim
        prefix="`periods` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.periods,jdbcType=INTEGER} </foreach>
      </trim>
      <trim prefix="`fund_code` = case"
        suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.fundCode,jdbcType=VARCHAR} </foreach>
      </trim>
      <trim
        prefix="`purpose_loan` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.purposeLoan,jdbcType=VARCHAR} </foreach>
      </trim>
      <trim
        prefix="`year_rete` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.yearRete,jdbcType=VARCHAR} </foreach>
      </trim>
      <trim
        prefix="`repayment_method` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.repaymentMethod,jdbcType=VARCHAR} </foreach>
      </trim>
      <trim
        prefix="`sale_repay_amount` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.saleRepayAmount,jdbcType=DECIMAL} </foreach>
      </trim>
      <trim
        prefix="`create_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.createTime,jdbcType=TIMESTAMP} </foreach>
      </trim>
      <trim
        prefix="`consult_fee` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.consultFee,jdbcType=INTEGER} </foreach>
      </trim>
      <trim
        prefix="`capital_record_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.capitalRecordId,jdbcType=BIGINT} </foreach>
      </trim>
      <trim
        prefix="`capital_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.capitalId,jdbcType=BIGINT} </foreach>
      </trim>
      <trim
        prefix="`contract_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.contractId,jdbcType=VARCHAR} </foreach>
      </trim>
      <trim
        prefix="`fund_order_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.fundOrderId,jdbcType=VARCHAR} </foreach>
      </trim>
    </trim>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated--> update `disburse_data` <foreach
      close=")" collection="list" item="item" open="(" separator=", "> #{item.id,jdbcType=BIGINT} </foreach>
    where `id` in <trim prefix="set" suffixOverrides=",">
      <trim prefix="`user_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.userId != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.userId,jdbcType=BIGINT} </if>
        </foreach>
      </trim>
      <trim
        prefix="`product_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.productId != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.productId,jdbcType=BIGINT} </if>
        </foreach>
      </trim>
      <trim
        prefix="`credit_no` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creditNo != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.creditNo,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`loan_no` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.loanNo != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.loanNo,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`sale_no` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.saleNo != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.saleNo,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`credit_amount` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creditAmount != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.creditAmount,jdbcType=DECIMAL} </if>
        </foreach>
      </trim>
      <trim
        prefix="`gross_interest` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.grossInterest != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.grossInterest,jdbcType=DECIMAL} </if>
        </foreach>
      </trim>
      <trim
        prefix="`credit_status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creditStatus != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.creditStatus,jdbcType=INTEGER} </if>
        </foreach>
      </trim>
      <trim
        prefix="`credit_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creditTime != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.creditTime,jdbcType=TIMESTAMP} </if>
        </foreach>
      </trim>
      <trim
        prefix="`loan_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.loanTime != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.loanTime,jdbcType=TIMESTAMP} </if>
        </foreach>
      </trim>
      <trim
        prefix="`repayment_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.repaymentTime != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.repaymentTime,jdbcType=TIMESTAMP} </if>
        </foreach>
      </trim>
      <trim
        prefix="`periods` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.periods != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.periods,jdbcType=INTEGER} </if>
        </foreach>
      </trim>
      <trim
        prefix="`fund_code` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fundCode != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.fundCode,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`purpose_loan` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.purposeLoan != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.purposeLoan,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`year_rete` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.yearRete != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.yearRete,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`repayment_method` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.repaymentMethod != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.repaymentMethod,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`sale_repay_amount` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.saleRepayAmount != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.saleRepayAmount,jdbcType=DECIMAL} </if>
        </foreach>
      </trim>
      <trim
        prefix="`create_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.createTime,jdbcType=TIMESTAMP} </if>
        </foreach>
      </trim>
      <trim
        prefix="`consult_fee` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.consultFee != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.consultFee,jdbcType=INTEGER} </if>
        </foreach>
      </trim>
      <trim
        prefix="`capital_record_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.capitalRecordId != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.capitalRecordId,jdbcType=BIGINT} </if>
        </foreach>
      </trim>
      <trim
        prefix="`capital_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.capitalId != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.capitalId,jdbcType=BIGINT} </if>
        </foreach>
      </trim>
      <trim
        prefix="`contract_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.contractId != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.contractId,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`fund_order_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fundOrderId != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.fundOrderId,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
    </trim>
  </update>
</mapper>