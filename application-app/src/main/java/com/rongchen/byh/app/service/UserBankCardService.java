package com.rongchen.byh.app.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rongchen.byh.app.dao.UserBankCardMapper;
import com.rongchen.byh.app.entity.UserBankCard;
import org.springframework.stereotype.Service;
/**

 * 项目名称：byh_java
 * 文件名称: UserBankCardService
 * 创建时间: 2025-04-03 11:50
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.app.service
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
@Service
public class UserBankCardService extends ServiceImpl<UserBankCardMapper, UserBankCard> {

    
    public int updateByPrimaryKey(UserBankCard record) {
        return baseMapper.updateByPrimaryKey(record);
    }
}
