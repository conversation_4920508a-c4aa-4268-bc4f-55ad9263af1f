package com.rongchen.byh.app.v2.service;

import com.rongchen.byh.app.dto.h5.UserCheckLoanApplyDto;
import com.rongchen.byh.app.vo.h5.UserCheckLoanApplyVo;
import com.rongchen.byh.common.core.object.ResponseResult;

public interface IUserCheckLoanV2Service {
    ResponseResult<UserCheckLoanApplyVo> apply(UserCheckLoanApplyDto userCheckLoanApplyDto);

    ResponseResult<UserCheckLoanApplyVo> queryResult();

    ResponseResult<UserCheckLoanApplyVo> flyQueryResult();

}