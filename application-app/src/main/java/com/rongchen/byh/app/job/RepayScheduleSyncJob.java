package com.rongchen.byh.app.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rongchen.byh.app.entity.DisburseData;
import com.rongchen.byh.app.entity.RepaySchedule;
import com.rongchen.byh.app.entity.RepayScheduleApply;
import com.rongchen.byh.app.service.DisburseDataService;
import com.rongchen.byh.app.service.LoanService;
import com.rongchen.byh.app.service.RepayScheduleApplyService;
import com.rongchen.byh.app.service.RepayScheduleService;
import com.rongchen.byh.common.core.enums.DisburseDataEnum;
import com.rongchen.byh.common.core.util.MDCUtil;
import com.rongchen.byh.common.redis.util.CommonRedisUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CancellationException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 还款计划同步定时任务
 */
@Component
@Slf4j
public class RepayScheduleSyncJob {

    // Redis分布式锁键前缀
    private static final String REPAY_SYNC_LOCK_PREFIX = "repay_schedule_sync:";
    // 锁超时时间（秒），例如10分钟
    private static final long LOCK_TIMEOUT_SECONDS = 360L;
    // 分页查询每页大小
    private static final long PAGE_SIZE = 100L;
    // 同步间隔（秒），例如15分
    private static final int IntervalMinute = 15;
    @Resource
    private DisburseDataService disburseDataService;
    @Resource
    private LoanService loanService;
    @Resource
    private CommonRedisUtil commonRedisUtil;
    @Resource(name = "taskExecutor")
    private Executor taskExecutor;

    @Resource
    private RepayScheduleService repayScheduleService;

    @Resource
    private RepayScheduleApplyService repayScheduleApplyService;

    /**
     * 定时同步还款计划。
     * 
     */
    @XxlJob("syncRepaymentSchedules")
    public void syncRepaymentSchedules() {

        String traceId = MDCUtil.setTraceId();
        MDCUtil.setTraceId(traceId);
        log.info("【定时任务】开始同步还款计划，Trace ID: {}", traceId);

        try {
            long currentPage = 1;
            boolean hasMoreData = true;

            while (hasMoreData) {
                final long pageNumForLog = currentPage;
                log.info("【定时任务】开始处理第 {} 页数据。", pageNumForLog);
                Page<DisburseData> page = new Page<>(currentPage, PAGE_SIZE);
                LambdaQueryWrapper<DisburseData> queryWrapper = new LambdaQueryWrapper<>();
                // 查询状态为还款中 (500) 的记录
                queryWrapper.eq(DisburseData::getCreditStatus, DisburseDataEnum.CreditStatus.REPAYING);
                queryWrapper.isNotNull(DisburseData::getLoanNo); // 确保 loanNo 不为空

                Page<DisburseData> resultPage = disburseDataService.page(page, queryWrapper);

                if (resultPage == null || CollectionUtils.isEmpty(resultPage.getRecords())) {
                    log.info("【定时任务】在第 {} 页未查询到需要同步的支用记录。", pageNumForLog);
                    hasMoreData = false;
                } else {
                    List<DisburseData> records = resultPage.getRecords();
                    log.info("【定时任务】查询到第 {} 页，共 {} 条需要同步的支用记录。", pageNumForLog, records.size());

                    Map<String, String> contextMap = MDCUtil.getCopyOfContextMap();
                    List<CompletableFuture<Void>> futures = new ArrayList<>();

                    for (DisburseData disburseData : records) {
                        futures.add(CompletableFuture.runAsync(() -> {
                            MDCUtil.setContextMap(contextMap);
                            try {
                                processSingleDisburse(disburseData);
                            } finally {
                                MDCUtil.clear();
                            }
                        }, taskExecutor));
                    }

                    try {
                        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                        log.info("【定时任务】第 {} 页的 {} 个任务已全部提交并等待完成。", pageNumForLog, futures.size());
                    } catch (CompletionException | CancellationException e) {
                        log.error("【定时任务】处理第 {} 页任务时发生错误，部分任务可能未完成: {}", pageNumForLog, e.getMessage(), e);
                    }

                    if (records.size() < PAGE_SIZE) {
                        // 如果当前页记录数小于页面大小，说明是最后一页
                        hasMoreData = false;
                    } else {
                        // 继续查询下一页
                        currentPage++;
                    }
                }
            }
        } finally {
            MDCUtil.remove(MDCUtil.Keys.TRACE_ID);
            log.info("【定时任务】同步还款计划结束，Trace ID: {}", traceId);
        }
    }

    /**
     * 处理单个支用记录的还款计划同步
     * 
     * @param disburseData 支用数据
     */
    private void processSingleDisburse(DisburseData disburseData) {
        String loanNo = disburseData.getLoanNo();
        if (loanNo == null) {
            log.warn("【定时任务】支用记录ID {} 的 loanNo 为空，跳过处理。", disburseData.getId());
            return;
        }

        String lockKey = REPAY_SYNC_LOCK_PREFIX + loanNo;
        boolean locked = false;
        try {
            // 尝试获取分布式锁
            locked = commonRedisUtil.tryLock(lockKey, 0, LOCK_TIMEOUT_SECONDS, TimeUnit.SECONDS);

            if (locked) {
                log.info("【定时任务】成功获取 loanNo [{}] 的同步锁，开始检查是否需要处理。", loanNo);

                // --- 新增检查逻辑 ---
                // 1. 查询是否存在状态为 'REPAYING' 的还款计划
                List<RepaySchedule> repayingSchedules = repayScheduleService.lambdaQuery()
                        .eq(RepaySchedule::getDisburseId, disburseData.getId())
                        .eq(RepaySchedule::getSettleFlag, "REPAYING")
                        .list();

                if (!repayingSchedules.isEmpty()) {
                    // 2. 如果存在，查询最新的还款申请记录
                    List<Long> scheduleIds = repayingSchedules.stream()
                            .map(RepaySchedule::getId)
                            .collect(Collectors.toList());

                    Optional<RepayScheduleApply> latestApplyOpt = repayScheduleApplyService.lambdaQuery()
                            .in(RepayScheduleApply::getRepayScheduleId, scheduleIds)
                            .orderByDesc(RepayScheduleApply::getCreateTime)
                            .last("LIMIT 1") // 获取最新的一条
                            .oneOpt(); // 使用 oneOpt 更安全

                    if (latestApplyOpt.isPresent()) {
                        RepayScheduleApply latestApply = latestApplyOpt.get();
                        Date createDate = latestApply.getCreateTime(); // 获取创建时间 (Date 类型)
                        if (createDate != null) {
                            LocalDateTime applyCreateTime = createDate.toInstant()
                                    .atZone(ZoneId.systemDefault())
                                    .toLocalDateTime(); // 转换为 LocalDateTime
                            long minutesSinceApply = Duration.between(applyCreateTime, LocalDateTime.now()).toMinutes();
                            // 3. 如果最新申请在间隔时间分钟内，则跳过本次同步
                            if (minutesSinceApply <= IntervalMinute) {
                                log.warn("【定时任务】loanNo [{}] 存在状态为REPAYING的还款计划，且最近的还款申请在 {} 分钟前，小于等于{}分钟，本次跳过同步。",
                                        loanNo, minutesSinceApply, IntervalMinute);
                                return; // 跳出方法，finally会释放锁
                            } else {
                                log.info("【定时任务】loanNo [{}] 存在状态为REPAYING的还款计划，但最近的还款申请在 {} 分钟前（超过{}分钟），继续执行同步。",
                                        loanNo, minutesSinceApply, IntervalMinute);
                            }
                        } else {
                            log.warn("【定时任务】loanNo [{}] 找到REPAYING状态的还款计划，但其最新还款申请记录的创建时间为空，无法进行时间判断，继续执行同步。", loanNo);
                        }
                    } else {
                        log.info("【定时任务】loanNo [{}] 存在状态为REPAYING的还款计划，但未找到对应的还款申请记录，继续执行同步。", loanNo);
                    }
                } else {
                    log.info("【定时任务】loanNo [{}] 未找到状态为REPAYING的还款计划，继续执行同步。", loanNo);
                }
                // --- 检查逻辑结束 ---

                // 检查通过，提交异步任务
                log.info("【定时任务】loanNo [{}] 检查通过，准备提交异步同步任务。", loanNo);
                Map<String, String> contextMap = MDCUtil.getCopyOfContextMap();
                taskExecutor.execute(() -> {
                    MDCUtil.setContextMap(contextMap);
                    try {
                        log.info("【定时任务】异步任务开始处理 loanNo [{}]。", loanNo);
                        // 调用第一个同步方法
                        try {
                            log.info("【定时任务】开始调用 queryRepaymentSchedule 方法， loanNo [{}]。", loanNo);
                            loanService.queryRepaymentSchedule(loanNo);
                            log.info("【定时任务】调用 queryRepaymentSchedule 方法尝试完成， loanNo [{}]。", loanNo);
                        } catch (Exception e) {
                            log.error("【定时任务】调用 queryRepaymentSchedule 方法失败， loanNo [{}]。错误: {}", loanNo,
                                    e.getMessage(),
                                    e);
                        }

                        // 调用第二个同步方法
                        try {
                            log.info("【定时任务】开始调用 queryApiRepaymentSchedule 方法， loanNo [{}]。", loanNo);
                            loanService.queryApiRepaymentSchedule(loanNo);
                            log.info("【定时任务】调用 queryApiRepaymentSchedule 方法尝试完成， loanNo [{}]。", loanNo);
                        } catch (Exception e) {
                            log.error("【定时任务】调用 queryApiRepaymentSchedule 方法失败， loanNo [{}]。错误: {}", loanNo,
                                    e.getMessage(), e);
                        }
                        log.info("【定时任务】异步任务处理完成 loanNo [{}]。", loanNo);
                    } catch (Exception e) {
                        log.error("【定时任务】处理 loanNo [{}] 的同步任务时发生未预期异常。错误: {}", loanNo, e.getMessage(), e);
                    } finally {
                        MDCUtil.clear();
                    }
                    // 注意：异步任务内部不再需要释放锁，锁的释放由外部的finally块统一处理
                });

            } else {
                log.warn("【定时任务】未能获取 loanNo [{}] 的同步锁，可能其他实例正在处理，跳过本次执行。", loanNo);
            }
        } catch (Exception e) {
            log.error("【定时任务】处理 loanNo [{}] 时发生错误。错误: {}", loanNo, e.getMessage(), e);
            // 异常发生时，锁也应在finally中释放
        } finally {
            // 无论成功、失败、跳过，只要获取了锁，就尝试释放
            if (locked) {
                try {
                    commonRedisUtil.unlock(lockKey);
                    log.info("【定时任务】释放 loanNo [{}] 的同步锁。", loanNo);
                } catch (Exception unlockEx) {
                    log.error("【定时任务】释放锁失败， loanNo [{}]。错误: {}", loanNo, unlockEx.getMessage(), unlockEx);
                }
            }
        }
    }

}