package com.rongchen.byh.app.service;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rongchen.byh.app.config.ZifangFactory;
import com.rongchen.byh.app.dao.CapitalDataMapper;
import com.rongchen.byh.app.dao.DisburseDataMapper;
import com.rongchen.byh.app.dao.RepayScheduleApplyMapper;
import com.rongchen.byh.app.dao.RepayScheduleMapper;
import com.rongchen.byh.app.entity.CapitalData;
import com.rongchen.byh.app.entity.DisburseData;
import com.rongchen.byh.app.entity.RepaySchedule;
import com.rongchen.byh.app.entity.RepayScheduleApply;
import com.rongchen.byh.common.api.zifang.dto.RepaymentResultDto;
import com.rongchen.byh.common.api.zifang.service.RepaymentApi;
import com.rongchen.byh.common.api.zifang.vo.RepaymentResultVo;
import com.rongchen.byh.common.core.constant.SettleFlagConstant;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.MDCUtil;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 
 * 项目名称：byh_java
 * 文件名称: RepayScheduleApplyService
 * 创建时间: 2025-04-05 18:16
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.app.service
 * 文件描述:
 * Copyright (c) 2025 byh_java All rights reserved.
 */
@Service
@Slf4j
public class RepayScheduleApplyService extends ServiceImpl<RepayScheduleApplyMapper, RepayScheduleApply> {

    @Resource
    private ZifangFactory zifangFactory;
    @Resource
    private RepayScheduleMapper repayScheduleMapper;
    @Resource
    private CapitalDataMapper capitalDataMapper; // 或者通过其他方式获取 Capital 信息
    @Resource
    private DisburseDataMapper disburseDataMapper;
    @Resource
    private LoanService loanService;
    @Resource(name = "taskExecutor") // Assuming the bean name is "taskExecutor"
    private TaskExecutor taskExecutor;
    @Resource
    DisburseService disburseService;

    public int insertSelective(RepayScheduleApply record) {
        return baseMapper.insertSelective(record);
    }

    public int updateByPrimaryKeySelective(RepayScheduleApply record) {
        return baseMapper.updateByPrimaryKeySelective(record);
    }

    public int updateByPrimaryKey(RepayScheduleApply record) {
        return baseMapper.updateByPrimaryKey(record);
    }

    public int updateBatch(List<RepayScheduleApply> list) {
        return baseMapper.updateBatch(list);
    }

    public int updateBatchSelective(List<RepayScheduleApply> list) {
        return baseMapper.updateBatchSelective(list);
    }

    public int batchInsert(List<RepayScheduleApply> list) {
        return baseMapper.batchInsert(list);
    }

    public int batchInsertOrUpdate(List<RepayScheduleApply> list) {
        return baseMapper.batchInsertOrUpdate(list);
    }

    /**
     * 查询资方还款结果并更新本地还款申请和还款计划的状态。
     * 注意：此方法需要调用方确保传入的 applyRecord 是需要查询状态的（例如 repay_status=0）。
     * 并且需要提供获取关联 CapitalData 的逻辑。
     *
     * @param applyRecord 需要查询状态的还款申请记录
     * @param capitalData 关联的资方信息 (需要调用方传入或在此方法内部查询)
     */
    @Transactional(rollbackFor = Exception.class)
    public void queryAndUpdateRepaymentStatus(RepayScheduleApply applyRecord, CapitalData capitalData) {
        if (applyRecord == null || applyRecord.getId() == null || capitalData == null
                || capitalData.getBeanName() == null) {
            log.error("【还款结果查询更新】参数不足，无法处理。applyRecord ID: {}, capitalData BeanName: {}",
                    applyRecord != null ? applyRecord.getId() : "null",
                    capitalData != null ? capitalData.getBeanName() : "null");
            return;
        }

        String repayApplyNo = applyRecord.getRepayApplyNo();
        Long userId = applyRecord.getUserId();
        Long scheduleId = applyRecord.getRepayScheduleId();

        log.info("【还款结果查询更新】开始处理还款申请号: {}, 对应计划ID: {}, 用户ID: {}, 资方: {}",
                repayApplyNo, scheduleId, userId, capitalData.getBeanName());

        try {
            RepaymentApi api = zifangFactory.getApi(capitalData.getBeanName(), RepaymentApi.class);
            RepaymentResultDto resultDto = new RepaymentResultDto();
            resultDto.setUserId(String.valueOf(userId));
            resultDto.setRepayApplyNo(repayApplyNo);

            ResponseResult<RepaymentResultVo> result = api.getRepaymentResult(resultDto);

            if (result.isSuccess()) {
                RepaymentResultVo data = result.getData();
                log.info("【还款结果查询更新】申请号: {}, 资方返回: {}", repayApplyNo, data); // 注意敏感信息
                int repayStatus = applyRecord.getRepayStatus(); // 默认为当前状态
                String reason = applyRecord.getReason();
                String settleFlag = null; // 默认为 null，表示不一定需要更新 schedule
                String datePay = null;
                String datePayTime = null;

                if ("0000".equals(data.getResponseCode())) {
                    if ("SUCCESS".equals(data.getStatus()) || "CLOSE".equals(data.getStatus())) {
                        repayStatus = 1; // 成功
                        reason = "查询资方状态成功";
                        settleFlag = SettleFlagConstant.CLOSE;
                        datePay = DateUtil.today();
                        datePayTime = data.getRepayTime();
                    } else if ("FAIL".equals(data.getStatus())) {
                        repayStatus = 2; // 失败
                        reason = data.getResult() != null ? data.getResult() : "查询资方状态失败";
                        settleFlag = SettleFlagConstant.RUNNING; // 失败时，计划状态应为 RUNNING
                    } else if ("REPAYING".equals(data.getStatus())) {
                        log.info("【还款结果查询更新】申请号: {}, 资方返回处理中状态: REPAYING，本次不更新状态", repayApplyNo);
                        return; // 处理中状态，不进行更新
                    } else {
                        log.warn("【还款结果查询更新】申请号: {}, 资方返回未处理状态: {}，本次不更新状态", repayApplyNo, data.getStatus());
                        return; // 其他未预期的状态，不进行更新
                    }

                    // 更新 RepayScheduleApply
                    RepayScheduleApply applyUpdate = new RepayScheduleApply();
                    applyUpdate.setId(applyRecord.getId());
                    applyUpdate.setRepayStatus(repayStatus);
                    applyUpdate.setReason(reason);
                    applyUpdate.setResponseTime(DateUtil.date());
                    this.updateById(applyUpdate);
                    log.info("【还款结果查询更新】申请号: {}, 更新 apply 记录 ID: {} 状态为: {}", repayApplyNo, applyRecord.getId(),
                            repayStatus);

                    // 如果需要更新 RepaySchedule
                    if (settleFlag != null && scheduleId != null) {
                        RepaySchedule scheduleUpdate = new RepaySchedule();
                        scheduleUpdate.setId(scheduleId);
                        scheduleUpdate.setSettleFlag(settleFlag);
                        if (datePay != null)
                            scheduleUpdate.setDatePay(datePay);
                        if (datePayTime != null)
                            scheduleUpdate.setDatePayTime(datePayTime);
                        int updatedRows = repayScheduleMapper.updateById(scheduleUpdate);
                        log.info("【还款结果查询更新】申请号: {}, 更新 schedule 记录 ID: {} 状态为: {}", repayApplyNo, scheduleId,
                                settleFlag);

                        // --- 添加了异步同步触发器 ---
                        if (updatedRows > 0 && SettleFlagConstant.CLOSE.equals(settleFlag)) {
                            triggerAsyncScheduleSync(repayApplyNo, scheduleId);
                        }
                        // --- End 已添加异步同步触发器 ---
                    }

                } else {
                    log.warn("【还款结果查询更新】申请号: {}, 资方查询响应码非0000: {} - {}", repayApplyNo, data.getResponseCode(),
                            data.getResponseMsg());
                    // 根据业务判断，是否需要将本地 apply 记录标记为失败？
                    // RepayScheduleApply applyUpdate = new RepayScheduleApply();
                    // applyUpdate.setId(applyRecord.getId());
                    // applyUpdate.setRepayStatus(2); // 标记为失败
                    // applyUpdate.setReason("查询资方结果响应失败: " + data.getResponseMsg());
                    // applyUpdate.setResponseTime(DateUtil.date());
                    // this.updateById(applyUpdate);
                }
            } else {
                log.error("【还款结果查询更新】申请号: {}, 调用资方查询API失败: {}", repayApplyNo, result.getErrorMessage());
                // API 调用失败，通常不更新本地状态，等待下次重试
            }
        } catch (Exception e) {
            log.error("【还款结果查询更新】申请号: {}, 处理异常", repayApplyNo, e);
            // 抛出异常以便事务回滚
            throw new RuntimeException("处理还款结果查询更新时发生异常: " + repayApplyNo, e);
        }
    }

    // --- Added Helper Method ---
    /**
     * 异步触发还款计划同步
     * 
     * @param repayApplyNo 还款申请号 (用于日志)
     * @param scheduleId   还款计划ID (用于查询关联信息)
     */
    private void triggerAsyncScheduleSync(String repayApplyNo, Long scheduleId) {
        Map<String, String> contextMap = MDCUtil.getCopyOfContextMap();
        taskExecutor.execute(() -> {
            MDCUtil.setContextMap(contextMap);
            String loanNo = null;
            try {
                RepaySchedule schedule = repayScheduleMapper.selectById(scheduleId);
                if (schedule != null && schedule.getDisburseId() != null) {
                    DisburseData disburseData = disburseDataMapper.selectById(schedule.getDisburseId());
                    if (disburseData != null && disburseData.getLoanNo() != null) {
                        loanNo = disburseData.getLoanNo();
                        log.info("【异步还款计划同步】触发：还款申请号 {}, 关联 LoanNo: {}, 开始同步...", repayApplyNo, loanNo);

                        try {
                            log.info("【异步还款计划同步】LoanNo [{}]：调用 queryRepaymentSchedule 开始", loanNo);
                            loanService.queryRepaymentSchedule(loanNo);
                            log.info("【异步还款计划同步】LoanNo [{}]：调用 queryRepaymentSchedule 完成", loanNo);
                        } catch (Exception e) {
                            log.error("【异步还款计划同步】LoanNo [{}]：调用 queryRepaymentSchedule 失败: {}", loanNo, e.getMessage(),
                                    e);
                        }

                        try {
                            log.info("【异步还款计划同步】LoanNo [{}]：调用 queryApiRepaymentSchedule 开始", loanNo);
                            loanService.queryApiRepaymentSchedule(loanNo);
                            log.info("【异步还款计划同步】LoanNo [{}]：调用 queryApiRepaymentSchedule 完成", loanNo);
                        } catch (Exception e) {
                            log.error("【异步还款计划同步】LoanNo [{}]：调用 queryApiRepaymentSchedule 失败: {}", loanNo,
                                    e.getMessage(), e);
                        }
                        log.info("【异步还款计划同步】触发：还款申请号 {}, LoanNo: {}, 同步任务尝试完成。", repayApplyNo, loanNo);

                        disburseService.checkOverDisburse(disburseData.getId());
                    } else {
                        log.warn("【异步还款计划同步】触发：还款申请号 {}, 但无法找到有效的 DisburseData 或 LoanNo (DisburseID: {})。",
                                repayApplyNo, schedule.getDisburseId());
                    }
                } else {
                    log.warn("【异步还款计划同步】触发：还款申请号 {}, 但无法找到有效的 RepaySchedule (ScheduleID: {})。", repayApplyNo,
                            scheduleId);
                }
            } catch (Exception e) {
                log.error("【异步还款计划同步】触发：处理还款申请号 {} (LoanNo: {}) 时发生未预期异常: {}", repayApplyNo, loanNo, e.getMessage(), e);
            } finally {
                MDCUtil.clear();
            }
        });
    }

}
