package com.rongchen.byh.app.constant;

import java.util.HashMap;
import java.util.Map;


/**
 * api 蚂蚁用信借款用途
 */
public enum LoanUseType {
    DEC("装修"),
    EDU("教育"),
    HEA("家用电器"),
    MAR("婚庆"),
    REN("租房"),
    MOD("手机数码"),
    TRA("旅游"),
    MED("医疗"),
    CAR("购车"),
    HOU("买房"),
    BFM("购买农机"),
    LRT("土地租金"),
    LEA("租赁"),
    SHO("购物"),
    BAM("购买农资"),
    OTH("其他消费");

    private final String description;
    private static final Map<String, String> codeToDescriptionMap = new HashMap<>();

    static {
        for (LoanUseType cd : LoanUseType.values()) {
            codeToDescriptionMap.put(cd.name(), cd.getDescription());
        }
    }

    LoanUseType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public static String getDescriptionByCode(String code) {
        return codeToDescriptionMap.getOrDefault(code, "其他消费");
    }
}
