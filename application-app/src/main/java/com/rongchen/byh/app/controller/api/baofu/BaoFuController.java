package com.rongchen.byh.app.controller.api.baofu;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.util.StrUtil;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.rongchen.byh.app.utils.UserTokenUtil;
import com.rongchen.byh.common.api.baofu.adapter.BaofuAdapterService;
import com.rongchen.byh.common.api.baofu.util.AmountUtil;
import com.rongchen.byh.common.api.baofu.vo.req.DirectPayReqVo;
import com.rongchen.byh.common.api.baofu.vo.rsp.DirectPayRspVo;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.GeneratorUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.math.BigDecimal;

import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 项目名称：byh_java
 * 文件名称: BaoFuController
 * 创建时间: 2025-03-14 14:57
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.app.controller.api.baofu
 * 文件描述:
 * Copyright (c) 2025 byh_java All rights reserved.
 */
@Tag(name = "宝付相关接口")
@ApiSupport(order = 3)
@RestController
@RequestMapping("/userApi/baofu")
@Slf4j
public class BaoFuController {


    @Resource
    private BaofuAdapterService baoFuService;

    /**
     * 宝付支付测试类
     * @param req
     * @return
     */
    @Operation(summary = "宝付支付测试")
    @SaIgnore
    @PostMapping("/pay-test")
    public ResponseResult<DirectPayRspVo> payTest(@RequestBody DirectPayReqVo req) {
        log.info("宝付支付测试开始   req:{}", req);
        //金额限制最大十元
        if (AmountUtil.centToYuan(req.getTxnAmt()).compareTo(BigDecimal.TEN) > 0) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "金额限制最大十元");
        }
        Long userId = UserTokenUtil.getUserId();
        req.setUserId(userId.toString());
        if(StrUtil.isBlank(req.getTransId())){
            req.setTransId(GeneratorUtil.generateOrderNo("BYH"));
        }
        return baoFuService.directPay(req);
    }

}
