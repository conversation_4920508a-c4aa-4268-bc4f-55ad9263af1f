package com.rongchen.byh.app.v2.handle.product;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 产品处理中心
 */
@Component
public class ProductHandleCenter implements ApplicationContextAware {

    private static Map<String, ProductHandleFactory> productHandleCenter;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {

        Map<String,ProductHandleFactory> map = applicationContext.getBeansOfType(ProductHandleFactory.class);
        productHandleCenter = map;
    }


    public static ProductHandleFactory getProductHandle(String beanName) {
        return productHandleCenter.get(beanName);
    }


}