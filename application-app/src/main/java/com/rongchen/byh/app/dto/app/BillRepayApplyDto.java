package com.rongchen.byh.app.dto.app;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 账单还款dto
 * @date 2024/12/15 14:22:11
 */
@Data
public class BillRepayApplyDto {
    @Schema(description = "本息还款计划id")
    @NotNull(message = "本息还款计划不能为空")
    private Long repayScheduleId;

    @Schema(description = "赊销还款计划id")
    @NotNull(message = "赊销还款计划不能为空")
    private Long repaySaleId;

    private String repayApplyNo;

    private String saleApplyNo;

    /**
     * 0：线上还款，1：线下还款
     */
    private String repayMethod;

    /**
     * 0对私，1对公
     */
    private String bankAccountType;

}
