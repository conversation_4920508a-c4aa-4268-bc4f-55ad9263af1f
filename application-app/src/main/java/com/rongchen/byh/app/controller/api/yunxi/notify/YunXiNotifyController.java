package com.rongchen.byh.app.controller.api.yunxi.notify;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.rongchen.byh.app.entity.SaleOrderRecord;
import com.rongchen.byh.app.service.SaleOrderRecordService;
import com.rongchen.byh.common.api.beiyihua.config.BeiYiHuaProperties;
import com.rongchen.byh.common.api.yunxi.config.YunXiApiConfig;
import com.rongchen.byh.common.api.yunxi.dto.YunXiApiRspDto;
import com.rongchen.byh.common.api.yunxi.dto.req.BenefitOrderNotifyReqDto;
import com.rongchen.byh.common.api.yunxi.dto.req.BenefitOrderQueryReqDto;
import com.rongchen.byh.common.api.yunxi.dto.req.EncryptedOrderNotifyReqDto;
import com.rongchen.byh.common.api.yunxi.service.YunXiApiService;
import com.rongchen.byh.common.api.yunxi.utils.YunXiApiUtils;
import com.rongchen.byh.common.api.yunxi.vo.YunXiOrderDetailVo;
import com.rongchen.byh.common.core.annotation.DisableDataFilter;
import com.rongchen.byh.common.core.util.HttpUtil;
import com.rongchen.byh.common.core.util.JsonUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 云樨API回调通知控制器
 */
@ApiSupport(order = 1)
@Tag(name = "云樨API回调通知")
@DisableDataFilter
@Slf4j
@RestController
@RequestMapping("/yunxi/notify")
public class YunXiNotifyController {

    @Resource
    private YunXiApiConfig config;

    @Resource
    private YunXiApiService yunxiApiService;

    @Resource
    private SaleOrderRecordService saleOrderRecordService;

    @Resource
    BeiYiHuaProperties beiYiHuaProperties;

    /**
     * 权益订单结果通知
     * 处理权益购买结果和退款审核结果的推送
     *
     * @param req 嵌套的加密通知请求
     * @return 处理结果
     */
    @PostMapping("/order")
    @Operation(summary = "权益订单结果通知")
    @SaIgnore
    public String handleOrderNotify(@RequestBody EncryptedOrderNotifyReqDto req) {
        if (req == null || req.getEncryptOrder() == null) {
            log.error("收到无效的通知请求：请求为空");
            return "FAIL";
        }

        log.info("收到加密的权益订单结果通知: {}", req);

        if (StrUtil.isBlank(req.getEncryptOrder())) {
            log.error("加密订单数据为空");
            return "FAIL";
        }

        try {
            // 解密订单数据
            String decryptedJson = null;
            try {
                log.info("尝试自动解密订单数据");
                decryptedJson = YunXiApiUtils.autoDecrypt(req.getEncryptOrder(), config.getAesKey());
                log.info("解密后的订单数据: {}", decryptedJson);
            } catch (Exception e) {
                log.error("订单数据解密失败", e);
                return "FAIL";
            }

            if (StrUtil.isBlank(decryptedJson)) {
                log.error("解密结果为空");
                return "FAIL";
            }

            // 转换为BenefitOrderNotifyReqDto对象
            BenefitOrderNotifyReqDto request = JSON.parseObject(decryptedJson, BenefitOrderNotifyReqDto.class);
            log.info("解析后的权益订单通知: {}", JsonUtils.toJsonString(request));


            if (request == null) {
                log.error("解析结果为空");
                return "FAIL";
            }

            // 验证签名
            Map<String, Object> params = JSON.parseObject(
                    JSON.toJSONString(request), Map.class);
            String sign = YunXiApiUtils.generateSign(params, config.getAppSecret());

            if (!sign.equals(request.getSign())) {
                log.error("权益订单结果通知签名验证失败");
            }

            // 验证appId
            if (!config.getAppId().equals(request.getAppId())) {
                log.error("权益订单结果通知appId不匹配");
                return "FAIL";
            }

            // 处理权益订单结果
            String orderStatus = request.getOrderStatus();
            String externalOrderNum = request.getExternalOrderNum();
            String orderNum = request.getOrderNum();

            log.info("处理权益订单结果通知: 内部订单号={}, 外部平台订单号={}, 订单状态={}",
                    externalOrderNum, orderNum, orderStatus);
            if (externalOrderNum.startsWith("jw-")) {
                log.info("【云樨】权益订单结果通知 分发给jw, 状态: {}, 内部订单号: {}, 云樨订单号: {}", orderStatus, externalOrderNum, orderNum);
                try {
                    return HttpUtil.postJson(beiYiHuaProperties.getYunXiCallbackUrl(), JSONObject.toJSONString(req));
                } catch (Exception e) {
                    log.error("【资方】授信审批结果分发 异常",e);
                }
            }


            // 通过云樨API获取最新订单详情
            BenefitOrderQueryReqDto queryRequest = new BenefitOrderQueryReqDto();
            queryRequest.setOrderNum(orderNum);
            YunXiApiRspDto<YunXiOrderDetailVo> yunXiResult = yunxiApiService.queryBenefitOrder(queryRequest);

            if (!yunXiResult.isSuccess()) {
                log.error("查询云樨订单详情失败 - 订单号: {}, 错误码: {}, 错误信息: {}",
                        orderNum, yunXiResult.getRspCode(), yunXiResult.getRspMsg());
                return "FAIL";
            }

            // 获取订单详情
            YunXiOrderDetailVo orderDetail = yunXiResult.getData();

            // 查询已有的权益赊销渠道记录
            SaleOrderRecord existingChannel = saleOrderRecordService.lambdaQuery()
                    .eq(SaleOrderRecord::getOutSaleNo, orderNum)
                    .one();

            if (existingChannel == null) {
                log.error("未找到对应的权益赊销渠道记录 - 云樨订单号: {}, 外部订单号: {}",
                        orderNum, externalOrderNum);
                return "FAIL";
            }

            // 更新渠道记录状态
            SaleOrderRecord updatedChannel = saleOrderRecordService.buildFromOrderDetail(
                    orderDetail,
                    existingChannel,
                    existingChannel.getSaleNo(),
                    existingChannel.getUserId());

            // 保存更新
            boolean updateResult = saleOrderRecordService.updateById(updatedChannel);
            if (!updateResult) {
                log.error("更新权益赊销渠道记录失败 - 云樨订单号: {}", orderNum);
                return "FAIL";
            }

            log.info("更新权益赊销渠道记录成功 - 云樨订单号: {}, 状态: {}", orderNum, orderStatus);

            // 根据订单状态处理业务逻辑
            switch (orderStatus) {
                case "PAY_SUCCESS":
                    // 支付成功,开通权益
                    log.info("订单支付成功,处理开通权益流程 - 订单号: {}", orderNum);
                    handleOrderPaySuccess(existingChannel);
                    break;
                case "PAY_FAIL":
                    // 支付失败
                    log.info("订单支付失败 - 订单号: {}", orderNum);
                    handleOrderPayFail(existingChannel);
                    break;
                case "PAY_ING":
                    // 支付中
                    log.info("订单支付处理中 - 订单号: {}", orderNum);
                    break;
                case "REFUND_ING":
                    // 退款处理中
                    log.info("订单退款处理中 - 订单号: {}", orderNum);
                    handleOrderRefundProcess(existingChannel);
                    break;
                case "REFUND_SUCCESS":
                    // 退款成功
                    log.info("订单退款成功,处理关闭权益流程 - 订单号: {}", orderNum);
                    handleOrderRefundSuccess(existingChannel);
                    break;
                case "REFUND_FAIL":
                    // 退款失败
                    log.info("订单退款失败 - 订单号: {}", orderNum);
                    handleOrderRefundFail(existingChannel);
                    break;
                default:
                    // 其他状态处理
                    log.info("订单状态[{}]无需特殊处理 - 订单号: {}", orderStatus, orderNum);
                    break;
            }

            return "SUCCESS";
        } catch (Exception e) {
            log.error("处理权益订单结果通知异常", e);
            return "FAIL";
        }
    }

    /**
     * 处理订单支付成功
     *
     * @param channel 赊销渠道记录
     */
    private void handleOrderPaySuccess(SaleOrderRecord channel) {
        // TODO: 实现支付成功后的业务逻辑
        // 1. 更新订单状态
        // 2. 开通用户权益
        // 3. 发送通知等
        log.info("处理订单支付成功 - 内部订单号: {}, 外部订单号: {}",
                channel.getSaleNo(), channel.getOutSaleNo());
    }

    /**
     * 处理订单支付失败
     *
     * @param channel 赊销渠道记录
     */
    private void handleOrderPayFail(SaleOrderRecord channel) {
        // TODO: 实现支付失败后的业务逻辑
        // 1. 更新订单状态
        // 2. 取消订单处理
        log.info("处理订单支付失败 - 内部订单号: {}, 外部订单号: {}",
                channel.getSaleNo(), channel.getOutSaleNo());
    }

    /**
     * 处理订单退款中
     *
     * @param channel 赊销渠道记录
     */
    private void handleOrderRefundProcess(SaleOrderRecord channel) {
        // TODO: 实现退款处理中的业务逻辑
        // 1. 更新订单状态
        // 2. 记录退款处理中状态
        log.info("处理订单退款中 - 内部订单号: {}, 外部订单号: {}",
                channel.getSaleNo(), channel.getOutSaleNo());
    }

    /**
     * 处理订单退款成功
     *
     * @param channel 赊销渠道记录
     */
    private void handleOrderRefundSuccess(SaleOrderRecord channel) {
        // TODO: 实现退款成功后的业务逻辑
        // 1. 更新订单状态
        // 2. 关闭权益
        // 3. 发送通知等
        log.info("处理订单退款成功 - 内部订单号: {}, 外部订单号: {}",
                channel.getSaleNo(), channel.getOutSaleNo());
    }

    /**
     * 处理订单退款失败
     *
     * @param channel 赊销渠道记录
     */
    private void handleOrderRefundFail(SaleOrderRecord channel) {
        // TODO: 实现退款失败后的业务逻辑
        // 1. 更新订单状态
        // 2. 继续维持用户权益
        log.info("处理订单退款失败 - 内部订单号: {}, 外部订单号: {}",
                channel.getSaleNo(), channel.getOutSaleNo());
    }
}
