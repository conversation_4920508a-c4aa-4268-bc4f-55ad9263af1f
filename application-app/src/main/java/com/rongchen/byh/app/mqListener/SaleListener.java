package com.rongchen.byh.app.mqListener;

import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.rongchen.byh.app.dao.RepaySaleApplyMapper;
import com.rongchen.byh.app.dao.SaleScheduleMapper;
import com.rongchen.byh.app.dto.app.BillRepayApplyDto;
import com.rongchen.byh.app.entity.RepaySaleApply;
import com.rongchen.byh.app.entity.SaleSchedule;
import com.rongchen.byh.app.service.DisburseRecordService;
import com.rongchen.byh.app.service.SaleRepayService;
import com.rongchen.byh.common.core.constant.SettleFlagConstant;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.rabbitmq.constants.QueueConstant;
import com.rongchen.byh.common.rabbitmq.dto.SaleDto;
import com.rongchen.byh.common.redis.util.CommonRedisUtil;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class SaleListener {

    @Resource
    private SaleScheduleMapper saleScheduleMapper;
    @Resource
    private SaleRepayService saleRepayService;
    @Resource
    DisburseRecordService disburseRecordService;
    @Resource
    RepaySaleApplyMapper repaySaleApplyMapper;
    @Resource
    RedissonClient redissonClient;
    @Resource
    CommonRedisUtil commonRedisUtil;

    @RabbitListener(queues = QueueConstant.SALE_QUEUE, ackMode = "MANUAL")
    public void receiver(String msg, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) { // 修改方法签名
       
        SaleDto saleDto = null;
        Long disburseId = null; // Original type for DB
        String term = null; // Original type for DB (assuming String based on later usage)
        String disburseIdStr = "N/A"; // String version for logging/lock key
        String termStr = "N/A"; // String version for logging/lock key
        String lockKey = "未知"; // 分布式锁键
        RLock lock = null; // 分布式锁对象

        try {
            log.info("接收到赊销还款消息: {}", msg);
            // 1. 解析消息
            try {
                saleDto = JSONObject.parseObject(msg, SaleDto.class);
            } catch (Exception e) {
                log.error("从消息解析 SaleDto 失败: {}. 错误: {}", msg, e.getMessage(), e);
                channel.basicNack(deliveryTag, false, false); // 解析失败，拒绝并不重入
                return;
            }

            // 2. 校验消息内容
            if (saleDto == null || saleDto.getDisburseId() == null || saleDto.getTerm() == null) {
                log.error("收到无效的 SaleDto (null 或缺少 disburseId/term): {}", msg);
                channel.basicNack(deliveryTag, false, false); // 无效数据，拒绝
                return;
            }
            // Store original types for business logic
            disburseId = saleDto.getDisburseId();
            term = saleDto.getTerm();
            // Create String versions for lock key and consistent logging
            disburseIdStr = String.valueOf(disburseId);
            termStr = String.valueOf(term); // Convert term to String for key

            // 3. 获取分布式锁
            lockKey = String.format("lock:sale:%s:%s", disburseIdStr, termStr);
            lock = redissonClient.getLock(lockKey);

            boolean locked = false;
            try {
                // 尝试获取锁，等待0秒，租约60秒
                locked = lock.tryLock(0, 60, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                log.warn("【赊销监听器】获取分布式锁时被中断，锁键: {}. 消息将重新排队。", lockKey, e);
                Thread.currentThread().interrupt(); // 恢复中断状态
                channel.basicNack(deliveryTag, false, true); // 要求重新排队
                return;
            }

            if (!locked) {
                log.warn("【赊销监听器】未能获取赊销处理锁（可能已有其他实例在处理），锁键: {}. 消息将重新排队。", lockKey);
                channel.basicNack(deliveryTag, false, true); // 要求重新排队
                return;
            }

            // 锁已获取，在 try...finally 中处理业务逻辑
            log.info("【赊销监听器】成功获取赊销处理锁，开始处理赊销还款消息，锁键: {}, 原始消息: {}", lockKey, msg);

            try {
                // 4. 设置 Trace ID (在锁内)
                String traceId = saleDto.getTraceId();
                Map<String, String> contextMap = new HashMap<>();
                contextMap.put("traceId", traceId);
                MDC.setContextMap(contextMap); // 确保在 finally 中 clear
                log.info("【赊销还款监听器】开始处理消息，RabbitMQ原始消息: {} tag: {}", msg, deliveryTag);
                // 5. 核心业务逻辑处理 (在锁内)
                SaleSchedule saleSchedule = saleScheduleMapper.selectByDisburseIdAndTerm(disburseId, term);
                if (saleSchedule == null) {
                    log.error("【赊销监听器】[锁内] 还款校验失败：未找到对应的赊销计划 (DisburseId: {}, Term: {})", disburseIdStr, termStr);
                    channel.basicNack(deliveryTag, false, false); // 业务数据问题，拒绝
                    return;
                }

                if (!saleSchedule.getSettleFlag().equals(SettleFlagConstant.RUNNING)) {
                    log.warn(
                            "【赊销监听器】[锁内] 还款校验失败：赊销状态不正确，预期 RUNNING，实际 {} (SaleScheduleId: {}, DisburseId: {}, Term: {})",
                            saleSchedule.getSettleFlag(), saleSchedule.getId(), disburseIdStr, termStr);
                    // 状态不正确，可能后续会变成 RUNNING，或者已经是终态。拒绝消息，不重试，避免阻塞。
                    channel.basicNack(deliveryTag, false, false);
                    return;
                }

                int i = saleScheduleMapper.payingCount(saleSchedule.getDisburseId());
                if (i > 0) {
                    log.info("【赊销监听器】[锁内] 还款校验失败：该赊销已有扣款在进行中 (SaleScheduleId: {}, DisburseId: {}, Term: {})",
                            saleSchedule.getId(), disburseIdStr, termStr);
                    // 有其他扣款进行中，暂时无法处理，拒绝消息，不重试，等待下次触发或轮询
                    channel.basicNack(deliveryTag, false, false);
                    return;
                }

                // 调用赊销接口
                BillRepayApplyDto billSaleApplyDto = new BillRepayApplyDto();
                billSaleApplyDto.setRepaySaleId(saleSchedule.getId());
                billSaleApplyDto.setRepayMethod("0"); // 假设 "0" 是MQ触发的类型
                billSaleApplyDto.setBankAccountType("0"); // 根据实际情况可能需要从 DTO 获取或设定
                ResponseResult<Void> saleResult = saleRepayService.saleRepayNew(billSaleApplyDto);
                if (!saleResult.isSuccess()) {
                    log.error("【赊销监听器】[锁内] 还款校验失败：调用资方赊销还款接口失败 (SaleScheduleId: {}). 原因: {}",
                            saleSchedule.getId(), saleResult.getErrorMessage());
                    disburseRecordService.saveRecord(2, "赊销(saleSchedule)id:" + saleSchedule.getId(), traceId,
                            "资方赊销还款失败，错误码: " + saleResult.getErrorCode() + ", 信息: " + saleResult.getErrorMessage());
                    channel.basicNack(deliveryTag, false, false); // 调用外部接口失败，拒绝
                    return;
                }

                // 注意：原逻辑在调用成功后重新查询了 SaleSchedule，这里保留该逻辑，但需确认其必要性
                SaleSchedule schedule = saleScheduleMapper.selectByDisburseIdAndTerm(disburseId, term);
                if (schedule == null) {
                    // 如果重新查询不到，可能是一个并发问题或数据不一致，记录错误并拒绝
                    log.error("【赊销监听器】[锁内] 还款校验警告：调用还款接口成功后重新查询 SaleSchedule 未找到 (DisburseId: {}, Term: {})",
                            disburseIdStr, termStr);
                    // 即使重新查询失败，核心还款调用已成功，可能需要确认消息。但为保守起见，先拒绝。
                    // 或者考虑补偿逻辑。此处暂时拒绝。
                    channel.basicNack(deliveryTag, false, false);
                    return;
                }

                if (schedule.getRepayApplyNo() == null || schedule.getRepayApplyNo().isEmpty()) {
                    log.error("【赊销监听器】[锁内] 还款校验警告：重新查询 SaleSchedule 后 repayApplyNo 为空 (DisburseId: {}, Term: {})",
                            disburseIdStr, termStr);
                    channel.basicNack(deliveryTag, false, false); // 数据问题，拒绝
                }

                RepaySaleApply repayScheduleApply = new RepaySaleApply();
                // 使用重新查询到的 schedule 的 repayApplyNo，如果它被更新了
                repayScheduleApply.setRepayApplyNo(schedule.getRepayApplyNo());
                repayScheduleApply.setUserId(saleSchedule.getUserId()); // userId 用最初查询的
                repayScheduleApply.setSaleScheduleId(saleSchedule.getId()); // scheduleId 用最初查询的
                repayScheduleApply.setRepayType(saleDto.getRepayType());
                repayScheduleApply.setRepayStatus(0); // 假设 0 是初始状态
                repaySaleApplyMapper.insert(repayScheduleApply);

                log.info("【赊销监听器】[锁内] 还款校验成功：赊销还款提交成功，已插入还款申请记录 (SaleScheduleId: {}, DisburseId: {}, Term: {})",
                        saleSchedule.getId(), disburseIdStr, termStr);

                // 6. 业务成功，确认消息 (不再设置 Redis 状态)
                channel.basicAck(deliveryTag, false);
                log.info("【赊销监听器】消息已确认 (ACK), 锁键: {}, deliveryTag: {}", lockKey, deliveryTag);

            } catch (Exception e) {
                // 捕获内部业务逻辑处理期间的意外错误
                log.error("【赊销监听器】[锁内] 业务处理期间发生意外错误，锁键: {}, 错误: {}",
                        lockKey, e.getMessage(), e);
                // 业务处理失败，拒绝消息并不重新入队
                channel.basicNack(deliveryTag, false, false);
                log.warn("【赊销监听器】因内部错误，消息已拒绝 (NACK)，锁键: {}, deliveryTag: {}", lockKey, deliveryTag);
            } finally {
                // 确保在业务逻辑块结束后释放锁
                if (lock != null && lock.isHeldByCurrentThread()) {
                    try {
                        lock.unlock();
                        log.debug("【赊销监听器】分布式锁已释放，锁键: {}", lockKey);
                    } catch (Exception unlockEx) {
                        log.error("【赊销监听器】在最外层 finally 中释放锁时发生异常, 锁键: {}", lockKey, unlockEx);
                    }
                }
                MDC.clear(); // 清理 MDC，放在锁释放之后
            }

        } catch (IOException e) {
            // 处理 basicAck 或 basicNack 可能抛出的 IOException
            log.error("【赊销监听器】手动确认/拒绝消息时发生 IO 错误, 锁键(可能未知): {}, deliveryTag: {}. 错误: {}",
                    (lockKey != null ? lockKey : "未知"), deliveryTag, e.getMessage(), e);
            // IO 异常通常表示与 Broker 通信问题，无法确定状态，只能记录日志
        } catch (Exception e) {
            // 捕获最外层的未知错误（例如 Redis 连接问题、解析之前的错误等）
            log.error("【赊销监听器】处理消息外层发生未知错误, 锁键(可能未知): {}, deliveryTag: {}. 错误: {}",
                    (lockKey != null ? lockKey : "未知"), deliveryTag, e.getMessage(), e);
            try {
                // 尝试拒绝消息
                channel.basicNack(deliveryTag, false, false);
                log.warn("【赊销监听器】因外层未知错误，消息已尝试拒绝 (NACK), 锁键(可能未知): {}, deliveryTag: {}",
                        (lockKey != null ? lockKey : "未知"), deliveryTag);
            } catch (IOException ioEx) {
                log.error("【赊销监听器】外层未知错误，尝试拒绝消息时再次发生 IO 错误, 锁键(可能未知): {}, deliveryTag: {}. 错误: {}",
                        (lockKey != null ? lockKey : "未知"), deliveryTag, ioEx.getMessage(), ioEx);
            }
        } finally {
            // 确保锁在最外层异常时也能尝试释放
            if (lock != null && lock.isHeldByCurrentThread()) {
                try {
                    lock.unlock();
                    log.warn("【赊销监听器】分布式锁在最外层 finally 中被释放（可能表示有异常发生在业务 try 块之前），锁键: {}", lockKey);
                } catch (Exception unlockEx) {
                    log.error("【赊销监听器】在最外层 finally 中释放锁时发生异常, 锁键: {}", lockKey, unlockEx);
                }
            }
        }
    }
}