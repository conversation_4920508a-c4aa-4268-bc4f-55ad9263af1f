package com.rongchen.byh.app.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.app.config.ThreadConfig;
import com.rongchen.byh.app.dao.DisburseDataMapper;
import com.rongchen.byh.app.dao.RepaySaleApplyMapper;
import com.rongchen.byh.app.dao.RepayScheduleApplyMapper;
import com.rongchen.byh.app.dao.RepayScheduleMapper;
import com.rongchen.byh.app.dao.UserCreditDataMapper;
import com.rongchen.byh.app.dao.UserRegisterResultMapper;
import com.rongchen.byh.app.dto.api.FenZhuanCreditNoticeDto;
import com.rongchen.byh.app.dto.api.FenZhuanLoanNoticeDto;
import com.rongchen.byh.app.dto.api.FenZhuanRepayNotice;
import com.rongchen.byh.app.entity.DisburseData;
import com.rongchen.byh.app.entity.RepaySchedule;
import com.rongchen.byh.app.entity.RepayScheduleApply;
import com.rongchen.byh.app.entity.UserCreditData;
import com.rongchen.byh.app.entity.UserRegisterResult;
import com.rongchen.byh.app.service.DisburseRecordService;
import com.rongchen.byh.app.service.FenZhuanZiFangService;
import com.rongchen.byh.common.api.zifang.vo.BaseVo;
import com.rongchen.byh.common.core.constant.SettleFlagConstant;
import com.rongchen.byh.common.rabbitmq.constants.QueueConstant;
import com.rongchen.byh.common.rabbitmq.dto.LoanSuccessDto;
import com.rongchen.byh.common.rabbitmq.dto.SaleDto;
import java.math.BigDecimal;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

/**
 * @ClassName FenZhuanZiFangServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/10 16:35
 * @Version 1.0
 **/
@Slf4j
@Service
public class FenZhuanZiFangServiceImpl implements FenZhuanZiFangService {

    @Resource
    UserRegisterResultMapper userRegisterResultMapper;
    @Resource
    DisburseDataMapper disburseDataMapper;
    @Resource
    UserCreditDataMapper userCreditDataMapper;
    @Resource
    RabbitTemplate rabbitTemplate;
    @Resource
    RepayScheduleApplyMapper repayScheduleApplyMapper;
    @Resource
    private RepaySaleApplyMapper repaySaleApplyMapper;
    @Resource
    private RepayScheduleMapper repayScheduleMapper;
    @Resource
    DisburseRecordService disburseRecordService;
    @Resource
    SmsLoanService smsLoanService;

    @Override
    public BaseVo creditNotice(FenZhuanCreditNoticeDto dto) {
        if (dto != null) {
            if (dto.getOrderNo() != null) {
                UserRegisterResult result = userRegisterResultMapper.queryByOrderNo(dto);
                if (result == null) {
                    log.info("分转资方 审核结果回调,用户进件结果为空,订单号：{}", dto.getOrderNo());
                    return null;
                }
                if ("VERIFY_SUCC".equals(dto.getOrderStatus())) {
                    result.setStatus(1);
                } else if ("VERIFY_FAIL".equals(dto.getOrderStatus())) {
                    result.setStatus(2);
                }
                userRegisterResultMapper.updateById(result);
            }
        }
        return null;
    }

    @Override
    public BaseVo loanNotice(FenZhuanLoanNoticeDto noticeDto) {
        BaseVo baseVo = new BaseVo();
        baseVo.setResponseCode("0000");
        baseVo.setResponseMsg("成功");
        String orderNo = noticeDto.getLoanRequestNo();
        String loanOrderNo = noticeDto.getLoanOrderNo();
        DisburseData disburseData = disburseDataMapper.selectByLoanNo(orderNo);
        if (noticeDto.getLoanStatus() == null) {
            baseVo.setResponseCode("9999");
            baseVo.setResponseMsg("状态不正确");
            return baseVo;
        }
        DisburseData upEntity = new DisburseData();
        upEntity.setId(disburseData.getId());
        String loanResult = noticeDto.getLoanStatus();
        // 放款失败
        if ("LOAN_FAILED".equals(loanResult)) {
            upEntity.setCreditStatus(400);
            disburseDataMapper.updateById(upEntity);
            UserCreditData userCreditData = userCreditDataMapper.queryByUserId(disburseData.getUserId());
            UserCreditData upCreditData = new UserCreditData();
            upCreditData.setId(userCreditData.getId());
            BigDecimal subtract = userCreditData.getFreezeAmount().subtract(disburseData.getCreditAmount());
            upCreditData.setFreezeAmount(subtract);
            BigDecimal add = userCreditData.getResidueAmount().add(disburseData.getCreditAmount());
            upCreditData.setResidueAmount(add);
            userCreditDataMapper.updateById(upCreditData);
        } else if ("LOAN_SUCCESS".equals(loanResult)) {
            upEntity.setCreditStatus(500);
            disburseData.setFundCode(loanOrderNo);
            disburseData.setLoanTime(DateUtil.parse(noticeDto.getUpdateTime()));
            upEntity.setFundCode(loanOrderNo);
            upEntity.setLoanTime(DateUtil.parse(noticeDto.getUpdateTime()));
            disburseDataMapper.updateById(upEntity);

            UserCreditData userCreditData = userCreditDataMapper.queryByUserId(disburseData.getUserId());
            UserCreditData upCreditData = new UserCreditData();
            upCreditData.setId(userCreditData.getId());
            BigDecimal subtract = userCreditData.getFreezeAmount().subtract(disburseData.getCreditAmount());
            upCreditData.setFreezeAmount(subtract);
            BigDecimal add = userCreditData.getWithdrawAmount().add(disburseData.getCreditAmount());
            upCreditData.setWithdrawAmount(add);
            userCreditDataMapper.updateById(upCreditData);

            Map<String, String> contextMap = MDC.getCopyOfContextMap();
            String traceId = contextMap.getOrDefault("traceId", DateUtil.now());

            LoanSuccessDto loanSuccessDto = new LoanSuccessDto();
            loanSuccessDto.setLoanNo(orderNo);
            loanSuccessDto.setTraceId(traceId);
            rabbitTemplate.convertAndSend(QueueConstant.LOAN_SUCCESS_QUEUE, JSONObject.toJSONString(loanSuccessDto));

        } else {
            baseVo.setResponseCode("9999");
            baseVo.setResponseMsg("状态不正确2");
        }
        return baseVo;
    }

    @Override
    public BaseVo repayNotice(FenZhuanRepayNotice repayNoticeDto) {
        Map<String, String> contextMap = MDC.getCopyOfContextMap();
        String traceId = contextMap.getOrDefault("traceId", DateUtil.now());

        BaseVo baseVo = new BaseVo();
        // 还款流水号
        String repayApplyNo = repayNoticeDto.getThirdRepayNo();

        RepayScheduleApply repayScheduleApply = repayScheduleApplyMapper.selectByRepayApplyNo(repayApplyNo);

        RepaySchedule repaySchedule = repayScheduleMapper.selectByRepayApplyNo(repayApplyNo);
        if (repaySchedule == null) {
            log.info("【资方】 还款结果通知 账单不存在，还款流水号：{}", repayApplyNo);
            baseVo.setResponseCode("9999");
            baseVo.setResponseMsg("还款流水号不存在");
            return baseVo;
        }
        RepaySchedule upRepayData = new RepaySchedule();
        upRepayData.setId(repaySchedule.getId());
        String status = repayNoticeDto.getRepayStatus();
        // 还款失败
        if (StrUtil.equals(status, "FAILED")) {
            if (repayScheduleApply != null) {
                repayScheduleApply.setRepayStatus(2);
                repayScheduleApply.setReason("暂无");
                repayScheduleApply.setResponseTime(DateUtil.date());
            }
            disburseRecordService.saveRecord("账单(repaySchedule)id:" + repaySchedule.getId(), traceId, "还款结果通知");
            upRepayData.setSettleFlag(SettleFlagConstant.RUNNING);
            log.info("【资方】 还款结果通知 还款失败，用户id：{}，还款流水号：{},失败原因：{}", repaySchedule.getUserId(), repayApplyNo, "未知");
        } else if (StrUtil.equals(status, "PAIED")) { // 还款成功
            // 还款成功
            if (repayScheduleApply != null) {
                repayScheduleApply.setRepayStatus(1);
                repayScheduleApply.setResponseTime(DateUtil.date());
            }

            upRepayData.setSettleFlag(SettleFlagConstant.CLOSE);
            upRepayData.setDatePay(DateUtil.today());
            upRepayData.setDatePayTime(repayNoticeDto.getRepayTime());
        } else if (StrUtil.equals(status, "PAYING")) {
            baseVo.setResponseCode("0000");
            baseVo.setResponseMsg("成功");
            return baseVo;
        }
        repayScheduleMapper.updateById(upRepayData);

        if (repayScheduleApply != null) {
            repayScheduleApplyMapper.updateById(repayScheduleApply);
        }
        int repayTerm = Integer.parseInt(repaySchedule.getRepayTerm());
        if (StrUtil.equals(status, "PAIED")) {
            // 发送扣赊销部分消息

            if (repayTerm <= 3) {
                SaleDto saleDto = new SaleDto();
                saleDto.setDisburseId(repaySchedule.getDisburseId());
                saleDto.setTerm(repaySchedule.getRepayTerm());
                saleDto.setTraceId(traceId);
                if (repayScheduleApply != null) {
                    saleDto.setRepayType(repayScheduleApply.getRepayType());
                }
                rabbitTemplate.convertAndSend(QueueConstant.SALE_QUEUE, JSONObject.toJSONString(saleDto));
            } else {
                if (checkSendSms(repayNoticeDto.getRepayTime(), repaySchedule.getRepayOwneDate())) {
                    // 发送短信
                    CompletableFuture.runAsync(() -> {
                        MDC.setContextMap(contextMap);
                        try {
                            smsLoanService.sendRepaySms(repaySchedule.getDisburseId(), repaySchedule.getRepayTerm());
                        } catch (Exception e) {

                        }

                    }, ThreadConfig.threadPoolExecutor());
                }
            }
        } else if (StrUtil.equals(status, "FAILED")) {
            // 发送扣款失败短信 主动提交还款 自动扣款
            if (sendMobileFlag(repaySchedule.getUserId())) {
                CompletableFuture.runAsync(() -> {
                    MDC.setContextMap(contextMap);
                    try {
                        smsLoanService.sendRepayFailSms(repaySchedule.getDisburseId(), repaySchedule.getId(),
                                repaySchedule.getUserId(), 1);
                    } catch (Exception e) {

                    }

                }, ThreadConfig.threadPoolExecutor());
            }
        }

        baseVo.setResponseCode("0000");
        baseVo.setResponseMsg("成功");
        return baseVo;
    }

    /**
     * 判断是否需要发送短信
     * 
     * @param repayTime   实际还款时间
     * @param needPayTime 自动扣款最后时间
     * @return true-发送短信 false-不发送短信
     */
    private boolean checkSendSms(String repayTime, String needPayTime) {
        needPayTime = needPayTime + " 23:59:59";
        DateTime repayDate = DateUtil.parse(repayTime);
        DateTime needDate = DateUtil.parse(needPayTime);
        long time = repayDate.getTime(); // 实际还款的时间戳
        long needTime = needDate.getTime(); // 自动扣款结束的时间戳
        // 自动扣款结束 小于 实际还款时间，说明是逾期还款，则不发短信
        if (needTime < time) {
            return false;
        }
        // 自动扣款结束 大于 实际还款时间，说明是自动扣款或者主动还款，则发短信
        return true;
    }

    public boolean sendMobileFlag(Long userId) {

        return true;
    }

}
