package com.rongchen.byh.app.vo.app;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName DisburseStatusVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/15 16:26
 * @Version 1.0
 **/
@Data
public class DisburseStatusVo {

    @Schema(description = "审核状态 1  审核中 , 2 审核完成  , 3 审核失败")
    private Integer creditStatus;

    @Schema(description = "授信额度")
    private String creditAmount;
}
