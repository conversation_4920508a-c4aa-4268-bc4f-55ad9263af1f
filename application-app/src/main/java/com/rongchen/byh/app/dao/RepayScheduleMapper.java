package com.rongchen.byh.app.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rongchen.byh.app.entity.DisburseData;
import com.rongchen.byh.app.entity.RepayPayVo;
import com.rongchen.byh.app.entity.RepaySchedule;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 项目名称：byh_java
 * 文件名称: RepayScheduleMapper
 * 创建时间: 2025-04-05 16:30
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.app.dao
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
public interface RepayScheduleMapper extends BaseMapper<RepaySchedule> {

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(RepaySchedule record);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(RepaySchedule record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(RepaySchedule record);

    int updateBatch(@Param("list") List<RepaySchedule> list);

    int updateBatchSelective(@Param("list") List<RepaySchedule> list);

    int batchInsert(@Param("list") List<RepaySchedule> list);

    int batchInsertOrUpdate(@Param("list") List<RepaySchedule> list);


    List<RepaySchedule> selectListByDisburseId(Long disburseId);

    RepaySchedule selectLastOne(@Param("disburseId") Long disburseId, @Param("today") String today);

    List<RepaySchedule> selectListRepay(@Param("today") String today);

    List<RepaySchedule> selectListOverdue(@Param("today") String today);

    RepaySchedule selectByRepayApplyNo(String repayApplyNo);

    List<DisburseData> selectListOverdueUp(String today);

    RepaySchedule selectByDisburseIdAndTerm(@Param("disburseId") Long disburseId, @Param("term") String term);

    @Select("SELECT count(*) FROM repay_schedule WHERE disburse_id = #{disburseId} AND  settle_flag = 'REPAYING'")
    int payingCount(Long disburseId);

    List<RepaySchedule> selectListRepayEq(@Param("today") String dateStr);

    RepaySchedule seletLast(RepaySchedule repaySchedule);

    List<RepayPayVo> repaying();

    @Select("select DISTINCT disburse_id FROM repay_schedule  WHERE auto_repay = #{autoRepay}")
    List<Long> selectDisburseIdAll(Integer autoRepay);
}