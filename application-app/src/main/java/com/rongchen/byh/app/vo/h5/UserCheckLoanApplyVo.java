package com.rongchen.byh.app.vo.h5;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 用户初筛相关业务
 * @date 2024/12/11 10:28:13
 */
@Data
public class UserCheckLoanApplyVo {
    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "审核状态 0 审核中 1 初审审核通过 2 初审审核拒绝 3 还未提交申请 , 4 转人工 5 复审通过 6 复审审核拒绝")
    private Integer auditStatus;

    @Schema(description = "授信额度")
    private BigDecimal creditAmount;

    @Schema(description = "绑卡状态 0 未绑卡 1 已绑卡")
    private Integer bindCardStatus;
}
