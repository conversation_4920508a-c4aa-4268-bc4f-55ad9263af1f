package com.rongchen.byh.app.dao;

import com.rongchen.byh.app.entity.SysConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
* <AUTHOR>
* @description 针对表【sys_config(系统配置表)】的数据库操作Mapper
* @createDate 2025-02-10 15:40:51
* @Entity com.rongchen.byh.app.entity.SysConfig
*/
@Mapper
public interface SysConfigMapper extends BaseMapper<SysConfig> {

    @Select("select config_value from sys_config where config_key = #{configKey}")
    String  selectByConfigKey(String configKey);
}




