package com.rongchen.byh.app.service;

import com.alibaba.fastjson.JSONObject;
import com.ancun.netsign.model.ApiRespBody;
import com.rongchen.byh.app.dto.app.*;
import com.rongchen.byh.app.entity.UserDetail;
import com.rongchen.byh.app.v2.dto.UserDetailDto;
import com.rongchen.byh.app.vo.app.SubmitUserFormVo;
import com.rongchen.byh.app.vo.app.UserFormVo;
import com.rongchen.byh.common.api.idCardVerify.vo.OcrNewVo;
import com.rongchen.byh.common.api.zifang.vo.contraact.ContractList;
import com.rongchen.byh.common.core.object.ResponseResult;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @ClassName AppUserDetailService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/11 11:50
 * @Version 1.0
 **/
public interface AppUserDetailService {
    ResponseResult<Void> idCardVerify(IdCardVerifyDto verifyDto, HttpServletRequest request);

    ResponseResult<JSONObject> faceLoveVerify(Long userId);

    ResponseResult<JSONObject> faceLoveVerify2(Long userId,HttpServletRequest request);

    ResponseResult<Void> faceLoveVerifyQuery(String serialNo);

    ResponseResult<Void> faceVerify(FaceVerifyDto verifyDto, HttpServletRequest request);

    ResponseResult<SubmitUserFormVo> submitUserForm(UserFormDto dto);

    ResponseResult<UserFormVo> queryUserForm();

    ResponseResult<Void> aiQianSign(AiSignDto dto);

    ResponseResult<List<ContractList>> queryContract(ContractDto contractDto);

    ResponseResult<List<ContractList>> queryContractBatch(ContractDto contractDto);

    ResponseResult<OcrNewVo> ocrQueryResult(OcrQueryResultDto ocrQueryResultDto);

    ResponseResult<Void> aiQianSignCredit();

    UserDetailDto getInfoForm();

}
