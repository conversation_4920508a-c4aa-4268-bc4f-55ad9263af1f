package com.rongchen.byh.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CreateTokenDto {

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "账号")
    private String account;

    @Schema(description = "设备类型")
    private String deviceType;

    @Schema(description = "token信息")
    private String token;

    @Schema(description = "productId")
    private Integer productId;
}
