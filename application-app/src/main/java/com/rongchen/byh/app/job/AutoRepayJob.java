package com.rongchen.byh.app.job;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.app.dao.DisburseDataMapper;
import com.rongchen.byh.app.dao.RepayScheduleMapper;
import com.rongchen.byh.app.entity.RepaySchedule;
import com.rongchen.byh.common.core.util.MDCUtil;
import com.rongchen.byh.common.rabbitmq.constants.QueueConstant;
import com.rongchen.byh.common.rabbitmq.dto.RepayDto;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;


/**
 * 每日查询需要自动还款的账单
 */
@Component
@Slf4j
public class AutoRepayJob {

    @Resource
    DisburseDataMapper disburseDataMapper;
    @Resource
    RepayScheduleMapper repayScheduleMapper;
    @Resource
    RabbitTemplate rabbitTemplate;

    @XxlJob("autoRepayHandler")
    public void autoRepayHandler() {

        String traceId = UUID.fastUUID().toString();
        MDCUtil.setTraceId(traceId);

        // 1.查询当天需要还款的账单
        String today = DateUtil.today();
        List<RepaySchedule> repayScheduleList = repayScheduleMapper.selectListRepay(today);
        if (CollectionUtil.isEmpty(repayScheduleList)) {
            log.info("当天需要还款的账单为空");
            return;
        }
        // 2.遍历账单，查询对应的放款信息，然后调用还款接口
        repayScheduleList.forEach(repaySchedule -> {
            String traceId1 = MDCUtil.generateTraceId();
            log.info("当天需要还款的账单 id:{} trace:{}",repaySchedule.getId(),traceId1);
            RepayDto repayDto = new RepayDto();
            repayDto.setDisburseId(repaySchedule.getDisburseId());
            repayDto.setRepayId(repaySchedule.getId());
            repayDto.setTraceId(traceId1);
            repayDto.setRepayType(2);
            rabbitTemplate.convertAndSend(QueueConstant.REPAY_QUEUE, JSONObject.toJSONString(repayDto));
        });

    }
}
