package com.rongchen.byh.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rongchen.byh.app.api.service.strategy.impl.MaYiOutApiProcess;
import com.rongchen.byh.app.constant.SourceMode;
import com.rongchen.byh.app.constant.UserAuditStatus;
import com.rongchen.byh.app.dao.ApiCreditRecordMapper;
import com.rongchen.byh.app.dao.SysConfigMapper;
import com.rongchen.byh.app.dao.UserBankCardMapper;
import com.rongchen.byh.app.dao.UserCreditDataMapper;
import com.rongchen.byh.app.dao.UserDataMapper;
import com.rongchen.byh.app.dao.UserDetailMapper;
import com.rongchen.byh.app.dao.UserLoanApplyMapper;
import com.rongchen.byh.app.dto.api.BindBankCardDataDto;
import com.rongchen.byh.app.dto.api.BindBankCardDto;
import com.rongchen.byh.app.dto.api.BindBankCardInfoDto;
import com.rongchen.byh.app.dto.api.RiskCallBackDto;
import com.rongchen.byh.app.entity.ApiCreditRecord;
import com.rongchen.byh.app.entity.UserBankCard;
import com.rongchen.byh.app.entity.UserCreditData;
import com.rongchen.byh.app.entity.UserData;
import com.rongchen.byh.app.entity.UserDetail;
import com.rongchen.byh.app.entity.UserLoanApply;
import com.rongchen.byh.app.service.OutApiService;
import com.rongchen.byh.app.utils.BankCodeUtil;
import com.rongchen.byh.common.api.beiyihua.dto.CreditPushDto;
import com.rongchen.byh.common.api.beiyihua.service.BeiYiHuaService;
import com.rongchen.byh.common.api.notice.dto.SendAirH5Dto;
import com.rongchen.byh.common.api.notice.service.NoticeSmsService;
import com.rongchen.byh.common.api.notice.vo.NoticeSmsVo;
import com.rongchen.byh.common.api.riskControl.config.RiskControlProperties;
import com.rongchen.byh.common.api.riskControl.dto.CrmPushDto;
import com.rongchen.byh.common.api.riskControl.service.CrmPushService;
import com.rongchen.byh.common.api.riskControl.service.impl.RiskControlServiceImpl;
import com.rongchen.byh.common.core.exception.MyRuntimeException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 外部api服务
 * @date 2024/12/11 19:45:03
 */
@Service
@Slf4j
public class OutApiServiceImpl implements OutApiService {
    @Resource
    private UserDetailMapper userDetailMapper;

    @Resource
    private UserDataMapper userDataMapper;

    @Resource
    private UserBankCardMapper userBankCardMapper;

    @Resource(name = "sendAirH5NoticeService")
    NoticeSmsService noticeSmsService;
    @Resource
    UserCreditDataMapper userCreditDataMapper;
    @Resource
    UserLoanApplyMapper userLoanApplyMapper;
    @Resource
    RiskControlProperties riskControlProperties;
    @Resource
    ApiCreditRecordMapper apiCreditRecordMapper;
    @Resource
    MaYiOutApiProcess mayiOutApiProcess;
    @Resource
    SysConfigMapper sysConfigMapper;
    @Resource
    CrmPushService crmPushService;
    @Resource
    BeiYiHuaService beiYiHuaService;
    @Resource
    private RedissonClient redissonClient;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String bindBankCard(@RequestBody BindBankCardDto bindBankCardDto) {
        log.info("crm回调参数：{}", bindBankCardDto);
        BindBankCardDataDto data = bindBankCardDto.getData();
//        String dataSign = createDataSign(bindBankCardDto.getTimestamp(), JSONObject.toJSONString(data));
//        if (!dataSign.equals(bindBankCardDto.getDataSign())) {
//            return "验签失败";
//        }

        List<BindBankCardInfoDto> bankList = data.getBankList();
        if (CollUtil.isEmpty(bankList)) {
            return "银行卡列表为空";
        }

        BindBankCardInfoDto bindBankCardInfoDto = bankList.get(0);
        String bankName = bindBankCardInfoDto.getBankName();
        String getByCode = BankCodeUtil.getBankName(bankName);
        if (getByCode != null) {
            bankName = getByCode;
        }
        // 使用用户身份证作为锁的key
        String lockKey = "user_bank_lock:" + data.getIDCard();
        RLock lock = redissonClient.getLock(lockKey);
        try {
            // 尝试获取锁，等待时间5秒，锁过期时间30秒
            boolean locked = lock.tryLock(5, 30, TimeUnit.SECONDS);
            if (!locked) {
                log.error("获取分布式锁失败，用户IDCard: {}", data.getIDCard());
                return "error";
            }
            UserDetail userDetail = userDetailMapper.selectOne(new LambdaQueryWrapper<UserDetail>().eq(UserDetail::getWebIdCard, data.getIDCard()));
            if (ObjectUtil.isEmpty(userDetail)) {
                return "用户不存在";
            }
            UserBankCard userBankCard = new UserBankCard();
            UserBankCard bankCard = userBankCardMapper.selectOne(new LambdaQueryWrapper<UserBankCard>().eq(UserBankCard::getUserId, userDetail.getUserId()));
            // 更新银行卡信息
            if (ObjectUtil.isNotEmpty(bankCard)) {
                userBankCard.setId(bankCard.getId());
                userBankCard.setUserId(userDetail.getUserId());
                userBankCard.setIdCard(data.getIDCard());
                userBankCard.setBankName(bankName);
                userBankCard.setBankAccount(bindBankCardInfoDto.getBankAccount());
                userBankCard.setMobile(bindBankCardInfoDto.getMobile());
                userBankCard.setContractNum(data.getContractNum());
                userBankCard.setCustomerName(data.getCustomerName());
                //添加两个字段
                userBankCard.setConsultingMode(data.getConsultingMode());
                userBankCard.setConsultingRate(data.getConsultingRate());
                userBankCardMapper.updateById(userBankCard);
                UserData userData = new UserData();
                userData.setId(userDetail.getUserId());
                userData.setAuditFlag(1);
                //更新审核状态
                userDataMapper.updateById(userData);
                //发送通知短信
                sms(userDetail);
                if (("1").equals(data.getConsultingMode())) {
                    log.info("返回固定金额  返回错误");
                    return "error";
                }
                return "success";
            }
            userBankCard.setUserId(userDetail.getUserId());
            userBankCard.setIdCard(data.getIDCard());
            userBankCard.setBankName(bankName);
            userBankCard.setBankAccount(bindBankCardInfoDto.getBankAccount());
            userBankCard.setMobile(bindBankCardInfoDto.getMobile());
            userBankCard.setContractNum(data.getContractNum());
            userBankCard.setCustomerName(data.getCustomerName());
            userBankCard.setConsultingMode(data.getConsultingMode());
            userBankCard.setConsultingRate(data.getConsultingRate());
            userBankCardMapper.insert(userBankCard);

            UserData userData = new UserData();
            userData.setId(userDetail.getUserId());
            userData.setAuditFlag(1);
            //更新审核状态
            userDataMapper.updateById(userData);
            //发送通知短信
            sms(userDetail);
            if (("1").equals(data.getConsultingMode())) {
                log.info("返回固定金额  返回错误");
                return "error";
            }
            return "success";
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("获取分布式锁时被中断", e);
            return "error";
        } finally {
            // 释放锁
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public static String createDataSign(String timestamp,String jsonData) {
        //签名生成规则：
        //（1）、将请求的数据转换成json字符串 $Data = json_encode(rquestArgs)
        //（2）、结合TimeStamp、Data生成签名： $Sign =
        return DigestUtil.md5Hex(jsonData + timestamp + jsonData).toUpperCase(Locale.ROOT);
    }

    public void sms(UserDetail userDetail){
        try {
            UserCreditData userCreditData = userCreditDataMapper.queryByUserId(userDetail.getUserId());
            UserData userData = userDataMapper.selectById(userDetail.getUserId());
            SendAirH5Dto sendAirH5Dto = new SendAirH5Dto();
            sendAirH5Dto.setUserName(userDetail.getAppName());
            sendAirH5Dto.setLoanAmount(userCreditData.getCreditAmount().toString());
            sendAirH5Dto.setPhone(userData.getMobile());

            NoticeSmsVo noticeSmsVo = noticeSmsService.sendSms(sendAirH5Dto);
            log.info("短信发送结果：{}", noticeSmsVo);
        } catch (Exception e) {

        }
    }

    @Override
    public JSONObject riskCallBack(RiskCallBackDto riskCallBackDto) {
        JSONObject result = new JSONObject();
        result.put("message", "成功");
        result.put("status", "1000");
        if (!"1000".equals(riskCallBackDto.getStatus())) {
            result.put("message", riskCallBackDto.getMessage());
            result.put("Status", "1000");
        }
        String s = RiskControlServiceImpl.decryptByAes(riskCallBackDto.getContent(), riskControlProperties.getApiCreditAesKey());
        log.info("风控回调解密结果：{}", s);
        JSONObject data = JSONObject.parseObject(s);
        RLock lock = redissonClient.getLock("api_credit_callBack:" + data.getString("credit_id"));
        boolean isLocked = false;
        try {
            isLocked = lock.tryLock(30, TimeUnit.SECONDS);
            if (!isLocked) {
                log.error("mayi_api_credit_queue获取锁失败，请稍后再试");
                return result;
            }
            UserLoanApply apply = userLoanApplyMapper.selectByCreditId(data.getString("credit_id"));
            if (ObjectUtil.isEmpty(apply)) {
                result.put("message", "未查询到申请记录");
                result.put("status", "1000");
                return result;
            }
            if (apply.getAuditsStatus() != 0) {
                result.put("message", "申请已审核");
                result.put("status", "1000");
                return result;
            }
            String creditNo = apply.getApiCreditNo();
            String creditStatus = data.getString("credit_status");
            Long userId = apply.getUserId();
            UserData userDataUp = new UserData();
            userDataUp.setId(userId);
            if ("103".equals(creditStatus)) {
                log.error("授信id：{} 风控卡单中，提前结束", creditStatus);
                return result;
            }
            if ("102".equals(creditStatus)) {
                apply.setAuditsStatus(2);
                userLoanApplyMapper.updateById(apply);
                // 更新用户审核状态
                userDataUp.setAuditStatus(UserAuditStatus.FIRST_REJECT);
                userDataMapper.updateById(userDataUp);
                notifyMaYi(creditNo, 3, "风控拒绝", null);
                log.error("授信id：{} 风控审核失败", creditStatus);
                return result;
            }
            UserDetail userDetail = userDetailMapper.queryByUserId(userId);
            UserData userData = userDataMapper.selectById(userId);
            String pushProduct = sysConfigMapper.selectByConfigKey("pushProduct");
            String phoneNo = userData.getMobile();
            String userName = userDetail.getAppName();
            String idNo = userDetail.getIdNumber();
            // 根据客户风险等级判断逻辑：
            //若等级为A1~Ax， 则产品1审核通过；产品1额度同步到CRM。
            //若等级为B1~Bx,则产品1审核拒绝。但风控给的额度仍然要传给CRM
            // 产品一拒绝，推送crm
            // 确认风险等级
            String risk = "B";
            BigDecimal amount = BigDecimal.ZERO;
            JSONObject amountDetail = data.getJSONObject("amount_detail");
            if (ObjectUtil.isEmpty(amountDetail)) {
                throw new MyRuntimeException("额度明细为空");
            }
            List<JSONObject> list = JSONObject.parseObject(amountDetail.getString("amount_units"), new TypeReference<List<JSONObject>>() {
            });
            for (JSONObject item : list) {
                if ("credit_rating".equals(item.get("name"))) {
                    risk = item.getString("amount");
                }
                if ("limit_amount".equals(item.get("name"))) {
                    amount = item.getBigDecimal("amount").divide(BigDecimal.valueOf(100));
                }
            }
            if (amount.compareTo(BigDecimal.ZERO) < 0) {
                amount = BigDecimal.ZERO;
            }
            if (risk.contains("B")) {
                apply.setAuditsStatus(2);
                userLoanApplyMapper.updateById(apply);
                // 更新用户审核状态
                userDataUp.setAuditStatus(UserAuditStatus.FIRST_REJECT);
                userDataMapper.updateById(userDataUp);
                // 通知上游结果
                notifyMaYi(creditNo, 3, "风控拒绝", null);
                log.error("手机号：{}，授信id：{} 风控审核失败", phoneNo, creditNo);
                // 1 代表推送产品二
                if ("1".equals(pushProduct)) {
                    // 同步产品二
                    pushProduct(userData, userDetail, amount);
                }
                // 同步crm
                pushCrm(userData, userName, idNo, amount, risk);
                // 更新用户风险等级
                userDetail.setRiskLevel(risk);
                userDetailMapper.updateById(userDetail);
                return result;
            }

            // 产品一通过，并同步crm
            if ("1".equals(pushProduct)) {
                // 同步产品二
                pushProduct(userData, userDetail, amount);
            }
            // 同步crm
            pushCrm(userData, userName, idNo, amount, risk);

            // 更新用户风险等级
            userDetail.setRiskLevel(risk);
            userDetailMapper.updateById(userDetail);
            // 审核通过
            apply.setAuditsStatus(1);
            userLoanApplyMapper.updateById(apply);
            // 更新用户审核状态
            userDataUp.setAuditStatus(UserAuditStatus.WAIT_APP_CREDIT);
            userDataUp.setAuditFlag(1);
            userDataUp.setSourceMode(SourceMode.API_MAYI);
            userDataMapper.updateById(userDataUp);
            // 保存授信额度
            UserCreditData userCreditData = new UserCreditData();
            userCreditData.setUserId(userId);
            userCreditData.setCreditAmount(amount);
            // 风控通过，剩余额度和授信额度保持一致
            userCreditData.setSourceMode(SourceMode.API_MAYI);
            userCreditData.setResidueAmount(userCreditData.getCreditAmount());
            userCreditDataMapper.insert(userCreditData);
            // 通知上游结果
            notifyMaYi(creditNo, 2, "成功", amount);
            return result;
        } catch (Exception e) {
            log.error("风控回调异常", e);
        } finally {
            if (isLocked) {
                lock.unlock();
            }
        }
        result.put("message", "系统异常");
        result.put("status", "1000");
        return result;
    }

    private void pushCrm(UserData userData, String userName, String idNo, BigDecimal amount, String creditRating) {
        try {
            CrmPushDto crmPushDto = new CrmPushDto();
            crmPushDto.setMobile(userData.getMobile());
            crmPushDto.setUserName(userName);
            crmPushDto.setIdNumber(idNo);
            crmPushDto.setCreditRating(creditRating);
            crmPushDto.setLimitAmount(amount);
            crmPushService.push(crmPushDto);
        } catch (Exception e) {
            log.error("crm同步异常", e);
        }
    }

    private void pushProduct(UserData userData, UserDetail userDetail, BigDecimal amount) {
        try {
            CreditPushDto creditPushDto = new CreditPushDto();
            creditPushDto.setUsername(userDetail.getAppName());
            creditPushDto.setIdCard(userDetail.getWebIdCard());
            creditPushDto.setMobile(userData.getMobile());
            creditPushDto.setCreditAmount(amount);
            beiYiHuaService.push(creditPushDto);
        } catch (Exception e) {
            log.error("产品二同步异常", e);
        }
    }

    private void notifyMaYi(String creditNo, int status, String msg, BigDecimal creditMoney) {
        if (StrUtil.isEmpty(creditNo)) {
            log.error("creditNo 为空 提前结束");
            return;
        }
        ApiCreditRecord apiCreditRecord = apiCreditRecordMapper.selectByCreditNo(creditNo);
        if (ObjectUtil.isEmpty(apiCreditRecord)) {
            log.error("apiCreditRecord 为空 提前结束");
            return;
        }
        ApiCreditRecord updateRecord = new ApiCreditRecord();
        updateRecord.setId(apiCreditRecord.getId());
        updateRecord.setCreditStatus(status);
        updateRecord.setFailReason(msg);
        updateRecord.setCreditMoney(creditMoney);
        apiCreditRecordMapper.updateById(updateRecord);
        try {
            mayiOutApiProcess.creditResultNotify(creditNo);
        } catch (Exception e) {
            log.error("授信id：{} 通知mayi失败", creditNo, e);
        }
    }
}
