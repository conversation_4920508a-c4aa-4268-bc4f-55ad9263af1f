package com.rongchen.byh.app.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.rongchen.byh.app.config.ZifangFactory;
import com.rongchen.byh.app.dao.CapitalDataMapper;
import com.rongchen.byh.app.dao.DisburseDataMapper;
import com.rongchen.byh.app.dao.SaleScheduleMapper;
import com.rongchen.byh.app.dao.UserBankCardMapper;
import com.rongchen.byh.app.dao.UserDataMapper;
import com.rongchen.byh.app.dto.app.BackVo;
import com.rongchen.byh.app.dto.app.BillRepayApplyDto;
import com.rongchen.byh.app.entity.BaofuPayDetail;
import com.rongchen.byh.app.entity.CapitalData;
import com.rongchen.byh.app.entity.DisburseData;
import com.rongchen.byh.app.entity.SaleOrderRecord;
import com.rongchen.byh.app.entity.SaleSchedule;
import com.rongchen.byh.app.entity.UserBankCard;
import com.rongchen.byh.app.entity.UserData;
import com.rongchen.byh.app.service.BaofuPayDetailService;
import com.rongchen.byh.app.service.SaleOrderRecordService;
import com.rongchen.byh.app.service.SaleRepayService;
import com.rongchen.byh.app.utils.CreditLoanNoUtils;
import com.rongchen.byh.app.utils.UserTokenUtil;
import com.rongchen.byh.common.api.baofu.adapter.BaofuAdapterService;
import com.rongchen.byh.common.api.baofu.constant.BaofuConstant;
import com.rongchen.byh.common.api.baofu.util.AmountUtil;
import com.rongchen.byh.common.api.baofu.vo.req.DirectPayReqVo;
import com.rongchen.byh.common.api.baofu.vo.req.RiskItemReqVo;
import com.rongchen.byh.common.api.baofu.vo.rsp.DirectPayRspVo;
import com.rongchen.byh.common.api.yunxi.config.YunXiPayWay;
import com.rongchen.byh.common.api.zifang.dto.SaleApplyDto;
import com.rongchen.byh.common.api.zifang.dto.SaleRepayApplyDetailDto;
import com.rongchen.byh.common.api.zifang.dto.SaleRepayApplyDto;
import com.rongchen.byh.common.api.zifang.dto.SaleRepayApplyRepayDto;
import com.rongchen.byh.common.api.zifang.service.OtherApi;
import com.rongchen.byh.common.api.zifang.vo.SaleApplyVo;
import com.rongchen.byh.common.api.zifang.vo.SaleRepayApplyVo;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.constant.SettleFlagConstant;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.redis.util.RedissonUtils;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/12/15 16:22:41
 */
@Slf4j
@Service
public class SaleRepayServiceImpl implements SaleRepayService {

    @Resource
    private OtherApi otherApi;
    @Resource
    private DisburseDataMapper disburseDataMapper;
    @Resource
    private UserBankCardMapper userBankCardMapper;
    @Resource
    private SaleScheduleMapper saleScheduleMapper;
    @Resource
    private ZifangFactory zifangFactory;
    @Resource
    private CapitalDataMapper capitalDataMapper;

    @Resource
    private BaofuAdapterService baofuAdapterService;

    @Resource
    private BaofuPayDetailService baofuPayDetailService;
    @Resource
    private SaleOrderRecordService saleOrderRecordService;
    @Resource
    private UserDataMapper userDataMapper;

    @Resource
    private RedissonUtils redissonUtils;

    @Override
    public ResponseResult<Void> saleRepay(BillRepayApplyDto billRepayApplyDto) {
        SaleSchedule saleSchedule = saleScheduleMapper.selectById(billRepayApplyDto.getRepayScheduleId());
        Long userId = UserTokenUtil.getUserId();
        BackVo backVo = userBankCardMapper.queryBackByUserId(userId);
        DisburseData disburseData = disburseDataMapper.selectById(saleSchedule.getDisburseId());
        // 本息划扣
        SaleRepayApplyDto saleRepayApplyDto = new SaleRepayApplyDto();
        saleRepayApplyDto.setRepayApplyNo(IdUtil.fastSimpleUUID());
        billRepayApplyDto.setSaleApplyNo(billRepayApplyDto.getSaleApplyNo());
        saleRepayApplyDto.setUserId(String.valueOf(userId));
        saleRepayApplyDto.setRepayMethod("0");
        String termStatus = saleSchedule.getTermStatus();
        String repayType = "CURRENT";
        if ("N".equals(termStatus)) {
            repayType = "OVERDUE";
        }
        saleRepayApplyDto.setRepayType(repayType);
        saleRepayApplyDto.setBankPhoneNo(backVo.getMobile());
        saleRepayApplyDto.setAmount(saleSchedule.getTotalAmt().toString());
        saleRepayApplyDto.setBankCardNo(backVo.getBankAccount());
        saleRepayApplyDto.setAccountCardType("1");
        saleRepayApplyDto.setIdNo(backVo.getIdCard());
        saleRepayApplyDto.setCustomerName(backVo.getName());
        // todo 等资方确认银行卡支行名
        saleRepayApplyDto.setBranchName("支行名称");
        saleRepayApplyDto.setBankAccountType("0");

        List<SaleRepayApplyRepayDto> repayList = new ArrayList<>();
        SaleRepayApplyRepayDto saleRepayApplyRepayDto = new SaleRepayApplyRepayDto();

        saleRepayApplyRepayDto.setSaleNo(disburseData.getSaleNo());
        saleRepayApplyRepayDto.setRepayAmt(saleSchedule.getTotalAmt().toString());
        saleRepayApplyRepayDto.setRepayTerm(saleSchedule.getRepayTerm());
        saleRepayApplyRepayDto.setSplitType("1");

        saleRepayApplyRepayDto.setPrinAmt(saleSchedule.getTermRetPrin().toString());
        saleRepayApplyRepayDto.setIntAmt(saleSchedule.getTermRetInt().toString());
        saleRepayApplyRepayDto.setForfeitAmt(saleSchedule.getTermRetFint().toString());

        repayList.add(saleRepayApplyRepayDto);
        List<SaleRepayApplyDetailDto> repayDetailList = new ArrayList<>();

        SaleRepayApplyDetailDto saleRepayApplyDetailDto = new SaleRepayApplyDetailDto();
        saleRepayApplyDetailDto.setRepayterm(saleSchedule.getRepayTerm());
        saleRepayApplyDetailDto.setPrintAmt(saleSchedule.getTermRetPrin().toString());
        saleRepayApplyDetailDto.setIntAmt(saleSchedule.getRepayTerm());
        saleRepayApplyDetailDto.setForfeitAmt(saleSchedule.getTermRetFint().toString());
        repayDetailList.add(saleRepayApplyDetailDto);
        saleRepayApplyRepayDto.setRepayDetailList(repayDetailList);
        saleRepayApplyDto.setRepayList(repayList);

        CapitalData capitalData = capitalDataMapper.selectById(disburseData.getCapitalId());
        if (ObjectUtil.isEmpty(capitalData)) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "资方未匹配");
        }
        OtherApi otherApi = zifangFactory.getApi(capitalData.getBeanName(), OtherApi.class);
        ResponseResult<SaleRepayApplyVo> saleRepayApply = otherApi.getSaleRepayApply(saleRepayApplyDto);
        if (!saleRepayApply.isSuccess()) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, saleRepayApply.getErrorMessage());
        }
        SaleRepayApplyVo data = saleRepayApply.getData();
        if (!"0000".equals(data.getResponseCode())) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, data.getResponseMsg());
        }
        return ResponseResult.success();
    }

    @Override
    public ResponseResult<Void> saleRepayNew(BillRepayApplyDto billRepayApplyDto) {
        // 获取还款计划ID
        Long repaySaleId = billRepayApplyDto.getRepaySaleId();
        if (repaySaleId == null) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "还款计划ID不能为空");
        }

        // 定义分布式锁的key
        String lockKey = "sale_repay_lock:" + repaySaleId;

        // 尝试获取分布式锁
        RLock lock = redissonUtils.getLock(lockKey);
        boolean locked = false;

        try {
            // 尝试获取锁，最多等待30秒，锁超时时间为60秒自动释放
            locked = lock.tryLock(30, 60, TimeUnit.SECONDS);

            if (!locked) {
                log.warn("还款请求频繁，订单ID: {}", repaySaleId);
                return ResponseResult.error(ErrorCodeEnum.FAIL, "操作太频繁，请稍后再试");
            }

            // 获取还款计划
            SaleSchedule saleSchedule = saleScheduleMapper.selectById(repaySaleId);
            if (saleSchedule == null) {
                return ResponseResult.error(ErrorCodeEnum.FAIL, "还款计划不存在");
            }

            // 检查订单状态，防止重复执行
            if (!SettleFlagConstant.RUNNING.equals(saleSchedule.getSettleFlag())) {
                log.warn("还款计划状态不允许操作，当前状态: {}, 订单ID: {}", saleSchedule.getSettleFlag(), repaySaleId);
                return ResponseResult.error(ErrorCodeEnum.FAIL, "该笔订单已在处理中或已完成，请勿重复操作");
            }

            // 生成唯一的还款申请编号
            String sequence = CreditLoanNoUtils.getSequence("sa-rp");
            saleSchedule.setRepayApplyNo(sequence);
            saleScheduleMapper.updateById(saleSchedule);

            Long userId = saleSchedule.getUserId();
            BackVo backVo = userBankCardMapper.queryBackByUserId(userId);
            DisburseData disburseData = disburseDataMapper.selectById(saleSchedule.getDisburseId());
            // 本息划扣
            SaleRepayApplyDto saleRepayApplyDto = new SaleRepayApplyDto();
            saleRepayApplyDto.setRepayApplyNo(sequence);
            saleRepayApplyDto.setUserId(String.valueOf(userId));
            saleRepayApplyDto.setRepayMethod(billRepayApplyDto.getRepayMethod());// 判断还款类型
            String todayDate = DateUtil.format(new Date(), "yyyy-MM-dd");
            int date = todayDate.compareTo(saleSchedule.getRepayOwnbDate());
            if (date < 0) {
                saleRepayApplyDto.setRepayType("PREPAYMENT");
            } else if (date == 0) {
                saleRepayApplyDto.setRepayType("CURRENT");
            } else {
                saleRepayApplyDto.setRepayType("OVERDUE");
            }
            saleRepayApplyDto.setBankPhoneNo(backVo.getMobile());
            saleRepayApplyDto.setAmount(saleSchedule.getTotalAmt().toString());
            saleRepayApplyDto.setBankCardNo(backVo.getBankAccount());
            saleRepayApplyDto.setAccountCardType("1");
            saleRepayApplyDto.setIdNo(backVo.getIdCard());
            saleRepayApplyDto.setCustomerName(backVo.getName());
            saleRepayApplyDto.setBranchName(backVo.getBankName());
            saleRepayApplyDto.setBankAccountType(billRepayApplyDto.getBankAccountType());

            List<SaleRepayApplyRepayDto> repayList = new ArrayList<>();
            SaleRepayApplyRepayDto saleRepayApplyRepayDto = new SaleRepayApplyRepayDto();

            saleRepayApplyRepayDto.setSaleNo(disburseData.getSaleNo());
            saleRepayApplyRepayDto.setRepayAmt(saleSchedule.getTotalAmt().toString());
            saleRepayApplyRepayDto.setRepayTerm(saleSchedule.getRepayTerm());
            saleRepayApplyRepayDto.setSplitType("1");
            saleRepayApplyRepayDto.setPrinAmt(saleSchedule.getTermRetPrin().toString());
            saleRepayApplyRepayDto.setIntAmt(saleSchedule.getTermRetInt().toString());
            saleRepayApplyRepayDto.setForfeitAmt(saleSchedule.getTermRetFint().toString());
            saleRepayApplyRepayDto.setDeratePrin("0");
            saleRepayApplyRepayDto.setDerateInt("0");
            repayList.add(saleRepayApplyRepayDto);
            List<SaleRepayApplyDetailDto> repayDetailList = new ArrayList<>();

            SaleRepayApplyDetailDto saleRepayApplyDetailDto = new SaleRepayApplyDetailDto();
            saleRepayApplyDetailDto.setRepayterm(saleSchedule.getRepayTerm());
            saleRepayApplyDetailDto.setPrintAmt(saleSchedule.getTermRetPrin().toString());
            saleRepayApplyDetailDto.setIntAmt(saleSchedule.getRepayTerm());
            saleRepayApplyDetailDto.setForfeitAmt(saleSchedule.getTermRetFint().toString());
            saleRepayApplyDetailDto.setDeratePrin("0");
            saleRepayApplyDetailDto.setDerateInt("0");
            repayDetailList.add(saleRepayApplyDetailDto);
            saleRepayApplyDto.setRepayList(repayList);
            saleRepayApplyRepayDto.setRepayDetailList(repayDetailList);

            CapitalData capitalData = capitalDataMapper.selectById(disburseData.getCapitalId());
            if (ObjectUtil.isEmpty(capitalData)) {
                return ResponseResult.error(ErrorCodeEnum.FAIL, "资方未匹配");
            }
            OtherApi otherApi = zifangFactory.getApi(capitalData.getBeanName(), OtherApi.class);

            // 先更新状态为还款中，防止重复处理
            SaleSchedule saleScheduleUpdate = new SaleSchedule();
            saleScheduleUpdate.setId(saleSchedule.getId());
            saleScheduleUpdate.setSettleFlag(SettleFlagConstant.REPAYING);
            saleScheduleMapper.updateById(saleScheduleUpdate);

            try {
                // 如果是分转的权益赊销，要使用宝付代扣在下单云樨，目前只支持分三期所以最大还款期数为3
                int repayTerm = Integer.parseInt(saleSchedule.getRepayTerm());

                if (capitalData.getBeanName().startsWith("fenZhuan") && repayTerm <= 3) {
                    log.info("【权益赊销】订单申请接口 - 宝付代扣下单");
                    // 调用宝付代扣下单接口
                    return yunXiaoBaoFuRepayHandler(userId, sequence, saleSchedule, disburseData, backVo, otherApi);
                } else {
                    // 兼容老的逻辑直接还款通知
                    ResponseResult<SaleRepayApplyVo> saleRepayApply = otherApi.getSaleRepayApply(saleRepayApplyDto);
                    if (!saleRepayApply.isSuccess()) {
                        // 还原状态
                        SaleSchedule saleScheduleRevert = new SaleSchedule();
                        saleScheduleRevert.setId(saleSchedule.getId());
                        saleScheduleRevert.setSettleFlag(SettleFlagConstant.RUNNING);
                        saleScheduleMapper.updateById(saleScheduleRevert);
                        return ResponseResult.error(ErrorCodeEnum.FAIL, saleRepayApply.getErrorMessage());
                    }
                    if (!"0000".equals(saleRepayApply.getData().getResponseCode())) {
                        // 还原状态
                        SaleSchedule saleScheduleRevert = new SaleSchedule();
                        saleScheduleRevert.setId(saleSchedule.getId());
                        saleScheduleRevert.setSettleFlag(SettleFlagConstant.RUNNING);
                        saleScheduleMapper.updateById(saleScheduleRevert);
                        return ResponseResult.error(ErrorCodeEnum.FAIL, saleRepayApply.getData().getResponseMsg());
                    }
                }
            } catch (Exception e) {

                log.error("还款申请异常，订单ID: {} ", repaySaleId, e);
                // 还原状态
                SaleSchedule saleScheduleRevert = new SaleSchedule();
                saleScheduleRevert.setId(saleSchedule.getId());
                saleScheduleRevert.setSettleFlag(SettleFlagConstant.RUNNING);
                saleScheduleMapper.updateById(saleScheduleRevert);
                return ResponseResult.error(ErrorCodeEnum.FAIL, "系统异常，请稍后再试");
            }

            // 非分转赊销场景，状态已经在前面设置为REPAYING
            return ResponseResult.success();
        } catch (InterruptedException e) {
            log.error("获取分布式锁异常，订单ID: {}", repaySaleId, e);
            Thread.currentThread().interrupt();
            return ResponseResult.error(ErrorCodeEnum.FAIL, "系统繁忙，请稍后再试");
        } finally {
            // 只有成功获取锁的情况下才需要释放锁
            if (locked && lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.debug("释放分布式锁，订单ID: {}", repaySaleId);
            }
        }
    }

    @Override
    public SaleSchedule selectLast(SaleSchedule saleSchedule) {
        return saleScheduleMapper.selectLast(saleSchedule);
    }

    @Override
    public void updateById(SaleSchedule saleSchedule1) {
        saleScheduleMapper.updateById(saleSchedule1);
    }

    private ResponseResult<Void> yunXiaoBaoFuRepayHandler(Long userId, String sequence, SaleSchedule saleSchedule,
            DisburseData disburseData, BackVo backVo, OtherApi otherApi) {
        ResponseResult<Void> FAIL = baofuPayResult(userId, sequence, saleSchedule);
        if (FAIL != null) {
            // 还原状态
            SaleSchedule saleScheduleRevert = new SaleSchedule();
            saleScheduleRevert.setId(saleSchedule.getId());
            saleScheduleRevert.setSettleFlag(SettleFlagConstant.REPAYING);
            saleScheduleMapper.updateById(saleScheduleRevert);
            return FAIL;
        }
        SaleApplyDto saleApplyDto = new SaleApplyDto();
        saleApplyDto.setLoanNo(disburseData.getLoanNo());
        saleApplyDto.setSaleNo(disburseData.getSaleNo());
        saleApplyDto.setPaymentNo(sequence);
        saleApplyDto.setMobile(backVo.getMobile());
        saleApplyDto.setApplyAmt(saleSchedule.getTotalAmt().toString());
        saleApplyDto.setUserId(String.valueOf(userId));
        // 代扣成功就下一单所以默认一期就行
        saleApplyDto.setApplyTerm("1");
        log.info("【权益赊销】订单申请接口 - 宝付支付单号：{},applyAmt:{},1期,申请赊销", sequence,
                saleApplyDto.getApplyAmt());
        ResponseResult<SaleApplyVo> saleApply = otherApi.getSaleApply(saleApplyDto);
        if (!saleApply.isSuccess()) {
            // return ResponseResult.error(ErrorCodeEnum.FAIL, saleApply.getErrorMessage());
            log.info("【权益赊销】订单申请接口 - 宝付支付单号：{},applyAmt:{},申请赊销失败", sequence,
                    saleApplyDto.getApplyAmt());
            SaleSchedule saleScheduleUp = new SaleSchedule();
            saleScheduleUp.setId(saleSchedule.getId());
            saleScheduleUp.setSettleFlag(SettleFlagConstant.CLOSE);
            saleScheduleMapper.updateById(saleScheduleUp);
            // 构建并保存渠道记录 失败记录后续定时任务重试
            SaleOrderRecord saleOrderRecord = new SaleOrderRecord();
            saleOrderRecord.setSaleChannel(SaleOrderRecordService.SALE_CHANNEL);
            saleOrderRecord.setSaleNo(disburseData.getSaleNo());
            saleOrderRecord.setUserId(String.valueOf(userId));
            saleOrderRecord.setOrderAmount(saleSchedule.getTotalAmt());
            saleOrderRecord.setUserMobile(backVo.getMobile());
            saleOrderRecord.setPaymentNo(sequence);
            saleOrderRecord.setOrderStatus("FAIL"); // 标记为失败状态
            saleOrderRecord.setPayWay(YunXiPayWay.BAOFU_PROXY_PAY); // 信用支付方式
            // 设置outSaleNo字段，避免数据库插入错误
            saleOrderRecord.setOutSaleNo(disburseData.getSaleNo());
            saleOrderRecordService.save(saleOrderRecord);
            return ResponseResult.success();
        }
        // 构建并保存渠道记录
        saleApply.getData().getYunXiOrderDetail().setOrderAmount(saleSchedule.getTotalAmt().toString());
        SaleOrderRecord saleOrderRecord = saleOrderRecordService.buildFromOrderDetail(
                saleApply.getData().getYunXiOrderDetail(),
                null, // 新建场景,不需要已有记录
                saleApplyDto.getSaleNo(),
                String.valueOf(userId));
        // 确保outSaleNo字段有值
        if (saleOrderRecord.getOutSaleNo() == null) {
            saleOrderRecord.setOutSaleNo(saleApplyDto.getSaleNo());
        }
        saleOrderRecordService.save(saleOrderRecord);
        log.info("【权益赊销】订单申请接口 - 宝付支付单号：{},applyAmt:{},申请赊销成功", sequence,
                saleSchedule.getTotalAmt());
        // 宝付支付成功基本上就是下单成功，这里直接返回成功
        SaleSchedule saleScheduleUp = new SaleSchedule();
        saleScheduleUp.setId(saleSchedule.getId());
        saleScheduleUp.setSettleFlag(SettleFlagConstant.CLOSE);
        saleScheduleMapper.updateById(saleScheduleUp);
        return ResponseResult.success();
    }

    @Nullable
    private ResponseResult<Void> baofuPayResult(Long userId, String sequence, SaleSchedule saleSchedule) {
        UserBankCard userBankCard = userBankCardMapper.queryByUserId(userId);
        DirectPayReqVo reqVo = new DirectPayReqVo();
        reqVo.setTransId(sequence);
        reqVo.setUserId(String.valueOf(userId));
        // 元转分
        reqVo.setTxnAmt(AmountUtil.yuanToCentStr(String.valueOf(saleSchedule.getTotalAmt())));
        reqVo.setProtocolNo(userBankCard.getBaoFuContractNum());
        UserData userData = userDataMapper.selectById(userId);
        // 设置风控参数
        RiskItemReqVo riskItem = new RiskItemReqVo();
        riskItem.setGoodsCategory("虚拟权益产品");
        riskItem.setGoodsName("云樨虚拟权益产品");
        riskItem.setGoodsCount("1");
        riskItem.setGoodsDescription("平台会员月卡/季卡/年卡");
        riskItem.setUserRegisterIp(StrUtil.isNotBlank(userData.getIp()) ? userData.getIp() : "127.0.0.1");
        riskItem.setClientIp(StrUtil.isNotBlank(userData.getIp()) ? userData.getIp() : "127.0.0.1");
        riskItem.setDeviceInfo("mobile");
        reqVo.setRiskItem(riskItem);
        log.info("[宝付-还款支付] 设置风控参数完成, 请求参数: {}", JSON.toJSONString(reqVo));

        ResponseResult<DirectPayRspVo> result = baofuAdapterService.directPay(reqVo);
        // 创建宝付支付详情对象
        BaofuPayDetail payDetail = new BaofuPayDetail();
        payDetail.setTransId(reqVo.getTransId());
        payDetail.setUserId(reqVo.getUserId());
        payDetail.setProtocolNo(reqVo.getProtocolNo());
        payDetail.setTxnAmt(reqVo.getTxnAmt());
        payDetail.setRiskItem(JSON.toJSONString(reqVo.getRiskItem()));
        // 支付失败
        if (!result.isSuccess()) {
            payDetail.setPayStatus("FAIL");
            payDetail.setMsgCode(result.getData().getCode());
            payDetail.setMessage(result.getData().getMessage());
            // 保存支付失败记录
            baofuPayDetailService.save(payDetail);
            return ResponseResult.error(ErrorCodeEnum.FAIL, result.getErrorMessage());
        }

        DirectPayRspVo data = result.getData();
        if (!BaofuConstant.BIZ_CODE_SUCCESS.equals(data.getCode())) {
            // 支付失败，保存详情
            payDetail.setPayStatus("FAIL");
            payDetail.setMsgCode(data.getCode());
            payDetail.setMessage(data.getMessage());
            payDetail.setOuPayOrderOn(data.getOrderId());
            // 保存支付失败记录
            baofuPayDetailService.save(payDetail);
            return ResponseResult.error(ErrorCodeEnum.FAIL, data.getMessage());
        }

        // 支付成功，保存详情
        payDetail.setPayStatus("SUCCESS");
        payDetail.setMsgCode(data.getCode());
        payDetail.setMessage(data.getMessage());
        payDetail.setOuPayOrderOn(data.getOrderId());
        payDetail.setSucceedAmt(data.getSuccAmt());
        // 设置支付成功时间
        if (StringUtils.hasText(data.getSuccTime())) {
            // 根据实际格式转换时间字符串为Date对象
            // 这里假设是标准格式，实际使用时请根据宝付返回的格式调整
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                payDetail.setSucceedTime(sdf.parse(data.getSuccTime()));
            } catch (Exception e) {
                log.error("解析支付成功时间失败", e);
                payDetail.setSucceedTime(new Date());
            }
        }
        // 保存支付成功记录
        baofuPayDetailService.save(payDetail);
        return null;
    }

}
