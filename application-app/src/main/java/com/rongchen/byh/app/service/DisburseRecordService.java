package com.rongchen.byh.app.service;

public interface DisburseRecordService {


    /**
     * 保存记录
     * @param loanNo  流程单号 放款：loanNo, 还款：
     * @param traceId 线程id
     * @param location 位置
     */
    void saveRecord(String loanNo,String traceId,String location);

    /**
     * 保存记录
     * @param type
     * @param loanNo
     * @param traceId
     * @param location
     */
    void saveRecord(Integer type,String loanNo,String traceId,String location);
}
