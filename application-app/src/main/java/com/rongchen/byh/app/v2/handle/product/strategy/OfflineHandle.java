package com.rongchen.byh.app.v2.handle.product.strategy;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.app.constant.LoanType;
import com.rongchen.byh.app.constant.SourceMode;
import com.rongchen.byh.app.constant.UserAuditStatus;
import com.rongchen.byh.app.dto.app.FaceVerifyDto;
import com.rongchen.byh.app.dto.app.IdCardVerifyDto;
import com.rongchen.byh.app.dto.app.LoanApplyDto;
import com.rongchen.byh.app.dto.app.UserFormDto;
import com.rongchen.byh.app.dto.h5.UserCheckLoanApplyDto;
import com.rongchen.byh.app.entity.UserCreditData;
import com.rongchen.byh.app.entity.UserData;
import com.rongchen.byh.app.entity.UserLoanApply;
import com.rongchen.byh.app.entity.UserStaff;
import com.rongchen.byh.app.utils.UserTokenUtil;
import com.rongchen.byh.app.v2.common.RedisEnum;
import com.rongchen.byh.app.v2.dto.LoginH5Dto;
import com.rongchen.byh.app.v2.entity.ProductPageMenu;
import com.rongchen.byh.app.v2.entity.UserProgress;
import com.rongchen.byh.app.v2.handle.product.AbstractProductHandle;
import com.rongchen.byh.app.vo.RegOrLoginVo;
import com.rongchen.byh.app.vo.app.SubmitUserFormVo;
import com.rongchen.byh.app.vo.app.UserFormVo;
import com.rongchen.byh.app.vo.h5.UserCheckLoanApplyVo;
import com.rongchen.byh.common.api.idCardVerify.vo.OcrVo;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;

/**
 * 线下模式业务处理
 */
@Slf4j
@Component("offline")
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class OfflineHandle extends AbstractProductHandle {


    @Override
    public ResponseResult<UserCheckLoanApplyVo> queryLoanResult(Object parame) {
        Long userId = UserTokenUtil.getUserId();
        UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userId, LoanType.CHECK, SourceMode.OFFLINE);
        UserCheckLoanApplyVo userCheckLoanApplyVo = new UserCheckLoanApplyVo();
        userCheckLoanApplyVo.setUserId(userId);
        if (ObjectUtil.isEmpty(userLoanApply)) {
            userCheckLoanApplyVo.setAuditStatus(3);
            return ResponseResult.success(userCheckLoanApplyVo);
        }
        UserData userData = userDataMapper.selectById(userId);
        if (ObjectUtil.isEmpty(userData)) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "用户不存在");
        }
        if (userData.getAuditStatus().equals(UserAuditStatus.FIRST_REJECT)) {
            userCheckLoanApplyVo.setAuditStatus(2);
            return ResponseResult.success(userCheckLoanApplyVo);
        } else if (userData.getAuditStatus().equals(UserAuditStatus.WAIT_REVIEW)) {
            userCheckLoanApplyVo.setAuditStatus(1);
            return ResponseResult.success(userCheckLoanApplyVo);
        } else if (userData.getAuditStatus().equals(UserAuditStatus.REVIEW_REJECT)) {
            userCheckLoanApplyVo.setAuditStatus(6);
            return ResponseResult.success(userCheckLoanApplyVo);
        } else if (userData.getAuditStatus() >= UserAuditStatus.WAIT_APP_CREDIT) {
            userCheckLoanApplyVo.setAuditStatus(5);
            UserCreditData userCreditData = userCreditDataMapper.queryByUserId(userId);
            if (ObjectUtil.isNotEmpty(userCreditData)){
                userCheckLoanApplyVo.setCreditAmount(userCreditData.getCreditAmount());
            }
            return ResponseResult.success(userCheckLoanApplyVo);
        } else {
            userCheckLoanApplyVo.setAuditStatus(userLoanApply.getAuditsStatus());
        }
        return ResponseResult.success(userCheckLoanApplyVo);
    }

    @Override
    public ResponseResult<UserCheckLoanApplyVo> submitTwoElementsData(UserCheckLoanApplyDto userCheckLoanApplyDto) {
        ResponseResult<UserCheckLoanApplyVo> supRes = super.submitTwoElementsData(userCheckLoanApplyDto);
        if (!supRes.isSuccess()){
            return supRes;
        }
        if (userCheckLoanApplyDto != null && userCheckLoanApplyDto.getIdNumber() != null){
            userCheckLoanApplyDto.setIdNumber(userCheckLoanApplyDto.getIdNumber().toUpperCase());
        }
        // 参数校验
        ResponseResult<UserCheckLoanApplyVo> preCheckResult = preCheckByTwoElementsData(userCheckLoanApplyDto);
        if (!preCheckResult.isSuccess()){
            return preCheckResult;
        }
        Long userId = UserTokenUtil.getUserId();
        UserStaff userStaff = userStaffMapper.selectByUserId(userId);
        if (ObjectUtil.isEmpty(userStaff)) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "请先绑定员工");
        }
        UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userId, LoanType.CHECK, SourceMode.OFFLINE);
        UserCheckLoanApplyVo userCheckLoanApplyVo = new UserCheckLoanApplyVo();
        if (userLoanApply != null) {
            UserCheckLoanApplyVo result = userCheckLoanApplyVo;
            result.setUserId(userId);
            result.setAuditStatus(userLoanApply.getAuditsStatus());
            return ResponseResult.success(result);
        }

        // 三要素校验
        UserData userData = userDataMapper.selectById(userId);
        ResponseResult<Void> verifyThreeResult = super.verifyThree(userCheckLoanApplyDto.getIdNumber(),userCheckLoanApplyDto.getUserName(),userData.getMobile());
        if (!verifyThreeResult.isSuccess()){
            return ResponseResult.error(ErrorCodeEnum.FAIL, verifyThreeResult.getErrorMessage());
        }
        // 记录申请、记录用户身份证号和姓名，mq
        ResponseResult<UserCheckLoanApplyVo> result = saveTwoElementsMq(userCheckLoanApplyDto, userId, SourceMode.OFFLINE);
        if (!result.isSuccess()){
            return ResponseResult.error(ErrorCodeEnum.FAIL, result.getErrorMessage());
        }
        return ResponseResult.success(null);
    }


    @Override
    public ResponseResult<SubmitUserFormVo> submitUserForm(UserFormDto dto) {
        return super.submitUserForm(dto);
    }

    @Override
    public void loanApply(LoanApplyDto loanApplyDto, Object params) {

    }

    @Override
    public ResponseResult<RegOrLoginVo> loginByH5(LoginH5Dto loginH5Dto, UserData userData) {
        // 绑定销售
        ResponseResult<RegOrLoginVo> result = longinBindStaff(userData.getId(),loginH5Dto.getInviteCode());
        if (!result.isSuccess()){
            return result;
        }
        RegOrLoginVo regOrLoginVo = new RegOrLoginVo();
        UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userData.getId(), LoanType.CHECK, userData.getSourceMode());
        regOrLoginVo.setApplyStatus(ObjectUtil.isEmpty(userLoanApply) ? 0 : 1);
        return ResponseResult.success(regOrLoginVo);
    }
	@Override
	public ResponseResult<OcrVo> submitOcrData(IdCardVerifyDto verifyDto, HttpServletRequest request) {
		return super.submitOcrData(verifyDto, request);
	}

    @Override
    public ResponseResult<Void> submitFaceData(FaceVerifyDto verifyDto) {
        ResponseResult<Void> voidResponseResult = super.submitFaceData(verifyDto);
        return voidResponseResult;
    }

    @Override
    public ResponseResult<ProductPageMenu> getPage(Long userId, String curPage, Integer productId) {
        ProductPageMenu beforeMenu = menuMapper.selectByCurPage(curPage,productId);
        Integer beforeSort = beforeMenu.getSort();
        ProductPageMenu returnMenu = new ProductPageMenu();
        //1.先查询redis，看是否有用户的页面位置，如果有获取缓存里面的url,如果没有就查询数据库
        if(redisTemplate.hasKey(RedisEnum.PAGE_USER_ID+userId)){
            ProductPageMenu redisMenu = (ProductPageMenu)redisTemplate.opsForValue().get(RedisEnum.PAGE_USER_ID + userId);
            if(redisMenu.getSort()>beforeSort){
                //说明用户之前执行过相关流程，可以跳步骤,这种情况不需要更新缓存
                //如果pageMenu的curPage值为h5登录页（/login）且是已经有token的情况下,则查询用户是否已经过了初筛，如果已经过了则直接跳转到home页面，否则直接返回redis里面存储的页面
                if(curPage.equals("/login")){
                    //查询是否过了提交了初筛
                    UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userId, LoanType.CHECK,  SourceMode.OFFLINE);
                    if(userLoanApply!=null){
                        //说明已经过了初筛则直接返回初筛结果页
                        beforeMenu.setCurPage("/home");
                        beforeMenu.setSort(null);
                        returnMenu = beforeMenu;
                    }else{
                        returnMenu = redisMenu;
                    }
                }else if(curPage.equals("pages/login/login")){
                    //查看是否是app登录页，如果是则直接返回首页
                    beforeMenu.setCurPage("pages/home/<USER>");
                    beforeMenu.setSort(null);
                    returnMenu = beforeMenu;
                }else{
                    //直接返回redis存储的页面，回到用户上一次推出的页面，即用户还没有跑完页面程序
                    returnMenu = redisMenu;
                }
            }
            else{
                //说明按照顺序在执行，这种情况需要更新缓存
                beforeMenu.setSort(beforeSort+1);
                beforeMenu.setCurPage(null);
                beforeMenu.setIsError(0);
                ProductPageMenu nextMenu = menuMapper.selectNext(beforeMenu);
                returnMenu= nextMenu;
                //异步存储数据到redis和mysql中
                asyncMethod.updateUserProgress(returnMenu,userId);
            }
        }else{
            //1.缓存过期或者没有缓存，从数据库里面查询用户上一次走到哪一个页面了
            UserProgress progress = userProgressMapper.selectByUserId(userId);
            if(progress!=null){
                ProductPageMenu mysqlMenu = progress.getPageMenu();
                if(mysqlMenu.getSort()>beforeSort){
                    //说明用户之前执行过相关流程，可以跳步骤,这种情况不需要更新缓存
                    //如果pageMenu的curPage值为h5登录页（/login）且是已经有token的情况下,则查询用户是否已经过了初筛，如果已经过了则直接跳转到home页面，否则直接返回mysql里面存储的页面
                    if(curPage.equals("/login")){
                        //查询是否过了提交了初筛
                        UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userId, LoanType.CHECK,  SourceMode.OFFLINE);
                        if(userLoanApply!=null){
                            //说明已经过了初筛则直接返回初筛结果页
                            beforeMenu.setCurPage("/home");
                            beforeMenu.setSort(null);
                            returnMenu = beforeMenu;
                        }else{
                            returnMenu = mysqlMenu;
                        }
                    }else if(curPage.equals("pages/login/login")){
                        //查看是否是app登录页，如果是则直接返回首页
                        beforeMenu.setCurPage("pages/home/<USER>");
                        beforeMenu.setSort(null);
                        returnMenu = beforeMenu;

                    }else{
                        //直接返回mysql存储的页面，回到用户上一次推出的页面，即用户还没有跑完页面程序
                        returnMenu = mysqlMenu;
                    }
                }else{
                    //说明没有跳步骤，直接返回下一个页面，需要更新缓存
                    beforeMenu.setSort(beforeSort+1);
                    beforeMenu.setIsError(0);
                    beforeMenu.setCurPage(null);
                    ProductPageMenu menuTwo = menuMapper.selectNext(beforeMenu);
                    returnMenu = menuTwo;
                    //异步存储数据到redis和mysql中
                    asyncMethod.updateUserProgress(returnMenu,userId);
                }
            }
            else{
                //mysql和redis都没有数据则按照当前顺序返回给用户下一个页面
                //说明按照顺序在执行
                beforeMenu.setSort(beforeSort+1);
                beforeMenu.setIsError(0);
                beforeMenu.setCurPage(null);
                ProductPageMenu menuTwor = menuMapper.selectNext(beforeMenu);
                returnMenu = menuTwor;
                //异步存储数据到redis和mysql中
                asyncMethod.updateUserProgress(returnMenu,userId);
            }
        }
        return ResponseResult.success(returnMenu);
    }
}
