package com.rongchen.byh.app.dto.app;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName BurialPointDro
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/1/8 14:27
 * @Version 1.0
 **/
@Data
public class BurialPointDto {

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "渠道id")
    private Long channelId;

    @Schema(description = "渠道密钥")
    private String channel;

    @Schema(description = "页面key")
    private String pageKey;


    @Schema(description = "页面名称")
    private String pageName;


    @Schema(description = "按钮key")
    private String buttonKey;


    @Schema(description = "按钮名称")
    private String buttonName;


    @Schema(description = "其他")
    private String other;
}
