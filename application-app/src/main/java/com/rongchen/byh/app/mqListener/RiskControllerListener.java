package com.rongchen.byh.app.mqListener;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.rabbitmq.client.Channel;
import com.rongchen.byh.app.constant.LoanType;
import com.rongchen.byh.app.constant.SourceMode;
import com.rongchen.byh.app.constant.UserAuditStatus;
import com.rongchen.byh.app.dao.HrzxCreditLogMapper;
import com.rongchen.byh.app.dao.StaffAuditRecordMapper;
import com.rongchen.byh.app.dao.UserCreditDataMapper;
import com.rongchen.byh.app.dao.UserDataMapper;
import com.rongchen.byh.app.dao.UserDetailMapper;
import com.rongchen.byh.app.dao.UserLoanApplyMapper;
import com.rongchen.byh.app.dao.UserStaffMapper;
import com.rongchen.byh.app.dto.h5.AirRiskControllerDto;
import com.rongchen.byh.app.entity.HrzxCreditLog;
import com.rongchen.byh.app.entity.StaffAuditRecord;
import com.rongchen.byh.app.entity.UserCreditData;
import com.rongchen.byh.app.entity.UserData;
import com.rongchen.byh.app.entity.UserDetail;
import com.rongchen.byh.app.entity.UserLoanApply;
import com.rongchen.byh.app.entity.UserStaff;
import com.rongchen.byh.app.service.NetworkFileDealService;
import com.rongchen.byh.app.utils.OssUtil;
import com.rongchen.byh.app.vo.h5.UserCheckLoanApplyVo;
import com.rongchen.byh.common.api.bankCredit.dto.HrzxAuthDto;
import com.rongchen.byh.common.api.idCardVerify.service.IdCardVerifyService;
import com.rongchen.byh.common.api.idCardVerify.vo.VerifyVo;
import com.rongchen.byh.common.api.riskControl.dto.CreditPreLoanAuditDto;
import com.rongchen.byh.common.api.riskControl.dto.CrmPushDto;
import com.rongchen.byh.common.api.riskControl.dto.PreLoanAuditDto;
import com.rongchen.byh.common.api.riskControl.service.CrmPushService;
import com.rongchen.byh.common.api.riskControl.service.RiskControlService;
import com.rongchen.byh.common.api.riskControl.vo.PreLoanAuditVo;
import com.rongchen.byh.common.api.zifang.utils.IdCardUtil;
import com.rongchen.byh.common.rabbitmq.constants.QueueConstant;
import com.rongchen.byh.common.redis.util.CommonRedisUtil;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 空中放款模式风控监听
 * @date 2025/2/20 18:52:06
 */
@Component
@Slf4j
public class RiskControllerListener {

    @Resource
    private RiskControlService riskControlService;
    @Resource
    private UserLoanApplyMapper userLoanApplyMapper;
    @Resource
    private UserDataMapper userDataMapper;
    @Resource
    private IdCardVerifyService idCardVerifyService;
    @Resource
    private UserDetailMapper userDetailMapper;
    @Resource
    private UserCreditDataMapper userCreditDataMapper;
    @Resource
    private CrmPushService crmPushService;
    @Resource
    private UserStaffMapper userStaffMapper;
    @Resource
    private StaffAuditRecordMapper staffAuditRecordMapper;

    @Resource
    private NetworkFileDealService networkFileDealService;
    @Resource
    private HrzxCreditLogMapper hrzxCreditLogMapper;
    @Resource
    private OssUtil ossUtil;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private CommonRedisUtil commonRedisUtil;

    @RabbitListener(queues = QueueConstant.RISK_CONTROL_QUEUE, ackMode = "MANUAL")
    public void receiver(String msg, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        String traceId = UUID.fastUUID().toString();
        Map<String, String> contextMap = new HashMap<>();
        contextMap.put("traceId", traceId);
        MDC.setContextMap(contextMap);

        log.info("【风控监听器】接收到消息，消息内容: {}, deliveryTag: {}", msg, deliveryTag);
        AirRiskControllerDto dto = null;
        String idempotencyKey = "未知";

        try {
            try {
                dto = JSONObject.parseObject(msg, AirRiskControllerDto.class);
            } catch (Exception e) {
                log.error("【风控监听器】消息解析失败，原始消息: {}, 错误: {}", msg, e.getMessage(), e);
                channel.basicNack(deliveryTag, false, false);
                return;
            }

            if (dto == null || dto.getUserId() == null || dto.getType() == null) {
                log.error("【风控监听器】接收到的 DTO 为空或缺少关键字段 (userId, type)，消息: {}", msg);
                channel.basicNack(deliveryTag, false, false);
                return;
            }

            String keyPart;
            if (dto.getType() == 2 && dto.getApplyId() != null) {
                keyPart = "applyId=" + dto.getApplyId();
            } else if ((dto.getType() == 0 || dto.getType() == 1) && dto.getIdNumber() != null) {
                keyPart = "idNumber=" + dto.getIdNumber();
            } else {
                log.warn("【风控监听器】无法为 userId={}, type={} 生成基于业务ID的幂等键，将使用 traceId={}",
                    dto.getUserId(),
                    dto.getType(), traceId);
                keyPart = "traceId=" + traceId;
            }
            idempotencyKey = String.format("risk_control:userId=%d:type=%d:%s",
                dto.getUserId(), dto.getType(), keyPart);

            String statusKey = commonRedisUtil.buildKey("mq", "processed", "status", idempotencyKey);
            RBucket<String> statusBucket = redissonClient.getBucket(statusKey);

            if ("COMPLETED".equals(statusBucket.get())) {
                log.warn("【风控监听器】检测到重复消息（已处理完成），幂等键: {}。消息将被确认。", idempotencyKey);
                channel.basicAck(deliveryTag, false);
                return;
            }

            log.info("【风控监听器】开始处理消息，幂等键: {}, 原始消息: {}", idempotencyKey, msg);
            try {
                ThreadUtil.sleep(10000);

                switch (dto.getType()) {
                    case 0:
                        handleOnlineResult(dto);
                        break;
                    case 1:
                        handlerOfflineResult(dto);
                        break;
                    case 2:
                        handleAirResult(dto);
                        break;
                    default:
                        log.warn("【风控监听器】未知的消息类型: {}，幂等键: {}", dto.getType(), idempotencyKey);
                        channel.basicAck(deliveryTag, false);
                        return;
                }

                statusBucket.set("COMPLETED", 1, TimeUnit.HOURS);
                log.info("【风控监听器】消息处理成功，幂等键: {}", idempotencyKey);
                channel.basicAck(deliveryTag, false);
                log.debug("【风控监听器】消息已确认 (ACK)，幂等键: {}, deliveryTag: {}", idempotencyKey, deliveryTag);

            } catch (Exception e) {
                log.error("【风控监听器】处理业务逻辑时发生异常，幂等键: {}, 错误: {}", idempotencyKey, e.getMessage(), e);
                channel.basicNack(deliveryTag, false, false);
                log.warn("【风控监听器】业务处理异常，消息已拒绝 (NACK)，幂等键: {}, deliveryTag: {}", idempotencyKey,
                    deliveryTag);
            }

        } catch (IOException e) {
            log.error("【风控监听器】手动确认/拒绝消息时发生 IO 错误, 幂等键: {}, deliveryTag: {}. 错误: {}",
                idempotencyKey, deliveryTag, e.getMessage(), e);
        } catch (Exception e) {
            log.error("【风控监听器】处理消息外层发生未知错误, 幂等键: {}, deliveryTag: {}. 错误: {}",
                idempotencyKey, deliveryTag, e.getMessage(), e);
            try {
                channel.basicNack(deliveryTag, false, false);
                log.warn("【风控监听器】外层未知错误，消息已尝试拒绝 (NACK)，幂等键: {}, deliveryTag: {}", idempotencyKey,
                    deliveryTag);
            } catch (IOException ioEx) {
                log.error("【风控监听器】外层未知错误，尝试拒绝消息时发生 IO 错误, 幂等键: {}, deliveryTag: {}. 错误: {}",
                    idempotencyKey, deliveryTag, ioEx.getMessage(), ioEx);
            }
            //aaaaa
        } finally {
            MDC.clear();
        }
    }

    private void handleAirResult(AirRiskControllerDto dto) {
        Long userId = dto.getUserId();
        UserData userDataUp = new UserData();
        userDataUp.setId(userId);
        HrzxAuthDto hrzxAuthDto = new HrzxAuthDto();
        UserLoanApply apply = userLoanApplyMapper.selectById(dto.getApplyId());
        UserData userData = userDataMapper.selectById(userId);
        UserDetail detail = userDetailMapper.queryByUserId(userId);
        String mobile = userData.getMobile();

        if (networkFileDealService.dealFaceImage(detail, hrzxAuthDto)) {
            log.error("【风控监听器】处理人脸图像失败，用户ID: {}, 错误: {}", userId);
            return;
        }
        if (networkFileDealService.dealProtocolFile(detail, hrzxAuthDto)) {
            log.error("【风控监听器】处理协议文件失败，用户ID: {}, 错误: {}", userId);
            return;
        }

        CreditPreLoanAuditDto creditPreLoanAuditDto = buildCreditDto(hrzxAuthDto, apply, detail, mobile);
        PreLoanAuditVo preLoanAuditVo = riskControlService.creditPreLoanAudit(creditPreLoanAuditDto);
        HrzxCreditLog addHrzxCreditLog = new HrzxCreditLog();
        addHrzxCreditLog.setUserId(userId);
        addHrzxCreditLog.setStatus(preLoanAuditVo.getResult());
        addHrzxCreditLog.setResult(preLoanAuditVo.getMessage());
        hrzxCreditLogMapper.insert(addHrzxCreditLog);

        Integer result = preLoanAuditVo.getResult();
        if (apply != null && result == -1) {
            apply.setAuditsStatus(2);
            userLoanApplyMapper.updateById(apply);
            // 更新用户审核状态
            userDataUp.setAuditStatus(UserAuditStatus.FIRST_REJECT);
            userDataMapper.updateById(userDataUp);
            return;
        } else if (apply != null && result >= 0) {
            if (result == 0 || result == 1) {
                apply.setAuditsStatus(1);

                CrmPushDto crmPushDto = new CrmPushDto();
                crmPushDto.setMobile(mobile);
                crmPushDto.setUserName(dto.getUserName());
                crmPushDto.setIdNumber(dto.getIdNumber());
                crmPushDto.setCreditRating(preLoanAuditVo.getCreditRating());
                crmPushDto.setIp(userData.getIp());
            } else {
                apply.setAuditsStatus(2);
                userLoanApplyMapper.updateById(apply);
                // 更新用户审核状态
                userDataUp.setAuditStatus(UserAuditStatus.FIRST_REJECT);
                userDataMapper.updateById(userDataUp);
                return;
            }
        }

        if(detail!=null && detail.getUserId() != null){
            //保存用户详情
            detail.setRiskLevel(preLoanAuditVo.getCreditRating());
            userDetailMapper.update(detail, new LambdaUpdateWrapper<UserDetail>().eq(UserDetail::getUserId, detail.getUserId()));
        }

        //保存用户loan申请
        if(apply!=null && apply.getUserId() != null){
            userLoanApplyMapper.updateById(apply);
        }

        if(userDataUp.getId() != null){
            //更新用户数据
            userDataUp.setAuditStatus(UserAuditStatus.WAIT_APP_CREDIT);
            userDataUp.setSourceMode(SourceMode.AIR);
            userDataMapper.updateById(userDataUp);
        }

        //保存用户征信数据
        UserCreditData userCreditData = new UserCreditData();
        userCreditData.setUserId(userId);
        userCreditData.setCreditAmount(ObjectUtil.isEmpty(preLoanAuditVo.getAmount()) ? BigDecimal.ZERO
            : preLoanAuditVo.getAmount());
        userCreditData.setResidueAmount(userCreditData.getCreditAmount());
        userCreditDataMapper.insert(userCreditData);
    }

    @NotNull
    private CreditPreLoanAuditDto buildCreditDto(HrzxAuthDto hrzxAuthDto, UserLoanApply apply, UserDetail detail,
        String mobile) {
        CreditPreLoanAuditDto creditPreLoanAuditDto = new CreditPreLoanAuditDto();
        creditPreLoanAuditDto.setIdcard_no(detail.getIdNumber());
        creditPreLoanAuditDto.setMobile(mobile);
        creditPreLoanAuditDto.setName(detail.getAppName());
        creditPreLoanAuditDto.setCredit_id(IdUtil.fastSimpleUUID());
        creditPreLoanAuditDto.setCredit_time(DateUtil.now());
        creditPreLoanAuditDto.setNation_ocr(IdCardUtil.nationOcr(detail.getNation()));
        creditPreLoanAuditDto.setIdcard_address_ocr(detail.getAddress());
        String[] split = detail.getValidDate().split("-");
        creditPreLoanAuditDto.setCert_start(split[0].replace(".", "-"));
        if ("长期".equals(split[1])) {
            split[1] = "2099.12.31";
        }
        creditPreLoanAuditDto.setCert_end(split[1].replace(".", "-"));
        JSONObject socialCreditCode = new JSONObject();
        socialCreditCode.put("requestNo", IdUtil.fastSimpleUUID());
        socialCreditCode.put("queryName", detail.getAppName());
        socialCreditCode.put("queryIdNoType", "01");
        socialCreditCode.put("queryIdNo", detail.getIdNumber());
        socialCreditCode.put("queryPhone", mobile);
        socialCreditCode.put("veriFaceTime", DateUtil.now());
        socialCreditCode.put("authTime", DateUtil.now());

        long ossStartTime = System.currentTimeMillis();
        log.info("开始并行 OSS 操作，userId: {}, 手机号: {}", detail.getUserId(), mobile);
        log.info("身份证正面: {}", hrzxAuthDto.getFrontIdCardFile());
        log.info("身份证反面: {}", hrzxAuthDto.getBackIdCardFile());
        log.info("人脸: {}", hrzxAuthDto.getOtherAuthFile());
        log.info("ca电子签名授权书pdf文件: {}", hrzxAuthDto.getCaFile());
        log.info("委托担保申请书: {}", hrzxAuthDto.getOtherFile());

        CompletableFuture<String> idCardFileFuture = ossUtil.mergeTwoImage(hrzxAuthDto.getFrontIdCardFile(),
            hrzxAuthDto.getBackIdCardFile(), mobile);
        CompletableFuture<String> otherAuthFileFuture = ossUtil.uploadOss(hrzxAuthDto.getOtherAuthFile());
        CompletableFuture<String> caFileFuture = ossUtil.uploadOss(hrzxAuthDto.getCaFile());
        CompletableFuture<String> otherFileFuture = ossUtil.pdfToZip(hrzxAuthDto.getOtherFile());

        CompletableFuture.allOf(idCardFileFuture, otherAuthFileFuture, caFileFuture, otherFileFuture).join();

        long ossEndTime = System.currentTimeMillis();
        log.info("并行 OSS 操作完成，耗时: {} ms，userId: {}, 手机号: {}", (ossEndTime - ossStartTime),
            detail.getUserId(),
            mobile);
        String otherAuthFile = otherAuthFileFuture.join();
        String idCardFile = idCardFileFuture.join();
        String caFile = caFileFuture.join();
        String otherFile = otherFileFuture.join();
        log.info("人脸: {}", otherAuthFile);
        log.info("身份证合并正反面: {}", idCardFile);
        log.info("ca电子签名授权书pdf文件: {}", caFile);
        log.info("委托担保申请书: {}", otherFile);

        socialCreditCode.put("requestNo", UUID.fastUUID().toString(true));
        socialCreditCode.put("idCardFile", idCardFile);
        socialCreditCode.put("otherAuthFile", otherAuthFile);
        socialCreditCode.put("caFile", caFile);
        socialCreditCode.put("otherFile", otherFile);
        socialCreditCode.put("queryDate", DateUtil.format(new Date(), "yyyyMMdd"));
        log.info("调用华融认证上传文件完成》》{}", JSONObject.toJSONString(socialCreditCode));
        JSONObject company = new JSONObject();
        company.put("social_credit_code", Base64.getEncoder().encodeToString(socialCreditCode.toJSONString()
            .getBytes(StandardCharsets.UTF_8)));
        creditPreLoanAuditDto.setCompany_info(company);
        return creditPreLoanAuditDto;
    }


    private void handlerOfflineResult(AirRiskControllerDto userCheckLoanApplyDto) {
        if (userCheckLoanApplyDto != null && userCheckLoanApplyDto.getIdNumber() != null) {
            userCheckLoanApplyDto.setIdNumber(userCheckLoanApplyDto.getIdNumber().toUpperCase());
        }
        // 前置校验
        if (!IdcardUtil.isValidCard(userCheckLoanApplyDto.getIdNumber())) {
            log.info("身份证号格式错误:{}", userCheckLoanApplyDto.getIdNumber());
            return;
        }
        // 是否已经提交过一次申请，是则提示已经提交无需再次提交
        Long userId = userCheckLoanApplyDto.getUserId();
        UserData userData = userDataMapper.selectById(userId);
        if (ObjectUtil.isEmpty(userData)) {
            log.error("用户不存在：{}", userId);
            return;
        }
        UserStaff userStaff = userStaffMapper.selectByUserId(userId);
        if (ObjectUtil.isEmpty(userStaff)) {
            log.info("请先绑定员工:{}", userId);
            return;
        }
        UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userId, LoanType.CHECK, 1);
        UserCheckLoanApplyVo userCheckLoanApplyVo = new UserCheckLoanApplyVo();
        if (userLoanApply != null) {
            log.info("已经提交过一次申请，无需再次提交");
            return;
        }
        UserDetail detail = userDetailMapper.selectOne(
            new LambdaQueryWrapper<UserDetail>().eq(UserDetail::getWebIdCard, userCheckLoanApplyDto.getIdNumber()));
        if (ObjectUtil.isNotEmpty(detail)) {
            log.info("身份证号已存在，请勿重复申请");
            return;
        }
        VerifyVo verifyResult = idCardVerifyService.verifyThree(userCheckLoanApplyDto.getIdNumber(),
            userCheckLoanApplyDto.getUserName(), userData.getMobile());
        if (verifyResult.getCode() != 1) {
            log.info("三要素校验失败:{}", userId);
            return;
        }
        // 记录申请
        UserLoanApply apply = new UserLoanApply();
        apply.setUserId(userId);
        apply.setApplyType(LoanType.CHECK);
        apply.setUserApplyInfo(JSONObject.toJSONString(userCheckLoanApplyDto));
        apply.setOnlineType(1);
        userLoanApplyMapper.insert(apply);

        // 记录用户身份证号和姓名
        UserDetail userDetail = new UserDetail();
        userDetail.setUserId(userId);
        userDetail.setWebName(userCheckLoanApplyDto.getUserName());
        userDetail.setWebIdCard(userCheckLoanApplyDto.getIdNumber());
        userDetail.setWebTwoElements(1);
        userDetailMapper.insert(userDetail);

        // 风控
        PreLoanAuditDto preLoanAuditDto = new PreLoanAuditDto();
        preLoanAuditDto.setApplyId(String.valueOf(apply.getId()));
        preLoanAuditDto.setMobile(userData.getMobile());
        preLoanAuditDto.setName(userCheckLoanApplyDto.getUserName());
        preLoanAuditDto.setIdNumber(userCheckLoanApplyDto.getIdNumber());
        PreLoanAuditVo preLoanAuditVo = riskControlService.offlineH5PreLoanAudit(preLoanAuditDto);
        Integer result = preLoanAuditVo.getResult();
        if (result == -1) {
            apply.setAuditsStatus(2);
            userLoanApplyMapper.updateById(apply);
            // 更新用户审核状态
            userData.setAuditStatus(UserAuditStatus.FIRST_REJECT);
            userDataMapper.updateById(userData);
            return;
        } else {
            if (result == 0 || result == 1) {
                apply.setAuditsStatus(1);
            } else {
                return;
            }
        }
        // 更新用户风险等级
        userDetail.setRiskLevel(preLoanAuditVo.getCreditRating());
        userDetailMapper.updateById(userDetail);

        userLoanApplyMapper.updateById(apply);
        // 更新用户审核状态
        userData.setAuditStatus(UserAuditStatus.WAIT_REVIEW);
        userDataMapper.updateById(userData);
        // 添加待审核记录

        StaffAuditRecord staffAuditRecord = new StaffAuditRecord();
        staffAuditRecord.setUserStaffId(userStaff.getId());
        staffAuditRecord.setSubmitTime(new Date());
        staffAuditRecordMapper.insert(staffAuditRecord);


        userCheckLoanApplyVo.setUserId(userId);
        userCheckLoanApplyVo.setAuditStatus(apply.getAuditsStatus());
        return;
    }

    private void handleOnlineResult(AirRiskControllerDto userCheckLoanApplyDto) {
        if (userCheckLoanApplyDto != null && userCheckLoanApplyDto.getIdNumber() != null) {
            userCheckLoanApplyDto.setIdNumber(userCheckLoanApplyDto.getIdNumber().toUpperCase());
        }
        // 前置校验
        if (!IdcardUtil.isValidCard(userCheckLoanApplyDto.getIdNumber())) {
            log.info("身份证号格式错误:{}", userCheckLoanApplyDto.getIdNumber());
            return;
        }
        // 是否已经提交过一次申请，是则提示已经提交无需再次提交
        Long userId = userCheckLoanApplyDto.getUserId();
        UserData userData = userDataMapper.selectById(userId);
        if (ObjectUtil.isEmpty(userData)) {
            log.info("用户不存在：{}", userId);
            return;
        }

        UserData userDataUp = new UserData();
        userDataUp.setId(userData.getId());

        UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userId, LoanType.CHECK, 0);
        if (userLoanApply != null) {
            log.info("已经提交过一次申请，无需再次提交");
            return;
        }
        // 二要素校验----->修改为三要素校验
        // VerifyVo verifyResult =
        // idCardVerifyService.verify(userCheckLoanApplyDto.getIdNumber(),
        // userCheckLoanApplyDto.getUserName());
        // if (verifyResult.getCode() != 0) {
        // return;
        // }
        VerifyVo verifyResult = idCardVerifyService.verifyThree(userCheckLoanApplyDto.getIdNumber(),
            userCheckLoanApplyDto.getUserName(), userData.getMobile());
        if (verifyResult.getCode() != 1) {
            log.info("三要素校验失败:{}", userId);
            return;
        }
        UserDetail detail = userDetailMapper.selectOne(
            new LambdaQueryWrapper<UserDetail>().eq(UserDetail::getWebIdCard, userCheckLoanApplyDto.getIdNumber()));
        if (ObjectUtil.isNotEmpty(detail)) {
            log.info("身份证号已存在，请勿重复申请");
            return;
        }
        // 记录申请
        UserLoanApply apply = new UserLoanApply();
        apply.setUserId(userId);
        apply.setApplyType(LoanType.CHECK);
        apply.setUserApplyInfo(JSONObject.toJSONString(userCheckLoanApplyDto));
        userLoanApplyMapper.insert(apply);

        // 记录用户身份证号和姓名
        UserDetail userDetail = new UserDetail();
        userDetail.setUserId(userId);
        userDetail.setWebName(userCheckLoanApplyDto.getUserName());
        userDetail.setWebIdCard(userCheckLoanApplyDto.getIdNumber());
        userDetail.setWebTwoElements(1);
        userDetailMapper.insert(userDetail);

        // 风控
        PreLoanAuditDto preLoanAuditDto = new PreLoanAuditDto();
        preLoanAuditDto.setApplyId(String.valueOf(apply.getId()));
        preLoanAuditDto.setMobile(userData.getMobile());
        preLoanAuditDto.setName(userCheckLoanApplyDto.getUserName());
        preLoanAuditDto.setIdNumber(userCheckLoanApplyDto.getIdNumber());
        PreLoanAuditVo preLoanAuditVo = riskControlService.preLoanAudit(preLoanAuditDto);
        Integer result = preLoanAuditVo.getResult();
        if (result == -1) {
            apply.setAuditsStatus(2);
            userLoanApplyMapper.updateById(apply);
            // 更新用户审核状态
            userDataUp.setAuditStatus(UserAuditStatus.FIRST_REJECT);
            userDataMapper.updateById(userDataUp);
            return;
        } else {
            if (result == 0) {
                apply.setAuditsStatus(1);
                UserData userData1 = new UserData();
                userData1.setId(userId);
                userData1.setAuditFlag(1);
                //更新审核状态
                userDataMapper.updateById(userData1);
            } else {
                apply.setAuditsStatus(4);

                // 同步crm
                // todo 同步失败是否需要重试
                // TODO 暂时不推送数据到crm,上线后打开
                CrmPushDto crmPushDto = new CrmPushDto();
                crmPushDto.setMobile(userData.getMobile());
                crmPushDto.setUserName(userCheckLoanApplyDto.getUserName());
                crmPushDto.setIdNumber(userCheckLoanApplyDto.getIdNumber());
                crmPushDto.setCreditRating(preLoanAuditVo.getCreditRating());
                // CrmPushVo push = crmPushService.push(crmPushDto);
                // if (push.getResult() != 0) {
                // apply.setAuditsStatus(2);
                // userLoanApplyMapper.updateById(apply);
                // return;
                // }
            }
        }
        // 更新用户风险等级
        userDetail.setRiskLevel(preLoanAuditVo.getCreditRating());
        userDetailMapper.updateById(userDetail);

        userLoanApplyMapper.updateById(apply);
        // 更新用户审核状态
        userDataUp.setAuditStatus(UserAuditStatus.WAIT_APP_CREDIT);
        userDataMapper.updateById(userDataUp);
        // 保存授信额度
        UserCreditData userCreditData = new UserCreditData();
        userCreditData.setUserId(userId);
        userCreditData.setCreditAmount(ObjectUtil.isEmpty(preLoanAuditVo.getAmount()) ? BigDecimal.ZERO
            : preLoanAuditVo.getAmount());
        userCreditData.setResidueAmount(userCreditData.getCreditAmount());
        userCreditDataMapper.insert(userCreditData);
        return;
    }

    public static void main(String[] args) {
        String address = IdCardUtil.addressResolution("广西省融水苗族自治县良寨乡安全村桐树屯112号");
        String newAddress = StrUtil.isEmpty("ssf") ? "未知" : "111";
        System.out.println(StrUtil.isEmpty(address) ? newAddress : address);
    }
}