package com.rongchen.byh.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 赊销结果表
 * @TableName repay_sale_apply
 */
@TableName(value ="repay_sale_apply")
@Data
public class RepaySaleApply implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 账单还款流水号
     */
    private String repayApplyNo;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 还款计划id
     */
    private Long saleScheduleId;

    /**
     * 还款类型
     * 1-主动提交还款
     * 2-自动跑批扣款
     * 3-逾期自动跑批扣款
     */
    private Integer repayType;

    /**
     * 还款状态 0 待审核 1 还款成功 2 还款失败
     */
    private Integer repayStatus;

    /**
     * 失败原因
     */
    private String reason;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 响应时间
     */
    private Date responseTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}