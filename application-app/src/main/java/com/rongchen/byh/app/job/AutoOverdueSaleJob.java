package com.rongchen.byh.app.job;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.app.dao.SaleScheduleMapper;
import com.rongchen.byh.app.entity.SaleSchedule;
import com.rongchen.byh.common.core.util.MDCUtil;
import com.rongchen.byh.common.rabbitmq.constants.QueueConstant;
import com.rongchen.byh.common.rabbitmq.dto.SaleDto;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

/**
 * 每日查询逾期扣款的赊销账单
 */
@Component
@Slf4j
public class AutoOverdueSaleJob {

    @Resource
    SaleScheduleMapper saleScheduleMapper;
    @Resource
    RabbitTemplate rabbitTemplate;

    @XxlJob("autoOverdueSaleJob")
    public void autoOverdueSaleJob() {
        String traceId = UUID.fastUUID().toString();
        MDCUtil.setTraceId(traceId);
        String today = DateUtil.today();

        List<SaleSchedule> list = saleScheduleMapper.selectListOverdue(today);
        if (CollectionUtil.isEmpty(list)) {
            log.info("当天需要扣款逾期赊销账单为空");
            return;
        }

        list.forEach(li -> {
            String traceId1 = MDCUtil.generateTraceId();
            log.info("当天需要扣款逾期赊销账单 id:{} traceId：{}",li.getId(),traceId1);

            SaleDto saleDto = new SaleDto();
            saleDto.setDisburseId(li.getDisburseId());
            saleDto.setTerm(li.getRepayTerm());
            saleDto.setTraceId(traceId1);
            saleDto.setRepayType(3);
            rabbitTemplate.convertAndSend(QueueConstant.SALE_QUEUE, JSONObject.toJSONString(saleDto));
        });

    }
}
