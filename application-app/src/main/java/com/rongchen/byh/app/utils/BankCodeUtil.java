package com.rongchen.byh.app.utils;

import java.util.HashMap;
import java.util.Map;

public class BankCodeUtil {
    
    private BankCodeUtil() {}
    
    private static final Map<String,String> map = new HashMap<>();
    
    static {
        map.put("BOC","中国银行");
        map.put("ABC","中国农业银行");
        map.put("ICBC","中国工商银行");
        map.put("CCB","中国建设银行");
        map.put("COMM","交通银行");
        map.put("CMB","招商银行");
        map.put("PSBC","中国邮政储蓄银行");
        map.put("SNB","苏宁银行");
        map.put("SPDB","上海浦东发展银行");
        map.put("CEB","中国光大银行");
        map.put("CITIC","中信银行");
        map.put("CGB","广东发展银行");
        map.put("CMBC","中国民生银行");
        map.put("HXB","华夏银行");
        map.put("PAB","平安银行");
        map.put("CIB","兴业银行");
        map.put("NJCB","南京银行");
        map.put("JSBC","江苏银行");
        map.put("BOSH","上海银行");
        map.put("JSRCU","江苏·农村商业银行");
        map.put("BJB","北京银行");
        map.put("NBCB","宁波银行");
        map.put("HSB","徽商银行");
        map.put("HBNX","湖北省农村信用社");
        map.put("HNRCC","河南省农村信用社");
        map.put("CQRCB","重庆农村商业银行");
        map.put("GDRCC","广东农村信用社");
        map.put("YNRCC","云南省农村信用社");
        map.put("HZCB","杭州银行");
        map.put("FJRCC","福建省农村信用社");
        map.put("CZB","浙商银行");
        map.put("GRCB","广州农村商业银行");
        map.put("HRBCB","哈尔滨银行");
        map.put("BOCD1","成都银行");
        map.put("HNNXS","湖南农村信用社");
        map.put("JNRCB","江南农商银行");
        map.put("GLB","桂林银行");
        map.put("SRCB","上海农商行");
        map.put("TJRCB","天津农商银行");
        map.put("BOJX","江西银行");
        map.put("HBRCC","河北省农村信用社");
        map.put("BJRCB","北京农村商业银行");
        map.put("JLRCC","吉林省农村信用联合社");
        map.put("QDCCB","青岛银行");
        map.put("GYCB","贵阳银行");
        map.put("BSB","包商银行");
        map.put("JXRCC","江西省农村信用社");
        map.put("SXRCB","陕西省农村信用社");
        map.put("CDRCB","成都农村商业银行");
        map.put("JRCB","江阴市农村商业银行");
        map.put("WRCB","无锡农村商业银行");
        map.put("RCCOSX","山西省农村信用社");
        map.put("DRCB","东莞农村商业银行");
        map.put("CSCB","长沙银行");
        map.put("HKBEA","东亚银行");
        map.put("SJB","盛京银行");
        map.put("ZYB","中原银行");
        map.put("BOGZ","贵州银行");
        map.put("BOZZ","郑州银行");
        map.put("HKB","汉口银行");
        map.put("XAB","西安银行");
        map.put("YZB","鄞州银行");
        map.put("LZYH","兰州银行");
        map.put("LJB","龙江银行");
        map.put("GSRCC","甘肃省农村信用社");
        map.put("HEBB","河北银行");
        map.put("BOJ","吉林银行");
        map.put("CAB","长安银行");
        map.put("EGB","恒丰银行");
        map.put("HNNX","海南省农村信用联合社");
        map.put("CZCB","浙江稠州商业银行");
        map.put("GBGB","广西北部湾银行");
        map.put("CQCB","重庆银行");
        map.put("CBHB","渤海银行");
        map.put("HLJRCC","黑龙江省农村信用社");
        map.put("GDNY","广东南粤银行");
        map.put("ARCU","安徽省农村信用联社");
        map.put("LNRCC","辽宁省农村信用社");
        map.put("WHRCB","武汉农村商业银行");
        map.put("CSRCB","常熟农村商业银行");
        map.put("JJBANK","九江银行");
        map.put("BOGA","甘肃银行");
        map.put("SZRCB","深圳农村商业银行");
        map.put("ZJKCCB","张家口银行");
        map.put("HBC","湖北银行");
        map.put("SCTFB","四川天府银行");
        map.put("BODY","长城华西银行");
        map.put("BOIM","内蒙古银行");
        map.put("ZJTLCB","浙江泰隆商业银行");
        map.put("KSRCB","昆山农村商业银行");
        map.put("BODG","东莞银行");
        map.put("CJCCB","江苏长江商业银行");
        map.put("TCRCB","太仓农村商业银行");
        map.put("RCBOZ","张家港农村商业银行");
        map.put("FDB","富滇银行");
        map.put("XMCCB","厦门银行");
        map.put("BOYK","营口银行");
        map.put("ORDOSB","鄂尔多斯银行");
        map.put("BOHZ","湖州银行");
        map.put("XTB","邢台银行");
        map.put("WEBANK","深圳前海微众银行");
        map.put("NXHHRCB","宁夏黄河农村商业银行");
        map.put("BOSZS","石嘴山银行");
        map.put("TJBHRCB","天津滨海农商银行");
        map.put("CRBZ","珠海华润银行");
        map.put("BOLY","洛阳银行");
        map.put("QSB","齐商银行");
        map.put("QZCCB","泉州银行");
        map.put("IBOK","企业银行");
        map.put("BOJS","晋商银行");
        map.put("BOSZ","苏州银行");
        map.put("BOBD","保定银行");
        map.put("XJRCC","新疆维吾尔自治区农村信用社联合社");
        map.put("JZCTS","焦作中旅银行");
        map.put("TLBANK","铁岭银行");
        map.put("HSBANK","衡水银行");
        map.put("ZGCCB","自贡市商业银行");
        map.put("YNHTB","云南红塔银行");
        map.put("QHRCC","青海省农村信用社联合社");
        map.put("HZUB","杭州联合银行");
        map.put("BOWH","乌海银行");
        map.put("HYB","韩亚银行");
        map.put("QLTB","青隆村镇银行");
        map.put("PJCB","盘锦银行");
        map.put("XJHHB","新疆汇和银行");
        map.put("YQCB","阳泉市商业银行");
        map.put("BOCFCB","中银富登村镇银行");
        map.put("WBC","友利银行");
        map.put("LSZCB","凉山州商业银行");
        map.put("CZCB1","长治商业银行");
        map.put("JCB","晋城银行");
        map.put("JTHJRB","金堂汇金村镇银行");
        map.put("GXRCU","广西省联社");
        map.put("DTCB","大同银行");
        map.put("IMNX","内蒙古自治区农村信用社");
        map.put("SNBEA","苏宁银行电子账户");
        map.put("HRRCB","和润村镇银行");
        map.put("SCNX","四川省联社");
        map.put("GZB","广州银行");
        map.put("GZRCC","贵州农村信用社");
    }

    public static String getBankName(String bankCode) {
        return map.get(bankCode);
    }
}
