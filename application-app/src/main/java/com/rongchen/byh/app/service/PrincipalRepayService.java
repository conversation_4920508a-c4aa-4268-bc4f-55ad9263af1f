package com.rongchen.byh.app.service;


import com.rongchen.byh.app.dto.app.BillRepayApplyDto;
import com.rongchen.byh.common.core.object.ResponseResult;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/12/15 16:11:48
 */
public interface PrincipalRepayService {
    ResponseResult<Void> principalRepay(BillRepayApplyDto billRepayApplyDto);

    /**
     * 本息还款
     * @date 2025/1/10 10:18
     *
     * @param billRepayApplyDto
     * @return com.rongchen.byh.common.core.object.ResponseResult<java.lang.Void>
     */
    ResponseResult<Void> loanRepay(BillRepayApplyDto billRepayApplyDto);

}
