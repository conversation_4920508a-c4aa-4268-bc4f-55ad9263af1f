package com.rongchen.byh.app.v2.controller.apply;


import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.rongchen.byh.app.dto.app.FaceVerifyDto;
import com.rongchen.byh.app.dto.app.IdCardVerifyDto;
import com.rongchen.byh.app.dto.app.UserFormDto;
import com.rongchen.byh.app.dto.h5.UserCheckLoanApplyDto;
import com.rongchen.byh.app.utils.UserTokenUtil;
import com.rongchen.byh.app.v2.dao.ProductDataMapper;
import com.rongchen.byh.app.v2.entity.ProductData;
import com.rongchen.byh.app.v2.handle.product.ProductHandleCenter;
import com.rongchen.byh.app.v2.handle.product.ProductHandleFactory;
import com.rongchen.byh.app.vo.app.SubmitUserFormVo;
import com.rongchen.byh.app.vo.h5.UserCheckLoanApplyVo;
import com.rongchen.byh.common.api.idCardVerify.vo.OcrVo;
import com.rongchen.byh.common.core.annotation.DisableDataFilter;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@ApiSupport(order = 2)
@Tag(name = "初筛授信相关接口")
@DisableDataFilter
@Slf4j
@RestController
@RequestMapping("/v2/credit")
@RequiredArgsConstructor
public class CreditController {
    private final ProductDataMapper productDataMapper;

    @Operation(summary = "提交身份证姓名二要素数据")
    @PostMapping("/submitTwoElementsData")
    public ResponseResult<UserCheckLoanApplyVo> submitTwoElementsData(@RequestBody @Validated UserCheckLoanApplyDto userCheckLoanApplyDto) {
        ProductData productData = productDataMapper.selectById(UserTokenUtil.getProductId());
        if (ObjectUtil.isEmpty(productData)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "产品不存在");
        }
        ProductHandleFactory productHandle = ProductHandleCenter.getProductHandle(productData.getBeanName());
        return productHandle.submitTwoElementsData(userCheckLoanApplyDto);
    }

    @Operation(summary = "提交ocr数据")
    @PostMapping("/submitOcrData")
    public ResponseResult<OcrVo> submitOcrData(@RequestBody @Validated IdCardVerifyDto verifyDto, HttpServletRequest request) {
        ProductData productData = productDataMapper.selectById(UserTokenUtil.getProductId());
        if (ObjectUtil.isEmpty(productData)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "产品不存在");
        }
        ProductHandleFactory productHandle = ProductHandleCenter.getProductHandle(productData.getBeanName());
        return productHandle.submitOcrData(verifyDto,request);
    }

    @Operation(summary = "提交人脸数据")
    @PostMapping("/submitFaceData")
    public ResponseResult<Void> submitFaceData(@RequestBody FaceVerifyDto verifyDto) {
        ProductData productData = productDataMapper.selectById(UserTokenUtil.getProductId());
        if (ObjectUtil.isEmpty(productData)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "产品不存在");
        }
        ProductHandleFactory productHandle = ProductHandleCenter.getProductHandle(productData.getBeanName());
        return productHandle.submitFaceData(verifyDto);
    }

    @Operation(summary = "提交详细信息表单")
    @PostMapping("/submitUserForm")
    public ResponseResult<SubmitUserFormVo> submitUserForm(@RequestBody UserFormDto dto) {
        ProductData productData = productDataMapper.selectById(UserTokenUtil.getProductId());
        if (ObjectUtil.isEmpty(productData)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "产品不存在");
        }
        ProductHandleFactory productHandle = ProductHandleCenter.getProductHandle(productData.getBeanName());
        return productHandle.submitUserForm(dto);
    }




    @Operation(summary = "查询初筛结果")
    @PostMapping("/queryLoanResult")
    public ResponseResult<UserCheckLoanApplyVo> queryLoanResult() {
        ProductData productData = productDataMapper.selectById(UserTokenUtil.getProductId());
        if (ObjectUtil.isEmpty(productData)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "产品不存在");
        }
        ProductHandleFactory productHandle = ProductHandleCenter.getProductHandle(productData.getBeanName());
        return productHandle.queryLoanResult(null);
    }


}
