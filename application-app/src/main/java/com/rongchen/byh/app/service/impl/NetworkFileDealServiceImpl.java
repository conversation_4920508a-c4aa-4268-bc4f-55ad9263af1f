package com.rongchen.byh.app.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.ancun.netsign.client.NetSignClient;
import com.ancun.netsign.model.ApiRespBody;
import com.ancun.netsign.model.DownloadContractOutput;
import com.rongchen.byh.app.dao.UserLoveLogMapper;
import com.rongchen.byh.app.entity.UserDetail;
import com.rongchen.byh.app.entity.UserLoveLog;
import com.rongchen.byh.app.service.NetworkFileDealService;
import com.rongchen.byh.app.utils.OssUtil;
import com.rongchen.byh.common.api.bankCredit.dto.HrzxAuthDto;
import com.rongchen.byh.common.core.util.HttpUtil;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadLocalRandom;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.1
 * @description 网络文件处理服务实现，已优化路径处理和日志记录。
 * @date 2025/2/24 12:26:38
 */
@Service
@Slf4j
public class NetworkFileDealServiceImpl implements NetworkFileDealService {

    @Resource
    private NetSignClient client;
    @Resource
    private UserLoveLogMapper userLoveLogMapper;
    @Resource
    private OssUtil ossUtil;
    private static final String DATE_FORMAT_PATTERN = "yyyyMM/dd"; // 日期格式模式
    public static final String DIR_PATH = "/usr/local/file";
    @Resource(name = "taskExecutor") // 注入自定义的线程池 Bean 用于执行异步任务
    private Executor fileTaskExecutor; // 文件处理任务执行器

    // 返回完整的目录路径字符串
    public String getDownloadDirPath(String dirName) {
        String date = DateUtil.format(new Date(), DATE_FORMAT_PATTERN);
        // 使用 Paths.get 实现健壮的路径构建
        Path fullPath = Paths.get(DIR_PATH, date, dirName);
        log.info("【路径构建】请求目录名: {}, 构建日期: {}, 完整路径: {}", dirName, date, fullPath);
        return fullPath.toString();
    }

    // 修改为接受 Path 对象
    public CompletableFuture<Void> downloadTask(String url, Path targetPath, List<String> list) {
        // 确保父目录存在
        // 假设 HttpUtil.downloadFile 分别接收目录路径和文件名
        // 记录更详细的上下文日志
        // 失败时将 URL 添加到列表
        // 使用注入的执行器
        return CompletableFuture.runAsync(() -> {
            try {
                // 确保父目录存在
                Files.createDirectories(targetPath.getParent());
                // 假设 HttpUtil.downloadFile 分别接收目录路径和文件名
                HttpUtil.downloadFile(url, targetPath.getParent().toString(), targetPath.getFileName().toString());
                log.info("成功从 {} 下载文件到 {}", url, targetPath);
            } catch (Exception e) {
                // 记录更详细的上下文日志
                log.error("从 URL: {} 下载文件到路径: {} 失败", url, targetPath, e);
                list.add(url); // 失败时将 URL 添加到列表
            }
        }, fileTaskExecutor);
    }

    @Override
    public boolean dealFaceImage(UserDetail userDetail, HrzxAuthDto hrzxAuthDto) {
        log.info("【处理人脸图像】方法启动，用户ID: {}", userDetail.getUserId());
        log.info("【处理人脸图像】开始处理，用户ID: {}, 人脸URL: {}, 身份证正面URL: {}, 身份证反面URL: {}",
                userDetail.getUserId(), userDetail.getFaceUrl(), userDetail.getIdCardFrondUrl(),
                userDetail.getIdCardReverseUrl());

        String frondUrl = userDetail.getIdCardFrondUrl();
        String reverseUrl = userDetail.getIdCardReverseUrl();
        String faceUrl = userDetail.getFaceUrl();

        // 检查URL是否为空
        if (StrUtil.isEmpty(frondUrl) || StrUtil.isEmpty(reverseUrl) || StrUtil.isEmpty(faceUrl)) {
            log.error("【处理人脸图像】URL为空，用户ID: {}, 正面URL: {}, 反面URL: {}, 人脸URL: {}",
                    userDetail.getUserId(), frondUrl, reverseUrl, faceUrl);
            return true;
        }

        // 构建目标路径
        String targetPath = Paths.get("/tmp", "qyc", "temp", String.valueOf(userDetail.getUserId())).toString();
        String frondPath = Paths.get(targetPath, "frond.jpg").toString();
        String reversePath = Paths.get(targetPath, "reverse.jpg").toString();
        String facePath = Paths.get(targetPath, "face.jpg").toString();
        log.info("【处理人脸图像】 用户ID: {} 人脸目录: {} 身份证正面目录: {} 身份证反面目录: {}", userDetail.getUserId(), facePath, frondPath,
                reversePath);
        // 创建目录
        try {
            Path targetDirectoryPath = Paths.get(targetPath);
            log.info("【处理人脸图像】准备创建目录: {}，用户ID: {}", targetDirectoryPath, userDetail.getUserId());
            Files.createDirectories(targetDirectoryPath);
        } catch (IOException e) {
            log.error("【处理人脸图像】创建目录失败，用户ID: {}, 目标路径: {}", userDetail.getUserId(), targetPath, e);
            return true;
        }

        // 下载文件
        List<String> failList = new ArrayList<>();
        log.info("【处理人脸图像】准备下载身份证正面，URL: {}, 目标路径: {}, 用户ID: {}", frondUrl, frondPath, userDetail.getUserId());
        CompletableFuture<Void> frondFuture = downloadTask(frondUrl, frondPath, failList);
        log.info("【处理人脸图像】准备下载身份证反面，URL: {}, 目标路径: {}, 用户ID: {}", reverseUrl, reversePath, userDetail.getUserId());
        CompletableFuture<Void> reverseFuture = downloadTask(reverseUrl, reversePath, failList);
        log.info("【处理人脸图像】准备下载人脸图像，URL: {}, 目标路径: {}, 用户ID: {}", faceUrl, facePath, userDetail.getUserId());
        CompletableFuture<Void> faceFuture = downloadTask(faceUrl, facePath, failList);

        CompletableFuture.allOf(frondFuture, reverseFuture, faceFuture).join();

        // 检查下载结果
        if (!failList.isEmpty()) {
            log.error("【处理人脸图像】下载失败，用户ID: {}, 失败URL列表: {}", userDetail.getUserId(), failList);
            return true;
        }

        // 检查文件是否存在且有效
        File frondFile = new File(frondPath);
        File reverseFile = new File(reversePath);
        File faceFile = new File(facePath);

        if (!frondFile.exists() || !reverseFile.exists() || !faceFile.exists()) {
            log.error("【处理人脸图像】文件不存在，用户ID: {}, 正面文件存在: {}, 反面文件存在: {}, 人脸文件存在: {}",
                    userDetail.getUserId(), frondFile.exists(), reverseFile.exists(), faceFile.exists());
            return true;
        }

        if (frondFile.length() == 0 || reverseFile.length() == 0 || faceFile.length() == 0) {
            log.error("【处理人脸图像】文件大小为0，用户ID: {}, 正面文件大小: {}, 反面文件大小: {}, 人脸文件大小: {}",
                    userDetail.getUserId(), frondFile.length(), reverseFile.length(), faceFile.length());
            return true;
        }
        log.info("【处理人脸图像】 用户ID: {} 人脸: {} 身份证正面: {} 身份证反面: {}", userDetail.getUserId(), facePath, frondPath,
                reversePath);
        // 设置文件路径
        hrzxAuthDto.setFrontIdCardFile(frondPath);
        hrzxAuthDto.setBackIdCardFile(reversePath);
        hrzxAuthDto.setOtherAuthFile(facePath);

        log.info("【处理人脸图像】处理完成，用户ID: {}, 正面文件: {}, 反面文件: {}, 人脸文件: {}",
                userDetail.getUserId(), frondPath, reversePath, facePath);
        log.info("【处理人脸图像】方法成功结束，用户ID: {}", userDetail.getUserId());
        return false;
    }

    private CompletableFuture<Void> downloadTask(String url, String targetPath, List<String> failList) {
        return CompletableFuture.runAsync(() -> {
            log.info("【异步下载任务】启动，URL: {}, 目标路径: {}", url, targetPath);
            boolean success = false;
            int maxRetries = 3; // 1初步尝试 + 2恢复
            for (int attempt = 1; attempt <= maxRetries; attempt++) {
                try {
                    if (attempt > 1) {
                        // 50至300毫秒之间的随机延迟
                        long delay = ThreadLocalRandom.current().nextLong(50, 300);
                        log.info("【处理人脸图像】下载失败，尝试第 {} 次重试，URL: {}, 等待 {} ms", attempt, url, delay);
                        Thread.sleep(delay);
                    } else {
                        log.info("【处理人脸图像】开始第 {} 次下载文件，URL: {}, 目标路径: {}", attempt, url, targetPath);
                    }

                    File file = new File(targetPath);
                    if (file.exists()) {
                        file.delete();
                    }
                    FileUtils.copyURLToFile(new URL(url), file);

                    // 下载后检查文件大小
                    if (file.length() == 0) {
                        log.warn("【处理人脸图像】下载成功但文件大小为0，URL: {}, 目标路径: {}, 尝试次数: {}", url, targetPath, attempt);
                        // 将尺寸0视为重试的失败
                        throw new IOException("Downloaded file size is zero");
                    }

                    log.info("【处理人脸图像】下载完成，尝试次数: {}, URL: {}, 目标路径: {}, 文件大小: {}",
                            attempt, url, targetPath, file.length());
                    success = true;
                    break; // 下载成功，退出重试循环
                } catch (Exception e) {
                    log.warn("【处理人脸图像】第 {} 次下载失败，URL: {}, 目标路径: {}. 原因: {}", attempt, url, targetPath, e.getMessage());
                    if (attempt == maxRetries) {
                        log.error("【处理人脸图像】下载最终失败 ({} 次尝试)，URL: {}, 目标路径: {}", maxRetries, url, targetPath, e);
                    }
                }
            }

            if (!success) {
                failList.add(url);
            }
        });
    }

    @Override
    public boolean dealProtocolFile(UserDetail detail, HrzxAuthDto hrzxAuthDto) {
        Long userId = detail.getUserId();
        log.info("【处理协议文件】方法启动，用户ID: {}", userId);
        UserLoveLog userPersonLoveLog = userLoveLogMapper.getByUserIdAndContractName(userId, "征信查询授权书");
        UserLoveLog userApplyLoveLog = userLoveLogMapper.getByUserIdAndContractName(userId, "委托担保申请书");

        if (ObjectUtil.isEmpty(userPersonLoveLog) || ObjectUtil.isEmpty(userApplyLoveLog)) {
            log.error("用户ID: {} 的协议文件记录未找到。征信查询授权书找到: {}, 委托担保申请书找到: {}",
                    userId, userPersonLoveLog != null, userApplyLoveLog != null);
            return true;
        }
        // 记录查找到的合同号
        log.info("【处理协议文件】用户ID: {}, 找到征信授权书记录，合同号: {}，找到担保申请书记录，合同号: {}",
                userId,
                userPersonLoveLog != null ? userPersonLoveLog.getContractNo() : "未找到",
                userApplyLoveLog != null ? userApplyLoveLog.getContractNo() : "未找到");

        try {
            // 使用 Paths 构建申请文件路径
            String applyFileName = detail.getAppName() + "_委托担保申请书.pdf";
            Path applyFilePath = Paths.get(getDownloadDirPath("apply"), applyFileName);
            // 使用 Files.createDirectories 创建目录
            log.info("【处理协议文件】准备创建申请文件目录: {}，用户ID: {}", applyFilePath.getParent(), userId);
            Files.createDirectories(applyFilePath.getParent());

            log.info("尝试下载申请合同 {} 到 {}", userApplyLoveLog.getContractNo(), applyFilePath);
            log.info("【处理协议文件】准备调用 client.downloadContract 下载申请合同，合同号: {}, 目标路径: {}, 用户ID: {}",
                    userApplyLoveLog.getContractNo(), applyFilePath, userId);
            ApiRespBody<DownloadContractOutput> respBody = client.downloadContract(userApplyLoveLog.getContractNo(),
                    applyFilePath.toString()); // 将路径作为字符串传递

            if (respBody.getCode() != 100000) {
                log.error("下载用户ID: {} 的申请合同失败，合同号: {}。原因: {}",
                        userId, userApplyLoveLog.getContractNo(), respBody.getMsg());
                return true;
            }
            hrzxAuthDto.setOtherFile(applyFilePath.toString());
            log.info("成功下载用户ID: {} 的申请合同，合同号: {}", userId, userApplyLoveLog.getContractNo());

            // 使用 Paths 构建 CA 文件路径
            String caFileName = detail.getAppName() + "_征信查询授权书.pdf";
            Path caFilePath = Paths.get(getDownloadDirPath("credit"), caFileName);
            // 使用 Files.createDirectories 创建目录
            log.info("【处理协议文件】准备创建 CA 文件目录: {}，用户ID: {}", caFilePath.getParent(), userId);
            Files.createDirectories(caFilePath.getParent());

            log.info("尝试下载 CA 合同 {} 到 {}", userPersonLoveLog.getContractNo(), caFilePath);
            log.info("【处理协议文件】准备调用 client.downloadContract 下载 CA 合同，合同号: {}, 目标路径: {}, 用户ID: {}",
                    userPersonLoveLog.getContractNo(), caFilePath, userId);
            ApiRespBody<DownloadContractOutput> caRespBody = client.downloadContract(userPersonLoveLog.getContractNo(),
                    caFilePath.toString()); // 将路径作为字符串传递

            if (caRespBody.getCode() != 100000) {
                // 对 CA 下载错误使用 caRespBody.getMsg()
                log.error("下载用户ID: {} 的 CA 合同失败，合同号: {}。原因: {}",
                        userId, userPersonLoveLog.getContractNo(), caRespBody.getMsg());
                return true;
            }
            hrzxAuthDto.setCaFile(caFilePath.toString());
            log.info("成功下载用户ID: {} 的 CA 合同，合同号: {}", userId, userPersonLoveLog.getContractNo());

        } catch (IOException e) {
            log.error("为用户ID: {} 创建目录或处理文件时发生 IOException", userId, e);
            return true;
        } catch (Exception e) {
            log.error("为用户ID: {} 下载协议文件期间发生意外错误", userId, e);
            return true;
        }

        log.info("【处理协议文件】方法成功结束，用户ID: {}", userId);
        return false; // 表示成功
    }

    @Override
    public boolean checkFaceImage(UserDetail detail, HrzxAuthDto hrzxAuthDto) {
        throw new RuntimeException("人脸图像检查失败");
    }

    @Override
    public boolean checkProtocolFile(UserDetail detail, HrzxAuthDto hrzxAuthDto) {
        throw new RuntimeException("协议文件检查失败");

    }

}
