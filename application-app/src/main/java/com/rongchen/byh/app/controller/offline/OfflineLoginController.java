package com.rongchen.byh.app.controller.offline;

import cn.dev33.satoken.annotation.SaIgnore;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.rongchen.byh.app.dto.OfflineRegOrLoginDto;
import com.rongchen.byh.app.dto.RegOrLoginDto;
import com.rongchen.byh.app.service.LoginService;
import com.rongchen.byh.app.vo.RegOrLoginVo;
import com.rongchen.byh.common.core.annotation.DisableDataFilter;
import com.rongchen.byh.common.core.object.ResponseResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 线下用户登录注册相关接口
 * @date 2025/2/7 11:07:17
 */
@ApiSupport(order = 1)
@Tag(name = "线下用户登录注册相关接口")
@DisableDataFilter
@Slf4j
@RestController
@RequestMapping("/userApi/offline/user")
public class OfflineLoginController {
    @Resource
    private LoginService loginService;

    @Operation(summary = "用户注册或登录")
    @SaIgnore
    @PostMapping("/regOrLogin")
    public ResponseResult<RegOrLoginVo> regOrLogin(@RequestBody @Validated OfflineRegOrLoginDto dto) {
        return loginService.offlineRegOrLogin(dto);
    }

    @Operation(summary = "校验邀请码是否正确")
    @SaIgnore
    @PostMapping("/checkInviteCode")
    public ResponseResult<Void> checkInviteCode(@RequestBody OfflineRegOrLoginDto dto) {
        return loginService.checkInviteCode(dto);
    }

}
