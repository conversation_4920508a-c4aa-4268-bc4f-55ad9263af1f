package com.rongchen.byh.app.service;

import com.rongchen.byh.app.dto.h5.UserCheckLoanApplyDto;
import com.rongchen.byh.app.vo.h5.UserCheckLoanApplyVo;
import com.rongchen.byh.common.core.object.ResponseResult;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 用户初筛相关业务
 * @date 2024/12/11 10:26:55
 */
public interface UserCheckLoanService {
    ResponseResult<UserCheckLoanApplyVo> apply(UserCheckLoanApplyDto userCheckLoanApplyDto);

    ResponseResult<UserCheckLoanApplyVo> queryResult();

    ResponseResult<UserCheckLoanApplyVo> flyApply(UserCheckLoanApplyDto userCheckLoanApplyDto);

    ResponseResult<UserCheckLoanApplyVo> flyQueryResult();

    ResponseResult<Void> applyNew();
}
