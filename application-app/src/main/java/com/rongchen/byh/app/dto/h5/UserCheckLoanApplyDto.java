package com.rongchen.byh.app.dto.h5;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 用户初筛贷款申请dto
 * @date 2024/12/11 09:58:44
 */
@Data
public class UserCheckLoanApplyDto {
    @Schema(description = "用户姓名")
    @NotEmpty(message = "用户姓名不能为空")
    private String userName;

    @Schema(description = "身份证号")
    @NotEmpty(message = "身份证号不能为空")
    private String idNumber;
}
