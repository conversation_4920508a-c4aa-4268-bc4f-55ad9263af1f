package com.rongchen.byh.app.v2.controller.login;


import cn.dev33.satoken.annotation.SaIgnore;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.rongchen.byh.app.dto.YzmCodeDto;
import com.rongchen.byh.app.dto.app.AppLoginDto;
import com.rongchen.byh.app.v2.dto.LoginH5Dto;
import com.rongchen.byh.app.v2.service.ILoginV2Service;
import com.rongchen.byh.app.vo.RegOrLoginVo;
import com.rongchen.byh.app.vo.app.RegisterUserVo;
import com.rongchen.byh.common.core.annotation.DisableDataFilter;
import com.rongchen.byh.common.core.object.ResponseResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@ApiSupport(order = 1)
@Tag(name = "用户登录注册相关接口")
@DisableDataFilter
@Slf4j
@RestController
@RequestMapping("/v2/userApi")
@RequiredArgsConstructor
public class LoginV2Controller {
    private final ILoginV2Service loginService;

    @Operation(summary = "h5用户注册或登录")
    @SaIgnore
    @PostMapping("/loginByH5")
    public ResponseResult<RegOrLoginVo> loginByH5(@RequestBody @Validated LoginH5Dto loginH5Dto) {
        return loginService.loginByH5(loginH5Dto);
    }

    @Operation(summary = "短信验证码获取")
    @SaIgnore
    @PostMapping("/sendCode")
    public ResponseResult<Void> sendCode(@RequestBody @Validated YzmCodeDto yzmCodeDto) {
        return loginService.sendCode(yzmCodeDto);
    }

    @Operation(summary = "校验邀请码是否正确")
    @SaIgnore
    @PostMapping("/checkInviteCode")
    public ResponseResult<Void> checkInviteCode(@RequestBody LoginH5Dto dto) {
        return loginService.checkInviteCode(dto);
    }

    @Operation(summary = "APP用户登录")
    @PostMapping("/loginByApp")
    @SaIgnore
    public ResponseResult<RegisterUserVo> loginByApp(@RequestBody AppLoginDto appLoginDto){
        return loginService.loginByApp(appLoginDto);
    }

}
