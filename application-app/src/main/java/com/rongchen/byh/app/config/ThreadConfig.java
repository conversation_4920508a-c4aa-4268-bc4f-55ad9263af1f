package com.rongchen.byh.app.config;
import java.util.concurrent.*;

import org.springframework.context.annotation.Bean;
public class ThreadConfig {


    public static ThreadPoolExecutor threadPoolExecutor() {
        int processors = Runtime.getRuntime().availableProcessors();
        int threadCount = Math.max(1, processors - 1);
        return new ThreadPoolExecutor(
                //核心线程数
                threadCount,
                //最大线程数
                threadCount,
                //线程持续时间
                60L,
                //时间单位：秒
                TimeUnit.SECONDS,
                //线程队列
                new ArrayBlockingQueue<>(100),
                //线程工厂
                Executors.defaultThreadFactory(),
                //拒绝策略
                new ThreadPoolExecutor.AbortPolicy()
        );
    }
}
