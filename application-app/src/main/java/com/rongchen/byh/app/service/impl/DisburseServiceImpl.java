package com.rongchen.byh.app.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rongchen.byh.app.config.ZifangFactory;
import com.rongchen.byh.app.constant.LoanType;
import com.rongchen.byh.app.constant.SourceMode;
import com.rongchen.byh.app.constant.ZiFangBeanConstant;
import com.rongchen.byh.app.dao.CapitalDataMapper;
import com.rongchen.byh.app.dao.DisburseDataMapper;
import com.rongchen.byh.app.dao.HrzxCreditLogMapper;
import com.rongchen.byh.app.dao.RepayScheduleMapper;
import com.rongchen.byh.app.dao.UserBankCardMapper;
import com.rongchen.byh.app.dao.UserCapitalRecordMapper;
import com.rongchen.byh.app.dao.UserCreditDataMapper;
import com.rongchen.byh.app.dao.UserDataMapper;
import com.rongchen.byh.app.dao.UserDetailMapper;
import com.rongchen.byh.app.dao.UserLoanApplyMapper;
import com.rongchen.byh.app.dao.UserLoveLogMapper;
import com.rongchen.byh.app.dao.UserRegisterResultMapper;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rongchen.byh.app.constant.LoanType;
import com.rongchen.byh.app.constant.SourceMode;
import com.rongchen.byh.app.dao.*;
import com.rongchen.byh.app.dto.app.BackVo;
import com.rongchen.byh.app.dto.app.LoanApplyDto;
import com.rongchen.byh.app.dto.app.TrialPaymentDto;
import com.rongchen.byh.app.entity.CapitalData;
import com.rongchen.byh.app.entity.DisburseData;
import com.rongchen.byh.app.entity.HrzxCreditLog;
import com.rongchen.byh.app.entity.RepaySchedule;
import com.rongchen.byh.app.entity.SaleOrderRecord;
import com.rongchen.byh.app.entity.UserBankCard;
import com.rongchen.byh.app.entity.UserCapitalRecord;
import com.rongchen.byh.app.entity.UserCreditData;
import com.rongchen.byh.app.entity.UserData;
import com.rongchen.byh.app.entity.UserDetail;
import com.rongchen.byh.app.entity.UserLoanApply;
import com.rongchen.byh.app.entity.UserLoveLog;
import com.rongchen.byh.app.entity.UserRegisterResult;
import com.rongchen.byh.app.loveSign.LoveSignProperties;
import com.rongchen.byh.app.loveSign.LoveSignService;
import com.rongchen.byh.app.loveSign.SignDto;
import com.rongchen.byh.app.loveSign.SignRulesDto;
import com.rongchen.byh.app.service.CreditService;
import com.rongchen.byh.app.service.DisburseService;
import com.rongchen.byh.app.service.NetworkFileDealService;
import com.rongchen.byh.app.service.RepayPlanUpdateService;
import com.rongchen.byh.app.service.SaleOrderRecordService;
import com.rongchen.byh.app.utils.CreditLoanNoUtils;
import com.rongchen.byh.app.utils.OssUtil;
import com.rongchen.byh.app.utils.UserTokenUtil;
import com.rongchen.byh.app.vo.app.DisburseAmountVo;
import com.rongchen.byh.app.vo.app.DisburseStatusVo;
import com.rongchen.byh.app.vo.app.WebSaleUrlVo;
import com.rongchen.byh.common.api.bankCredit.dto.HrzxAuthDto;
import com.rongchen.byh.common.api.baofu.adapter.BaofuAdapterService;
import com.rongchen.byh.common.api.riskControl.dto.PreLoanAuditAppDto;
import com.rongchen.byh.common.api.riskControl.dto.PreLoanAuditAppRelationsDto;
import com.rongchen.byh.common.api.riskControl.service.RiskControlService;
import com.rongchen.byh.common.api.riskControl.vo.PreLoanAuditVo;
import com.rongchen.byh.common.api.zifang.dto.BindBankSmsDto;
import com.rongchen.byh.common.api.zifang.dto.CreditAuditDto;
import com.rongchen.byh.common.api.zifang.dto.HfUrlDto;
import com.rongchen.byh.common.api.zifang.dto.LoanElementDto;
import com.rongchen.byh.common.api.zifang.dto.QueryBindBankResultDto;
import com.rongchen.byh.common.api.zifang.dto.RepayPlanCalcDto;
import com.rongchen.byh.common.api.zifang.dto.VerifyBindBankSmsDto;
import com.rongchen.byh.common.api.zifang.service.BindCardApi;
import com.rongchen.byh.common.api.zifang.service.ContractApi;
import com.rongchen.byh.common.api.zifang.service.LoanApi;
import com.rongchen.byh.common.api.zifang.service.OtherApi;
import com.rongchen.byh.common.api.zifang.utils.BackNameUtil;
import com.rongchen.byh.common.api.zifang.utils.CityCodeUtil;
import com.rongchen.byh.common.api.zifang.utils.IdCardUtil;
import com.rongchen.byh.common.api.zifang.vo.BindBankSmsVo;
import com.rongchen.byh.common.api.zifang.vo.CreditApplyVo;
import com.rongchen.byh.common.api.zifang.vo.HfUrlVo;
import com.rongchen.byh.common.api.zifang.vo.LoanElementVo;
import com.rongchen.byh.common.api.zifang.vo.QueryBindBankResultVo;
import com.rongchen.byh.common.api.zifang.vo.RepayPlanCalcVo;
import com.rongchen.byh.common.api.zifang.vo.VerifyBindBankSmsVo;
import com.rongchen.byh.common.api.zifang.vo.loanElement.PayChannel;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.constant.SettleFlagConstant;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.MyModelUtil;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName DisburseServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/12 16:09
 * @Version 1.0
 **/
@Slf4j
@Service
public class DisburseServiceImpl implements DisburseService {

    @Resource
    UserBankCardMapper userBankCardMapper;
    @Resource
    LoanApi loanApi;
    @Resource
    ContractApi contractApi;
    @Resource
    BindCardApi bindCardApi;
    @Resource
    DisburseDataMapper disburseDataMapper;
    @Resource
    CreditService creditService;
    @Resource
    UserCreditDataMapper userCreditDataMapper;
    @Resource
    OtherApi otherApi;
    @Resource
    NetworkFileDealService networkFileDealService;

    @Resource
    UserDetailMapper userDetailMapper;
    @Resource
    UserDataMapper userDataMapper;
    @Resource
    HrzxCreditLogMapper hrzxCreditLogMapper;
    @Resource
    RedissonClient redissonClient;
    @Resource
    RiskControlService riskControlService;
    @Resource
    UserLoanApplyMapper userLoanApplyMapper;
    @Resource
    ZifangFactory zifangFactory;
    @Resource
    UserCapitalRecordMapper userCapitalRecordMapper;
    @Resource
    CapitalDataMapper capitalDataMapper;
    @Resource
    UserRegisterResultMapper userRegisterResultMapper;
    @Resource
    private SaleOrderRecordService saleOrderRecordService;
    @Resource
    LoveSignService loveSignService;
    @Resource
    LoveSignProperties loveSignProperties;
    @Resource
    UserLoveLogMapper userLoveLogMapper;
    @Resource
    RepayScheduleMapper repayScheduleMapper;
    @Resource
    private OssUtil ossUtil;

    @Resource
    private BaofuAdapterService baofuAdapterService;
    @Resource
    private RepayPlanUpdateService asyncRepayPlanUpdateService;

    @Resource(name = "taskExecutor") // 注入自定义的线程池 Bean，用于执行异步任务
    private Executor taskExecutor;

    private static final BigDecimal SALE_REPAY_RATE = new BigDecimal("0.3599");
    private static final BigDecimal PERIOD = new BigDecimal("3");

    private static final String BAOFU_CHANNEL = "BAOFU";
    private static final String FENZHUAN_CHANNEL = "fenzhuanChannel";

    @Override
    public ResponseResult<List<BackVo>> backList() {
        List<BackVo> backVoList = userBankCardMapper.queryBackListByUserId(UserTokenUtil.getUserId());
        return ResponseResult.success(backVoList);
    }

    public static void main(String[] args) {
        BigDecimal dto = new BigDecimal("1000");
        BigDecimal totalAmount = dto.multiply(SALE_REPAY_RATE);
        BigDecimal saleAmount = totalAmount.subtract(new BigDecimal(100)).subtract(new BigDecimal(100))
                .subtract(new BigDecimal(100));
        BigDecimal periodAmount = saleAmount.divide(PERIOD, 2, BigDecimal.ROUND_HALF_UP);
        BigDecimal finalSaleAmount = saleAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
        System.out.println(periodAmount);
        System.out.println(finalSaleAmount);
    }

    @Override
    public ResponseResult<RepayPlanCalcVo> trialPayment(TrialPaymentDto dto) {
        if (dto.getAmount() == null || dto.getAmount().compareTo(new BigDecimal(500)) < 0) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "金额不能小于500元");
        }
        RepayPlanCalcDto calcDto = new RepayPlanCalcDto();
        calcDto.setAmount(dto.getAmount().toString());
        calcDto.setPeriod(dto.getPeriod());
        LoanApi loanApi = zifangFactory.getApi(ZiFangBeanConstant.MAYI, LoanApi.class);
        ResponseResult<RepayPlanCalcVo> result = loanApi.repayPlanCalc(calcDto);
        if (!result.getData().getResponseCode().equals("0000")) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, result.getData().getResponseMsg());
        }
        RepayPlanCalcVo data = result.getData();
        totalCompute(data);
        BigDecimal totalAmount = dto.getAmount().multiply(SALE_REPAY_RATE);
        BigDecimal saleAmount = totalAmount.subtract(new BigDecimal(data.getTotalInt()))
                .subtract(new BigDecimal(data.getTotalGuar())).subtract(new BigDecimal(data.getTotalService()));
        BigDecimal periodAmount = saleAmount.divide(PERIOD, 2, BigDecimal.ROUND_HALF_UP);
        BigDecimal finalSaleAmount = saleAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
        data.getRepayPlanList().forEach(repayPlan -> {
            if (repayPlan.getPeriod().equals("1") || repayPlan.getPeriod().equals("2")) {
                repayPlan.setSaleAmt(periodAmount.toString());
                repayPlan.setAmt(periodAmount.add(new BigDecimal(repayPlan.getAmt())).toString());
            }
            if (repayPlan.getPeriod().equals("3")) {
                BigDecimal subtract = finalSaleAmount.subtract(periodAmount).subtract(periodAmount);
                repayPlan.setSaleAmt(subtract.toString());
                repayPlan.setAmt(subtract.add(new BigDecimal(repayPlan.getAmt())).toString());
            }
        });
        data.setTotalSale(finalSaleAmount.toString());
        data.setTotalAmt(finalSaleAmount.add(new BigDecimal(data.getTotalAmt())).toString());
        result.setData(data);
        return result;
    }

    @Override
    public ResponseResult<LoanElementVo> bindBackPay() {
        LoanElementDto dto = new LoanElementDto();
        String creditNo = CreditLoanNoUtils.getSequence(CreditLoanNoUtils.CREDIT);
        dto.setCreditNo(creditNo);
        dto.setUserId(UserTokenUtil.getUserId().toString());
        ContractApi contractApi = zifangFactory.getApi(ZiFangBeanConstant.MAYI, ContractApi.class);
        ResponseResult<LoanElementVo> result = contractApi.loanElement(dto);
        LoanElementVo data = result.getData();
        data.setCreditNo(creditNo);
        // 判断是不是分转资方
        CapitalData capitalData = capitalDataMapper.selectById(2);
        if (capitalData != null && capitalData.getStatus() == 1) {
            /**
             * 深圳产品那边说的需要同时绑定两个资方
             * 这个添加一个通道判断
             */
            List<PayChannel> payChannelList = data.getPayChannelList();
            UserRegisterResult registerResult = userRegisterResultMapper.selectByUserId(UserTokenUtil.getUserId());
            if (registerResult != null
                    && StrUtil.isEmpty(registerResult.getBindId())
                    && registerResult.getStatus() != 2) {
                PayChannel payChannel = new PayChannel();
                payChannel.setCode(FENZHUAN_CHANNEL);
                payChannel.setName("分转资方");
                payChannelList.add(payChannel);
            }
            ResponseResult<LoanElementVo> baofuChannelResult = baofuAdapterService.getPayChannel();
            if (baofuChannelResult.isSuccess() && baofuChannelResult.getData() != null) {
                if (baofuChannelResult.getData().getPayChannelList() != null) {
                    payChannelList.addAll(baofuChannelResult.getData().getPayChannelList());
                }
            }
            log.info("绑卡 渠道 payChannelList: {}", payChannelList);
            data.setPayChannelList(payChannelList);
            result.setData(data);
        }

        return result;
    }

    @Override
    public ResponseResult<BindBankSmsVo> bindSendCode(BindBankSmsDto dto) {
        dto.setUserId(UserTokenUtil.getUserId().toString());
        String bankName = dto.getBankName();
        dto.setBankName(BackNameUtil.getCodeByName(bankName));
        dto.setScene("04");
        ResponseResult<BindBankSmsVo> result = null;
        // 判断是否是 分转资方
        if (FENZHUAN_CHANNEL.equals(dto.getPayChannel())) {
            // 生成绑卡流水号
            String bindRequestNo = IdUtil.fastSimpleUUID();
            // 获取进件orderNo
            UserRegisterResult userRegisterResult = userRegisterResultMapper.selectByUserId(UserTokenUtil.getUserId());
            if (ObjectUtil.isEmpty(userRegisterResult)) {
                return ResponseResult.error(ErrorCodeEnum.FAIL, "请先完成进件");
            }
            BindCardApi bindCardApi = zifangFactory.getApi(ZiFangBeanConstant.FENZHUAN, BindCardApi.class);
            BindBankSmsDto smsDto = new BindBankSmsDto();
            smsDto.setCreditNo(userRegisterResult.getOrderNo());
            smsDto.setSerialNo(bindRequestNo);
            smsDto.setIdNo(dto.getIdNo());
            smsDto.setCustName(dto.getCustName());
            smsDto.setPhoneNo(dto.getPhoneNo());
            smsDto.setUserId(dto.getUserId());
            smsDto.setBankCardNum(dto.getBankCardNum());

            result = bindCardApi.getBindBankSMS(smsDto);
            if (result.isSuccess()) {
                BindBankSmsVo data = result.getData();
                if (data.getResponseCode().equals("0000")) {
                    String messageNo = data.getMessageNo();
                    String bindStatus = data.getBindStatus();
                    UserRegisterResult upData = new UserRegisterResult();
                    upData.setId(userRegisterResult.getId());
                    if (bindStatus.equals("VALID")) {
                        String bindId = data.getBindId();
                        upData.setBindRequestNo(bindRequestNo);
                        upData.setBindId(bindId);
                        upData.setStatus(1);
                        userRegisterResultMapper.updateById(upData);
                    } else {
                        upData.setBindRequestNo(bindRequestNo);
                        upData.setStatus(1);
                        userRegisterResultMapper.updateById(upData);
                    }
                }
            }

        } else if (BAOFU_CHANNEL.equals(dto.getPayChannel())) {
            // 使用宝付服务
            result = baofuAdapterService.getBindCardSms(dto);
        } else {
            BindCardApi bindCardApi = zifangFactory.getApi(ZiFangBeanConstant.MAYI, BindCardApi.class);
            result = bindCardApi.getBindBankSMS(dto);
        }
        if (result.isSuccess()
            && result.getData() != null
            && "0000".equals(result.getData().getResponseCode())) {
            UserBankCard userBankCard = userBankCardMapper.queryByUserId(UserTokenUtil.getUserId());
            if (userBankCard != null) {
                userBankCard.setBankAccount(dto.getBankCardNum());
                userBankCard.setBankName(bankName);
                userBankCard.setMobile(dto.getPhoneNo());
                userBankCard.setUpdateTime(new Date());
                userBankCardMapper.updateById(userBankCard);
            } else {
                userBankCard = new UserBankCard();
                userBankCard.setUserId(UserTokenUtil.getUserId());
                userBankCard.setBankAccount(dto.getBankCardNum());
                userBankCard.setBankName(bankName);
                userBankCard.setMobile(dto.getPhoneNo());
                userBankCard.setIdCard(dto.getIdNo());
                userBankCard.setContractNum("webNoContractNum");
                userBankCard.setCustomerName(dto.getCustName());
                userBankCardMapper.insert(userBankCard);
            }
        }
        return result;
    }

    @Override
    public ResponseResult<VerifyBindBankSmsVo> verifyBindSend(VerifyBindBankSmsDto dto) {
        String payChannel = dto.getPayChannel();
        if (FENZHUAN_CHANNEL.equals(payChannel)) {
            UserRegisterResult userRegisterResult = userRegisterResultMapper.selectByUserId(UserTokenUtil.getUserId());
            if (StrUtil.isNotEmpty(userRegisterResult.getBindId())) {
                VerifyBindBankSmsVo smsVo = new VerifyBindBankSmsVo();
                smsVo.setResponseCode("0000");
                smsVo.setResponseMsg("成功");
                return ResponseResult.success(smsVo);
            }

            dto.setCreditNo(userRegisterResult.getOrderNo());
            dto.setSerialNo(userRegisterResult.getBindRequestNo());
            BindCardApi bindCardApi = zifangFactory.getApi(ZiFangBeanConstant.FENZHUAN, BindCardApi.class);
            ResponseResult<VerifyBindBankSmsVo> result = bindCardApi.getVerifyBindBankSMSUrl(dto);
            if (result.getData().getResponseCode().equals("0000")) {
                if (result.getData().getBindStatus().equals("VALID")) {
                    // 更新绑卡id
                    userRegisterResult.setBindId(result.getData().getBankCardBindId());
                    userRegisterResultMapper.updateById(userRegisterResult);
                    return result;
                }
            }
            return ResponseResult.error(ErrorCodeEnum.FAIL);
        } else if (BAOFU_CHANNEL.equals(payChannel)) {
            // 使用宝付服务
            ResponseResult<VerifyBindBankSmsVo> verifyBindCardSms = baofuAdapterService.verifyBindCardSms(dto);
            if (verifyBindCardSms.isSuccess()
                    && verifyBindCardSms.getData() != null
                    && verifyBindCardSms.getData().getBindStatus().equals("VALID")) {

                UserBankCard userBankCard = userBankCardMapper.queryByUserId(UserTokenUtil.getUserId());
                if (userBankCard != null) {
                    userBankCard.setBaoFuContractNum(verifyBindCardSms.getData().getAgreementNumber());
                    userBankCard.setUpdateTime(new Date());
                    userBankCardMapper.updateById(userBankCard);
                }
                return verifyBindCardSms;
            }
            return ResponseResult.error(ErrorCodeEnum.FAIL);
        } else {
            dto.setSerialNo(CreditLoanNoUtils.getSequence("bk"));
            dto.setScene("04");
            BindCardApi bindCardApi = zifangFactory.getApi(ZiFangBeanConstant.MAYI, BindCardApi.class);
            ResponseResult<VerifyBindBankSmsVo> result = bindCardApi.getVerifyBindBankSMSUrl(dto);

            if (result == null || result.getData() == null) {
                return ResponseResult.error(ErrorCodeEnum.FAIL, "绑卡验证请求失败");
            }

            if (!"0000".equals(result.getData().getResponseCode())) {
                return ResponseResult.error(ErrorCodeEnum.FAIL, result.getData().getResponseMsg());
            }

            // 异步查询绑卡结果，避免同步等待
            QueryBindBankResultDto resultDto = new QueryBindBankResultDto();
            resultDto.setMessageNo(dto.getMessageNo());

            // 使用更优雅的重试机制
            int maxRetries = 5;
            int retryIntervalMs = 3000;

            for (int i = 0; i < maxRetries; i++) {
                try {
                    Thread.sleep(retryIntervalMs);
                    ResponseResult<QueryBindBankResultVo> bankResult = bindCardApi.getQueryBindBankResult(resultDto);

                    if (bankResult != null
                            && bankResult.getData() != null
                            && "0000".equals(bankResult.getData().getResponseCode())) {
                        return result;
                    }
                } catch (Exception e) {
                    log.error("查询绑卡结果异常, 流水号：{}, 重试次数：{}/{}, 异常: {}",
                            dto.getMessageNo(), i + 1, maxRetries, e.getMessage());
                    if (i == maxRetries - 1) {
                        return ResponseResult.error(ErrorCodeEnum.FAIL, "绑卡查询失败，请稍后重试");
                    }
                }
            }

            return ResponseResult.error(ErrorCodeEnum.FAIL, "绑卡验证超时，请稍后查询结果");
        }
    }

    @Override
    public ResponseResult<QueryBindBankResultVo> bindBankResult(QueryBindBankResultDto dto) {
        // 检查是否为宝付渠道
        String payChannel = dto.getPayChannel();
        Long userId = UserTokenUtil.getUserId();
        if (BAOFU_CHANNEL.equals(payChannel)) {
            // 使用宝付服务
            dto.setUserId(userId.toString());
            return baofuAdapterService.queryBindCardResult(dto);
        } else if (FENZHUAN_CHANNEL.equals(payChannel)) {
            BindCardApi bindCardApi = zifangFactory.getApi(ZiFangBeanConstant.FENZHUAN, BindCardApi.class);
            // 获取进件orderNo
            UserRegisterResult userRegisterResult = userRegisterResultMapper.selectByUserId(UserTokenUtil.getUserId());
            if (ObjectUtil.isEmpty(userRegisterResult)) {
                return ResponseResult.error(ErrorCodeEnum.FAIL, "请先完成进件");
            }
            return bindCardApi.getQueryBindBankResult(dto);
        } else {
            // 默认使用原服务
            BindCardApi bindCardApi = zifangFactory.getApi(ZiFangBeanConstant.MAYI, BindCardApi.class);
            return bindCardApi.getQueryBindBankResult(dto);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> loanApply(LoanApplyDto dto) {
        Long userId = UserTokenUtil.getUserId();
        CapitalData capitalData = zifangFactory.matchCapitalByUserId(userId);
        if (ObjectUtil.isEmpty(capitalData)) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "资方未匹配");
        }

        // 校验是否有一条进行中的支用
        LambdaQueryWrapper<DisburseData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DisburseData::getUserId, userId);
        queryWrapper.in(DisburseData::getCreditStatus, Arrays.asList(100, 300));
        boolean exists = disburseDataMapper.exists(queryWrapper);
        if (exists) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "已有进行中的申请，请稍后再试！");
        }

        // 校验借款金额与余额
        UserCreditData creditData = userCreditDataMapper.queryByUserId(userId);
        if (creditData.getResidueAmount().compareTo(dto.getCreditAmount()) < 0) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "余额不足");
        }
        UserDetail userDetail = userDetailMapper.queryByUserId(userId);
        UserData userData = userDataMapper.selectById(userId);
        if (ObjectUtil.isEmpty(userDetail)) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "用户信息不存在");
        }
        if (ObjectUtil.isNotEmpty(userData.getSourceMode())
                && (userData.getSourceMode() == SourceMode.ONLINE || userData.getSourceMode() == SourceMode.ONLINE_ONLY_REGISTER)) {
            HrzxAuthDto hrzxAuthDto = new HrzxAuthDto();
            // 处理人脸照片
            if (networkFileDealService.dealFaceImage(userDetail, hrzxAuthDto)) {
                return ResponseResult.error(ErrorCodeEnum.FAIL, "人脸照片处理失败");
            }
            // 处理协议文件
            if (networkFileDealService.dealProtocolFile(userDetail, hrzxAuthDto)) {
                return ResponseResult.error(ErrorCodeEnum.FAIL, "协议文件处理失败");
            }
            // 过二次风控
            PreLoanAuditVo preLoanAuditVo = dealOnlineCredit(hrzxAuthDto, userDetail, userData.getMobile());

            Integer result = preLoanAuditVo.getResult();
            HrzxCreditLog addHrzxCreditLog = new HrzxCreditLog();
            addHrzxCreditLog.setUserId(userId);
            addHrzxCreditLog.setStatus(result);
            addHrzxCreditLog.setResult(preLoanAuditVo.getMessage());
            hrzxCreditLogMapper.insert(addHrzxCreditLog);
            if (!(result == 0 || result == 1)) {
                return ResponseResult.error(ErrorCodeEnum.FAIL, "您的征信未通过");
            }
        }
        DisburseData data = new DisburseData();
        data.setUserId(userId);
        data.setProductId(dto.getProductId());
        data.setCreditNo(dto.getCreditNo());
        data.setLoanNo(CreditLoanNoUtils.getSequence(CreditLoanNoUtils.LOAN));
        data.setSaleNo(CreditLoanNoUtils.getSequence("sl"));
        data.setCreditAmount(dto.getCreditAmount());
        data.setPeriods(dto.getPeriods());
        data.setPurposeLoan(dto.getPurposeLoan());
        data.setYearRete(dto.getYearRete());
        data.setRepaymentMethod(dto.getRepaymentMethod());
        data.setCreditStatus(100);
        data.setCapitalId(capitalData.getId());
        UserCapitalRecord userCapitalRecord = new UserCapitalRecord();
        userCapitalRecord.setUserId(userId);
        userCapitalRecord.setCapitalId(capitalData.getId());
        userCapitalRecordMapper.insert(userCapitalRecord);
        data.setCapitalRecordId(userCapitalRecord.getId());
        int insert = disburseDataMapper.insert(data);
        // 异步签署赊销资方爱签协议
        CompletableFuture.runAsync(() -> {
            signYunxiAiSign(userId);
        });
        if (insert > 0) {
            if (!ZiFangBeanConstant.FENZHUAN.equals(capitalData.getBeanName())) {
                ResponseResult<Void> result = creditService.creditApply(capitalData, userId);
                if (!result.isSuccess()) {
                    data.setCreditStatus(200);
                    data.setCreditTime(DateUtil.date());
                    disburseDataMapper.updateById(data);
                    userCapitalRecord.setCreditStatus(2);
                    userCapitalRecordMapper.updateById(userCapitalRecord);
                    return ResponseResult.error(ErrorCodeEnum.FAIL);
                }
            } else {
                LoanApi loanApi = zifangFactory.getApi(ZiFangBeanConstant.FENZHUAN, LoanApi.class);
                // 获取进件orderNo和绑卡流水号
                UserRegisterResult userRegisterResult = userRegisterResultMapper.selectByUserId(
                        UserTokenUtil.getUserId());
                if (ObjectUtil.isEmpty(userRegisterResult)) {
                    return ResponseResult.error(ErrorCodeEnum.FAIL, "请先完成进件");
                }
                com.rongchen.byh.common.api.zifang.dto.LoanApplyDto applyDto = new com.rongchen.byh.common.api.zifang.dto.LoanApplyDto();
                applyDto.setApplyAmount(dto.getCreditAmount().toPlainString());
                applyDto.setSerialNo(userRegisterResult.getBindId());
                applyDto.setCreditNo(userRegisterResult.getOrderNo());
                applyDto.setLoanNo(data.getLoanNo());
                applyDto.setApplyTerm(dto.getPeriods().toString());
                ResponseResult<CreditApplyVo> result = loanApi.loanApply(applyDto);
                if (result.getData().getResponseCode().equals("0000")) {
                    // 更新creditNo为对面的借款单号
                    data.setCreditStatus(300);
                    data.setCreditNo(result.getData().getLoanOrderNo());

                    UserRegisterResult upResult = new UserRegisterResult();
                    upResult.setId(userRegisterResult.getId());
                    upResult.setLoanOrderNo(result.getData().getLoanOrderNo());
                    userRegisterResultMapper.updateById(upResult);

                    disburseDataMapper.updateById(data);
                } else {
                    return ResponseResult.error(ErrorCodeEnum.FAIL, result.getData().getResponseMsg());
                }
            }
            creditData.setResidueAmount(creditData.getResidueAmount().subtract(data.getCreditAmount()));
            creditData.setFreezeAmount(creditData.getFreezeAmount().add(data.getCreditAmount()));
            userCreditDataMapper.updateById(creditData);
            userCapitalRecord.setCreditStatus(1);
            userCapitalRecordMapper.updateById(userCapitalRecord);
            return ResponseResult.success();
        }
        return ResponseResult.error(ErrorCodeEnum.FAIL);
    }

    private JSONObject buildCreditInfo(HrzxAuthDto hrzxAuthDto, UserDetail detail, String mobile) {
        JSONObject socialCreditCode = new JSONObject();
        socialCreditCode.put("requestNo", IdUtil.fastSimpleUUID());
        socialCreditCode.put("queryName", detail.getAppName());
        socialCreditCode.put("queryIdNoType", "01");
        socialCreditCode.put("queryIdNo", detail.getIdNumber());
        socialCreditCode.put("queryPhone", mobile);
        socialCreditCode.put("veriFaceTime", DateUtil.now());
        socialCreditCode.put("authTime", DateUtil.now());

        // --- 开始计时 ---
        long ossStartTime = System.currentTimeMillis();
        log.info("开始并行 OSS 操作，userId: {}, 手机号: {}", detail.getUserId(), mobile);
        log.info("身份证正面: {}", hrzxAuthDto.getFrontIdCardFile());
        log.info("身份证反面: {}", hrzxAuthDto.getBackIdCardFile());
        log.info("人脸: {}", hrzxAuthDto.getOtherAuthFile());
        log.info("ca电子签名授权书pdf文件: {}", hrzxAuthDto.getCaFile());
        log.info("委托担保申请书: {}", hrzxAuthDto.getOtherFile());
        // 开始并行 OSS 操作
        CompletableFuture<String> idCardFileFuture = ossUtil.mergeTwoImage(hrzxAuthDto.getFrontIdCardFile(),
                hrzxAuthDto.getBackIdCardFile(), mobile);
        CompletableFuture<String> otherAuthFileFuture = ossUtil.uploadOss(hrzxAuthDto.getOtherAuthFile());
        CompletableFuture<String> caFileFuture = ossUtil.uploadOss(hrzxAuthDto.getCaFile());
        CompletableFuture<String> otherFileFuture = ossUtil.pdfToZip(hrzxAuthDto.getOtherFile());

        // 等待所有操作完成
        CompletableFuture.allOf(idCardFileFuture, otherAuthFileFuture, caFileFuture, otherFileFuture).join();

        // --- 结束计时 ---
        long ossEndTime = System.currentTimeMillis();
        log.info("并行 OSS 操作完成，耗时: {} ms，userId: {}, 手机号: {}", (ossEndTime - ossStartTime), detail.getUserId(),
                mobile);

        // 组装 socialCreditCode 对象
        socialCreditCode.put("idCardFile", idCardFileFuture.join());
        socialCreditCode.put("otherAuthFile", otherAuthFileFuture.join());
        socialCreditCode.put("caFile", caFileFuture.join());
        socialCreditCode.put("otherFile", otherFileFuture.join());

        socialCreditCode.put("queryDate", DateUtil.format(new Date(), "yyyyMMdd"));
        return socialCreditCode;
    }

    public PreLoanAuditVo dealOnlineCredit(HrzxAuthDto hrzxAuthDto, UserDetail detail, String mobile) {
        // 调用app授信 获取授信结果
        List<PreLoanAuditAppRelationsDto> list = new ArrayList<>();
        PreLoanAuditAppRelationsDto relationsDto = new PreLoanAuditAppRelationsDto();
        relationsDto.setRelation_mobile(detail.getEmergencyMobileOne());
        relationsDto.setRelation_name(detail.getEmergencyNameOne());
        relationsDto.setRelation_type(Integer.valueOf(detail.getRelationshipOne()));
        list.add(relationsDto);
        PreLoanAuditAppRelationsDto relationsDto1 = new PreLoanAuditAppRelationsDto();
        relationsDto1.setRelation_mobile(detail.getEmergencyMobileTwo());
        relationsDto1.setRelation_name(detail.getEmergencyNameTwo());
        relationsDto1.setRelation_type(Integer.valueOf(detail.getRelationshipTwo()));
        list.add(relationsDto1);
        PreLoanAuditAppDto auditAppDto = new PreLoanAuditAppDto();
        auditAppDto.setIdcard_no(detail.getIdNumber());
        auditAppDto.setMobile(mobile);
        auditAppDto.setName(detail.getAppName());
        auditAppDto.setCredit_id(IdUtil.fastSimpleUUID());
        auditAppDto.setCredit_time(DateUtil.now());
        auditAppDto.setChannel("4");
        auditAppDto.setProduct_code(4);
        String[] split = detail.getValidDate().split("-");
        auditAppDto.setCert_start(split[0].replace(".", "-"));
        if ("长期".equals(split[1])) {
            split[1] = "2099.12.31";
        }
        auditAppDto.setCert_end(split[1].replace(".", "-"));
        auditAppDto.setNation_ocr(IdCardUtil.nationOcr(detail.getNation()));
        auditAppDto.setIdcard_address_ocr(detail.getAddress());
        auditAppDto.setEducation(IdCardUtil.educationSw(detail.getEducationLevel()));
        auditAppDto.setMarriage_state(IdCardUtil.marriageStateSw(detail.getMaritalStatus()));
        auditAppDto.setLive_address(detail.getCustAddressProvice() + detail.getCustAddressCity()
                + detail.getCustAddressCounty() + detail.getCustAddress());
        String resolution = IdCardUtil.addressResolution(detail.getAddress());
        if (resolution != null) {
            auditAppDto.setIdcard_city_code(resolution);
        } else {
            auditAppDto.setIdcard_city_code(CityCodeUtil.getCodeByName(detail.getCustAddressCity()));
        }
        auditAppDto.setLive_city_code(CityCodeUtil.getCodeByName(detail.getCustAddressCity()));
        auditAppDto.setIssued_org_ocr(detail.getAuthority());
        auditAppDto.setAnnual_income(Integer.valueOf(detail.getIncomeMonth()) * 12);
        auditAppDto.setIf_register("Y");
        auditAppDto.setIf_sign("Y");
        UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(detail.getUserId(), LoanType.CHECK, 0);
        if (ObjectUtil.isNotEmpty(userLoanApply) && userLoanApply.getAuditsStatus() == 4) {
            auditAppDto.setIf_sign("N");
        }
        auditAppDto.setPhone_number("");
        auditAppDto.setMobile_startime("");
        auditAppDto.setWifi_sensitive("");
        auditAppDto.setAddress_book_num(0);
        auditAppDto.setAddress_book_num11(0);
        auditAppDto.setAddress_book_sensitive("");
        auditAppDto.setContact_operator("");
        auditAppDto.setOverdue_message_m60(0);
        JSONObject param = new JSONObject();
        param.put("house", IdCardUtil.houseSw(detail.getHouseStatus()));
        auditAppDto.setHouse_info(param);
        auditAppDto.setRelations(list);

        // Call the modified buildCreditInfo and get the JSONObject
        JSONObject socialCreditCodeResult = buildCreditInfo(hrzxAuthDto, detail, mobile);

        // Now create the companyInfo JSONObject using the result
        JSONObject companyInfo = new JSONObject();
        // Encode the entire result JSONObject for the 'social_credit_code' field (as it
        // was previously)
        companyInfo.put("social_credit_code", Base64.getEncoder().encodeToString(socialCreditCodeResult.toJSONString()
                .getBytes(StandardCharsets.UTF_8)));

        auditAppDto.setCompany_info(companyInfo);
        PreLoanAuditVo preLoanAuditVo = riskControlService.preLoanAuditApp(auditAppDto);
        return preLoanAuditVo;
    }

    @Override
    public ResponseResult<DisburseStatusVo> queryCreditStatus() {
        CreditAuditDto dto = new CreditAuditDto();
        Long userId = UserTokenUtil.getUserId();
        DisburseData data = disburseDataMapper.selectByUserId(userId);
        dto.setUserId(userId.toString());
        dto.setCreditNo(data.getCreditNo());
        DisburseStatusVo vo = new DisburseStatusVo();
        vo.setCreditAmount(data.getCreditAmount().toString());
        if (data.getCreditStatus() == 100 || data.getCreditStatus() == 300) {
            vo.setCreditStatus(1);
        } else if (data.getCreditStatus() == 200 || data.getCreditStatus() == 400) {
            vo.setCreditStatus(3);
        } else {
            vo.setCreditStatus(2);
        }
        return ResponseResult.success(vo);
    }

    @Override
    public ResponseResult<DisburseAmountVo> disburseAmount() {
        Long userId = UserTokenUtil.getUserId();
        log.info("【首页额度查询】开始处理，userId: {}", userId);
        CompletableFuture.runAsync(()->{
            try {
                long startTime = System.currentTimeMillis();
                log.info("【首页额度查询】触发异步还款计划更新检查，userId: {}", userId);
                asyncRepayPlanUpdateService.triggerRepayPlanUpdate(userId);
                long endTime = System.currentTimeMillis();
                log.info("【首页额度查询】异步还款计划更新检查完成，userId: {}, 耗时: {}ms", userId, endTime - startTime);
            } catch (Exception e) {
                log.error("【首页额度查询】提交异步还款计划更新任务失败，userId: {}, error: ", userId, e);
            }
        }, taskExecutor);

        UserCreditData userCreditData = userCreditDataMapper.queryByUserId(userId);
        DisburseAmountVo disburseAmountVo = (userCreditData != null)
            ? MyModelUtil.copyTo(userCreditData, DisburseAmountVo.class)
            : new DisburseAmountVo();

        log.info("【首页额度查询】处理完成，userId: {}", userId);
        return ResponseResult.success(disburseAmountVo);
    }

    @Override
    public ResponseResult<WebSaleUrlVo> webSaleUrl() {
        try {
            Long userId = UserTokenUtil.getUserId();
            DisburseData data = disburseDataMapper.selectByUserId(userId);
            if (data == null) {
                return ResponseResult.success(null);
            }
            UserData userData = userDataMapper.selectById(userId);
            HfUrlDto dto = new HfUrlDto();
            dto.setUserId(userId.toString());
            dto.setUserMobile(userData.getMobile());
            dto.setLoanNo(data.getSaleNo());
            dto.setType("RIGHTS");

            Long capitalId = data.getCapitalId();
            CapitalData capitalData = capitalDataMapper.selectById(capitalId);
            if (capitalData.getBeanName().startsWith("fenZhuan")) {
                // 查询渠道
                SaleOrderRecord saleChannel = saleOrderRecordService.getSaleChannel(data.getSaleNo(), userId);
                if (ObjectUtil.isEmpty(saleChannel)
                        || saleChannel.getOrderStatus() == null
                        // 先检查是否包含SUCCESS就认为权益生效开始
                        || !saleChannel.getOrderStatus().contains("SUCCESS")
                        // 权益过期
                        || (saleChannel.getExpireTime() != null && saleChannel.getExpireTime().before(new Date()))) {
                    // 统一错误提示文案
                    return ResponseResult.error(ErrorCodeEnum.FAIL, "暂未达到兑换条件，如有疑问联系在线客服!");
                }

                if (ObjectUtil.isNotEmpty(saleChannel)) {
                    log.info("[分转] 获取赊销地址 渠道赊销单号: {}", saleChannel.getOutSaleNo());
                    dto.setOutSaleNo(saleChannel.getOutSaleNo());
                }
                // 如果orderStatus返回 REFUND_SUCCESS说明权益方退款了，那么就不能打开权益地址了
                if (saleChannel.getOrderStatus().contains("REFUND_SUCCESS")) {
                    return ResponseResult.error(ErrorCodeEnum.FAIL, "权益已失效，如有疑问联系在线客服!");
                }

            }

            OtherApi otherApi = zifangFactory.getApi(capitalData.getBeanName(), OtherApi.class);
            ResponseResult<HfUrlVo> h5Url = otherApi.getH5Url(dto);
            if (h5Url.isSuccess()) {
                WebSaleUrlVo webSaleUrlVo = new WebSaleUrlVo();
                webSaleUrlVo.setUrl(h5Url.getData().getH5Url());
                return ResponseResult.success(webSaleUrlVo);
            }
            return ResponseResult.success(null);
        } catch (Exception e) {
            log.error("获取赊销地址异常 {}", e);
            return ResponseResult.success(null);
        }

    }

    public void totalCompute(RepayPlanCalcVo vo) {
        vo.setTotalAmt("0");
        vo.setTotalPrcp("0");
        vo.setTotalInt("0");
        vo.setTotalGuar("0");
        vo.setTotalService("0");
        vo.getRepayPlanList().forEach(item -> {
            vo.setTotalAmt(new BigDecimal(vo.getTotalAmt()).add(new BigDecimal(item.getAmt()))
                    .setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            vo.setTotalPrcp(new BigDecimal(vo.getTotalPrcp()).add(new BigDecimal(item.getPrcpAmt()))
                    .setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            vo.setTotalInt(new BigDecimal(vo.getTotalInt()).add(new BigDecimal(item.getIntAmt()))
                    .setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            vo.setTotalGuar(new BigDecimal(vo.getTotalGuar()).add(new BigDecimal(item.getGuarAmt()))
                    .setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            vo.setTotalService(new BigDecimal(vo.getTotalService()).add(new BigDecimal(item.getServiceAmt()))
                    .setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        });

    }

    private void signYunxiAiSign(Long userId) {
        log.info("开始执行云曦AI签约");
        UserData userData = userDataMapper.selectById(userId);
        UserDetail detail = userDetailMapper.queryByUserId(userId);
        SignDto signDto = new SignDto();
        signDto.setName(detail.getWebName());
        signDto.setAccount(userData.getMobile());
        signDto.setIdCard(detail.getWebIdCard());
        signDto.setContractNo(IdUtil.fastSimpleUUID());
        signDto.setUserId(userId);
        List<SignRulesDto> rules = new ArrayList<>();
        ResponseResult<Void> result = ResponseResult.success();
        signDto.setContractName("云樨会员服务协议");
        SignRulesDto signRulesDto1 = new SignRulesDto();
        SignRulesDto signRulesDto2 = new SignRulesDto();
        signRulesDto1.setKey("idCard");
        signRulesDto1.setValue(detail.getWebIdCard());
        signRulesDto2.setKey("userName");
        signRulesDto2.setValue(detail.getWebName());
        rules.add(signRulesDto1);
        rules.add(signRulesDto2);
        signDto.setRules(rules);
        result = loveSignService.signAll(signDto, loveSignProperties.getYunxiMemeber());
        loveSign(signDto, result, userId);
        signDto.setContractNo(IdUtil.fastSimpleUUID());
        signDto.setContractName("委托扣款协议");
        result = loveSignService.signAll(signDto, loveSignProperties.getDelegationDeduction());
        loveSign(signDto, result, userId);
    }

    public void loveSign(SignDto dto, ResponseResult<Void> result, Long userId) {
        UserLoveLog log = new UserLoveLog();
        log.setUserId(userId);
        log.setContractName(dto.getContractName());
        log.setContractNo(dto.getContractNo());
        if (result.isSuccess()) {
            log.setContractStatus(1);
        } else {
            log.setContractStatus(2);
        }
        userLoveLogMapper.insert(log);
    }

    /**
     * 绑卡--查询新的支付通道
     */
    @Override
    public ResponseResult<LoanElementVo> newBindBackPay() {
        // 判断用户的最新支用状态是否是还款中，如果是才允许获取通道，换绑银行卡等下面操作
        DisburseData disburseData = new DisburseData();
        disburseData.setUserId(UserTokenUtil.getUserId());
        disburseData.setCreditStatus(500);
        DisburseData disburseRes = disburseDataMapper.selectNewByUser(disburseData);
        if (disburseRes == null) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "该用户不在还款中状态，不允许更换银行卡");
        } else {
            LoanElementDto dto = new LoanElementDto();
            String creditNo = CreditLoanNoUtils.getSequence(CreditLoanNoUtils.CREDIT);
            dto.setCreditNo(creditNo);
            dto.setLoanNo(disburseRes.getLoanNo());
            dto.setUserId(UserTokenUtil.getUserId().toString());
            ResponseResult<LoanElementVo> result = contractApi.loanElement(dto);

            LoanElementVo data = result.getData();
            if (!"0000".equals(data.getResponseCode())) {
                return ResponseResult.error(ErrorCodeEnum.FAIL, "未获取到该用户的订单");
            }
            data.setCreditNo(creditNo);
            /**
             * 深圳产品那边说的需要同时绑定两个资方
             * 这个添加一个通道判断
             */
            List<PayChannel> payChannelList = data.getPayChannelList();
            // PayChannel payChannel = new PayChannel();
            // payChannel.setCode(fenzhuanChannel);
            // payChannel.setName("分转资方");
            // payChannelList.add(payChannel);
            data.setPayChannelList(payChannelList);
            return result;
        }
    }

    /**
     *
     * 新绑卡获取验证码
     */
    @Override
    public ResponseResult<BindBankSmsVo> newBindSendCode(BindBankSmsDto dto) {

        dto.setUserId(UserTokenUtil.getUserId().toString());
        String bankName = dto.getBankName();
        dto.setBankName(BackNameUtil.getCodeByName(bankName));
        dto.setSerialNo(CreditLoanNoUtils.getSequence("bk"));
        // 添加新增参数
        DisburseData disburseData = new DisburseData();
        disburseData.setUserId(UserTokenUtil.getUserId());
        disburseData.setCreditStatus(500);
        DisburseData disburseRes = disburseDataMapper.selectNewByUser(disburseData);
        dto.setLoanNo(disburseRes.getLoanNo());
        dto.setScene("03");

        ResponseResult<BindBankSmsVo> result = bindCardApi.getBindBankSMS(dto);
        if (result.isSuccess()
            && result.getData() != null
            && "0000".equals(result.getData().getResponseCode())) {
            UserBankCard userBankCard = userBankCardMapper.queryByUserId(UserTokenUtil.getUserId());
            if (userBankCard != null) {
                userBankCard.setBankAccount(dto.getBankCardNum());
                userBankCard.setBankName(bankName);
                userBankCard.setMobile(dto.getPhoneNo());
                userBankCardMapper.updateById(userBankCard);
            } else {
                userBankCard = new UserBankCard();
                userBankCard.setUserId(UserTokenUtil.getUserId());
                userBankCard.setBankAccount(dto.getBankCardNum());
                userBankCard.setBankName(bankName);
                userBankCard.setMobile(dto.getPhoneNo());
                userBankCard.setIdCard(dto.getIdNo());
                userBankCard.setContractNum("webNoContractNum");
                userBankCard.setCustomerName(dto.getCustName());
                userBankCardMapper.insert(userBankCard);
            }
        }
        return result;

    }

    @Override
    public ResponseResult<VerifyBindBankSmsVo> newVerifyBindSend(VerifyBindBankSmsDto dto) {
        DisburseData disburseData = new DisburseData();
        disburseData.setUserId(UserTokenUtil.getUserId());
        disburseData.setCreditStatus(500);
        DisburseData disburseRes = disburseDataMapper.selectNewByUser(disburseData);
        dto.setSerialNo(CreditLoanNoUtils.getSequence("bk"));
        dto.setScene("03");
        dto.setLoanNo(disburseRes.getLoanNo());
        ResponseResult<VerifyBindBankSmsVo> result = bindCardApi.getVerifyBindBankSMSUrl(dto);
        if (result.getData().getResponseCode().equals("0000")) {
            QueryBindBankResultDto resultDto = new QueryBindBankResultDto();
            resultDto.setMessageNo(dto.getMessageNo());
            int i = 5;
            while (i > 0) {
                try {
                    Thread.sleep(3000);
                    ResponseResult<QueryBindBankResultVo> bankResult = bindCardApi.getQueryBindBankResult(
                            resultDto);
                    if (bankResult.getData().getResponseCode().equals("0000")) {
                        return result;
                    }
                    i--;
                } catch (Exception e) {
                    log.error("查询绑卡结果异常, 流水号：{} , 异常: {}", dto.getMessageNo(), e);
                    return ResponseResult.error(ErrorCodeEnum.FAIL);
                }
            }
            return ResponseResult.error(ErrorCodeEnum.FAIL);
        }
        return ResponseResult.error(ErrorCodeEnum.FAIL, result.getData().getResponseMsg());
    }

    @Override
    public DisburseData selectOneDisburse(DisburseData disburseData) {
        DisburseData disburseRes = disburseDataMapper.selectNewByUser(disburseData);
        return disburseRes;
    }

    @Override
    @Transactional(rollbackFor = {RuntimeException.class,Exception.class})
    public ResponseResult<Void> checkOverDisburse(Long disburseId) {
        DisburseData disburseData = disburseDataMapper.selectById(disburseId);
        if (disburseData == null) {
            return ResponseResult.error(ErrorCodeEnum.FAIL,"支用不存在");
        }
        // 查询还款完成的笔数
        LambdaQueryWrapper<RepaySchedule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RepaySchedule::getDisburseId,disburseId);
        queryWrapper.eq(RepaySchedule::getSettleFlag, SettleFlagConstant.CLOSE);
        Long count = repayScheduleMapper.selectCount(queryWrapper);
        if (!disburseData.getPeriods().equals(count.intValue())) {
            return ResponseResult.error(ErrorCodeEnum.FAIL,"当前未结清");
        }
        // 更新支用完成
        DisburseData data = new DisburseData();
        data.setId(disburseId);
        data.setCreditStatus(600);
        data.setRepaymentTime(DateUtil.date());
        disburseDataMapper.updateById(data);
        // 返还授信额度
        BigDecimal creditAmount = disburseData.getCreditAmount();
        UserCreditData userCreditData = userCreditDataMapper.queryByUserId(disburseData.getUserId());
        BigDecimal withdrawAmount = userCreditData.getWithdrawAmount();
        BigDecimal subtract = withdrawAmount.subtract(creditAmount);
        BigDecimal residueAmount = userCreditData.getResidueAmount();
        BigDecimal add = residueAmount.add(creditAmount);
        UserCreditData upData = new UserCreditData();
        upData.setId(userCreditData.getId());
        upData.setWithdrawAmount(subtract);
        upData.setResidueAmount(add);
        userCreditDataMapper.updateById(upData);
        return ResponseResult.success();
    }

}
