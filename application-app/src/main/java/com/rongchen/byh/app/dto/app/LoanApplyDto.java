package com.rongchen.byh.app.dto.app;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName LoanApplyDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/14 18:00
 * @Version 1.0
 **/
@Data
public class LoanApplyDto {

    @Schema(description = "产品id  暂时固定为1")
    private Long productId;

    @Schema(description = "授信流水号")
    private String creditNo;

    @Schema(description = "授信金额")
    private BigDecimal creditAmount;

    @Schema(description = "放款期限")
    private Integer periods;

    @Schema(description = "借款用途")
    private String purposeLoan;

    @Schema(description = "年利率")
    private String yearRete;

    @Schema(description = "还款方式" , hidden = true)
    private String repaymentMethod;
}
