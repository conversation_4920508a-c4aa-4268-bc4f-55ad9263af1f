package com.rongchen.byh.app.controller.h5;

import cn.dev33.satoken.annotation.SaIgnore;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.rongchen.byh.app.dto.RegOrLoginDto;
import com.rongchen.byh.app.vo.RegOrLoginVo;
import com.rongchen.byh.common.core.annotation.DisableDataFilter;
import com.rongchen.byh.common.core.object.ResponseResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @description h5用户注册相关接口
 * @date 2024/12/11 11:10:11
 */
@ApiSupport(order = 1)
@Tag(name = "h5用户注册相关接口")
@DisableDataFilter
@Slf4j
@RestController
@RequestMapping("/user")
public class UserRegisterController {
//    @Operation(summary = "用户注册或登录")
//    @SaIgnore
//    @PostMapping("/regOrLogin")
//    public ResponseResult<RegOrLoginVo> regOrLogin(@RequestBody @Validated RegOrLoginDto regOrLoginDto) {
////        return loginService.regOrLogin(regOrLoginDto);
//    }
}
