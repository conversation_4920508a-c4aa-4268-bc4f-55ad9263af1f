package com.rongchen.byh.app.api.service.strategy.factory;

import cn.hutool.core.util.StrUtil;
import com.rongchen.byh.app.api.service.strategy.process.OutApiAbstractProcess;
import com.rongchen.byh.common.core.exception.MyRuntimeException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 外部api工厂类
 * @date 2025/3/20 11:44:27
 */
@Component
public class OutApiFactory {
    @Resource
    List<OutApiAbstractProcess> outApiAbstractProcessList;

    public OutApiAbstractProcess getOutApiAbstractProcess(String channel) {
        if (StrUtil.isEmpty(channel)) {
            throw new MyRuntimeException("渠道不能为空");
        }
        for (OutApiAbstractProcess outApiAbstractProcess : outApiAbstractProcessList) {
            if (outApiAbstractProcess.getChannel().equals(channel)) {
                return outApiAbstractProcess;
            }
        }
        return null;
    }

}
