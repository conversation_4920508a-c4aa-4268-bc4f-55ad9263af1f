package com.rongchen.byh.app.api.dto.credit;

import lombok.Data;
import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025/3/20 11:23:52
 */
@Data
public class CreditDto {

    /**
     * 请求方
     */
    private String reqSysCode;

    /**
     * 资金方编码
     */
    private String fundCode;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 用户授信信息
     */
    private CreditInfo creditInfo;

    /**
     * 授信结果信息
     */
    private CreditResultInfo creditResultInfo;

    @Data
    public static class CreditInfo {
        /**
         * 授信流水号
         */
        private String creditNo;

        /**
         * 用户编号
         */
        private String userId;

        /**
         * 借款人姓名
         */
        private String custName;

        /**
         * 身份证
         */
        private String idNo;

        /**
         * 授信金额（单位元，精确到小数点后两位）
         */
        private String creditAmount;

        /**
         * 申请期限（单位：月）
         */
        private String applyTerm;

        /**
         * 借款用途编码
         */
        private String applyUse;

        /**
         * 性别
         */
        private String sex;

        /**
         * 民族
         */
        private String nation;

        /**
         * 出生日期（格式：yyyy-MM-dd）
         */
        private String birthday;

        /**
         * 户籍省区域编码
         */
        private String idProvinceCode;

        /**
         * 户籍市区域编码
         */
        private String idCityCode;

        /**
         * 户籍区区域编码
         */
        private String idAreaCode;

        /**
         * 户籍省
         */
        private String idProvince;

        /**
         * 户籍市
         */
        private String idCity;

        /**
         * 户籍区
         */
        private String idArea;

        /**
         * 户籍地址
         */
        private String idAddress;

        /**
         * 签发机关
         */
        private String signOrganization;

        /**
         * 身份证有效期起始时间（格式：yyyy-MM-dd）
         */
        private String idValidDateBegin;

        /**
         * 身份证有效期结束时间（格式：yyyy-MM-dd，长期：2099-12-31）
         */
        private String idValidDateEnd;

        /**
         * 手机号
         */
        private String phoneNo;

        /**
         * 居住省区域编码
         */
        private String liveProvinceCode;

        /**
         * 居住市区域编码
         */
        private String liveCityCode;

        /**
         * 居住区区域编码
         */
        private String liveAreaCode;

        /**
         * 居住省
         */
        private String liveProvince;

        /**
         * 居住市
         */
        private String liveCity;

        /**
         * 居住区
         */
        private String liveArea;

        /**
         * 详细居住地址
         */
        private String liveAddress;

        /**
         * 居住地址邮编
         */
        private String liveAddressPostcode;

        /**
         * 住宅电话
         */
        private String liveAddressPhone;

        /**
         * 客户学历
         */
        private String custEducation;

        /**
         * 学位
         */
        private String degree;

        /**
         * 邮箱
         */
        private String email;

        /**
         * 银行预留手机号
         */
        private String phone;

        /**
         * 银行卡号
         */
        private String bankCardNum;

        /**
         * 银行编码
         */
        private String bankCode;

        /**
         * 婚姻状况
         */
        private String marryType;

        /**
         * 子女状况（1:无, 2:有, 3:子女求学中, 4:子女已工作, 5:未说明情况）
         */
        private String childrenStatus;

        /**
         * 配偶姓名
         */
        private String partnerName;

        /**
         * 配偶联系方式
         */
        private String partnerPhone;

        /**
         * 配偶身份证号
         */
        private String partnerIdNo;

        /**
         * 配偶单位名称
         */
        private String partnerWorkUnit;

        /**
         * 职业
         */
        private String occupation;

        /**
         * 个人月收入
         */
        private String income;

        /**
         * 家庭年收入
         */
        private String famliySalary;

        /**
         * 单位名称
         */
        private String companyName;

        /**
         * 单位所在省区域编码
         */
        private String companyProvinceCode;

        /**
         * 单位所在市区域编码
         */
        private String companyCityCode;

        /**
         * 单位所在区区域编码
         */
        private String companyAreaCode;

        /**
         * 单位所在省
         */
        private String companyProvince;

        /**
         * 单位所在市
         */
        private String companyCity;

        /**
         * 单位所在区
         */
        private String companyArea;

        /**
         * 单位详细地址
         */
        private String companyAddress;

        /**
         * 单位联系方式
         */
        private String companyPhone;

        /**
         * 本单位工作开始时间（格式：yyyy-MM-dd）
         */
        private String workStartTime;

        /**
         * 本单位工作结束时间（格式：yyyy-MM-dd）
         */
        private String workEndTime;

        /**
         * 欺诈分
         */
        private String fraudScore;

        /**
         * 设备品牌
         */
        private String deviceBrand;

        /**
         * 手机型号
         */
        private String devAlias;

        /**
         * 设备ID
         */
        private String deviceId;

        /**
         * IP地址
         */
        private String clientIp;

        /**
         * 经度（至少精确到小数点后5位）
         */
        private String lng;

        /**
         * 纬度（至少精确到小数点后5位）
         */
        private String lat;

        /**
         * 设备GPS定位城市
         */
        private String gpsCity;

        /**
         * LBS定位地址
         */
        private String lbsAddress;

        /**
         * GPS定位地址
         */
        private String gpsAddress;

        /**
         * 操作系统（Android/IOS）
         */
        private String os;

        /**
         * 手机系统版本号
         */
        private String osVersion;

        /**
         * 人脸识别分数
         */
        private String faceScore;

        /**
         * 人脸识别渠道
         */
        private String faceSource;

        /**
         * 人脸完成时间（格式：yyyy-MM-dd HH:mm:ss）
         */
        private String faceTime;

        /**
         * 联系人列表
         */
        private List<ContactInfo> contactList;

        /**
         * 图片列表
         */
        private List<PictureInfo> pictureList;

        /**
         * 文件列表
         */
        private List<FileInfo> fileList;

        /**
         * 职务
         */
        private String duty;

        /**
         * 职称
         */
        private String technical;

        /**
         * 公司所属行业
         */
        private String companyTrade;

        /**
         * 公司性质
         */
        private String companyNature;

        /**
         * 就业状况
         */
        private String employment;

        /**
         * 居住状况
         */
        private String residentialStatus;

        /**
         * 渠道编码（例如：LXJAPI:乐享借）
         */
        private String channel;

        /**
         * 客户渠道来源
         */
        private String sourceChannel;

        /**
         * 申请时间（格式：yyyy-MM-dd HH:mm:ss）
         */
        private String applyTime;
    }

    @Data
    public static class CreditResultInfo {
        /**
         * 审批结果
         */
        private String approvalResult;

        /**
         * 审批结果描述
         */
        private String approvalResultDesc;

        /**
         * 审批额度（单位元，若审批拒绝则无）
         */
        private String totalCredit;

        /**
         * 额度失效日期（格式：yyyy-MM-dd）
         */
        private String subExpDate;
    }

    @Data
    public static class PictureInfo {
        /**
         * 图片传输方式（0：url）
         */
        private String methods;

        /**
         * 图片类型（0:身份证头像照, 1:身份证国徽照, 2:活检照片）
         */
        private String pictureType;

        /**
         * 图片地址
         */
        private String pictureUrl;
    }

    @Data
    public static class ContactInfo {
        /**
         * 联系人关系
         */
        private String contactRelation;

        /**
         * 联系人姓名
         */
        private String contactName;

        /**
         * 联系人手机号
         */
        private String contactPhoneNo;
    }

    @Data
    public static class FileInfo {
        /**
         * 文件名
         */
        private String fileName;

        /**
         * 文件code
         */
        private String fileCode;

        /**
         * 文件路径
         */
        private String fileUrl;
    }
}