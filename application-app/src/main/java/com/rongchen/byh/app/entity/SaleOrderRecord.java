package com.rongchen.byh.app.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**

 * 项目名称：byh_java
 * 文件名称: SaleOrderRecord
 * 创建时间: 2025-03-21 16:57
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.app.entity
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */

/**
 * 权益赊销下单表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "`sale_order_record`")
public class SaleOrderRecord implements Serializable {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 权益渠道
     */
    @TableField(value = "`sale_channel`")
    private String saleChannel;

    /**
     * 用户ID
     */
    @TableField(value = "`user_id`")
    private String userId;

    /**
     * 内部单号
     */
    @TableField(value = "`sale_no`")
    private String saleNo;

    /**
     * 权益外部单号
     */
    @TableField(value = "`out_sale_no`")
    private String outSaleNo;

    /**
     * 支付单号
     */
    @TableField(value = "`payment_no`")
    private String paymentNo;

    /**
     * 订单金额(元)
     */
    @TableField(value = "`order_amount`")
    private BigDecimal orderAmount;

    /**
     * 用户手机号(AES加密)
     */
    @TableField(value = "`user_mobile`")
    private String userMobile;

    /**
     * 支付方式(BAOFU_PROXY_PAY-宝付)
     */
    @TableField(value = "`pay_way`")
    private String payWay;

    /**
     * 券包号
     */
    @TableField(value = "`coupon_package_id`")
    private String couponPackageId;

    /**
     * 券包名
     */
    @TableField(value = "`coupon_package_name`")
    private String couponPackageName;

    /**
     * 支付完成时间
     */
    @TableField(value = "`pay_time`")
    private Date payTime;

    /**
     * 权益失效时间
     */
    @TableField(value = "`expire_time`")
    private Date expireTime;

    /**
     * 订单状态(PAY_ING-支付中,PAY_SUCCESS-支付成功,REFUND_ING-退款中,REFUND_SUCCESS-退款成功,REFUND_FAIL-退款失败)
     */
    @TableField(value = "`order_status`")
    private String orderStatus;

    /**
     * 可退状态(1-可退,0-不可退)
     */
    @TableField(value = "`refundable`")
    private Boolean refundable;

    /**
     * 退款金额(元)
     */
    @TableField(value = "`return_amount`")
    private BigDecimal returnAmount;

    /**
     * 重试次数
     */
    @TableField(value = "`retry_num`")
    private Long retryNum;

    /**
     * 退款时间
     */
    @TableField(value = "`return_time`")
    private Date returnTime;

    /**
     * 创建时间
     */
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}