package com.rongchen.byh.app.api.service.strategy.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rongchen.byh.app.api.dto.credit.CreditDto;
import com.rongchen.byh.app.api.dto.credit.CreditQueryDto;
import com.rongchen.byh.app.api.dto.credit.CreditResultNotifyDto;
import com.rongchen.byh.app.api.dto.loan.LoanInfoNoticeDto;
import com.rongchen.byh.app.api.dto.loan.SaleInfoDto;
import com.rongchen.byh.app.api.dto.loan.loanInfoDto;
import com.rongchen.byh.app.api.dto.loan.loanResultInfoDto;
import com.rongchen.byh.app.api.service.strategy.process.OutApiAbstractProcess;
import com.rongchen.byh.app.api.vo.credit.CreditQueryVo;
import com.rongchen.byh.app.config.ZifangFactory;
import com.rongchen.byh.app.constant.LoanUseType;
import com.rongchen.byh.app.dao.*;
import com.rongchen.byh.app.entity.*;
import com.rongchen.byh.app.service.*;
import com.rongchen.byh.app.utils.BankCodeUtil;
import com.rongchen.byh.common.api.zifang.dto.ContractSignedQueryDto;
import com.rongchen.byh.common.api.zifang.dto.SaleSignedQueryDto;
import com.rongchen.byh.common.api.zifang.service.mayi.MaYiService;
import com.rongchen.byh.common.api.zifang.service.mayi.config.ZiFangProperties;
import com.rongchen.byh.common.api.zifang.utils.ZiFangUtil;
import com.rongchen.byh.common.api.zifang.vo.BaseVo;
import com.rongchen.byh.common.core.constant.SettleFlagConstant;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.HttpUtil;
import com.rongchen.byh.common.rabbitmq.constants.QueueConstant;
import com.rongchen.byh.common.rabbitmq.dto.LoanSuccessDto;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 蚂蚁外部api业务
 * @date 2025/3/20 11:59:40
 */
@Service
@Slf4j
public class MaYiOutApiProcess extends OutApiAbstractProcess {

    @Resource
    DisburseDataMapper disburseDataMapper;
    @Resource
    UserCreditDataMapper userCreditDataMapper;
    @Resource
    RabbitTemplate rabbitTemplate;
    @Resource
    private ZiFangProperties ziFangProperties;
    @Resource
    private ApiCreditRecordMapper apiCreditRecordMapper;

    @Autowired
    private DisburseService disburseService;
    @Autowired
    private RepayScheduleService repayScheduleService;
    @Autowired
    private SaleRepayService saleRepayService;
    @Resource
    UserCapitalRecordMapper userCapitalRecordMapper;
    @Resource
    RedissonClient redissonClient;
    @Resource
    private UserBankCardMapper userBankCardMapper;
    @Resource
    private UserDetailMapper userDetailMapper;
    @Resource
    private ApiUserContractMapper apiUserContractMapper;
    @Resource
    private MaYiService maYiService;

    @Resource(name = "taskExecutor")
    private Executor taskExecutor;
    @Resource
    RepayScheduleApplyMapper repayScheduleApplyMapper;
    @Resource
    RepaySaleApplyMapper repaySaleApplyMapper;
    @Resource
    ZifangFactory zifangFactory;
    @Resource
    CapitalDataMapper capitalDataMapper;
    @Autowired
    private DisburseDataService disburseDataService;
    @Resource
    MaYiOutApiProcess maYiOutApiProcess;
    @Resource
    RepayScheduleApplyService repayScheduleApplyService;

    @Override
    public JSONObject credit(JSONObject param) {
        try {
            JSONObject jsonObject = this.parseParam(param);
            log.info("api授信通知渠道：mayi，解密后参数：{}", jsonObject);
            Map<String, String> contextMap = MDC.getCopyOfContextMap();
            String traceId = contextMap.getOrDefault("traceId", DateUtil.now());
            jsonObject.put("traceId", traceId);
            CreditDto creditDto = jsonObject.toJavaObject(CreditDto.class);
            if (ObjectUtil.isEmpty(creditDto)) {
                log.info("creditDto 为空 提前结束");
                JSONObject result = new JSONObject();
                result.put("responseCode", "0002");
                result.put("responseMsg", "creditDto 为空");
                return buildResult(result);
            }
            CreditDto.CreditInfo creditInfo = creditDto.getCreditInfo();
            String creditNo = creditDto.getCreditInfo().getCreditNo();
            if (StrUtil.isEmpty(creditNo)) {
                log.info("creditNo 为空 提前结束");
                JSONObject result = new JSONObject();
                result.put("responseCode", "0002");
                result.put("responseMsg", "creditDto 为空");
                return buildResult(result);
            }

            ApiCreditRecord record = apiCreditRecordMapper.selectByCreditNo(creditNo);
            if (record != null) {
                JSONObject result = new JSONObject();
                result.put("responseCode", "0000");
                result.put("responseMsg", "成功");
                return buildResult(result);
            }

            // 组装进件数据
            ApiCreditRecord apiCreditRecord = new ApiCreditRecord();
            apiCreditRecord.setCreditNo(creditNo);
            apiCreditRecord.setOutUserId(creditInfo.getUserId());
            apiCreditRecord.setCreditStatus(1);
            apiCreditRecord.setFailReason("授信中");
            apiCreditRecord.setChannel(creditInfo.getChannel());
            if (StrUtil.isNotEmpty(creditInfo.getSourceChannel())) {
                apiCreditRecord.setSourceChannel(creditInfo.getSourceChannel());
            }
            apiCreditRecordMapper.insert(apiCreditRecord);
            rabbitTemplate.convertAndSend(QueueConstant.MAYI_API_CREDIT_QUEUE, JSONObject.toJSONString(jsonObject));
            JSONObject result = new JSONObject();
            result.put("responseCode", "0000");
            result.put("responseMsg", "成功");
            return buildResult(result);
        } catch (Exception e) {
            JSONObject result = new JSONObject();
            result.put("responseCode", "9999");
            result.put("responseMsg", "异常");
            return buildResult(result);
        }
    }

    @Override
    public JSONObject creditQueryResult(JSONObject param) {
        JSONObject request = parseParam(param);
        CreditQueryDto creditQueryDto = request.toJavaObject(CreditQueryDto.class);
        String creditNo = creditQueryDto.getCreditNo();
        ApiCreditRecord record = apiCreditRecordMapper.selectByCreditNo(creditNo);
        if (record == null) {
            CreditQueryVo resultVo = CreditQueryVo.builder().responseCode("9999").responseMsg("暂无数据").build();
            JSONObject jsonObject = (JSONObject) JSONObject.toJSON(resultVo);
            return buildResult(jsonObject);
        }
        int creditStatus = Optional.ofNullable(record.getCreditStatus()).orElse(1);
        CreditQueryVo resultVo = new CreditQueryVo();
        resultVo.setResponseCode("0000");
        resultVo.setResponseMsg("成功");
        String approval = "";
        String approvalMsg = "";
        if (creditStatus == 1) {
            approval = "03";
            approvalMsg = "授信中";
        } else if (creditStatus == 2) {
            approval = "01";
            approvalMsg = "授信成功";
        } else if (creditStatus == 3) {
            approval = "02";
            approvalMsg = "授信失败";
        }
        resultVo.setApprovalResult(approval);
        resultVo.setApprovalResultDesc(approvalMsg);
        if ("01".equals(approval)) {
            resultVo.setTotalCredit(Optional.ofNullable(record.getCreditMoney()).orElse(BigDecimal.ZERO).toString());
            String dateStr = DateUtil.offsetDay(record.getCreateTime(), 30).toString("yyyy-MM-dd");
            resultVo.setSubExpDate(dateStr);
            resultVo.setRateType("02");
            resultVo.setQuotaRate("24");
        }
        JSONObject jsonObject = (JSONObject) JSONObject.toJSON(resultVo);
        return buildResult(jsonObject);
    }

    /**
     * 回调授信接口
     *
     * @param creditNo
     * @return
     */
    public ResponseResult<Void> creditResultNotify(String creditNo) throws Exception {

        ApiCreditRecord record = apiCreditRecordMapper.selectByCreditNo(creditNo);
        CreditResultNotifyDto dto = new CreditResultNotifyDto();
        dto.setCreditNo(creditNo);
        int creditStatus = Optional.ofNullable(record.getCreditStatus()).orElse(1);
        String approval = "";
        String approvalMsg = "";
        if (creditStatus == 1) {
            approval = "03";
            approvalMsg = "授信中";
        } else if (creditStatus == 2) {
            approval = "01";
            approvalMsg = "授信成功";
        } else if (creditStatus == 3) {
            approval = "02";
            approvalMsg = "授信失败";
        }
        dto.setApprovalResult(approval);
        dto.setApprovalResultDesc(approvalMsg);
        if ("01".equals(approval)) {
            dto.setTotalCredit(Optional.ofNullable(record.getCreditMoney()).orElse(BigDecimal.ZERO).toString());
            String dateStr = DateUtil.offsetDay(record.getCreateTime(), 30).toString("yyyy-MM-dd");
            dto.setSubExpDate(dateStr);
            dto.setRateType("02");
            dto.setQuotaRate("24");
        }
        dto.setUserId(record.getOutUserId());
        // todo
        dto.setReqSysCode(ziFangProperties.getReqSysCode2());
        dto.setProductCode(ziFangProperties.getProductCode2());

        JSONObject jsonObject = (JSONObject) JSONObject.toJSON(dto);
        JSONObject resJson = sendRequest(jsonObject, "/channel/{channel}/creditResultNotify");

        return ResponseResult.success();

    }

    protected String getUrl(String channel, String path) {
        return StrUtil.replace(path, "{channel}", channel);
    }

    protected JSONObject sendRequest(JSONObject params, String path) throws Exception {
        log.info("【资方接口】 地址：{}", path);
        log.info("【资方接口】 加密前参数：{}", params);
        String body = ZiFangUtil.buildRequest(params, ziFangProperties.getPrivateKey(),
                ziFangProperties.getOutPublicKey());
        log.info("【资方接口】 加密后参数：{}", body);
        String res = HttpUtil.postJson(ziFangProperties.getHost() + getUrl(ziFangProperties.getChannelCode2(), path),
                body);
        log.info("【资方接口】 解密前响应：{}", res);
        JSONObject map = ZiFangUtil.parseRequest(res, ziFangProperties.getPrivateKey(),
                ziFangProperties.getOutPublicKey());
        log.info("【资方接口】 解密后响应：{}", map);
        return map;
    }

    @Override
    public String getChannel() {
        return "mayi";
    }

    @Override
    protected JSONObject parseParam(JSONObject param) {
        try {
            JSONObject result = ZiFangUtil.parseRequest(param.toJSONString(), ziFangProperties.getPrivateKey(),
                    ziFangProperties.getOutPublicKey());
            log.info("【蚂蚁资方api接口】，解密结果：{}", result);
            return result;
        } catch (Exception e) {
            log.error("【蚂蚁资方api接口】 解密异常", e);
        }
        return new JSONObject();
    }

    @Override
    protected JSONObject buildResult(JSONObject result) {
        try {
            String res = ZiFangUtil.buildNoticeResponse(result.toJSONString(), ziFangProperties.getPrivateKey(),
                    ziFangProperties.getOutPublicKey());
            return JSONObject.parseObject(res);
        } catch (Exception e) {
            log.error("【蚂蚁资方api接口】 加密", e);
        }
        return new JSONObject();
    }

    @Override
    public JSONObject getRepayNotice(JSONObject param) {
        JSONObject jsonObject = this.parseParam(param);
        // 1.判断还款状态成功还是失败，如果是失败则不进行后面的逻辑
        String status = jsonObject.getString("status");
        if ("SUCCESS".equals(status)) {
            String loanNo = jsonObject.getString("loanNo");
            // 根据货款编号找到user_id
            DisburseData data = new DisburseData();
            data.setLoanNo(loanNo);
            DisburseData disburseData = disburseService.selectOneDisburse(data);
            if (disburseData == null) {
                log.info("api还款结果没有该货款编码对应的用户，还款编码是：{}", loanNo);
                return buildResult(new JSONObject());
            }
            String repayTerm = jsonObject.getString("repayTerm");
            // 还款期数存在多个期数用"，"隔开 所以要取出所有的期数
            RepaySchedule repaySchedule = new RepaySchedule();
            repaySchedule.setUserId(disburseData.getUserId());
            String repayType = jsonObject.getString("repayType");
            // CLEAN-全部结清、OVERDUE-归还逾期、CURRENT-归还当期到期、OVER-归还到期（逾期+当期到期）、PREPAYMENT-提前还当期
            // 判断还款类型
            if ("OVERDUE".equals(repayType)) {
                this.handleNormal(repaySchedule, jsonObject,"O");
            } else if ("CLEAN".equals(repayType)) {
                this.handleClean(disburseData, jsonObject);
            } else {
                this.handleNormal(repaySchedule, jsonObject,"N");
            }
            return buildResult(retOk());
        } else {
            return buildResult(retFail());
        }
    }

    public JSONObject retFail() {
        return resJson("9999","失败");
    }
    public JSONObject retOk() {
        return resJson("0000","成功");
    }

    public JSONObject resJson(String code,String msg) {
        JSONObject object = new JSONObject();
        object.put("responseCode",code);
        object.put("responseMsg",msg);
        return object;
    }


    /**
     * 提前结清
     * @param disburseData
     * @param jsonObject
     */
    private void handleClean(DisburseData disburseData, JSONObject jsonObject) {
        // 查询所有账单
        LambdaQueryWrapper<RepaySchedule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RepaySchedule::getDisburseId,disburseData.getId());
        List<RepaySchedule> list = repayScheduleService.list(queryWrapper);
        if (CollectionUtil.isEmpty(list)) {
            log.error("【api还款结果提前结清】借款单号：{} 查询还款计划为空", disburseData.getLoanNo());
            return;
        }
        String repayTerm = jsonObject.getString("repayTerm");
        String repayTime = jsonObject.getString("repayTime");
        List<String> split = StrUtil.split(repayTerm, ",");
        List<RepaySchedule> upList = new ArrayList<>(12);
        List<RepaySchedule> delList = new ArrayList<>(12);
        String repayMode = jsonObject.getString("repayMode");
        String repayApplyNo = jsonObject.getString("repayApplyNo");
        BigDecimal amt = jsonObject.getBigDecimal("amt");

        // 已还利息
        BigDecimal alreadyPaidInterest = new BigDecimal("0.00");
        // 总还款金额
        BigDecimal totalAmount = new BigDecimal("0.00");

        List<RepayScheduleApply> repayScheduleApplyList = new ArrayList<>(12);

        for (RepaySchedule repaySchedule : list) {
            if (repaySchedule.getSettleFlag().equals(SettleFlagConstant.CLOSE)) {
                totalAmount = totalAmount.add(repaySchedule.getTotalAmt());
                alreadyPaidInterest.add(repaySchedule.getTermRetInt());
                continue;
            }
            String repayMethod = "0";
            if ("02".equals(repayMode)) {
                repayMethod = "1";
            }
            RepaySchedule upData = new RepaySchedule();
            upData.setId(repaySchedule.getId());
            if (split.contains(repaySchedule.getRepayTerm())) {
                upData.setSettleFlag("CLOSE");
                upData.setRepayApplyNo(repayApplyNo);
                upData.setRepayMethod(repayMethod);
                upData.setDatePay(repayTime.substring(0, 10));
                upData.setDatePayTime(repayTime);

                upData.setTotalAmt(amt);

                totalAmount = totalAmount.add(amt);

                upList.add(upData);

                try {
                    RepayScheduleApply repayScheduleApply = new RepayScheduleApply();
                    repayScheduleApply.setRepayApplyNo(repayApplyNo);
                    repayScheduleApply.setUserId(repaySchedule.getUserId());
                    repayScheduleApply.setRepayScheduleId(repaySchedule.getId());
                    repayScheduleApply.setRepayType(1);
                    repayScheduleApply.setRepayStatus(1);
                    repayScheduleApplyList.add(repayScheduleApply);
                } catch (Exception e) {
                    log.error("保存本息还款记录失败: ", e);
                }
            } else {
                delList.add(upData);
            }
        }

        // 总利息
        BigDecimal totalInterest = totalAmount.subtract(disburseData.getCreditAmount());

        DisburseData up = new DisburseData();
        up.setId(disburseData.getId());
        up.setCreditStatus(600);
        up.setGrossInterest(totalInterest);
        up.setRepaymentTime(new Date());

        maYiOutApiProcess.settleDown(up,upList,delList,repayScheduleApplyList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void settleDown(DisburseData up,List<RepaySchedule> upList,List<RepaySchedule> delList,List<RepayScheduleApply> applyList) {
        // 删除其他的还款账单
        repayScheduleService.removeByIds(delList);
        // 修改账单
        repayScheduleService.updateBatchById(upList);
        // 修改支用表状态
        disburseDataService.updateById(up);
        // 添加还款记录
        repayScheduleApplyService.saveBatch(applyList);
    }

    /**
     * 正常还款或逾期还款
     * @param repaySchedule
     * @param jsonObject
     * @param termStatus
     */
    private void handleNormal(RepaySchedule repaySchedule, JSONObject jsonObject,String termStatus) {
        String repayTerm = jsonObject.getString("repayTerm");
        String repayTime = jsonObject.getString("repayTime");
        String[] res = repayTerm.split(",");
        for (String str : res) {
            // 查询本期的还款计划数据
            repaySchedule.setRepayTerm(str);
            RepaySchedule repay1 = repayScheduleService.getBaseMapper().seletLast(repaySchedule);
            repay1.setSettleFlag("CLOSE");
            repay1.setRepayApplyNo(jsonObject.getString("repayApplyNo"));
            repay1.setTermStatus(termStatus);
            String repayMethod = "0";
            if ("02".equals(jsonObject.getString("repayMode"))) {
                repayMethod = "1";
            }
            repay1.setRepayMethod(repayMethod);
            repay1.setDatePay(repayTime.substring(0, 10));
            repay1.setDatePayTime(repayTime);
            repay1.setUpdateTime(new Date());
            repayScheduleService.updateById(repay1);
            try {
                RepayScheduleApply repayScheduleApply = new RepayScheduleApply();
                repayScheduleApply.setRepayApplyNo(jsonObject.getString("repayApplyNo"));
                repayScheduleApply.setUserId(repay1.getUserId());
                repayScheduleApply.setRepayScheduleId(repay1.getId());
                repayScheduleApply.setRepayType(1);
                repayScheduleApply.setRepayStatus(1);
                repayScheduleApplyMapper.insert(repayScheduleApply);
            } catch (Exception e) {
                log.error("保存本息还款记录失败: ", e);
            }

        }
    }

    @Override
    public JSONObject getSaleNotice(JSONObject param) {
        JSONObject jsonObject = this.parseParam(param);
        // 1.判断还款状态成功还是失败，如果是失败则不进行后面的逻辑
        String status = jsonObject.getString("status");
        if ("SUCCESS".equals(status)) {
            String saleNo = jsonObject.getString("saleNo");
            String repayTime = jsonObject.getString("repayTime");
            // 根据赊销编号找到user_id
            DisburseData data = new DisburseData();
            data.setSaleNo(saleNo);
            DisburseData disburseData = disburseService.selectOneDisburse(data);

            if (disburseData == null) {
                log.info("api还款结果没有该货款编码对应的用户，还款编码是：{}", saleNo);
                return buildResult(new JSONObject());
            }
            String repayTerm = jsonObject.getString("repayTerm");
            // 还款期数存在多个期数用"，"隔开 所以要取出所有的期数
            SaleSchedule saleSchedule = new SaleSchedule();
            saleSchedule.setUserId(disburseData.getUserId());
            String[] res = repayTerm.split(",");
            for (String str : res) {
                // 查询本期的还款计划数据
                saleSchedule.setRepayTerm(str);
                SaleSchedule saleSchedule1 = saleRepayService.selectLast(saleSchedule);
                saleSchedule1.setRepayApplyNo(jsonObject.getString("repayApplyNo"));
                saleSchedule1.setSettleFlag("CLOSE");
                saleSchedule1.setDatePay(repayTime.substring(0, 10));
                saleSchedule1.setDatePayTime(repayTime);
                saleSchedule1.setUpdateTime(new Date());
                saleRepayService.updateById(saleSchedule1);

                try {
                    RepaySaleApply repaySaleApply = new RepaySaleApply();
                    repaySaleApply.setRepayApplyNo(jsonObject.getString("repayApplyNo"));
                    repaySaleApply.setUserId(saleSchedule1.getUserId());
                    repaySaleApply.setSaleScheduleId(saleSchedule1.getId());
                    repaySaleApply.setRepayType(1);
                    repaySaleApply.setRepayStatus(1);
                    repaySaleApplyMapper.insert(repaySaleApply);
                } catch (Exception e) {
                    log.error("保存赊销还款记录失败: ", e);
                }
            }
            return buildResult(retOk());
        } else {
            return buildResult(retFail());
        }
    }

    @Override
    public JSONObject loan(JSONObject param) {
        JSONObject jsonObject = this.parseParam(param);
        LoanInfoNoticeDto loanInfoNoticeDto = jsonObject.toJavaObject(LoanInfoNoticeDto.class);
        log.info("【蚂蚁资方api用信通知接口请求参数】 loanInfoNoticeDto：{}", loanInfoNoticeDto);

        BaseVo baseVo = new BaseVo();
        baseVo.setResponseCode("0000");
        baseVo.setResponseMsg("成功");

        String fundCode = loanInfoNoticeDto.getFundCode();

        loanInfoDto loanInfo = loanInfoNoticeDto.getLoanInfo();
        loanResultInfoDto loanResultInfo = loanInfoNoticeDto.getLoanResultInfo();

        String loanNo = loanInfo.getLoanNo();

        RLock lock = redissonClient.getLock("loanNoLock" + loanNo);
        boolean isLocked = false;
        try {
            isLocked = lock.tryLock(5, TimeUnit.SECONDS);
            if (!isLocked) {
                baseVo.setResponseCode("9999");
                baseVo.setResponseMsg("操作繁忙,loanNo:" + loanNo);
                return buildResult(JSON.parseObject(baseVo.toString()));
            }

            String loanResult = loanResultInfo.getLoanResult();
            DisburseData disburseData = disburseDataMapper.selectByLoanNo(loanNo);
            if (disburseData == null) {
                disburseData = buildDisburseData(loanInfoNoticeDto);
            }

            // 放款失败
            if ("04".equals(loanResult)) {
                disburseData.setCreditStatus(400);
                disburseDataMapper.updateById(disburseData);
            } else if ("06".equals(loanResult)) {
                disburseData.setCreditStatus(500);
                disburseData.setFundCode(fundCode);
                disburseData.setLoanTime(DateUtil.parse(loanResultInfo.getLoanTime()));
                disburseDataMapper.updateById(disburseData);

                UserCreditData userCreditData = userCreditDataMapper.queryByUserId(disburseData.getUserId());
                UserCreditData upCreditData = new UserCreditData();
                upCreditData.setId(userCreditData.getId());
                BigDecimal subtract = userCreditData.getResidueAmount().subtract(disburseData.getCreditAmount());
                upCreditData.setResidueAmount(subtract);
                BigDecimal add = userCreditData.getWithdrawAmount().add(disburseData.getCreditAmount());
                upCreditData.setWithdrawAmount(add);
                userCreditDataMapper.updateById(upCreditData);

                Map<String, String> contextMap = MDC.getCopyOfContextMap();
                String traceId = contextMap.getOrDefault("traceId", DateUtil.now());

                LoanSuccessDto loanSuccessDto = new LoanSuccessDto();
                loanSuccessDto.setLoanNo(loanNo);
                loanSuccessDto.setTraceId(traceId);
                // 绑卡
                UserBankCard userBankCard = new UserBankCard();
                userBankCard.setUserId(disburseData.getUserId());
                userBankCard.setIdCard(loanInfo.getIdNo());
                userBankCard.setBankName(BankCodeUtil.getBankName(loanInfo.getBankCode()));
                userBankCard.setBankAccount(loanInfo.getBankCardNum());
                userBankCard.setMobile(loanInfo.getBankPhone());
                userBankCard.setContractNum("webNoContractNum");
                UserDetail userDetail = userDetailMapper.queryByUserId(disburseData.getUserId());
                if (ObjectUtil.isNotEmpty(userDetail)) {
                    userBankCard.setCustomerName(userDetail.getAppName());
                }
                userBankCardMapper.insert(userBankCard);
                rabbitTemplate.convertAndSend(QueueConstant.LOAN_SUCCESS_API_QUEUE,
                        JSONObject.toJSONString(loanSuccessDto));

            } else {
                baseVo.setResponseCode("9999");
                baseVo.setResponseMsg("状态不正确");
            }
            // 下载合同
            downloadContract(loanInfoNoticeDto, disburseData.getUserId());
            return buildResult(JSON.parseObject(JSONObject.toJSONString(baseVo)));
        } catch (Exception e) {
            log.error("【蚂蚁资方api用信通知接口请求参数】 借款单号：{}", loanNo, e);
            baseVo.setResponseCode("9999");
            baseVo.setResponseMsg("系统异常：" + e.getMessage());
            return buildResult(JSON.parseObject(JSONObject.toJSONString(baseVo)));
        } finally {
            if (isLocked) {
                lock.unlock();
            }
        }

    }

    public void downloadContract(LoanInfoNoticeDto loanInfoNoticeDto, Long userId) {
        List<CompletableFuture<?>> futureList = new ArrayList<>();
        // 下载本息合同
        futureList.add(CompletableFuture.supplyAsync(() -> {
            ContractSignedQueryDto contractSignedQueryDto = new ContractSignedQueryDto();
            contractSignedQueryDto.setLoanNo(loanInfoNoticeDto.getLoanInfo().getLoanNo());
            return maYiService.apiContractSignedQuery(contractSignedQueryDto);
        }, taskExecutor)
                .thenAccept(result -> {
                    if (result.isSuccess()) {
                        ApiUserContract apiUserContract = new ApiUserContract();
                        apiUserContract.setLoanNo(loanInfoNoticeDto.getLoanInfo().getLoanNo());
                        apiUserContract.setUserId(userId);
                        apiUserContract.setType(0);
                        apiUserContract.setContractInfo(JSONObject.toJSONString(result.getData()));
                        apiUserContractMapper.insert(apiUserContract);
                    }
                }).exceptionally((t) -> {
                    log.error("异步任务执行异常", t);
                    return null;
                }));

        // 下载赊销合同
        List<SaleInfoDto> saleInfoList = loanInfoNoticeDto.getLoanInfo().getSaleInfoList();
        for (SaleInfoDto saleInfoDto : saleInfoList) {
            CompletableFuture.supplyAsync(() -> {
                SaleSignedQueryDto queryDto = new SaleSignedQueryDto();
                queryDto.setSaleNo(saleInfoDto.getSaleNo());
                return maYiService.getApiSaleSignedQuery(queryDto);
            }, taskExecutor).thenAccept(result -> {
                if (result.isSuccess()) {
                    ApiUserContract apiUserContract = new ApiUserContract();
                    apiUserContract.setLoanNo(saleInfoDto.getSaleNo());
                    apiUserContract.setUserId(userId);
                    apiUserContract.setType(1);
                    apiUserContract.setContractInfo(JSONObject.toJSONString(result.getData()));
                    apiUserContractMapper.insert(apiUserContract);
                }
            });
        }
        DateTime startTime = DateUtil.date();
        try {
            // 下载合同看日志平均耗时在5秒以上，所以设置30秒超时
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).get(30, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("下载合同异常，userId:{},耗时：{},异常", userId,
                    DateUtil.between(startTime, DateUtil.date(), DateUnit.SECOND), e);
        }
    }

    /**
     * 构建保存支出数据
     *
     * @param loanInfoNoticeDto
     * @return
     */
    private DisburseData buildDisburseData(LoanInfoNoticeDto loanInfoNoticeDto) {
        loanInfoDto loanInfo = loanInfoNoticeDto.getLoanInfo();
        List<SaleInfoDto> saleInfoList = loanInfo.getSaleInfoList();
        loanResultInfoDto loanResultInfo = loanInfoNoticeDto.getLoanResultInfo();

        String loanNo = loanInfo.getLoanNo();

        ApiCreditRecord apiCreditRecord = apiCreditRecordMapper.selectByCreditNo(loanInfo.getCreditNo());
        Long userId = apiCreditRecord.getUserId();

        DisburseData disburseData = new DisburseData();
        disburseData.setUserId(userId);
        disburseData.setProductId(1L);
        disburseData.setCreditNo(loanInfo.getCreditNo());
        disburseData.setLoanNo(loanNo);
        if (!saleInfoList.isEmpty() && saleInfoList.get(0) != null) {
            SaleInfoDto saleInfoDto = saleInfoList.get(0);
            disburseData.setSaleNo(saleInfoDto.getSaleNo());
            disburseData.setSaleModel(saleInfoDto.getSaleModel());
            disburseData.setSaleChannel(saleInfoDto.getSaleChannel());
        } else {
            disburseData.setSaleNo("");
            disburseData.setSaleModel("");
            disburseData.setSaleChannel("");
        }
        disburseData.setCreditAmount(new BigDecimal(loanInfo.getApplyAmount()));
        // disburseData.setGrossInterest(); // 获取利息,账单加
        disburseData.setCreditStatus(300);
        disburseData.setCreditTime(DateUtil.parse(loanResultInfo.getLoanTime()));
        // disburseData.setRepaymentTime(); // 结清时间
        disburseData.setPeriods(Integer.valueOf(loanInfo.getApplyTerm()));
        disburseData.setFundCode(loanInfoNoticeDto.getLoanResultInfo().getFundCode());
        disburseData.setPurposeLoan(LoanUseType.getDescriptionByCode(loanInfo.getApplyUse())); // 借款用途
        disburseData.setYearRete("0.36"); // 年利率
        // disburseData.setRepaymentMethod(); // 还款方式
        // disburseData.setSaleRepayAmount(); // 赊销还款金额,账单加
        disburseData.setConsultFee(0); // 咨询费用扣款状态 0-待扣款 1-扣款成功 2-扣款失败

        UserCapitalRecord userCapitalRecord = new UserCapitalRecord();
        userCapitalRecord.setUserId(userId);
        userCapitalRecord.setCapitalId(1L);
        userCapitalRecordMapper.insert(userCapitalRecord);

        disburseData.setCapitalRecordId(userCapitalRecord.getId());
        disburseData.setCapitalId(1L);
        disburseDataMapper.insert(disburseData);
        return disburseData;
    }
}
