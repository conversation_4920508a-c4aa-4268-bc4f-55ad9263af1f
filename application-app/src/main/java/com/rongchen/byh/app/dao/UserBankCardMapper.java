package com.rongchen.byh.app.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rongchen.byh.app.dto.app.BackVo;
import com.rongchen.byh.app.entity.UserBankCard;
import java.util.List;

/**
 * 项目名称：byh_java
 * 文件名称: UserBankCardMapper
 * 创建时间: 2025-04-03 11:50
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.app.dao
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
public interface UserBankCardMapper extends BaseMapper<UserBankCard> {

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(UserBankCard record);

    List<BackVo> queryBackListByUserId(Long userId);

    BackVo queryBackByUserId(Long userId);

    UserBankCard queryByUserId(Long userId);
}