package com.rongchen.byh.app.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.rongchen.byh.app.api.service.strategy.impl.MaYiOutApiProcess;
import com.rongchen.byh.app.constant.SourceMode;
import com.rongchen.byh.app.constant.UserAuditStatus;
import com.rongchen.byh.app.dao.ApiCreditRecordMapper;
import com.rongchen.byh.app.dao.SysConfigMapper;
import com.rongchen.byh.app.dao.UserCreditDataMapper;
import com.rongchen.byh.app.dao.UserDataMapper;
import com.rongchen.byh.app.dao.UserDetailMapper;
import com.rongchen.byh.app.dao.UserLoanApplyMapper;
import com.rongchen.byh.app.entity.ApiCreditRecord;
import com.rongchen.byh.app.entity.UserCreditData;
import com.rongchen.byh.app.entity.UserData;
import com.rongchen.byh.app.entity.UserDetail;
import com.rongchen.byh.app.entity.UserLoanApply;
import com.rongchen.byh.common.api.beiyihua.dto.CreditPushDto;
import com.rongchen.byh.common.api.beiyihua.service.BeiYiHuaService;
import com.rongchen.byh.common.api.riskControl.dto.CrmPushDto;
import com.rongchen.byh.common.api.riskControl.service.CrmPushService;
import com.rongchen.byh.common.api.riskControl.service.RiskControlService;
import com.rongchen.byh.common.api.riskControl.vo.PreLoanAuditVo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 风控审核任务
 * @date 2025/3/28 20:17:34
 */
@Component
@Slf4j
public class RiskAuditJob {
    @Resource
    UserLoanApplyMapper userLoanApplyMapper;
    @Resource
    RiskControlService riskControlService;
    @Resource
    UserDetailMapper userDetailMapper;
    @Resource
    ApiCreditRecordMapper apiCreditRecordMapper;
    @Resource
    UserDataMapper userDataMapper;
    @Resource
    SysConfigMapper sysConfigMapper;
    @Resource
    MaYiOutApiProcess mayiOutApiProcess;
    @Resource
    UserCreditDataMapper userCreditDataMapper;
    @Resource
    CrmPushService crmPushService;
    @Resource
    BeiYiHuaService beiYiHuaService;

    @XxlJob("riskAuditHandler")
    public void riskAuditHandler() {
        String param = XxlJobHelper.getJobParam();
        Integer day = 1;
        if (StrUtil.isNotEmpty(param)) {
            day = Integer.valueOf(param);
        }
        Map<String, String> contextMap = new HashMap<>();
        String traceId = IdUtil.fastSimpleUUID();
        contextMap.put("traceId", traceId);
        MDC.setContextMap(contextMap);
        List<UserLoanApply> userLoanApplyList = userLoanApplyMapper.selectInProcessList(day);
        if (CollUtil.isEmpty(userLoanApplyList)) {
            log.info("没有待审核的风控申请");
            return;
        }
        List<CompletableFuture<?>> futureList = new ArrayList<>();
        for (UserLoanApply apply : userLoanApplyList) {
            CompletableFuture.supplyAsync(() -> {
                MDC.setContextMap(contextMap);
                return riskControlService.queryCreditResult(apply.getCreditId());}).thenAccept(preLoanAuditVo -> {
                Integer result = preLoanAuditVo.getResult();
                UserData userDataUp = new UserData();
                userDataUp.setId(apply.getUserId());
                String creditNo = apply.getApiCreditNo();
                if (result == 2) {
                    log.info("授信id：{} 风控卡单中", creditNo);
                    return;
                }
                if (result == -1) {
                    apply.setAuditsStatus(2);
                    userLoanApplyMapper.updateById(apply);
                    // 更新用户审核状态
                    userDataUp.setAuditStatus(UserAuditStatus.FIRST_REJECT);
                    userDataMapper.updateById(userDataUp);
                    notifyMaYi(creditNo, 3, "风控拒绝", null);
                    log.error("授信id：{} 风控审核失败", creditNo);
                    return;
                }
                Long userId = apply.getUserId();
                UserDetail userDetail = userDetailMapper.queryByUserId(userId);
                UserData userData = userDataMapper.selectById(userId);
                String pushProduct = sysConfigMapper.selectByConfigKey("pushProduct");
                String phoneNo = userData.getMobile();
                String userName = userDetail.getAppName();
                String idNo = userDetail.getIdNumber();
                // 非0或非1 直接拒绝
                if (!(result == 0 || result == 1)) {
                    apply.setAuditsStatus(2);
                    userLoanApplyMapper.updateById(apply);
                    // 更新用户审核状态
                    userDataUp.setAuditStatus(UserAuditStatus.FIRST_REJECT);
                    userDataMapper.updateById(userDataUp);
                    notifyMaYi(creditNo, 3, "系统异常", null);
                    return;
                }
                // 0 或者 1
                // 根据客户风险等级判断逻辑：
                //若等级为A1~Ax， 则产品1审核通过；产品1额度同步到CRM。
                //若等级为B1~Bx,则产品1审核拒绝。但风控给的额度仍然要传给CRM
                // 产品一拒绝，推送crm
                // 确认风险等级
                if (preLoanAuditVo.getCreditRating().contains("B")) {
                    apply.setAuditsStatus(2);
                    userLoanApplyMapper.updateById(apply);
                    // 更新用户审核状态
                    userDataUp.setAuditStatus(UserAuditStatus.FIRST_REJECT);
                    userDataMapper.updateById(userDataUp);
                    // 通知上游
                    notifyMaYi(creditNo, 3, "风控拒绝", null);
                    log.error("手机号：{}，授信id：{} 风控审核失败", phoneNo, creditNo);
                    // 1 代表推送产品二
                    if ("1".equals(pushProduct)) {
                        // 同步产品二
                        pushProduct(userData, userDetail, preLoanAuditVo);
                    }
                    // 同步crm
                    pushCrm(userData, userName, idNo, preLoanAuditVo);
                    // 更新用户风险等级
                    userDetail.setRiskLevel(preLoanAuditVo.getCreditRating());
                    userDetailMapper.updateById(userDetail);
                    return;
                }
                // 产品一通过，并同步crm
                if ("1".equals(pushProduct)) {
                    // 同步产品二
                    pushProduct(userData, userDetail, preLoanAuditVo);
                }
                // 同步crm
                pushCrm(userData, userName, idNo, preLoanAuditVo);

                // 更新用户风险等级
                userDetail.setRiskLevel(preLoanAuditVo.getCreditRating());
                userDetailMapper.updateById(userDetail);
                // 审核通过
                apply.setAuditsStatus(1);
                userLoanApplyMapper.updateById(apply);
                // 更新用户审核状态
                userDataUp.setAuditStatus(UserAuditStatus.WAIT_APP_CREDIT);
                userDataUp.setAuditFlag(1);
                userDataUp.setSourceMode(SourceMode.API_MAYI);
                userDataMapper.updateById(userDataUp);
                // 保存授信额度
                UserCreditData userCreditData = new UserCreditData();
                userCreditData.setUserId(userId);
                userCreditData.setCreditAmount(ObjectUtil.isEmpty(preLoanAuditVo.getAmount()) ? BigDecimal.ZERO : preLoanAuditVo.getAmount());
                // 风控通过，剩余额度和授信额度保持一致
                userCreditData.setResidueAmount(userCreditData.getCreditAmount());
                userCreditDataMapper.insert(userCreditData);
                // 通知上游
                notifyMaYi(creditNo, 2, "成功", userCreditData.getCreditAmount());
            });
        }
        try {
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).get(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("异步任务执行异常", e);
        }
    }

    private void notifyMaYi(String creditNo, int status, String msg, BigDecimal creditMoney) {
        if (StrUtil.isEmpty(creditNo)) {
            log.error("creditNo 为空 提前结束");
            return;
        }
        ApiCreditRecord apiCreditRecord = apiCreditRecordMapper.selectByCreditNo(creditNo);
        if (ObjectUtil.isEmpty(apiCreditRecord)) {
            log.error("apiCreditRecord 为空 提前结束");
            return;
        }
        ApiCreditRecord updateRecord = new ApiCreditRecord();
        updateRecord.setId(apiCreditRecord.getId());
        updateRecord.setCreditStatus(status);
        updateRecord.setFailReason(msg);
        updateRecord.setCreditMoney(creditMoney);
        apiCreditRecordMapper.updateById(updateRecord);
        try {
            mayiOutApiProcess.creditResultNotify(creditNo);
        } catch (Exception e) {
            log.error("授信id：{} 通知mayi失败", creditNo, e);
        }
    }

    private void pushCrm(UserData userData, String userName, String idNo, PreLoanAuditVo preLoanAuditVo) {
        try {
            CrmPushDto crmPushDto = new CrmPushDto();
            crmPushDto.setMobile(userData.getMobile());
            crmPushDto.setUserName(userName);
            crmPushDto.setIdNumber(idNo);
            crmPushDto.setCreditRating(preLoanAuditVo.getCreditRating());
            crmPushDto.setLimitAmount(ObjectUtil.isEmpty(preLoanAuditVo.getAmount()) ? BigDecimal.ZERO : preLoanAuditVo.getAmount());
            crmPushService.push(crmPushDto);
        } catch (Exception e) {
            log.error("crm同步异常", e);
        }
    }

    private void pushProduct(UserData userData, UserDetail userDetail, PreLoanAuditVo preLoanAuditVo) {
        try {
            CreditPushDto creditPushDto = new CreditPushDto();
            creditPushDto.setUsername(userDetail.getAppName());
            creditPushDto.setIdCard(userDetail.getWebIdCard());
            creditPushDto.setMobile(userData.getMobile());
            creditPushDto.setCreditAmount(ObjectUtil.isEmpty(preLoanAuditVo.getAmount()) ? BigDecimal.ZERO : preLoanAuditVo.getAmount());
            beiYiHuaService.push(creditPushDto);
        } catch (Exception e) {
            log.error("产品二同步异常", e);
        }
    }
}
