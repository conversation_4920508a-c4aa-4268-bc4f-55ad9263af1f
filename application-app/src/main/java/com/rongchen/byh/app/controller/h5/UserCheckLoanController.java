package com.rongchen.byh.app.controller.h5;

import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.rongchen.byh.app.dto.app.IdCardVerifyDto;
import com.rongchen.byh.app.dto.h5.UserCheckLoanApplyDto;
import com.rongchen.byh.app.service.AppUserDetailService;
import com.rongchen.byh.app.service.UserCheckLoanService;
import com.rongchen.byh.app.vo.h5.UserCheckLoanApplyVo;
import com.rongchen.byh.common.core.annotation.DisableDataFilter;
import com.rongchen.byh.common.core.object.ResponseResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description h5页面用户相关接口
 * @date 2024/12/10 16:59:02
 */
@ApiSupport(order = 1)
@Tag(name = "h5页面用户相关接口")
@DisableDataFilter
@Slf4j
@RestController
@RequestMapping("/userApi/userCheckLoan")
public class UserCheckLoanController {
    @Resource
    private UserCheckLoanService userCheckLoanService;
    @Resource
    private AppUserDetailService appUserDetailService;
    @PostMapping("/apply")
    @Operation(summary = "初筛贷款申请")
    public ResponseResult<UserCheckLoanApplyVo> apply(@RequestBody @Validated UserCheckLoanApplyDto userCheckLoanApplyDto) {
        return userCheckLoanService.apply(userCheckLoanApplyDto);
    }

    @PostMapping("/queryResult")
    @Operation(summary = "初筛贷款申请结果查询")
    public ResponseResult<UserCheckLoanApplyVo> queryResult() {
        return userCheckLoanService.queryResult();
    }


    @PostMapping("/fly/apply")
    @Operation(summary = "空中飞行初筛贷款申请")
    public ResponseResult<UserCheckLoanApplyVo> flyApply(@RequestBody @Validated UserCheckLoanApplyDto userCheckLoanApplyDto) {
        return userCheckLoanService.flyApply(userCheckLoanApplyDto);
    }

    @PostMapping("/fly/queryResult")
    @Operation(summary = "空中飞行初筛贷款申请结果查询")
    public ResponseResult<UserCheckLoanApplyVo> flyQueryResult() {
        return userCheckLoanService.flyQueryResult();
    }

    @PostMapping("/fly/applyNew")
    @Operation(summary = "空中放款新申请接口")
    public ResponseResult<Void> applyNew() {
        return userCheckLoanService.applyNew();
    }
}
