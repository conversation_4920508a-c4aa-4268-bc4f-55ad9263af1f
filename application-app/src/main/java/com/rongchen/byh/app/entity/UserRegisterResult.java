package com.rongchen.byh.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 用户进件结果表
 * @TableName user_register_result
 */
@TableName(value ="user_register_result")
@Data
public class UserRegisterResult implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 资方id
     */
    private Long capitalId;

    /**
     * 绑卡流水号
     */
    private String bindRequestNo;

    /**
     * 绑卡id
     */
    private String bindId;

    /**
     * 借款单号
     */
    private String loanOrderNo;

    /**
     * 0 进行中 1 成功 2 失败
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}