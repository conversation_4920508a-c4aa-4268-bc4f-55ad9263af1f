<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.dao.HrzxCreditLogMapper">

    <resultMap id="BaseResultMap" type="com.rongchen.byh.app.entity.HrzxCreditLog">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="result" column="result" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,status,
        result,create_time,update_time
    </sql>
    <select id="selectByUserId" resultType="com.rongchen.byh.app.entity.HrzxCreditLog">
        select
        <include refid="Base_Column_List"/>
        from hrzx_credit_log
        where user_id = #{userId} limit 1
    </select>
</mapper>
