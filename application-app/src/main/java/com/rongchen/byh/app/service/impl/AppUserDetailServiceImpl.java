package com.rongchen.byh.app.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.ancun.netsign.client.NetSignClient;
import com.ancun.netsign.model.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.rongchen.byh.app.config.FileUtilService;
import com.rongchen.byh.app.config.ZifangFactory;
import com.rongchen.byh.app.constant.LoanType;
import com.rongchen.byh.app.constant.ZiFangBeanConstant;
import com.rongchen.byh.app.constant.SourceMode;
import com.rongchen.byh.app.dao.*;
import com.rongchen.byh.app.dto.PullFacePhotoDto;
import com.rongchen.byh.app.dto.app.*;
import com.rongchen.byh.app.entity.*;
import com.rongchen.byh.app.loveSign.LoveSignProperties;
import com.rongchen.byh.app.loveSign.LoveSignService;
import com.rongchen.byh.app.loveSign.SignDto;
import com.rongchen.byh.app.loveSign.SignRulesDto;
import com.rongchen.byh.app.service.AppUserDetailService;
import com.rongchen.byh.app.service.CreditService;
import com.rongchen.byh.app.utils.FileUtil;
import com.rongchen.byh.app.utils.UserTokenUtil;
import com.rongchen.byh.app.v2.common.IdCardUtil;
import com.rongchen.byh.app.v2.dto.UserDetailDto;
import com.rongchen.byh.app.vo.app.SubmitUserFormVo;
import com.rongchen.byh.app.vo.app.UserFormVo;
import com.rongchen.byh.common.api.idCardVerify.dto.FaceNewDto;
import com.rongchen.byh.common.api.idCardVerify.dto.OcrDto;
import com.rongchen.byh.common.api.idCardVerify.service.FaceVerifyService;
import com.rongchen.byh.common.api.idCardVerify.service.IdCardOcrService;
import com.rongchen.byh.common.api.idCardVerify.service.IdCardVerifyService;
import com.rongchen.byh.common.api.idCardVerify.vo.*;
import com.rongchen.byh.common.api.riskControl.dto.PreLoanAuditAppDto;
import com.rongchen.byh.common.api.riskControl.dto.PreLoanAuditAppRelationsDto;
import com.rongchen.byh.common.api.riskControl.service.RiskControlService;
import com.rongchen.byh.common.api.riskControl.vo.PreLoanAuditVo;
import com.rongchen.byh.common.api.zifang.dto.ContractListDto;
import com.rongchen.byh.common.api.zifang.service.ContractApi;
import com.rongchen.byh.common.api.zifang.utils.AreaCodeUtil;
import com.rongchen.byh.common.api.zifang.utils.CityCodeUtil;
import com.rongchen.byh.common.api.zifang.utils.ProvinceCodeUtil;
import com.rongchen.byh.common.api.zifang.vo.ContractListVo;
import com.rongchen.byh.common.api.zifang.vo.contraact.ContractList;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.ContextUtil;
import com.rongchen.byh.common.core.util.MyModelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ClassName AppUserDetailServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/11 11:50
 * @Version 1.0
 **/
@Service
@Slf4j
public class AppUserDetailServiceImpl implements AppUserDetailService {

    @Resource
    IdCardOcrService idCardOcrService;
    @Resource
    UserDetailMapper userDetailMapper;
    @Resource
    UserDataMapper userDataMapper;
    @Resource
    FaceVerifyService faceVerifyService;
    @Resource
    UserLoanApplyMapper userLoanApplyMapper;
    @Resource
    RiskControlService riskControlService;
    @Resource
    UserCreditDataMapper userCreditDataMapper;
    @Resource
    LoveSignService loveSignService;
    @Resource
    LoveSignProperties loveSignProperties;
    @Resource
    UserLoveLogMapper userLoveLogMapper;
    @Resource
    ContractApi contractApi;
    @Resource
    FileUtilService fileUtilService;
    @Resource
    NetSignClient client;
    private List<String> prefix = Arrays.asList("http", "https");
    @Resource
    ZifangFactory zifangFactory;
    @Resource
    CreditService creditService;
    @Resource
    IdCardVerifyService idCardVerifyService;
    @Resource
    private CapitalDataMapper capitalDataMapper;
    @Value("${spring.profiles.active}")
    private String active;


    @Override
    public ResponseResult<Void> idCardVerify(IdCardVerifyDto verifyDto, HttpServletRequest request) {
        Long userId = UserTokenUtil.getUserId();
        UserDetail detail = userDetailMapper.queryByUserId(userId);
        if (detail == null) {
            detail = new UserDetail();
            detail.setUserId(userId);
            userDetailMapper.insert(detail);
        }
//1.处理身份证正面信息
        OcrDto ocrDto = new OcrDto();
//        ocrDto.setImageBase64(verifyDto.getIdCardFrondBase64());
        ocrDto.setImageUrl(verifyDto.getIdCardFrondUrl());
        if (StrUtil.isNotEmpty(verifyDto.getIdCardFrondUrl()) && verifyDto.getIdCardFrondUrl().startsWith("data:image")) {
            ocrDto.setImageBase64(verifyDto.getIdCardFrondBase64());
            ocrDto.setImageUrl(null);
            verifyDto.setIdCardFrondUrl(null);
        }
        ocrDto.setCardSide("FRONT");
        OcrVo ocrVo = idCardOcrService.idCardOcr(ocrDto);
        if (ocrVo.getCode() != 0){
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,ocrVo.getMsg());
        }
        //1.1如果ocr之前填写过身份证信息，则进行判断是否上传的是同一个人的信息
        if (StrUtil.isNotEmpty(detail.getWebIdCard())) {
            if (!ocrVo.getIdCardResult().getName().equals(detail.getWebName()) || !ocrVo.getIdCardResult().getIdNum().equals(detail.getWebIdCard())){
                return ResponseResult.error(ErrorCodeEnum.FAIL,"姓名或身份证号不一致");
            }
        }
        //1.2如果用户之前已经ocr了，则不允许第二次ocr
        Long count = userDetailMapper.selectCount(new LambdaQueryWrapper<UserDetail>().eq(UserDetail::getIdNumber, ocrVo.getIdCardResult().getIdNum()));
        if (count > 0){
            return ResponseResult.error(ErrorCodeEnum.FAIL,"身份证号已被注册");
        }
        //2.处理身份证的背面信息
        ocrDto = new OcrDto();
//        ocrDto.setImageBase64(verifyDto.getIdCardReverseBase64());
        ocrDto.setImageUrl(verifyDto.getIdCardReverseUrl());
        if (StrUtil.isNotEmpty(verifyDto.getIdCardReverseUrl()) && verifyDto.getIdCardReverseUrl().startsWith("data:image")) {
            ocrDto.setImageBase64(verifyDto.getIdCardReverseBase64());
            ocrDto.setImageUrl(null);
            verifyDto.setIdCardReverseUrl(null);
        }
        ocrDto.setCardSide("BACK");
        OcrVo ocrVo1 = idCardOcrService.idCardOcr(ocrDto);
        //2.1判断身份证的有效期是否有效
        if (ocrVo1.getCode() != 0){
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,ocrVo1.getMsg());
        }
        if (ocrVo1.getIdCardResult().getValidDate() == null){
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"有效期不能为空");
        }
        String[] split = ocrVo1.getIdCardResult().getValidDate().split("-");
        if (DateUtil.now().compareTo(split[1]) == 1){
            return ResponseResult.error(ErrorCodeEnum.FAIL,"身份证有效期已过期,请更换身份证");
        }
        //2.2存储身份证相关信息，更新UserDetail表
        UserDetail userDetail = new UserDetail();
        try {
            if (StrUtil.isEmpty(verifyDto.getIdCardFrondUrl())) {
                userDetail.setIdCardFrondUrl(fileUtilService.upResult(FileUtil.base64ToMultipartFile(verifyDto.getIdCardFrondBase64()),request).getData().getDownloadUrl());
            } else {
                userDetail.setIdCardFrondUrl(verifyDto.getIdCardFrondUrl());
            }
            if (StrUtil.isEmpty(verifyDto.getIdCardReverseUrl())) {
                userDetail.setIdCardReverseUrl(fileUtilService.upResult(FileUtil.base64ToMultipartFile(verifyDto.getIdCardReverseBase64()),request).getData().getDownloadUrl());
            } else {
                userDetail.setIdCardReverseUrl(verifyDto.getIdCardReverseUrl());
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        userDetail.setUserId(userId);
        if (StrUtil.isEmpty(detail.getWebIdCard())) {
            UserData userData = userDataMapper.selectById(userId);
            userDetail.setWebName(ocrVo.getIdCardResult().getName());
            userDetail.setWebIdCard(ocrVo.getIdCardResult().getIdNum());
            userDetail.setWebTwoElements(1);
            if ("prod".equals(active)) {
                VerifyVo verifyResult = idCardVerifyService.verifyThree(ocrVo.getIdCardResult().getIdNum(),
                        ocrVo.getIdCardResult().getName(), userData.getMobile());
                log.info("三要素校验结果：{}", verifyResult);
                if (verifyResult.getCode() != 1) {
                    log.info("三要素校验失败:{}", userId);
                    return ResponseResult.error(ErrorCodeEnum.FAIL, "您的姓名，身份证号，手机号不一致，请核对");
                }
            }
        }
        userDetail.setAppName(ocrVo.getIdCardResult().getName());
        userDetail.setSex(ocrVo.getIdCardResult().getSex());
        userDetail.setNation(ocrVo.getIdCardResult().getNation());
        userDetail.setBirth(ocrVo.getIdCardResult().getBirth());
        userDetail.setAddress(ocrVo.getIdCardResult().getAddress());
        userDetail.setIdNumber(ocrVo.getIdCardResult().getIdNum());
        userDetail.setAuthority(ocrVo1.getIdCardResult().getAuthority());
        userDetail.setValidDate(ocrVo1.getIdCardResult().getValidDate());
        userDetail.setOcrResult(1);
        UpdateWrapper<UserDetail> updateWrapper = new UpdateWrapper();
        updateWrapper.eq("user_id",userId);
        int update = userDetailMapper.update(userDetail, updateWrapper);
        if (update == 1){
            return ResponseResult.success();
        }
        return ResponseResult.error(ErrorCodeEnum.DATA_SAVE_FAILED);
    }



    @Override
    public ResponseResult<JSONObject> faceLoveVerify(Long userId) {
        UserDetail detail = userDetailMapper.queryByUserId(userId);
        AuthInput input = new AuthInput();
        input.setRealName(detail.getWebName());
        input.setIdCardNo(detail.getWebIdCard());
        input.setBizId(UUID.randomUUID().toString());
        ApiRespBody<AuthOutput> apiRespBody = client.personAuthFace(input);
        if (apiRespBody.getCode() != ApiResponseInfo._100000.getCode()) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, apiRespBody.getMsg());
        }
        String data = JSONObject.toJSONString(apiRespBody.getData());
        JSONObject jsonObject = JSONObject.parseObject(data);
        jsonObject.put("bizId",input.getBizId());
        return ResponseResult.success(jsonObject);
    }

    @Override
    public ResponseResult<JSONObject> faceLoveVerify2(Long userId,HttpServletRequest request) {
        Integer productId = UserTokenUtil.getProductId();
        UserDetail detail = userDetailMapper.queryByUserId(userId);
        AuthInput input = new AuthInput();
        input.setRealName(detail.getWebName());
        input.setIdCardNo(detail.getWebIdCard());
        input.setBizId(UUID.randomUUID().toString());
        String device = request.getHeader("Device");
        if(device == null){
            input.setIsIframe(1);
        }
        log.info("faceLoveVerify2 请求参数：{}",JSONObject.toJSONString(input));
        ApiRespBody<AuthOutput> apiRespBody = client.personAuthFace(input);
        if (apiRespBody.getCode() != ApiResponseInfo._100000.getCode()) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, apiRespBody.getMsg());
        }
        String data = JSONObject.toJSONString(apiRespBody.getData());
        JSONObject jsonObject = JSONObject.parseObject(data);
        jsonObject.put("bizId",input.getBizId());
        return ResponseResult.success(jsonObject);
    }


    @Override
    public ResponseResult<Void> faceLoveVerifyQuery(String serialNo){
        Long userId = UserTokenUtil.getUserId();
        AuthInput input = new AuthInput();
//        input.setAccount(userData.getMobile());
        input.setSerialNo(serialNo);
        ApiRespBody<?> apiRespBody = NetSignClient.sendRequest(loveSignProperties.getBaseUrl() + "auth/pull/facePhoto", input, loveSignProperties.getAppId(), loveSignProperties.getRsaPriKey(), (MuiltFile) null);
        log.info("用户：{} 人脸结果:{}",userId,JSONObject.toJSONString(apiRespBody));
        if (apiRespBody.getData() != null && apiRespBody.getData() instanceof JSONObject) {

            System.out.println("人脸用户是："+userId);
            PullFacePhotoDto template = (PullFacePhotoDto)((JSONObject)apiRespBody.getData()).toJavaObject(PullFacePhotoDto.class);
            if (template.getVerifyScore() < 70) {
                return ResponseResult.error(ErrorCodeEnum.FAIL,"人脸评分不足");
            }
            HttpServletRequest request = ContextUtil.getHttpRequest();
            UserDetail userDetail = new UserDetail();
            try {
                userDetail.setFaceUrl(fileUtilService.upResult(FileUtil.base64ToMultipartFile(Base64.getEncoder().encodeToString(template.getData())),request).getData().getDownloadUrl());
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            userDetail.setFaceScore((template.getLivingScore() == null ? template.getVerifyScore() : template.getLivingScore()) + "");
            userDetail.setFaceConfidence(template.getVerifyScore() + "");
            userDetail.setFaceSource("腾讯云");
            userDetail.setFaceTime(DateUtil.now());
            userDetail.setFaceResult(1);

            userDetail.setUserId(userId);
            UpdateWrapper<UserDetail> updateWrapper = new UpdateWrapper();
            updateWrapper.eq("user_id",userId);
            int update = userDetailMapper.update(userDetail, updateWrapper);
            if (update == 1){
                return ResponseResult.success();
            }
            return ResponseResult.error(ErrorCodeEnum.FAIL);
        }
        return ResponseResult.error(ErrorCodeEnum.FAIL,"认证失败请重新认证");
    }


    @Override
    public ResponseResult<Void> faceVerify(FaceVerifyDto verifyDto, HttpServletRequest request) {
//        UserInput input = new UserInput();
//        input.setBizId(verifyDto.getBizId());
//        ApiRespBody apiRespBody = client.faceResult(input);

        UserDetail detail = userDetailMapper.queryByUserId(UserTokenUtil.getUserId());
        FaceNewDto dto = new FaceNewDto();
        dto.setOrderNo(verifyDto.getOrderNo());
        FaceNewVo newVo = faceVerifyService.faceImgVerifyNew(dto);
        if (newVo == null || newVo.getResult() == null){
            return ResponseResult.error(ErrorCodeEnum.FAIL);
        }
        FaceNewResultVo faceNewVo = newVo.getResult();
        if (faceNewVo.getPhoto() == null || faceNewVo.getSimilarity() == null){
            return ResponseResult.error(ErrorCodeEnum.FAIL);
        }
        if (new BigDecimal(faceNewVo.getSimilarity()).compareTo(new BigDecimal(70)) <0){
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,newVo.getMsg());
        }
        UserDetail userDetail = new UserDetail();
        try {
            userDetail.setFaceUrl(fileUtilService.upResult(FileUtil.base64ToMultipartFile(faceNewVo.getPhoto()),request).getData().getDownloadUrl());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        userDetail.setFaceScore(faceNewVo.getSimilarity());
        userDetail.setFaceConfidence(faceNewVo.getSimilarity());
        userDetail.setFaceSource("腾讯云");
        userDetail.setFaceTime(DateUtil.now());
        userDetail.setFaceResult(1);
        Long userId = UserTokenUtil.getUserId();
        userDetail.setUserId(userId);
        UpdateWrapper<UserDetail> updateWrapper = new UpdateWrapper();
        updateWrapper.eq("user_id",userId);
        int update = userDetailMapper.update(userDetail, updateWrapper);
        if (update == 1){
            return ResponseResult.success();
        }
        return ResponseResult.error(ErrorCodeEnum.DATA_SAVE_FAILED);
    }


    @Override
    public ResponseResult<Void> aiQianSign(AiSignDto dto) {
        Long userId = UserTokenUtil.getUserId();
        UserData userData = userDataMapper.selectById(userId);
        UserDetail detail = userDetailMapper.queryByUserId(userId);
        SignDto signDto = new SignDto();
        signDto.setName(detail.getWebName());
        signDto.setAccount(userData.getMobile());
        signDto.setIdCard(detail.getWebIdCard());
        signDto.setContractNo(IdUtil.fastSimpleUUID());
        signDto.setUserId(userId);
        List<SignRulesDto> rules = new ArrayList<>();
        ResponseResult<Void> result = ResponseResult.success();
        for (int i = 0; i < 3; i++){
            if (dto.getLocation() == 1){
                signDto.setContractName("人脸识别授权书");
                SignRulesDto rulesDto = new SignRulesDto();
                rulesDto.setKey("platformName");
                rulesDto.setValue("七叶草");
                rules.add(rulesDto);
                signDto.setRules(rules);
                result = loveSignService.signAll(signDto, loveSignProperties.getFace());
                loveSign(signDto,result);
                return result;
            }
            if (dto.getLocation() == 2){
                signDto.setRules(rules);
                signDto.setContractName("个人信息共享授权书");
                result = loveSignService.signAll(signDto,loveSignProperties.getPerson());
                loveSign(signDto,result);
                return result;
            }
            if (dto.getLocation() == 3){
                signDto.setContractNo(IdUtil.fastSimpleUUID());
                signDto.setContractName("电子签授权书");
                result = loveSignService.signAll(signDto,loveSignProperties.getSign());
                loveSign(signDto,result);
            }
        }
        return result;
    }

    @Override
    public ResponseResult<Void> aiQianSignCredit() {
        Long userId = UserTokenUtil.getUserId();
        UserData userData = userDataMapper.selectById(userId);
        UserDetail detail = userDetailMapper.queryByUserId(userId);
        SignDto signDto = new SignDto();
        signDto.setName(detail.getWebName());
        signDto.setAccount(userData.getMobile());
        signDto.setIdCard(detail.getWebIdCard());
        signDto.setContractNo(IdUtil.fastSimpleUUID());
        signDto.setUserId(userId);
        List<SignRulesDto> rules = new ArrayList<>();
        ResponseResult<Void> result = ResponseResult.success();
        signDto.setContractName("征信查询授权书");
        SignRulesDto signRulesDto1 = new SignRulesDto();
        SignRulesDto signRulesDto2 = new SignRulesDto();
        SignRulesDto signRulesDto3 = new SignRulesDto();
        SignRulesDto signRulesDto4 = new SignRulesDto();
        SignRulesDto signRulesDto5 = new SignRulesDto();
        SignRulesDto signRulesDto6 = new SignRulesDto();
        signRulesDto1.setKey("idNumber");
        signRulesDto1.setValue(detail.getWebIdCard());
        signRulesDto2.setKey("signUserName");
        signRulesDto2.setValue(detail.getWebName());
        signRulesDto3.setKey("mobile");
        signRulesDto3.setValue(userData.getMobile());
        signRulesDto4.setKey("address");
        signRulesDto4.setValue(detail.getAddress());
        signRulesDto5.setKey("signDate");
        signRulesDto5.setValue(new SimpleDateFormat("yyyy 年 MM 月 dd 日").format(new Date()));
        signRulesDto6.setKey("type");
        signRulesDto6.setValue("身份证");
        rules.add(signRulesDto1);
        rules.add(signRulesDto2);
        rules.add(signRulesDto3);
        rules.add(signRulesDto4);
        rules.add(signRulesDto5);
        rules.add(signRulesDto6);
        signDto.setRules(rules);
        result = loveSignService.signAll(signDto, loveSignProperties.getCredit());
        loveSign(signDto, result);
        signDto.setContractNo(IdUtil.fastSimpleUUID());
        signDto.setContractName("委托担保申请书");
        result = loveSignService.signAll(signDto, loveSignProperties.getEntrusted());
        loveSign(signDto, result);
        return result;
    }

    @Override
    public UserDetailDto getInfoForm() {
        Long userId = UserTokenUtil.getUserId();
//        Long userId = 1868927128301592602L;
//        UserDetail userDetail = userDetailMapper.queryByUserId(userId);
        UserDetailDto userDetail = userDetailMapper.selectInfoByUserId(userId);
        if(userDetail==null){
            userDetail = new UserDetailDto();
        }
        return userDetail;
    }

    @Override
    public ResponseResult<List<ContractList>> queryContract(ContractDto contractDto) {
        Long userId = UserTokenUtil.getUserId();
        UserDetail userDetail = userDetailMapper.queryByUserId(userId);
        ContractListDto contractListDto = new ContractListDto();
        contractListDto.setUserId(userId+"");
        contractListDto.setScene(contractDto.getScene());
        contractListDto.setIdent(userDetail.getIdNumber());
        contractListDto.setName(userDetail.getAppName());
        if ("02".equals(contractDto.getScene()) || "03".equals(contractDto.getScene())) {
            contractListDto.setLoanNo(contractDto.getCreditNo());
        }

        ContractApi contractApi = zifangFactory.getApi(ZiFangBeanConstant.MAYI, ContractApi.class);
        ResponseResult<ContractListVo> result = contractApi.contractListQuery(contractListDto);
        if (!result.isSuccess()) {
            return ResponseResult.error(ErrorCodeEnum.FAIL,result.getErrorMessage());
        }
        ContractListVo data = result.getData();
        List<ContractList> contractLists = data.getContractLists();

        //分转资方
        ContractApi contractFen = zifangFactory.getApi(ZiFangBeanConstant.FENZHUAN, ContractApi.class);

        ResponseResult<ContractListVo> resultFen = contractFen.contractListQuery(contractListDto);
        if (resultFen.isSuccess()) {
            ContractListVo data1 = resultFen.getData();
            List<ContractList> contractLists1 = data1.getContractLists();
            contractLists.addAll(contractLists1);
        }


        return ResponseResult.success(contractLists);
    }

    @Override
    public ResponseResult<List<ContractList>> queryContractBatch(ContractDto contractDto) {
        Long userId = UserTokenUtil.getUserId();
        UserDetail userDetail = userDetailMapper.queryByUserId(userId);
        ContractListDto contractListDto = new ContractListDto();
        contractListDto.setUserId(userId+"");
        contractListDto.setIdent(userDetail.getIdNumber());
        contractListDto.setName(userDetail.getAppName());
        contractListDto.setLoanNo(contractDto.getCreditNo());

        List<CompletableFuture<?>> futureList = new ArrayList<>();
        List<ContractList> contractLists = new ArrayList<>();
        List<String> scenes = Arrays.asList("01","02","03","04");

        ContractApi contractApi = zifangFactory.getApi(ZiFangBeanConstant.MAYI, ContractApi.class);
        for (String scene : scenes) {
            ContractListDto dto = MyModelUtil.copyTo(contractListDto, ContractListDto.class);
            dto.setScene(scene);
            futureList.add(CompletableFuture.supplyAsync(() -> {
                return contractApi.contractListQuery(dto);
            }).thenAccept(result -> {
                if (result.isSuccess()) {
                    ContractListVo data = result.getData();
                    List<ContractList> list = data.getContractLists();
                    if (CollectionUtil.isNotEmpty(list)) {
                        contractLists.addAll(list);
                    }
                }
            }).exceptionally((t) -> {
                log.error("异步任务执行异常", t);
                return null;
            }));
        }
        try {
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).get(3, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("异步任务执行异常", e);
        }

        List<CompletableFuture<?>> futureList2 = new ArrayList<>();
        ContractApi contractApi2 = zifangFactory.getApi(ZiFangBeanConstant.FENZHUAN, ContractApi.class);
        for (String scene : scenes) {
            ContractListDto dto = MyModelUtil.copyTo(contractListDto, ContractListDto.class);
            dto.setScene(scene);
            futureList2.add(CompletableFuture.supplyAsync(() -> {
                return contractApi2.contractListQuery(dto);
            }).thenAccept(result -> {
                if (result.isSuccess()) {
                    ContractListVo data = result.getData();
                    List<ContractList> list = data.getContractLists();
                    if (CollectionUtil.isNotEmpty(list)) {
                        contractLists.addAll(list);
                    }
                }
            }).exceptionally((t) -> {
                log.error("异步任务执行异常", t);
                return null;
            }));
        }
        try {
            CompletableFuture.allOf(futureList2.toArray(new CompletableFuture[0])).get(3, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("异步任务执行异常", e);
        }

        return ResponseResult.success(contractLists);
    }

    @Override
    public ResponseResult<OcrNewVo> ocrQueryResult(OcrQueryResultDto ocrQueryResultDto) {

        OcrNewVo ocrNewVo = idCardOcrService.idCardOcrNew(ocrQueryResultDto.getOrderNo());

        return ResponseResult.success(ocrNewVo);
    }


    public void loveSign(SignDto dto , ResponseResult<Void> result){
        UserLoveLog log = new UserLoveLog();
        log.setUserId(UserTokenUtil.getUserId());
        log.setContractName(dto.getContractName());
        log.setContractNo(dto.getContractNo());
        if (result.isSuccess()){
            log.setContractStatus(1);
        }else {
            log.setContractStatus(2);
        }
        userLoveLogMapper.insert(log);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<SubmitUserFormVo> submitUserForm(UserFormDto dto) {
        //联系人手机号与当前手机号进行校验
        Long userId = UserTokenUtil.getUserId();
        UserData userData = userDataMapper.selectById(userId);
        if (userData.getMobile().equals(dto.getEmergencyMobileOne()) || userData.getMobile().equals(dto.getEmergencyMobileTwo())){
            return ResponseResult.error(ErrorCodeEnum.FAIL,"紧急联系人手机号与当前手机号不能一致");
        }
        if (dto.getEmergencyMobileOne().equals(dto.getEmergencyMobileTwo())) {
            return ResponseResult.error(ErrorCodeEnum.FAIL,"紧急联系人手机号不能重复");
        }
        UserDetail detail = userDetailMapper.queryByUserId(userId);
        if (detail.getWebName().equals(dto.getEmergencyNameOne()) || detail.getWebName().equals(dto.getEmergencyNameTwo())){
            return ResponseResult.error(ErrorCodeEnum.FAIL,"紧急联系人姓名与用户姓名不能一致");
        }
        if (dto.getEmergencyNameOne().equals(dto.getEmergencyNameTwo())) {
            return ResponseResult.error(ErrorCodeEnum.FAIL,"紧急联系人姓名不能重复");
        }
        UserDetail userDetail = MyModelUtil.copyTo(dto, UserDetail.class);
        if (dto.getFormFlag() == 1){
            userDetail.setFormTime(DateUtil.now());
        }
        userDetail.setUserId(userId);
        userDetail.setFormFlag(1);
        UpdateWrapper<UserDetail> updateWrapper = new UpdateWrapper();
        updateWrapper.eq("user_id",userId);
        int update = userDetailMapper.update(userDetail, updateWrapper);
        SubmitUserFormVo vo = new SubmitUserFormVo();
        vo.setFlag(0);
        if (update == 1){

            // 记录申请
            UserLoanApply apply = new UserLoanApply();
            apply.setUserId(userId);
            apply.setApplyType(LoanType.LOAN);
            apply.setAuditsStatus(1);
            apply.setCreateTime(new Date());
            userLoanApplyMapper.insert(apply);
            // 向新资方提交进件
            CapitalData capitalData = capitalDataMapper.selectById(2);
            if (ObjectUtil.isNotEmpty(capitalData) && capitalData.getStatus() == 1) {
                creditService.creditApply(capitalData, userId);
            }
            return ResponseResult.success(vo);
        }
        vo.setFlag(1);
        return ResponseResult.success(vo);
    }

    public PreLoanAuditVo preLoanAuditApp(Long userId){
        // 记录申请
        UserLoanApply apply = new UserLoanApply();
        apply.setUserId(userId);
        apply.setApplyType(LoanType.LOAN);
        apply.setAuditsStatus(0);
        apply.setCreateTime(new Date());
        userLoanApplyMapper.insert(apply);
        //调用app授信 获取授信结果
        UserDetail detail = userDetailMapper.queryByUserId(userId);
        UserData data = userDataMapper.selectById(userId);
        List<PreLoanAuditAppRelationsDto> list = new ArrayList<>();
        PreLoanAuditAppRelationsDto relationsDto = new PreLoanAuditAppRelationsDto();
        relationsDto.setRelation_mobile(detail.getEmergencyMobileOne());
        relationsDto.setRelation_name(detail.getEmergencyNameOne());
        relationsDto.setRelation_type(Integer.valueOf(detail.getRelationshipOne()));
        list.add(relationsDto);
        PreLoanAuditAppRelationsDto relationsDto1 = new PreLoanAuditAppRelationsDto();
        relationsDto1.setRelation_mobile(detail.getEmergencyMobileTwo());
        relationsDto1.setRelation_name(detail.getEmergencyNameTwo());
        relationsDto1.setRelation_type(Integer.valueOf(detail.getRelationshipTwo()));
        list.add(relationsDto1);
        PreLoanAuditAppDto auditAppDto = new PreLoanAuditAppDto();
        auditAppDto.setIdcard_no(detail.getIdNumber());
        auditAppDto.setMobile(data.getMobile());
        auditAppDto.setName(detail.getAppName());
        auditAppDto.setCredit_id(IdUtil.fastSimpleUUID());
        auditAppDto.setCredit_time(DateUtil.now());
        auditAppDto.setChannel("4");
        auditAppDto.setProduct_code(4);
        String[] split = detail.getValidDate().split("-");
        auditAppDto.setCert_start(split[0].replace(".", "-"));
        auditAppDto.setCert_end(split[1].replace(".", "-"));
        auditAppDto.setNation_ocr(nationOcr(detail.getNation()));
        auditAppDto.setIdcard_address_ocr(detail.getAddress());
        auditAppDto.setEducation(educationSw(detail.getEducationLevel()));
        auditAppDto.setMarriage_state(marriageStateSw(detail.getMaritalStatus()));
        auditAppDto.setLive_address(detail.getCustAddressProvice()+ detail.getCustAddressCity() + detail.getCustAddressCounty() + detail.getCustAddress());
        String resolution = addressResolution(detail.getAddress());
        if (resolution != null){
            auditAppDto.setIdcard_city_code(resolution);
        }else {
            auditAppDto.setIdcard_city_code(CityCodeUtil.getCodeByName(detail.getCustAddressCity()));
        }
        auditAppDto.setLive_city_code(CityCodeUtil.getCodeByName(detail.getCustAddressCity()));
        auditAppDto.setIssued_org_ocr(detail.getAuthority());
        auditAppDto.setAnnual_income(Integer.valueOf(detail.getIncomeMonth()) * 12);
        auditAppDto.setIf_register("Y");
        auditAppDto.setIf_sign("Y");
        UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userId, LoanType.CHECK, 0);
        if (ObjectUtil.isNotEmpty(userLoanApply) && userLoanApply.getAuditsStatus() == 4){
            auditAppDto.setIf_sign("N");
        }
        auditAppDto.setPhone_number("");
        auditAppDto.setMobile_startime("");
        auditAppDto.setWifi_sensitive("");
        auditAppDto.setAddress_book_num(0);
        auditAppDto.setAddress_book_num11(0);
        auditAppDto.setAddress_book_sensitive("");
        auditAppDto.setContact_operator("");
        auditAppDto.setOverdue_message_m60(0);
        JSONObject param = new JSONObject();
        param.put("house",houseSw(detail.getHouseStatus()));
        auditAppDto.setHouse_info(param);
        auditAppDto.setRelations(list);
        PreLoanAuditVo preLoanAuditVo = riskControlService.preLoanAuditApp(auditAppDto);
        return preLoanAuditVo;
    }


    public Integer nationOcr(String nation){
        switch (nation) {
            case "汉族":
                return 1;
            case "蒙古族":
                return 2;
            case "回族":
                return 3;
            case "藏族":
                return 4;
            case "维吾尔族":
                return 5;
            case "苗族":
                return 6;
            case "彝族":
                return 7;
            case "壮族":
                return 8;
            case "布依族":
                return 9;
            case "朝鲜族":
                return 10;
            case "满族":
                return 11;
            case "侗族":
                return 12;
            case "瑶族":
                return 13;
            case "白族":
                return 14;
            case "土家族":
                return 15;
            case "哈尼族":
                return 16;
            case "哈萨克族":
                return 17;
            case "傣族":
                return 18;
            case "黎族":
                return 19;
            case "僳僳族":
                return 20;
            case "佤族":
                return 21;
            case "畲族":
                return 22;
            case "高山族":
                return 23;
            case "拉祜族":
                return 24;
            case "水族":
                return 25;
            case "东乡族":
                return 26;
            case "纳西族":
                return 27;
            case "景颇族":
                return 28;
            case "柯尔克孜族":
                return 29;
            case "土族":
                return 30;
            case "达斡尔族":
                return 31;
            case "仫佬族":
                return 32;
            case "羌族":
                return 33;
            case "布朗族":
                return 34;
            case "撒拉族":
                return 35;
            case "毛南族":
                return 36;
            case "仡佬族":
                return 37;
            case "锡伯族":
                return 38;
            case "阿昌族":
                return 39;
            case "普米族":
                return 40;
            case "塔吉克族":
                return 41;
            case "怒族":
                return 42;
            case "乌孜别克族":
                return 43;
            case "俄罗斯族":
                return 44;
            case "鄂温克族":
                return 45;
            case "德昂族":
                return 46;
            case "保安族":
                return 47;
            case "裕固族":
                return 48;
            case "京族":
                return 49;
            case "塔塔尔族":
                return 50;
            case "独龙族":
                return 51;
            case "鄂伦春族":
                return 52;
            case "赫哲族":
                return 53;
            case "门巴族":
                return 54;
            case "珞巴族":
                return 55;
            case "基诺族":
                return 56;
            default:
                return 99;
        }
    }

    public Integer educationSw(String education){
        switch (education) {
            case "1":
                return 3;
            case "2":
                return 4;
            case "3":
                return 5;
            case "4":
            case "5":
                return 6;
            default:
               return 99;
        }
    }

    public Integer marriageStateSw(String maritalStatus){
        switch (maritalStatus) {
            case "1":
                return 0;
            case "2":
                return 1;
            case "3":
                return 2;
            case "4":
                return 3;
            default:
               return 99;
        }
    }

    public Integer houseSw(String houseStatus){
        switch (houseStatus) {
            case "1":
            case "2":
                return 3;
            case "3":
                return 5;
            case "4":
                return 4;
            default:
               return 99;
        }
    }

    public static String addressResolution(String address){
        if (address.contains("北京市")){
            address = "北京市"+address;
        }else if (address.contains("天津市")){
            address = "天津市"+address;
        }else if (address.contains("上海市")){
            address = "上海市"+address;
        }else if (address.contains("重庆市")){
            address = "重庆市"+address;
        }
        String regex="(?<province>[^省]+自治区|.*?省|.*?行政区|.*?市)(?<city>[^市]+自治州|.*?地区|.*?行政单位|.+盟|市辖区|.*?市|.*?县)(?<county>[^县]+县|.+区|.+市|.+旗|.+海域|.+岛)?(?<town>[^区]+区|.+镇)?(?<village>.*)";
        Matcher m= Pattern.compile(regex).matcher(address);
        String province=null,city=null,county=null,town=null,village=null;
        JSONObject jso = new JSONObject();
        while(m.find()){
            province=m.group("province");
            jso.put("province", province==null?"":province.trim());
            city=m.group("city");
            jso.put("city", city==null?"":city.trim());
            county=m.group("county");
            jso.put("county", county==null?"":county.trim());
            town=m.group("town");
            jso.put("town", town==null?"":town.trim());
            village=m.group("village");
            jso.put("village", village==null?"":village.trim());
        }
        String codeByName = AreaCodeUtil.getCodeByName(city + "_" + county);
        if (codeByName != null){
            return codeByName;
        }else {
            String codeByName1 = CityCodeUtil.getCodeByName(city);
            if (codeByName1 != null){
                return codeByName1;
            }else {
                return ProvinceCodeUtil.getCodeByName(province);
            }
        }
    }



    @Override
    public ResponseResult<UserFormVo> queryUserForm() {
        UserFormVo userFormVo = userDetailMapper.queryUserFormByUserId(UserTokenUtil.getUserId());
        return ResponseResult.success(userFormVo);
    }


}
