package com.rongchen.byh.app.utils;

import net.bytebuddy.utility.RandomString;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @ClassName CreditLoanNoUtils
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/13 15:42
 * @Version 1.0
 **/
public class CreditLoanNoUtils {


    public static final String CREDIT = "cit";
    public static final String LOAN = "ln";

    public static final String REPAY = "rp";


    /**
     * 生成日期+随机6位字符流水号
     *
     * @param code 前缀
     */
    public static String getSequence(String code) {
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMddhhmmss");
        return code+ "-" + df.format(new Date())+ RandomString.make(6);
    }
}
