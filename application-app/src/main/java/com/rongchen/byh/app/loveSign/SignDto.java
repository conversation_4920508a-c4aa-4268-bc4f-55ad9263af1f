package com.rongchen.byh.app.loveSign;

import lombok.Data;

import java.util.List;

@Data
public class SignDto {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 合同编号
     */
    private String contractNo;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 用户唯一识别码，手机号码
     */
    private String account;

    /**.
     * 用户姓名
     */
    private String name;

    /**
     * 身份证号码
     */
    private String idCard;

    /**
     * 规则
     */
    private List<SignRulesDto> rules;
}
