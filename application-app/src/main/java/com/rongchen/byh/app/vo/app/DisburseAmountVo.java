package com.rongchen.byh.app.vo.app;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName DisburseAmountVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/15 17:35
 * @Version 1.0
 **/
@Data
public class DisburseAmountVo {

    @Schema(description = "授信额度")
    private String creditAmount;

    @Schema(description = "已用额度")
    private String withdrawAmount;

    @Schema(description = "剩余额度")
    private String residueAmount;

    @Schema(description = "冻结额度")
    private BigDecimal freezeAmount;


}
