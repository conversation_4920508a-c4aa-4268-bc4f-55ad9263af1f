package com.rongchen.byh.app.service;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rongchen.byh.app.entity.BaofuPayDetail;
import com.rongchen.byh.app.dao.BaofuPayDetailMapper;
/**

 * 项目名称：byh_java
 * 文件名称: BaofuPayDetailService
 * 创建时间: 2025-03-11 19:05
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.app.service
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
@Service
public class BaofuPayDetailService extends ServiceImpl<BaofuPayDetailMapper, BaofuPayDetail> {

    
    public int updateByPrimaryKey(BaofuPayDetail record) {
        return baseMapper.updateByPrimaryKey(record);
    }
}
