package com.rongchen.byh.app.controller.inner;


import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.rongchen.byh.app.config.ZifangFactory;
import com.rongchen.byh.app.dao.CapitalDataMapper;
import com.rongchen.byh.app.dao.DisburseDataMapper;
import com.rongchen.byh.app.dao.RepayScheduleMapper;
import com.rongchen.byh.app.dao.UserBankCardMapper;
import com.rongchen.byh.app.dto.app.BackVo;
import com.rongchen.byh.app.entity.CapitalData;
import com.rongchen.byh.app.entity.DisburseData;
import com.rongchen.byh.app.entity.RepaySchedule;
import com.rongchen.byh.app.utils.CreditLoanNoUtils;
import com.rongchen.byh.common.api.zifang.dto.PreRepayApplyDto;
import com.rongchen.byh.common.api.zifang.dto.RepaymentApplyDetailDto;
import com.rongchen.byh.common.api.zifang.dto.RepaymentApplyDto;
import com.rongchen.byh.common.api.zifang.dto.RepaymentApplyRepayDto;
import com.rongchen.byh.common.api.zifang.service.RepaymentApi;
import com.rongchen.byh.common.api.zifang.vo.PreRepayApplyVo;
import com.rongchen.byh.common.api.zifang.vo.RepaymentApplyVo;
import com.rongchen.byh.common.core.annotation.DisableDataFilter;
import com.rongchen.byh.common.core.constant.SettleFlagConstant;
import com.rongchen.byh.common.core.object.ResponseResult;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@ApiSupport(order = 1)
@Tag(name = "内部 线下还款模式-还款信息流推送到资方")
@DisableDataFilter
@Slf4j
@RestController
@RequestMapping("/inner")
public class RepayAllController {


    @Resource
    RepayScheduleMapper repayScheduleMapper;
    @Resource
    DisburseDataMapper disburseDataMapper;
    @Resource
    CapitalDataMapper capitalDataMapper;
    @Resource
    ZifangFactory zifangFactory;
    @Resource
    UserBankCardMapper userBankCardMapper;


    @PostMapping("/re/{scheduleId}")
    @SaIgnore
    public void re(@PathVariable("scheduleId") String scheduleId) {
        String sequence = CreditLoanNoUtils.getSequence("ln-rp");
        //获取本息还款计划
        RepaySchedule repaySchedule = repayScheduleMapper.selectById(Long.parseLong(scheduleId));
        repaySchedule.setRepayApplyNo(sequence);
        repayScheduleMapper.updateById(repaySchedule);
        //获取订单
        DisburseData disburseData = disburseDataMapper.selectById(repaySchedule.getDisburseId());
        //调用还款试算
        PreRepayApplyDto applyDto = new PreRepayApplyDto();
        applyDto.setMerserno(cn.hutool.core.lang.UUID.randomUUID().toString().replace("-", ""));
        applyDto.setLoanNo(disburseData.getLoanNo());
        //判断还款类型
        String todayDate = DateUtil.format(new Date() , "yyyy-MM-dd");
        applyDto.setPaytotalamt(repaySchedule.getTotalAmt().toString());
        applyDto.setPrePayType("3");
        applyDto.setPaytotalamt(disburseData.getLoanNo().toString());
        applyDto.setTerm(repaySchedule.getRepayTerm());
        CapitalData capitalData = capitalDataMapper.selectById(disburseData.getCapitalId());
        RepaymentApi repaymentApi = zifangFactory.getApi(capitalData.getBeanName(), RepaymentApi.class);
        ResponseResult<PreRepayApplyVo> preRepayApply = repaymentApi.getPreRepayApply(applyDto);
        if (preRepayApply.getData().getResponseCode().equals("0000")) {
            PreRepayApplyVo data = preRepayApply.getData();

//            //调用本息还款
//            // 本息划扣
            RepaymentApplyDto repaymentApplyDto = new RepaymentApplyDto();
            repaymentApplyDto.setFundCode(disburseData.getFundCode());
            repaymentApplyDto.setRepayApplyNo(IdUtil.fastSimpleUUID());
            repaymentApplyDto.setRepayApplyNo(sequence);
            repaymentApplyDto.setUserId(disburseData.getUserId().toString());
            repaymentApplyDto.setRepayMethod("1");
            repaymentApplyDto.setRepayType("CLEAN");

            // 获取银行卡信息
            BackVo backVo = userBankCardMapper.queryBackByUserId(disburseData.getUserId());
            repaymentApplyDto.setBankPhoneNo(backVo.getMobile());
            repaymentApplyDto.setAmount(repaySchedule.getTotalAmt().toString());
            repaymentApplyDto.setBankCardNo(backVo.getBankAccount());
            repaymentApplyDto.setAccountCardType("1");
            repaymentApplyDto.setIdNo(backVo.getIdCard());
            repaymentApplyDto.setCustomerName(backVo.getName());
            repaymentApplyDto.setBranchName(backVo.getBankName());
            repaymentApplyDto.setBankAccountType("1");

            List<RepaymentApplyRepayDto> repaymentApplyRepayList = new ArrayList<>();
            List<RepaymentApplyDetailDto> repaymentApplyDetailList = new ArrayList<>();
            RepaymentApplyRepayDto repaymentApplyRepayDto = new RepaymentApplyRepayDto();
            repaymentApplyRepayDto.setLoanNo(disburseData.getLoanNo());
            BigDecimal add = new BigDecimal(data.getGuarantorAmt())
                    .add(new BigDecimal(data.getPayNormAmt()))
                    .add(new BigDecimal(data.getPayInteAmt()))
                    .add(new BigDecimal(data.getPayEnteAmt()))
                    .add(new BigDecimal(data.getServiceAmt()))
                    .add(new BigDecimal(data.getFee()));
            repaymentApplyRepayDto.setRepayAmt(add.toString());
            repaymentApplyRepayDto.setRepayTerm(repaySchedule.getRepayTerm());
            repaymentApplyRepayDto.setGuarantorFee(data.getGuarantorAmt());
            repaymentApplyRepayDto.setSplitType("1");
            repaymentApplyRepayDto.setPrinAmt(data.getPayNormAmt());
            repaymentApplyRepayDto.setIntAmt(data.getPayInteAmt());
            repaymentApplyRepayDto.setForfeitAmt(data.getPayEnteAmt());
            repaymentApplyRepayDto.setServiceAmt(data.getServiceAmt());
            repaymentApplyRepayDto.setBreachFee(data.getFee());
            repaymentApplyRepayList.add(repaymentApplyRepayDto);
            repaymentApplyDto.setRepayList(repaymentApplyRepayList);


            RepaymentApplyDetailDto repaymentApplyDetailDto = new RepaymentApplyDetailDto();
            repaymentApplyDetailDto.setRepayterm(repaySchedule.getRepayTerm());
            repaymentApplyDetailDto.setPrintAmt(data.getPayNormAmt());
            repaymentApplyDetailDto.setIntAmt(data.getPayInteAmt());
            repaymentApplyDetailDto.setForfeitAmt(data.getPayEnteAmt());
            repaymentApplyDetailDto.setGuarantorFee(data.getGuarantorAmt());
            repaymentApplyDetailDto.setServiceAmt(data.getServiceAmt());
            repaymentApplyDetailDto.setBreachFee(data.getFee());
            repaymentApplyDetailList.add(repaymentApplyDetailDto);

            repaymentApplyRepayDto.setRepayDetailList(repaymentApplyDetailList);

            ResponseResult<RepaymentApplyVo> repaymentApply = repaymentApi.getRepaymentApply(repaymentApplyDto);
            if (repaymentApply.getData().getResponseCode().equals("0000")){
                RepaySchedule repayScheduleUp = new RepaySchedule();
                repayScheduleUp.setId(repaySchedule.getId());
                repayScheduleUp.setSettleFlag(SettleFlagConstant.REPAYING);
                repayScheduleMapper.updateById(repayScheduleUp);

            }
        }
    }
}
