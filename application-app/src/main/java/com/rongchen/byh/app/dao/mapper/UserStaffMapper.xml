<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.dao.UserStaffMapper">

    <resultMap id="BaseResultMap" type="com.rongchen.byh.app.entity.UserStaff">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="staffId" column="staff_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,staff_id,
        create_time
    </sql>
    <select id="selectByUserId" resultType="com.rongchen.byh.app.entity.UserStaff">
        select
        <include refid="Base_Column_List"/>
        from user_staff
        where user_id = #{userId}
    </select>
</mapper>
