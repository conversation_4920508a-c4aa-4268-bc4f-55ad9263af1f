package com.rongchen.byh.app.service;

import com.rongchen.byh.app.dto.RepaymentDto;
import com.rongchen.byh.app.dto.app.BillDetailDto;
import com.rongchen.byh.app.dto.app.BillRepayApplyDto;
import com.rongchen.byh.app.dto.app.BillRepayResultDto;
import com.rongchen.byh.app.dto.inner.InnerRepayDto;
import com.rongchen.byh.app.vo.app.BillDetailVo;
import com.rongchen.byh.app.vo.app.BillListVo;
import com.rongchen.byh.app.vo.app.BillRepayResultVo;
import com.rongchen.byh.app.vo.app.RepaymentDetailVo;
import com.rongchen.byh.common.core.object.ResponseResult;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 账单相关业务
 * @date 2024/12/14 16:26:50
 */
public interface AppUserBillService {
    ResponseResult<List<BillListVo>> billList();

    ResponseResult<BillDetailVo> billDetail(BillDetailDto billDetailDto);

    ResponseResult<Void> billApply(RepaymentDto repaymentDto);

    ResponseResult<BillRepayResultVo> getBillRepaymentResult(RepaymentDto repaymentDto);

    ResponseResult<RepaymentDetailVo> repaymentDetail(RepaymentDto repaymentDto);

    ResponseResult<Void> innerRepay(InnerRepayDto repayDto);

    ResponseResult<Void> innerRepaySale(InnerRepayDto repayDto);
}
