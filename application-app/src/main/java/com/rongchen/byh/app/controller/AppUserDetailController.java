package com.rongchen.byh.app.controller;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSONObject;
import com.ancun.netsign.model.ApiRespBody;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.rongchen.byh.app.dto.app.*;
import com.rongchen.byh.app.service.AppUserDetailService;
import com.rongchen.byh.app.utils.UserTokenUtil;
import com.rongchen.byh.app.vo.app.SubmitUserFormVo;
import com.rongchen.byh.app.vo.app.UserFormVo;
import com.rongchen.byh.common.api.idCardVerify.vo.OcrNewVo;
import com.rongchen.byh.common.api.zifang.vo.contraact.ContractList;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.log.annotation.IgnoreResponseLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @ClassName AppUserDetailController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/11 11:48
 * @Version 1.0
 **/
@Tag(name = "app ocr至留资相关接口")
@ApiSupport(order = 3)
@RestController
@RequestMapping("/userApi")
public class AppUserDetailController {

    @Resource
    AppUserDetailService appUserDetailService;

    /**
     * 对身份证正反面进行处理以及保存相关数据1
     * */
    @Operation(summary = "身份认证")
    @PostMapping("/idCardVerify")
    public ResponseResult<Void> idCardVerify(@RequestBody IdCardVerifyDto verifyDto, HttpServletRequest request){
        return appUserDetailService.idCardVerify(verifyDto,request);
    }
    /**
     * 通过姓名和身份证进行验证app使用1
     * */
    @Operation(summary = "爱签人脸验证app")
    @PostMapping("/faceLoveVerify")
    public ResponseResult<JSONObject> faceLoveVerify(){
        Long userId = UserTokenUtil.getUserId();
        return appUserDetailService.faceLoveVerify(userId);
    }
    /**
     * 通过姓名和身份证进行验证h5使用
     * 模式兼容后同一使用该接口
     * */
    @Operation(summary = "爱签人脸验证h5")
    @PostMapping("/faceLoveVerify2")
    public ResponseResult<JSONObject> faceLoveVerify2(HttpServletRequest request){
        Long userId = UserTokenUtil.getUserId();
        return appUserDetailService.faceLoveVerify2(userId,request);
    }
/**
 * 对人脸参数结果的查询，根据业务id查询1
 * */
    @Operation(summary = "爱签人脸验证结果查询")
    @PostMapping("/faceLoveVerifyQuery")
    @IgnoreResponseLog
    public ResponseResult<Void> faceLoveVerifyQuery(@RequestBody FaceVerifyDto verifyDto){
        return appUserDetailService.faceLoveVerifyQuery(verifyDto.getBizId());
    }
/**
 * 对人脸参数结果的查询，根据人脸校验置信区间查询1，已经没用了
 * */
    @Operation(summary = "人脸验证")
    @PostMapping("/faceVerify")
    @IgnoreResponseLog
    public ResponseResult<Void> faceVerify(@RequestBody FaceVerifyDto verifyDto, HttpServletRequest request){
        return appUserDetailService.faceVerify(verifyDto,request);
    }

/**
 * 爱签签署电子协议接口
 * */
    @Operation(summary = "爱签调用")
    @PostMapping("/aiQianSign")
    public ResponseResult<Void> aiQianSign(@RequestBody AiSignDto dto){
        return appUserDetailService.aiQianSign(dto);
    }

    @Operation(summary = "爱签征信和委托担保申请书调用")
    @PostMapping("/aiQianSignCredit")
    public ResponseResult<Void> aiQianSignCredit(){
        return appUserDetailService.aiQianSignCredit();
    }


    @Operation(summary = "表单提交")
    @PostMapping("/submitUserForm")
    public ResponseResult<SubmitUserFormVo> submitUserForm(@RequestBody UserFormDto dto){
        return appUserDetailService.submitUserForm(dto);
    }


    @Operation(summary = "获取用户表单信息---->只有app人脸才会回去用户表单")
    @PostMapping("/queryUserForm")
    public ResponseResult<UserFormVo> queryUserForm(){
        return appUserDetailService.queryUserForm();
    }


    @Operation(summary = "查询合同接口")
    @PostMapping("/queryContract")
    public ResponseResult<List<ContractList>> queryContract(@RequestBody ContractDto contractDto) {
        return appUserDetailService.queryContract(contractDto);
    }


    @Operation(summary = "查询合同接口批量")
    @PostMapping("/queryContractBatch")
    public ResponseResult<List<ContractList>> queryContractBatch(@RequestBody ContractDto contractDto) {
        return appUserDetailService.queryContractBatch(contractDto);
    }


    @Operation(summary = "ocr查询结果---只有app的ocr时才会去访问这个接口，如果成功才调用/aiQianSign接口")
    @PostMapping("/ocrQueryResult")
    @IgnoreResponseLog
    public ResponseResult<OcrNewVo> ocrQueryResult(@RequestBody OcrQueryResultDto ocrQueryResultDto) {
        ThreadUtil.sleep(100);
        return appUserDetailService.ocrQueryResult(ocrQueryResultDto);
    }
}
