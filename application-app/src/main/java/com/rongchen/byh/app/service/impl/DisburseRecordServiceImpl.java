package com.rongchen.byh.app.service.impl;

import com.rongchen.byh.app.dao.DisburseRecordMapper;
import com.rongchen.byh.app.entity.DisburseRecord;
import com.rongchen.byh.app.service.DisburseRecordService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service
public class DisburseRecordServiceImpl implements DisburseRecordService {

    @Resource
    DisburseRecordMapper disburseRecordMapper;


    @Override
    public void saveRecord(String loanNo,String traceId,String location) {
        DisburseRecord disburseRecord = new DisburseRecord();
        disburseRecord.setType(1);
        disburseRecord.setLoanNo(loanNo);
        disburseRecord.setTraceId(traceId);
        disburseRecord.setLocation(location);
        disburseRecordMapper.insert(disburseRecord);
    }

    @Override
    public void saveRecord(Integer type,String loanNo,String traceId,String location) {
        DisburseRecord disburseRecord = new DisburseRecord();
        disburseRecord.setType(type);
        disburseRecord.setLoanNo(loanNo);
        disburseRecord.setTraceId(traceId);
        disburseRecord.setLocation(location);
        disburseRecordMapper.insert(disburseRecord);
    }

}
