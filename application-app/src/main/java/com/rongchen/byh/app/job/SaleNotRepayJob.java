package com.rongchen.byh.app.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.app.dao.SaleScheduleMapper;
import com.rongchen.byh.app.entity.SaleSchedule;
import com.rongchen.byh.common.core.util.MDCUtil;
import com.rongchen.byh.common.rabbitmq.constants.QueueConstant;
import com.rongchen.byh.common.rabbitmq.dto.RepayDto;
import com.rongchen.byh.common.rabbitmq.dto.SaleDto;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 本息已还款赊销未还款定时任务
 * @date 2025/4/18 14:31:56
 */
@Component
@Slf4j
public class SaleNotRepayJob {
    @Resource
    private SaleScheduleMapper saleScheduleMapper;
    @Resource
    private RabbitTemplate rabbitTemplate;


    @XxlJob("saleNotRepayHandler")
    public void saleNotRepayHandler() {
        log.info("===========开始执行本息已还款赊销未还款定时任务开始======");
        // 查询本息已还款但是赊销未还款订单
        List<SaleSchedule> saleScheduleList = saleScheduleMapper.selectNotRepayList();
        if (CollUtil.isEmpty(saleScheduleList)) {
            log.info("===========没有查询到本息已还款赊销未还款的订单，提前结束======");
            return;
        }
        saleScheduleList.forEach(saleSchedule -> {
            String traceId = MDCUtil.generateTraceId();
            SaleDto saleDto = new SaleDto();
            saleDto.setDisburseId(saleSchedule.getDisburseId());
            saleDto.setTerm(saleSchedule.getRepayTerm());
            saleDto.setTraceId(traceId);
            saleDto.setRepayType(2);
            rabbitTemplate.convertAndSend(QueueConstant.SALE_QUEUE, JSONObject.toJSONString(saleDto));
        });
    }
}
