package com.rongchen.byh.app.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.qcloud.cos.model.ObjectMetadata;
import com.rongchen.byh.common.api.bankCredit.config.HuaRongZhengXinProperties;
import com.rongchen.byh.common.api.bankCredit.utils.DocumentTemplateUtil;
import com.rongchen.byh.common.core.util.ImageUtil;
import com.rongchen.byh.common.qcloud.cos.wrapper.QcloudCosTemplate;
import java.io.File;
import java.io.FileInputStream;
import java.util.Date;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description oss上传 工具类, 提供图片合并、PDF转ZIP及通用文件上传功能。
 *              核心操作已优化为异步执行，提高并发处理能力。
 * @date 2025/3/17 16:52:42
 */
@Component
@Slf4j
public class OssUtil {
    // COS 访问域名
    public static final String domain = "https://12qyc-**********.cos.ap-guangzhou.myqcloud.com";
    @Resource
    private QcloudCosTemplate qcloudCosTemplate; // 腾讯云 COS 操作模板
    @Resource
    private HuaRongZhengXinProperties huaRongZhengXinProperties; // 文件路径等配置
    @Resource(name = "taskExecutor") // 注入自定义的线程池 Bean，用于执行异步任务
    private Executor ossTaskExecutor;

    /**
     * 异步合并两张图片并上传到 OSS。
     * 操作将在独立的线程池中执行。
     *
     * @param path1  图片一的本地路径
     * @param path2  图片二的本地路径
     * @param mobile 用户手机号（用于构建路径）
     * @return CompletableFuture<String> 包含最终 OSS URL 的 Future 对象。
     *         如果操作成功，Future 完成时结果为 URL 字符串；
     *         如果操作失败（合并、压缩或上传失败），Future 完成时结果为空字符串 ""。
     */
    public CompletableFuture<String> mergeTwoImage(String path1, String path2, String mobile) {
        // 保存当前 MDC 上下文
        // Map<String, String> contextMap = MDCUtil.getCopyOfContextMap(); // Optional:
        // Also redundant if taskExecutor is used

        return CompletableFuture.supplyAsync(() -> {
            // try { // try block might need adjustment if contextMap setting was the only
            // content
            // 设置 MDC 上下文 - REMOVED as it should be handled by MdcTaskDecorator
            // MDCUtil.setContextMap(contextMap);

            // 检查输入路径
            if (StrUtil.isEmpty(path1) || StrUtil.isEmpty(path2)) {
                log.error("【合并身份证图片】路径为空，手机号: {}, 正面路径: {}, 反面路径: {}", mobile, path1, path2);
                return "";
            }

            // 检查文件是否存在和可读
            File file1 = new File(path1);
            File file2 = new File(path2);
            if (!file1.exists() || !file2.exists() || !file1.canRead() || !file2.canRead()) {
                log.error("【合并身份证图片】文件不存在或不可读，手机号: {}, 正面文件: {}, 反面文件: {}",
                        mobile, file1.getAbsolutePath(), file2.getAbsolutePath());
                return "";
            }

            // 检查文件大小
            if (file1.length() == 0 || file2.length() == 0) {
                log.error("【合并身份证图片】文件大小为0，手机号: {}, 正面文件大小: {}, 反面文件大小: {}",
                        mobile, file1.length(), file2.length());
                return "";
            }

            // 检查文件格式
            if (!isImageFile(file1) || !isImageFile(file2)) {
                log.error("【合并身份证图片】文件格式不正确，手机号: {}, 正面文件格式: {}, 反面文件格式: {}",
                        mobile, getFileExtension(file1), getFileExtension(file2));
                return "";
            }

            // 合并图片
            log.info("【合并身份证图片】开始合并，手机号: {}, 正面文件: {}, 反面文件: {}", mobile, path1, path2);
            String mergedPath = mergeImages(path1, path2, mobile);
            if (StrUtil.isEmpty(mergedPath)) {
                log.error("【合并身份证图片】合并失败，手机号: {}, 正面文件: {}, 反面文件: {}", mobile, path1, path2);
                return "";
            }
            log.info("【合并身份证图片】合并成功，手机号: {}, 合并文件: {}", mobile, mergedPath);
            // 上传到OSS
            log.info("【合并身份证图片】开始上传到OSS，手机号: {}, 合并文件: {}", mobile, mergedPath);
            String ossUrl = uploadFile(mergedPath, "/idcard/" + mobile + "/merged.jpg");
            if (StrUtil.isEmpty(ossUrl)) {
                log.error("【合并身份证图片】上传到OSS失败，手机号: {}, 合并文件: {}", mobile, mergedPath);
                return "";
            }
            log.info("【合并身份证图片】处理完成，手机号: {}, OSS URL: {}", mobile, ossUrl);
            return ossUrl;
        }, ossTaskExecutor);
    }

    private boolean isImageFile(File file) {
        String extension = getFileExtension(file);
        return "jpg".equalsIgnoreCase(extension) || "jpeg".equalsIgnoreCase(extension) ||
                "png".equalsIgnoreCase(extension);
    }

    private String getFileExtension(File file) {
        String name = file.getName();
        int lastDot = name.lastIndexOf('.');
        return lastDot == -1 ? "" : name.substring(lastDot + 1);
    }

    /**
     * 异步将 PDF 文件转换为 ZIP 压缩包并上传到 OSS。
     * 操作将在独立的线程池中执行。
     * 注意：转换逻辑依赖 DocumentTemplateUtil 的实现。
     *
     * @param path 原始 PDF 文件的本地路径。
     * @return CompletableFuture<String> 包含最终 OSS URL 的 Future 对象。
     *         如果操作成功，Future 完成时结果为 URL 字符串；
     *         如果操作失败，Future 完成时结果为空字符串 ""。
     */
    public CompletableFuture<String> pdfToZip(String path) {
        // 保存当前 MDC 上下文
        // Map<String, String> contextMap = MDCUtil.getCopyOfContextMap(); // Optional:
        // Also redundant if taskExecutor is used

        return CompletableFuture.supplyAsync(() -> {
            // try { // try block might need adjustment if contextMap setting was the only
            // content
            log.info("[pdfToZip] 开始处理 PDF 到 ZIP 转换和上传，输入路径: {}", path);
            String fileSavePath = huaRongZhengXinProperties.getFileSavePath();
            String date = DateUtil.format(new Date(), "yyyyMM/dd");
            String zipDir = fileSavePath + "/" + date + "/zip";
            log.debug("[pdfToZip] 计算出的 Zip 目录: {}", zipDir);
            // 确保目录存在
            File dir = new File(zipDir);
            if (!dir.exists()) {
                log.info("[pdfToZip] 目录 {} 不存在，尝试创建。", zipDir);
                dir.mkdirs();
            }
            // 输出的 zip 文件完整路径
            String outPath = zipDir + path.substring(path.lastIndexOf("/"), path.lastIndexOf(".")) + ".zip";
            log.debug("[pdfToZip] 计算出的预期输出 Zip 路径: {}", outPath);
            // 创建文档处理选项
            DocumentTemplateUtil.ProcessOptions options = DocumentTemplateUtil.ProcessOptions.builder()
                    .convertToPdf(false)
                    .compressPdf(true) // 注意：如果原始文件不是PDF，此选项可能无效或导致错误
                    .maxPdfSize(300 * 1024)
                    .zipCompress(true)
                    .base64Encode(false)
                    .outputPath(outPath) // 指定完整的输出路径
                    .build();
            log.debug("[pdfToZip] 创建的处理选项: {}", options);
            try {
                // 设置 MDC 上下文 - REMOVED as it should be handled by MdcTaskDecorator
                // MDCUtil.setContextMap(contextMap);
                log.info("[pdfToZip] 开始调用 DocumentTemplateUtil 处理文件: {}", path);
                File inputFile = new File(path);
                if (!inputFile.exists()) {
                    log.error("[pdfToZip] 输入文件不存在: {}", path);
                    return "";
                }
                // 耗时
                long processStartTime = System.nanoTime(); // Changed to nanoTime for consistency
                // 1. 根据选项处理文件转换和压缩（依赖 DocumentTemplateUtil）
                File processedFile = DocumentTemplateUtil.processFileConversion(inputFile, options);
                long processEndTime = System.nanoTime(); // Changed to nanoTime
                long processDuration = TimeUnit.NANOSECONDS.toMillis(processEndTime - processStartTime);
                log.info("[pdfToZip] DocumentTemplateUtil 处理完成. 输入: {}, 输出: {}, 耗时: {} ms", path,
                        processedFile.getAbsolutePath(),
                        processDuration);

                // 2. 确定最终待上传的 ZIP 文件
                File finalZipFile;
                File expectedZipFile = new File(outPath);

                if (expectedZipFile.exists()) {
                    finalZipFile = expectedZipFile;
                    log.info("[pdfToZip] 找到预期路径的 zip 文件: {}", outPath);
                } else if (processedFile.exists()
                        && !processedFile.getAbsolutePath().equals(inputFile.getAbsolutePath())) {
                    // 如果预期路径没有，但 processFileConversion 返回了一个存在且不同于输入文件的新文件路径
                    finalZipFile = processedFile;
                    log.warn("[pdfToZip] 预期 zip 文件 {} 不存在，使用 DocumentTemplateUtil 的输出路径作为最终 zip: {}", outPath,
                            finalZipFile.getAbsolutePath());
                } else {
                    log.error("[pdfToZip] 无法确定有效的 ZIP 文件。预期路径 {} 和处理程序输出路径 {} 均无效或不存在。", outPath,
                            processedFile.getAbsolutePath());
                    return ""; // 无法找到有效的 zip 文件
                }
                log.info("[pdfToZip] 最终确定待上传的 ZIP 文件: {}", finalZipFile.getAbsolutePath());

                // 3. 上传到OSS
                try (FileInputStream fis = new FileInputStream(finalZipFile)) {
                    // **重要：基于 finalZipFile 的实际路径计算相对路径作为 OSS key**
                    String actualZipPath = finalZipFile.getAbsolutePath();
                    String relativeOutPath = actualZipPath.replace(huaRongZhengXinProperties.getFileSavePath(), "");
                    if (!relativeOutPath.startsWith("/")) {
                        relativeOutPath = "/" + relativeOutPath;
                    }
                    // Replace backslashes with forward slashes for OSS keys
                    relativeOutPath = relativeOutPath.replace('\\', '/');
                    log.info("[pdfToZip] 准备上传到 OSS. Key: {}, 文件大小: {}", relativeOutPath, finalZipFile.length());
                    ObjectMetadata metadata = new ObjectMetadata();
                    metadata.setContentLength(finalZipFile.length());

                    long uploadStartTime = System.nanoTime(); // 记录开始时间
                    qcloudCosTemplate.putObject(relativeOutPath, fis, metadata); // 使用元数据的新方法
                    long uploadEndTime = System.nanoTime(); // 记录结束时间
                    long uploadDurationMillis = TimeUnit.NANOSECONDS.toMillis(uploadEndTime - uploadStartTime); // 计算
                                                                                                                // duration
                    log.info("[pdfToZip] 文件上传到 OSS 成功. Key: {}, Size: {}, Duration: {} ms",
                            relativeOutPath, finalZipFile.length(), uploadDurationMillis);
                    // 4. 删除本地临时文件 (可选)
                    // finalZipFile.delete();
                    // inputFile.delete();
                    String finalUrl = domain + relativeOutPath;
                    log.info("[pdfToZip] 处理成功完成，返回 URL: {}", finalUrl);
                    return finalUrl; // 返回最终的 OSS URL
                }

            } catch (Exception e) {
                log.error("[pdfToZip] 处理过程中发生异常. Input path: {}, Error: {}", path, e.getMessage(), e);
                return ""; // 发生异常，返回空字符串
            } finally {
                // 清理 MDC
                // MDCUtil.clear(); // Ensure clear is handled correctly either here or by
                // decorator
            }
            // }
        }, ossTaskExecutor); // 指定使用 ossTaskExecutor 线程池执行
    }

    /**
     * 异步上传本地文件到 OSS。
     * 操作将在独立的线程池中执行。
     *
     * @param path 要上传的本地文件的完整路径。
     * @return CompletableFuture<String> 包含最终 OSS URL 的 Future 对象。
     *         如果操作成功，Future 完成时结果为 URL 字符串；
     *         如果操作失败，Future 完成时结果为空字符串 ""。
     */
    public CompletableFuture<String> uploadOss(String path) {
        // 使用 supplyAsync 将任务提交到 ossTaskExecutor 线程池
        return CompletableFuture.supplyAsync(() -> {
            if (path == null) {
                log.error("[uploadOss] 异步上传文件错误：输入的路径为 null。");
                return ""; // Prevent NullPointerException and indicate failure
            }
            log.info("[uploadOss] 开始处理上传，输入路径: {}", path);
            try {
                File fileToUpload = new File(path);
                if (!fileToUpload.exists()) {
                    log.error("[uploadOss] 错误：待上传的文件不存在: {}", path);
                    return "";
                }
                try (FileInputStream fis = new FileInputStream(fileToUpload)) {
                    // 构建相对路径作为 OSS key
                    String relativePath = path.replace(huaRongZhengXinProperties.getFileSavePath(), "");
                    if (!relativePath.startsWith("/")) {
                        relativePath = "/" + relativePath;
                    }
                    // Replace backslashes with forward slashes for OSS keys
                    relativePath = relativePath.replace('\\', '/');
                    log.debug("[uploadOss] 计算出的 OSS Key: {}", relativePath);
                    ObjectMetadata metadata = new ObjectMetadata();
                    metadata.setContentLength(fileToUpload.length());
                    log.info("[uploadOss] 准备上传到 OSS. Key: {}, 文件大小: {}", relativePath, fileToUpload.length());
                    long uploadStartTime = System.nanoTime(); // 记录开始时间
                    qcloudCosTemplate.putObject(relativePath, fis, metadata); // 使用元数据的新方法
                    long uploadEndTime = System.nanoTime(); // 记录结束时间
                    long uploadDurationMillis = TimeUnit.NANOSECONDS.toMillis(uploadEndTime - uploadStartTime); // 计算
                                                                                                                // duration
                    log.info("[uploadOss] 文件上传到 OSS 成功. Key: {}, Size: {}, Duration: {} ms",
                            relativePath, fileToUpload.length(), uploadDurationMillis);
                    // 可选：删除本地文件? fileToUpload.delete();
                    String finalUrl = domain + relativePath;
                    log.info("[uploadOss] 处理成功完成，返回 URL: {}", finalUrl);
                    return finalUrl; // 返回最终的 OSS URL
                }
            } catch (Exception e) {
                log.error("[uploadOss] 处理过程中发生异常. Input path: {}, Error: {}", path, e.getMessage(), e);
                return ""; // 发生异常，返回空字符串
            }
        }, ossTaskExecutor); // 指定使用 ossTaskExecutor 线程池执行
    }

    private String mergeImages(String path1, String path2, String mobile) {
        try {
            String date = DateUtil.format(new Date(), "yyyyMM/dd");
            String dirPath = huaRongZhengXinProperties.getFileSavePath() + "/" + date + "/idcard/";
            File dir = new File(dirPath);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            String savePath = dirPath + mobile + path1.substring(path1.lastIndexOf("."));
            log.info("【合并身份证图片】开始合并图片，手机号: {}, 正面路径: {}, 反面路径: {}, 保存路径: {}", mobile, path1, path2, savePath);
            // 合并图片
            String[] paths = { path1, path2 };
            boolean merged = ImageUtil.merge(paths, savePath, 1080, 1080);
            if (!merged) {
                log.error("【合并身份证图片】图片合并失败，手机号: {}, 路径: {}, {}", mobile, path1, path2);
                return "";
            }

            // 压缩并调整尺寸
            String processedPath = ImageUtil.compressAndResize(savePath, 300 * 1024, 1080, 1080);
            if (processedPath == null) {
                log.error("【合并身份证图片】图片压缩或调整尺寸失败，手机号: {}, 路径: {}", mobile, savePath);
                return savePath;
            }
            log.info("【合并身份证图片】合并压缩调整尺寸图片成功，手机号: {}, 正面路径: {}, 反面路径: {}, 保存路径: {}", mobile, path1, path2,
                    processedPath);
            return processedPath;
        } catch (Exception e) {
            log.error("【合并身份证图片】合并图片异常，手机号: {}, 路径: {}, {}", mobile, path1, path2, e);
            return "";
        }
    }

    private String uploadFile(String filePath, String ossKey) {
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                log.error("【上传文件】文件不存在，路径: {}", filePath);
                return "";
            }

            try (FileInputStream fis = new FileInputStream(file)) {
                ObjectMetadata metadata = new ObjectMetadata();
                metadata.setContentLength(file.length());

                long uploadStartTime = System.nanoTime();
                qcloudCosTemplate.putObject(ossKey, fis, metadata);
                long uploadEndTime = System.nanoTime();
                long uploadDurationMillis = TimeUnit.NANOSECONDS.toMillis(uploadEndTime - uploadStartTime);

                log.info("【上传文件】上传成功，路径: {}, 大小: {}, 耗时: {} ms",
                        filePath, file.length(), uploadDurationMillis);

                // 确保 domain 和 ossKey 之间的斜杠正确
                String domain = OssUtil.domain;
                if (domain.endsWith("/") && ossKey.startsWith("/")) {
                    return domain + ossKey.substring(1);
                } else if (!domain.endsWith("/") && !ossKey.startsWith("/")) {
                    return domain + "/" + ossKey;
                } else {
                    return domain + ossKey;
                }
            }
        } catch (Exception e) {
            log.error("【上传文件】上传异常，路径: {}", filePath, e);
            return "";
        }
    }

    public static void main(String[] args) {
        // 测试不同的 URL 拼接场景
        String domain = "https://12qyc-**********.cos.ap-guangzhou.myqcloud.com";
        String mobile = "13972171776";

        // 场景1: domain 不以 / 结尾，ossKey 以 / 开头
        String ossKey1 = "/idcard/" + mobile + "/merged.jpg";
        String result1 = domain + ossKey1;
        System.out.println("场景1: " + result1);

        // 场景2: domain 以 / 结尾，ossKey 以 / 开头
        String domain2 = domain + "/";
        String result2 = domain2 + ossKey1;
        System.out.println("场景2: " + result2);

        // 场景3: domain 不以 / 结尾，ossKey 不以 / 开头
        String ossKey3 = "idcard/" + mobile + "/merged.jpg";
        String result3 = domain + "/" + ossKey3;
        System.out.println("场景3: " + result3);

        // 场景4: domain 以 / 结尾，ossKey 不以 / 开头
        String result4 = domain2 + ossKey3;
        System.out.println("场景4: " + result4);
    }
}
