package com.rongchen.byh.app.loveSign;



import com.ancun.netsign.client.NetSignClient;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

@Configuration
@EnableConfigurationProperties(LoveSignProperties.class)
public class LoveSignConfig {

    @Resource
    LoveSignProperties loveSignProperties;


    @Bean
    public NetSignClient getClient(){
        NetSignClient client = new NetSignClient(loveSignProperties.getBaseUrl()
                , loveSignProperties.getAppId()
                , loveSignProperties.getRsaPriKey());
        return client;
    }

}
