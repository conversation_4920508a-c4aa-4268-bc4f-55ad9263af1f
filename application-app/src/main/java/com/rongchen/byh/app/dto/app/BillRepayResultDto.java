package com.rongchen.byh.app.dto.app;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 账单还款结果查询dto
 * @date 2024/12/15 14:22:11
 */
@Data
public class BillRepayResultDto {
    @Schema(description = "本息还款计划id")
    @NotNull(message = "本息还款计划不能为空")
    private Long repayScheduleId;

    @Schema(description = "赊销还款计划id")
    @NotNull(message = "赊销还款计划不能为空")
    private Long repaySaleId;

}
