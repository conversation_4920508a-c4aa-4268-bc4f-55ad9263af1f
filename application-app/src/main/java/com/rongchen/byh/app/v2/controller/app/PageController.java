package com.rongchen.byh.app.v2.controller.app;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.util.ObjectUtil;
import com.rongchen.byh.app.dto.app.IdCardVerifyDto;
import com.rongchen.byh.app.utils.UserTokenUtil;
import com.rongchen.byh.app.v2.entity.ProductData;
import com.rongchen.byh.app.v2.entity.ProductPageMenu;
import com.rongchen.byh.app.v2.handle.product.ProductHandleCenter;
import com.rongchen.byh.app.v2.handle.product.ProductHandleFactory;
import com.rongchen.byh.app.v2.service.IProductPageMenuService;
import com.rongchen.byh.common.api.idCardVerify.vo.OcrVo;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Tag(name = "页面相关接口")
@RestController
@RequestMapping("/v2/pageApi")

public class PageController {
    @Autowired
    private IProductPageMenuService productPageMenuService;

    @Operation(summary = "获取页面跳转详情包括动态协议,暂时不用")
    @GetMapping("/getPageNew")
    @SaIgnore
    public ResponseResult<ProductPageMenu> getPage( String curPage, Integer isError,@RequestParam(required = false) String productCode) {
        //根据页面路由查询下一个页面
        ProductPageMenu menu =  productPageMenuService.selectByCurPage(curPage,isError,productCode);
        return ResponseResult.success(menu);
    }


    @Operation(summary = "获取路由新接口")
    @GetMapping("/getPage")
    @SaIgnore
    public ResponseResult<ProductPageMenu> getPageNew( String curPage, Integer isError,@RequestParam(required = false) String productCode) {
        //根据页面路由查询下一个页面
        ProductPageMenu menu =  productPageMenuService.getPageNew(curPage,isError,productCode);
        return ResponseResult.success(menu);
    }


}
