package com.rongchen.byh.app.v2.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.rongchen.byh.app.v2.dao.UserDetailFormMapper;
import com.rongchen.byh.app.v2.dto.CommonDto;
import com.rongchen.byh.app.v2.entity.UserDetailForm;
import com.rongchen.byh.app.v2.service.IUserDetailFormService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UserDetailFormServiceImpl implements IUserDetailFormService {
    @Autowired
    private UserDetailFormMapper userDetailFormMapper;
    @Override
    public List<UserDetailForm> selectByCapitalId(int capitalId) {
        List<UserDetailForm> list=  userDetailFormMapper.selectByCapitalId(capitalId);
        for (UserDetailForm one :list
             ) {
            String optionValue = one.getOptionValue();
            String filedName = one.getFiledName();
            List<CommonDto> optionList = JSON.parseObject(optionValue, new TypeReference<List<CommonDto>>() {});
            one.setOptionList(optionList);
            List<CommonDto> filedList = JSON.parseObject(filedName, new TypeReference<List<CommonDto>>() {});
            one.setFiledList(filedList);
        }
        return list;
    }
}