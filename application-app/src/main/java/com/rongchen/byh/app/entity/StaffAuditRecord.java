package com.rongchen.byh.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 员工审核记录表
 * @TableName staff_audit_record
 */
@TableName(value ="staff_audit_record")
@Data
public class StaffAuditRecord implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户员工绑定关系表id
     */
    private Long userStaffId;

    /**
     * 复审提交时间
     */
    private Date submitTime;

    /**
     * 芝麻分截图
     */
    private String sesameImages;

    /**
     * 微信截图
     */
    private String wechatImages;

    /**
     * 报告截图
     */
    private String reportImages;

    /**
     * 审批结果 1-通过 2-拒绝
     */
    private Integer auditStatus;

    /**
     * 授信金额
     */
    private Integer creditAmount;

    /**
     * 拒绝原因
     */
    private String refuseReason;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}