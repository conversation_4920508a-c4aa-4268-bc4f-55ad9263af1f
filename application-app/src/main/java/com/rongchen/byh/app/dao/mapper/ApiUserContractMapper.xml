<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.dao.ApiUserContractMapper">

    <resultMap id="BaseResultMap" type="com.rongchen.byh.app.entity.ApiUserContract">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="loanNo" column="loan_no" jdbcType="VARCHAR"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="contractInfo" column="contract_info" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,loan_no,user_id,
        type,contract_info,create_time,
        update_time
    </sql>
</mapper>
