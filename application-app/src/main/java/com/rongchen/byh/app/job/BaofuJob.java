package com.rongchen.byh.app.job;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rongchen.byh.app.config.ZifangFactory;
import com.rongchen.byh.app.dao.CapitalDataMapper;
import com.rongchen.byh.app.dao.DisburseDataMapper;
import com.rongchen.byh.app.dao.SaleScheduleMapper;
import com.rongchen.byh.app.dao.UserBankCardMapper;
import com.rongchen.byh.app.dao.UserDataMapper;
import com.rongchen.byh.app.entity.BaofuPayDetail;
import com.rongchen.byh.app.entity.BaofuPayRecord;
import com.rongchen.byh.app.entity.CapitalData;
import com.rongchen.byh.app.entity.SaleOrderRecord;
import com.rongchen.byh.app.entity.DisburseData;
import com.rongchen.byh.app.entity.UserData;
import com.rongchen.byh.app.service.BaofuPayDetailService;
import com.rongchen.byh.app.service.BaofuPayRecordService;
import com.rongchen.byh.app.service.SaleOrderRecordService;
import com.rongchen.byh.common.api.baofu.adapter.BaofuAdapterService;
import com.rongchen.byh.common.api.baofu.vo.req.DirectPayReqVo;
import com.rongchen.byh.common.api.baofu.vo.req.RiskItemReqVo;
import com.rongchen.byh.common.api.baofu.vo.rsp.DirectPayRspVo;
import com.rongchen.byh.common.api.yunxi.config.YunXiPayWay;
import com.rongchen.byh.common.api.yunxi.service.YunXiApiService;
import com.rongchen.byh.common.api.yunxi.vo.YunXiOrderDetailVo;
import com.rongchen.byh.common.api.zifang.dto.SaleApplyDto;
import com.rongchen.byh.common.api.zifang.service.OtherApi;
import com.rongchen.byh.common.api.zifang.service.fenzhuan.FenZhuanService;
import com.rongchen.byh.common.api.zifang.vo.SaleApplyVo;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.JsonUtils;
import com.rongchen.byh.common.core.util.MDCUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 项目名称：byh_java
 * 文件名称: BaofuBankCardResultJob
 * 创建时间: 2025-03-11 18:11
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.app.job
 * 文件描述: 宝付支付相关定时任务
 * Copyright (c) 2025 byh_java All rights reserved.
 */
@Component
@Slf4j
public class BaofuJob {

    @Resource
    private BaofuAdapterService baofuAdapterService;
    @Resource
    private UserBankCardMapper userBankCardMapper;
    @Resource
    private CapitalDataMapper capitalDataMapper;
    @Resource
    private DisburseDataMapper disburseDataMapper;
    @Resource
    private BaofuPayRecordService baofuPayRecordService;
    @Resource
    private BaofuPayDetailService baofuPayDetailService;
    @Resource
    private SaleOrderRecordService saleOrderRecordService;
    @Resource
    private YunXiApiService yunXiApiService;
    @Resource
    private FenZhuanService fenZhuanService;
    @Resource
    private ZifangFactory zifangFactory;
    @Resource
    private UserDataMapper userDataMapper;
    @Resource
    private SaleScheduleMapper saleScheduleMapper;

    // 最大重试次数
    private static final int MAX_RETRY_COUNT = 3;
    // 最大重试时间窗口（分钟）
    private static final int MAX_RETRY_WINDOW_MINUTES = 30;
    // 云溪渠道标识
    private static final String YUNXI_CHANNEL = "YUNXI";
    // 失败状态标识
    private static final String FAIL_STATUS = "FAIL";
    // 支付成功状态标识
    private static final String PAY_SUCCESS_STATUS = "SUCCESS";

    /**
     * todo 宝付绑卡结果查询定时任务
     *
     */
    @XxlJob("baofuBankCardResultJob")
    public void baofuBankCardResultJob() {
        // 设置全局TraceId
        String jobTraceId = MDCUtil.generateTraceId();
        MDCUtil.put(MDCUtil.Keys.TRACE_ID, jobTraceId);
        log.info("开始执行宝付绑卡结果查询任务, TraceId: {}", jobTraceId);

        try {
            // 查询分转渠道
            List<CapitalData> capitalDataList = capitalDataMapper.selectList(new LambdaQueryWrapper<CapitalData>());
            if (capitalDataList.isEmpty()) {
                log.info("没有分转渠道");
                return;
            }
            CapitalData capitalData = capitalDataList
                    .stream()
                    .filter(ca -> ca.getBeanName().startsWith("fenZhuan"))
                    .findFirst().orElse(null);
            if (capitalData == null) {
                log.info("没有分转渠道");
                return;
            }
            List<DisburseData> disburseDataList = disburseDataMapper.selectList(
                    new LambdaQueryWrapper<DisburseData>().eq(DisburseData::getCapitalId, capitalData.getId()));
            if (disburseDataList == null) {
                log.info("没有分转渠道");
                return;
            }

            // todo 宝付绑卡结果查询定时任务
            // for (DisburseData disburseData : disburseDataList) {
            // log.info("查询银行卡: {}", disburseData.getBankCardNo());
            // QueryBindBankResultDto queryDto = new QueryBindBankResultDto();
            // queryDto.setMessageNo(disburseData.getBankCardNo());
            // baofuAdapterService.queryBindCardResult(queryDto);
            // }

        } catch (Exception e) {
            log.error("宝付绑卡结果查询任务执行失败", e);
        } finally {
            // 清除MDC上下文
            MDCUtil.clear();
        }
    }

    /**
     * 宝付支付失败重试定时任务
     * 处理逻辑：
     * 1. 首先从BaofuPayDetail表查询支付失败的记录
     * 2. 查询对应的BaofuPayRecord表重试记录，如果不存在则创建
     * 3. 如果重试次数小于3次且在30分钟内，则进行重试
     * 4. 超过重试次数或超时的记录，标记为需要告警处理
     */
    @XxlJob("baofuPayRetryJob")
    public void baofuPayRetryJob() {
        // 设置全局TraceId
        String jobTraceId = MDCUtil.generateTraceId();
        MDCUtil.put(MDCUtil.Keys.TRACE_ID, jobTraceId);
        log.info("开始执行宝付支付失败重试任务, TraceId: {}", jobTraceId);

        try {
            // 1. 首先从BaofuPayDetail表查询状态为FAIL的支付记录
            LambdaQueryWrapper<BaofuPayDetail> detailQuery = new LambdaQueryWrapper<BaofuPayDetail>()
                    .eq(BaofuPayDetail::getPayStatus, "FAIL");

            List<BaofuPayDetail> failedPayDetails = baofuPayDetailService.list(detailQuery);
            log.info("查询到{}条支付失败详情记录", failedPayDetails.size());

            if (failedPayDetails.isEmpty()) {
                log.info("没有需要重试的支付失败记录");
                return;
            }

            // 处理每条失败记录
            for (BaofuPayDetail payDetail : failedPayDetails) {
                // 为每个订单处理设置独立的TraceId
                String orderTraceId = MDCUtil.generateTraceId();
                MDCUtil.put(MDCUtil.Keys.TRACE_ID, orderTraceId);

                try {
                    String userId = payDetail.getUserId();
                    String originalTransId = payDetail.getTransId();

                    if (StringUtils.isEmpty(userId) || StringUtils.isEmpty(originalTransId)) {
                        log.warn("支付详情记录userId或transId为空，跳过处理: {}, TraceId: {}", payDetail.getId(), orderTraceId);
                        continue;
                    }

                    log.info("开始处理支付失败记录 - userId: {}, transId: {}, TraceId: {}",
                            userId, originalTransId, orderTraceId);

                    // 2. 查询BaofuPayRecord表中是否已有该记录
                    BaofuPayRecord payRecord = baofuPayRecordService.getByUserIdAndTransId(userId, originalTransId);

                    // 3. 如果不存在记录，创建新记录
                    if (payRecord == null) {
                        payRecord = createNewPayRecord(payDetail);
                        log.info("创建新的支付重试记录: userId={}, originalTransId={}, TraceId: {}",
                                userId, originalTransId, orderTraceId);
                    }

                    // 如果记录已经是成功状态，跳过处理
                    if ("SUCCESS".equals(payRecord.getPayStatus())) {
                        log.info("支付记录已成功，无需重试: userId={}, originalTransId={}, TraceId: {}",
                                userId, originalTransId, orderTraceId);
                        continue;
                    }

                    // 4. 检查重试次数和时间窗口
                    Integer retryCount = payRecord.getRetryCount();
                    if (retryCount == null) {
                        retryCount = 0;
                        payRecord.setRetryCount(retryCount);
                    }

                    // 如果重试次数已达上限，标记为告警
                    if (retryCount >= MAX_RETRY_COUNT) {
                        log.warn(
                                "支付记录重试次数已达上限，标记为需要告警: userId={}, originalTransId={}, retryCount={}, TraceId: {}",
                                userId, originalTransId, retryCount, orderTraceId);
                        payRecord.setNotifyStatus(true);
                        payRecord.setErrorMessage("重试次数已达上限：" + retryCount);
                        baofuPayRecordService.updateById(payRecord);
                        continue;
                    }

                    // 5. 根据重试次数确定等待时间
                    int waitSeconds;
                    switch (retryCount) {
                        case 0:
                            waitSeconds = 1;
                            break;
                        case 1:
                            waitSeconds = 3;
                            break;
                        case 2:
                            waitSeconds = 5;
                            break;
                        default:
                            waitSeconds = 0;
                            break;
                    }
                    // 等待指定时间后执行重试
                    if (waitSeconds > 0) {
                        log.info("订单{}第{}次重试，等待{}秒, TraceId: {}",
                                originalTransId, retryCount + 1, waitSeconds, orderTraceId);
                        TimeUnit.SECONDS.sleep(waitSeconds);
                        // 执行重试支付
                        retryPayment(payRecord, payDetail);
                    }

                } catch (Exception e) {
                    log.error("处理支付失败记录异常: {}, TraceId: {}", payDetail.getTransId(), orderTraceId, e);
                }
            }

        } catch (Exception e) {
            log.error("宝付支付失败重试任务执行失败, TraceId: {}", jobTraceId, e);
        } finally {
            // 清除MDC上下文
            MDCUtil.clear();
        }
    }

    /**
     * 根据支付详情创建新的支付记录
     *
     * @param payDetail 支付详情
     * @return 支付记录
     */
    private BaofuPayRecord createNewPayRecord(BaofuPayDetail payDetail) {
        BaofuPayRecord payRecord = new BaofuPayRecord();

        // 设置原始订单号
        String originalId = payDetail.getTransId();
        log.info("原始订单号: {}, 长度: {}", originalId, originalId.length());
        payRecord.setOriginalTransId(originalId);

        // 生成新的交易订单号（原始订单号前缀-MMddHHmmss-重试标识）
        String prefix = originalId.split("-")[0]; // 获取原始订单号的前缀(cit/ln/rp)
        String timeStr = DateUtil.format(new Date(), "MMddHHmmss");
        String retryMark = "R" + RandomUtil.randomString(4).toUpperCase(); // 生成4位随机重试标识
        String newTransId = prefix + "-" + timeStr + "-" + retryMark;

        log.info("新交易订单号生成过程: 前缀[{}] + 时间戳[{}] + 重试标识[{}] = {}, 长度: {}",
                prefix, timeStr, retryMark, newTransId, newTransId.length());

        payRecord.setTransId(newTransId);

        // 基础信息复制
        payRecord.setUserId(payDetail.getUserId());
        log.info("复制用户信息: userId={}", payDetail.getUserId());

        payRecord.setProtocolNo(payDetail.getProtocolNo());
        log.info("复制协议号: protocolNo={}", payDetail.getProtocolNo());

        payRecord.setTxnAmt(payDetail.getTxnAmt());
        log.info("复制交易金额: txnAmt={}", payDetail.getTxnAmt());

        payRecord.setPayStatus(payDetail.getPayStatus());
        payRecord.setMsgCode(payDetail.getMsgCode());
        payRecord.setErrorMessage(payDetail.getMessage());
        payRecord.setOuPayOrderOn(payDetail.getOuPayOrderOn());
        log.info("复制状态信息: payStatus={}, msgCode={}, errorMessage={}, ouPayOrderOn={}",
                payDetail.getPayStatus(), payDetail.getMsgCode(), payDetail.getMessage(), payDetail.getOuPayOrderOn());

        // 如果存在，设置支付成功相关信息
        if (payDetail.getSucceedTime() != null) {
            payRecord.setSucceedTime(payDetail.getSucceedTime());
            log.info("设置支付成功时间: {}", DateUtil.formatDateTime(payDetail.getSucceedTime()));
        }
        if (payDetail.getSucceedAmt() != null) {
            payRecord.setSucceedAmt(payDetail.getSucceedAmt());
            log.info("设置支付成功金额: {}", payDetail.getSucceedAmt());
        }

        // 设置重试相关参数
        payRecord.setRetryCount(0);
        payRecord.setMaxRetryCount(MAX_RETRY_COUNT);
        payRecord.setFirstErrorTime(payDetail.getCreateTime());
        payRecord.setNotifyStatus(false);
        log.info("初始化重试参数: retryCount=0, maxRetryCount={}, firstErrorTime={}",
                MAX_RETRY_COUNT, DateUtil.formatDateTime(payDetail.getCreateTime()));

        // 生成新的流水号
        String msgId = "qyc_retry_" + RandomUtil.randomString(32);
        payRecord.setMsgId(msgId);
        log.info("生成新流水号: {}", msgId);

        // 保存记录
        baofuPayRecordService.save(payRecord);
        log.info("=== 创建支付重试记录完成 ===");
        log.info("原始订单号: {}", payRecord.getOriginalTransId());
        log.info("新订单号: {} (长度: {})", payRecord.getTransId(), payRecord.getTransId().length());
        log.info("用户ID: {}", payRecord.getUserId());
        return payRecord;
    }

    /**
     * 重试支付
     *
     * @param payRecord 支付记录
     */
    private void retryPayment(BaofuPayRecord payRecord, BaofuPayDetail payDetail) {
        log.info("=== 开始执行支付重试 ===");
        log.info("原始订单号: {}", payRecord.getOriginalTransId());
        log.info("当前订单号: {}", payRecord.getTransId());
        log.info("用户ID: {}", payRecord.getUserId());
        log.info("当前重试次数: {}", payRecord.getRetryCount());

        try {

            // 生成新的交易订单号
            String originalId = payRecord.getOriginalTransId();
            String prefix = originalId.split("-")[0]; // 获取原始订单号的前缀
            String timeStr = DateUtil.format(new Date(), "MMddHHmmss");
            String retryMark = "R" + RandomUtil.randomString(4).toUpperCase() + (payRecord.getRetryCount() + 1);
            String newTransId = prefix + "-" + timeStr + "-" + retryMark;

            log.info("新交易订单号生成过程:");
            log.info("前缀: {}", prefix);
            log.info("时间戳: {}", timeStr);
            log.info("重试标识: {}", retryMark);
            log.info("最终订单号: {} (长度: {})", newTransId, newTransId.length());

            payRecord.setTransId(newTransId);

            // 构建支付请求对象
            DirectPayReqVo reqVo = new DirectPayReqVo();
            reqVo.setTransId(newTransId);
            reqVo.setUserId(payRecord.getUserId());
            reqVo.setTxnAmt(payRecord.getTxnAmt());
            reqVo.setProtocolNo(payRecord.getProtocolNo());
            reqVo.setRiskItem(JsonUtils.parseObject(payDetail.getRiskItem(), RiskItemReqVo.class));

            log.info("构建支付请求参数: transId={}, userId={}, txnAmt={}, protocolNo={}",
                    newTransId, payRecord.getUserId(), payRecord.getTxnAmt(), payRecord.getProtocolNo());

            // 更新重试次数和时间
            int retryCount = payRecord.getRetryCount() == null ? 0 : payRecord.getRetryCount();
            payRecord.setRetryCount(retryCount + 1);
            payRecord.setLastRetryTime(new Date());
            if (payRecord.getFirstErrorTime() == null) {
                payRecord.setFirstErrorTime(new Date());
            }
            log.info("更新重试信息: 重试次数={}, 最后重试时间={}, 首次错误时间={}",
                    payRecord.getRetryCount(),
                    DateUtil.formatDateTime(payRecord.getLastRetryTime()),
                    DateUtil.formatDateTime(payRecord.getFirstErrorTime()));

            // 生成新的流水号
            String msgId = "qyc_retry_" + RandomUtil.randomString(32);
            payRecord.setMsgId(msgId);
            log.info("生成新流水号: {}", msgId);

            // 先保存更新后的记录
            baofuPayRecordService.updateById(payRecord);
            log.info("保存更新后的支付记录");

            // 发起支付请求
            log.info("开始发起支付请求...");
            ResponseResult<DirectPayRspVo> result = baofuAdapterService.directPay(reqVo);

            // 处理支付结果
            if (!result.isSuccess()) {
                log.error("支付请求失败:");
                log.error("原始订单号: {}", payRecord.getOriginalTransId());
                log.error("新订单号: {}", newTransId);
                log.error("错误信息: {}", result.getErrorMessage());
                payRecord.setErrorMessage(result.getErrorMessage());
                baofuPayRecordService.updateById(payRecord);
                return;
            }

            DirectPayRspVo data = result.getData();
            if (!"0000".equals(data.getCode())) {
                log.error("业务处理失败:");
                log.error("原始订单号: {}", payRecord.getOriginalTransId());
                log.error("新订单号: {}", newTransId);
                log.error("错误码: {}", data.getCode());
                log.error("错误信息: {}", data.getMessage());
                payRecord.setMsgCode(data.getCode());
                payRecord.setErrorMessage(data.getMessage());
                payRecord.setOuPayOrderOn(data.getOrderId());
                baofuPayRecordService.updateById(payRecord);
                return;
            }

            // 支付成功
            log.info("=== 支付重试成功 ===");
            log.info("原始订单号: {}", payRecord.getOriginalTransId());
            log.info("新订单号: {}", newTransId);
            log.info("宝付订单号: {}", data.getOrderId());
            log.info("成功金额: {}", data.getSuccAmt());

            payRecord.setPayStatus("SUCCESS");
            payRecord.setMsgCode(data.getCode());
            payRecord.setErrorMessage(data.getMessage());
            payRecord.setOuPayOrderOn(data.getOrderId());
            payRecord.setSucceedAmt(data.getSuccAmt());

            // 设置支付成功时间
            try {
                if (data.getSuccTime() != null && !data.getSuccTime().isEmpty()) {
                    payRecord.setSucceedTime(DateUtil.parse(data.getSuccTime(), "yyyy-MM-dd HH:mm:ss"));
                    log.info("支付成功时间: {}", data.getSuccTime());
                }
            } catch (Exception e) {
                log.warn("解析支付成功时间失败: {}", data.getSuccTime(), e);
            }

            // 保存支付成功记录
            baofuPayRecordService.updateById(payRecord);
            log.info("更新支付成功记录完成");

            // 支付成功后直接处理权益购买
            if ("SUCCESS".equals(payRecord.getPayStatus())) {
                try {
                    // 直接调用权益购买逻辑
                    processRightsPurchase(payRecord);
                } catch (Exception e) {
                    log.error("支付成功后处理权益购买异常：{}", e.getMessage(), e);
                    // 失败时设置状态，由定时任务重试
                    payRecord.setRightsPurchased(false);
                    payRecord.setRightsFailCount(
                            payRecord.getRightsFailCount() == null ? 1 : payRecord.getRightsFailCount() + 1);
                    baofuPayRecordService.updateById(payRecord);
                }
            }

        } catch (Exception e) {
            log.error("=== 重试支付过程发生异常 ===");
            log.error("原始订单号: {}", payRecord.getOriginalTransId());
            log.error("新订单号: {}", payRecord.getTransId());
            log.error("异常信息: ", e);

            // 更新重试次数
            int retryCount = payRecord.getRetryCount() == null ? 0 : payRecord.getRetryCount();
            payRecord.setRetryCount(retryCount + 1);
            payRecord.setLastRetryTime(new Date());
            payRecord.setErrorMessage("重试过程异常: " + e.getMessage());

            baofuPayRecordService.updateById(payRecord);
            log.info("更新异常状态完成");
        }
    }

    /**
     * 宝付支付成功后权益购买定时任务
     * 处理逻辑：
     * 1. 查询支付状态为SUCCESS但未进行权益购买的记录
     * 2. 根据支付记录信息调用云溪权益购买接口
     * 3. 处理权益购买结果并记录
     * 4. 失败的记录将在下次执行时进行重试
     */
    @XxlJob("baofuRightsPurchaseJob")
    public void baofuRightsPurchaseJob() {
        // 设置全局TraceId
        String jobTraceId = MDCUtil.generateTraceId();
        MDCUtil.put(MDCUtil.Keys.TRACE_ID, jobTraceId);
        log.info("=== 开始执行宝付支付成功后权益购买任务 === TraceId: {}", jobTraceId);
        long startTime = System.currentTimeMillis();

        try {
            // 1. 查询支付状态为SUCCESS但未执行权益购买的订单
            List<BaofuPayRecord> successPayRecords = baofuPayRecordService.lambdaQuery()
                    .eq(BaofuPayRecord::getPayStatus, PAY_SUCCESS_STATUS)
                    .isNull(BaofuPayRecord::getRightsPurchased)
                    .or()
                    .eq(BaofuPayRecord::getPayStatus, PAY_SUCCESS_STATUS)
                    .eq(BaofuPayRecord::getRightsPurchased, false)
                    .list();

            log.info("查询到{}条支付成功但未执行权益购买的记录", successPayRecords.size());

            if (successPayRecords.isEmpty()) {
                log.info("没有需要执行权益购买的记录");
                return;
            }

            // 处理每条成功记录
            for (BaofuPayRecord payRecord : successPayRecords) {
                // 为每个订单处理设置独立的TraceId
                String orderTraceId = MDCUtil.generateTraceId();
                MDCUtil.put(MDCUtil.Keys.TRACE_ID, orderTraceId);

                try {
                    log.info("开始处理权益购买记录 - transId: {}, userId: {}, TraceId: {}",
                            payRecord.getTransId(), payRecord.getUserId(), orderTraceId);
                    processRightsPurchase(payRecord);
                } catch (Exception e) {
                    log.error("处理权益购买异常 - 交易ID: {}, 用户ID: {}, TraceId: {}, 异常信息: {}",
                            payRecord.getTransId(), payRecord.getUserId(), orderTraceId, e.getMessage(), e);
                }
            }

            long endTime = System.currentTimeMillis();
            log.info("=== 宝付支付成功后权益购买任务执行完成 === 耗时: {}ms, 处理订单数: {}, TraceId: {}",
                    (endTime - startTime), successPayRecords.size(), jobTraceId);
        } catch (Exception e) {
            log.error("宝付支付成功后权益购买任务执行失败, TraceId: {}", jobTraceId, e);
        } finally {
            // 清除MDC上下文
            MDCUtil.clear();
        }
    }

    /**
     * 处理单个支付记录的权益购买逻辑
     *
     * @param payRecord 支付记录
     */
    private void processRightsPurchase(BaofuPayRecord payRecord) {
        String transId = payRecord.getTransId();
        String userId = payRecord.getUserId();

        log.info("=== 开始处理权益购买 ===");
        log.info("交易ID: {}", transId);
        log.info("用户ID: {}", userId);
        log.info("支付金额: {}", payRecord.getTxnAmt());

        try {
            // 1. 查询用户信息
            UserData userData = userDataMapper.selectById(Long.valueOf(userId));
            if (userData == null) {
                log.error("权益购买失败 - 未找到用户信息: userId={}", userId);
                return;
            }

            // 2. 查询用户放款信息
            DisburseData disburseData = disburseDataMapper.selectByUserId(Long.valueOf(userId));
            if (disburseData == null) {
                log.error("权益购买失败 - 未找到放款信息: userId={}", userId);
                return;
            }

            // 3. 查询资方信息
            CapitalData capitalData = capitalDataMapper.selectById(disburseData.getCapitalId());
            if (capitalData == null || !capitalData.getBeanName().startsWith("fenZhuan")) {
                log.error("权益购买失败 - 不是分转渠道或未找到资方信息: userId={}, capitalData={}",
                        userId, capitalData != null ? capitalData.getBeanName() : "null");
                return;
            }

            // 4. 准备云溪权益购买请求
            OtherApi otherApi = zifangFactory.getApi(capitalData.getBeanName(), OtherApi.class);

            // 元转分后的金额转回元
            String amountStr = new BigDecimal(payRecord.getTxnAmt())
                    .divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP)
                    .toString();

            // 构建权益申请参数
            SaleApplyDto saleApplyDto = new SaleApplyDto();
            saleApplyDto.setLoanNo(disburseData.getLoanNo());
            saleApplyDto.setSaleNo(disburseData.getSaleNo());
            saleApplyDto.setPaymentNo(transId);
            saleApplyDto.setMobile(userData.getMobile());
            saleApplyDto.setApplyAmt(amountStr);
            saleApplyDto.setUserId(userId);
            saleApplyDto.setApplyTerm("1"); // 默认一期

            log.info("【权益赊销】订单申请接口 - 宝付支付单号：{}, 申请金额：{}, 1期, 申请赊销",
                    transId, amountStr);

            // 5. 调用云溪权益购买接口
            ResponseResult<SaleApplyVo> saleApply = otherApi.getSaleApply(saleApplyDto);

            // 6. 处理权益购买结果
            if (!saleApply.isSuccess()) {
                log.error("【权益赊销】订单申请失败 - 宝付支付单号：{}, 申请金额：{}, 错误信息：{}",
                        transId, amountStr, saleApply.getErrorMessage());

                // 记录失败信息，供重试任务处理
                SaleOrderRecord saleOrderRecord = new SaleOrderRecord();
                saleOrderRecord.setSaleChannel(SaleOrderRecordService.SALE_CHANNEL);
                saleOrderRecord.setSaleNo(disburseData.getSaleNo());
                saleOrderRecord.setUserId(userId);
                saleOrderRecord.setOrderAmount(new BigDecimal(amountStr));
                saleOrderRecord.setUserMobile(userData.getMobile());
                saleOrderRecord.setPaymentNo(transId);
                saleOrderRecord.setOrderStatus(FAIL_STATUS); // 标记为失败状态
                saleOrderRecord.setPayWay(YunXiPayWay.BAOFU_PROXY_PAY); // 信用支付方式
                saleOrderRecord.setRetryNum(0L); // 初始重试次数
                saleOrderRecordService.save(saleOrderRecord);

                // 将支付记录标记为已处理，后续由YunXiSaleJob重试处理
                payRecord.setRightsPurchased(true);
                payRecord.setRightsStatus(FAIL_STATUS);
                payRecord.setRightsMessage(saleApply.getErrorMessage());
                baofuPayRecordService.updateById(payRecord);

                log.info("【权益赊销】已记录失败信息，等待重试 - 宝付支付单号：{}", transId);
                return;
            }

            // 7. 处理成功情况
            SaleApplyVo saleApplyVo = saleApply.getData();
            YunXiOrderDetailVo orderDetail = saleApplyVo.getYunXiOrderDetail();

            // 设置订单金额
            orderDetail.setOrderAmount(amountStr);

            // 8. 保存渠道记录
            SaleOrderRecord saleOrderRecord = saleOrderRecordService.buildFromOrderDetail(
                    orderDetail,
                    null, // 新建场景,不需要已有记录
                    disburseData.getSaleNo(),
                    userId);
            saleOrderRecordService.save(saleOrderRecord);

            log.info("【权益赊销】订单申请成功 - 宝付支付单号：{}, 申请金额：{}, 云溪订单号：{}",
                    transId, amountStr, orderDetail.getOrderNum());

            // 9. 更新支付记录为已处理
            payRecord.setRightsPurchased(true);
            payRecord.setRightsStatus("SUCCESS");
            payRecord.setRightsOrderNo(orderDetail.getOrderNum());
            baofuPayRecordService.updateById(payRecord);

            log.info("【权益赊销】权益购买流程完成 - 宝付支付单号：{}", transId);

        } catch (Exception e) {
            log.error("权益购买处理异常 - 交易ID: {}, 用户ID: {}, 异常信息: {}",
                    transId, userId, e.getMessage(), e);

            // 异常情况下，不修改状态，下次会继续处理
            payRecord.setRightsFailCount(
                    payRecord.getRightsFailCount() == null ? 1 : payRecord.getRightsFailCount() + 1);

            if (payRecord.getRightsFailCount() >= MAX_RETRY_COUNT) {
                log.error("权益购买失败次数超过上限({}) - 交易ID: {}, 用户ID: {}",
                        MAX_RETRY_COUNT, transId, userId);
                payRecord.setRightsPurchased(true); // 标记为已处理，不再重试
                payRecord.setRightsStatus("FAIL_MAX_RETRY");
                payRecord.setRightsMessage("超过最大重试次数: " + payRecord.getRightsFailCount());

                // 发送告警通知
                sendRightsPurchaseAlarm(payRecord, e.getMessage());
            }

            baofuPayRecordService.updateById(payRecord);
        }
    }

    /**
     * 发送权益购买失败告警
     *
     * @param payRecord 支付记录
     * @param errorMsg  错误信息
     */
    private void sendRightsPurchaseAlarm(BaofuPayRecord payRecord, String errorMsg) {
        // TODO: 实现告警通知逻辑，比如发送钉钉、邮件等
        log.error("【告警】权益购买失败达到最大重试次数 - 交易ID: {}, 用户ID: {}, 错误信息: {}",
                payRecord.getTransId(), payRecord.getUserId(), errorMsg);
    }
}
