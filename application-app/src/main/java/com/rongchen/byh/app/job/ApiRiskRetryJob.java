package com.rongchen.byh.app.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.app.dao.ApiRiskFailRecordMapper;
import com.rongchen.byh.app.entity.ApiRiskFailRecord;
import com.rongchen.byh.common.api.riskControl.dto.ApiCreditPreLoanAuditDto;
import com.rongchen.byh.common.api.riskControl.service.RiskControlService;
import com.rongchen.byh.common.core.util.MDCUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description api风控重试
 * @date 2025/4/8 19:11:27
 */
@Component
@Slf4j
public class ApiRiskRetryJob {
    @Resource
    private RiskControlService riskControlService;

    @Resource(name = "taskExecutor") // 注入自定义的线程池 Bean，用于执行异步任务
    private Executor taskExecutor;

    @Resource
    private ApiRiskFailRecordMapper apiRiskFailRecordMapper;
    @XxlJob("apiRiskRetryJob")
    public void apiRiskRetryJob() {
        String param = XxlJobHelper.getJobParam();
        Integer day = 1;
        if (StrUtil.isNotEmpty(param)) {
            day = Integer.valueOf(param);
        }
        String traceId = UUID.fastUUID().toString();
        MDCUtil.setTraceId(traceId);
        log.info("===========apiRiskRetryJob start=========");
        //
        List<ApiRiskFailRecord> list = apiRiskFailRecordMapper.getInProcessList(day);
        if (CollUtil.isEmpty(list)) {
            log.info("=======没有待重试的api风控申请======");
            return;
        }
        List<CompletableFuture<?>> futureList = new ArrayList<>();
        List<ApiRiskFailRecord> failList = new ArrayList<>();
        for (ApiRiskFailRecord record : list) {
            futureList.add(CompletableFuture.supplyAsync(() -> {
                ApiCreditPreLoanAuditDto apiCreditPreLoanAuditDto = JSONObject.parseObject(record.getRequestParam(), ApiCreditPreLoanAuditDto.class);
                return riskControlService.apiCreditPreLoanAudit(apiCreditPreLoanAuditDto);
            }, taskExecutor).thenApply(result -> {
                if (result.getResult() != 1) {
                    failList.add(record);
                }
                return result;
            }).exceptionally(e -> {
                log.error("异步任务执行异常", e);
                return null;
            }));
        }
        try {
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).get(100, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("异步任务执行异常", e);
        }
        log.info("===========apiRiskRetryJob end，执行失败列表：{}=========", JSONObject.toJSONString(failList));
    }
}
