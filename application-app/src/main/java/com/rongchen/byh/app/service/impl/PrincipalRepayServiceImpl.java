package com.rongchen.byh.app.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.rongchen.byh.app.config.ZifangFactory;
import com.rongchen.byh.app.constant.ZiFangBeanConstant;
import com.rongchen.byh.app.dao.CapitalDataMapper;
import com.rongchen.byh.app.dao.DisburseDataMapper;
import com.rongchen.byh.app.dao.RepayScheduleMapper;
import com.rongchen.byh.app.dao.UserBankCardMapper;
import com.rongchen.byh.app.dao.UserRegisterResultMapper;
import com.rongchen.byh.app.dto.app.BackVo;
import com.rongchen.byh.app.dto.app.BillRepayApplyDto;
import com.rongchen.byh.app.entity.CapitalData;
import com.rongchen.byh.app.entity.DisburseData;
import com.rongchen.byh.app.entity.RepaySchedule;
import com.rongchen.byh.app.entity.UserRegisterResult;
import com.rongchen.byh.app.service.PrincipalRepayService;
import com.rongchen.byh.app.utils.CreditLoanNoUtils;
import com.rongchen.byh.app.utils.UserTokenUtil;
import com.rongchen.byh.common.api.zifang.dto.PreRepayApplyDto;
import com.rongchen.byh.common.api.zifang.dto.RepaymentApplyDetailDto;
import com.rongchen.byh.common.api.zifang.dto.RepaymentApplyDto;
import com.rongchen.byh.common.api.zifang.dto.RepaymentApplyRepayDto;
import com.rongchen.byh.common.api.zifang.service.RepaymentApi;
import com.rongchen.byh.common.api.zifang.vo.PreRepayApplyVo;
import com.rongchen.byh.common.api.zifang.vo.RepaymentApplyVo;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.constant.SettleFlagConstant;
import com.rongchen.byh.common.core.object.ResponseResult;

import java.math.BigDecimal;
import java.util.*;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/12/15 16:15:56
 */
@Slf4j
@Service
public class PrincipalRepayServiceImpl implements PrincipalRepayService {

    @Resource
    private DisburseDataMapper disburseDataMapper;
    @Resource
    private RepayScheduleMapper repayScheduleMapper;
    @Resource
    private UserBankCardMapper userBankCardMapper;
    @Resource
    private RepaymentApi repaymentApi;
    @Resource
    ZifangFactory zifangFactory;
    @Resource
    CapitalDataMapper capitalDataMapper;
    @Resource
    UserRegisterResultMapper userRegisterResultMapper;

    @Override
    public ResponseResult<Void> principalRepay(BillRepayApplyDto billRepayApplyDto) {
        RepaySchedule repaySchedule = repayScheduleMapper.selectById(billRepayApplyDto.getRepayScheduleId());
        Long userId = UserTokenUtil.getUserId();
        BackVo backVo = userBankCardMapper.queryBackByUserId(userId);
        DisburseData disburseData = disburseDataMapper.selectById(repaySchedule.getDisburseId());
        // 本息划扣
        RepaymentApplyDto repaymentApplyDto = new RepaymentApplyDto();
        repaymentApplyDto.setFundCode(disburseData.getFundCode());
        repaymentApplyDto.setRepayApplyNo(IdUtil.fastSimpleUUID());
        billRepayApplyDto.setRepayApplyNo(repaymentApplyDto.getRepayApplyNo());
        repaymentApplyDto.setUserId(String.valueOf(userId));
        repaymentApplyDto.setRepayMethod("0");
        String termStatus = repaySchedule.getTermStatus();
        String repayType = "CURRENT";
        if ("N".equals(termStatus)) {
            repayType = "OVERDUE";
        }
        repaymentApplyDto.setRepayType(repayType);
        repaymentApplyDto.setBankPhoneNo(backVo.getMobile());
        repaymentApplyDto.setAmount(repaySchedule.getTotalAmt().toString());
        repaymentApplyDto.setBankCardNo(backVo.getBankAccount());
        repaymentApplyDto.setAccountCardType("1");
        repaymentApplyDto.setIdNo(backVo.getIdCard());
        repaymentApplyDto.setCustomerName(backVo.getName());
        // todo 等资方确认银行卡支行名
        repaymentApplyDto.setBranchName("支行名");
        repaymentApplyDto.setBankAccountType("0");
        List<RepaymentApplyRepayDto> repaymentApplyRepayList = new ArrayList<>();
        List<RepaymentApplyDetailDto> repaymentApplyDetailList = new ArrayList<>();
        RepaymentApplyRepayDto repaymentApplyRepayDto = new RepaymentApplyRepayDto();
        repaymentApplyRepayDto.setLoanNo(disburseData.getLoanNo());
        repaymentApplyRepayDto.setRepayAmt(repaySchedule.getTotalAmt().toString());
        repaymentApplyRepayDto.setRepayTerm(repaySchedule.getRepayTerm());
        repaymentApplyRepayDto.setSplitType("1");
        repaymentApplyRepayDto.setPrinAmt(repaySchedule.getTermRetPrin().toString());
        repaymentApplyRepayDto.setIntAmt(repaySchedule.getTermRetInt().toString());
        repaymentApplyRepayList.add(repaymentApplyRepayDto);

        RepaymentApplyDetailDto repaymentApplyDetailDto = new RepaymentApplyDetailDto();
        repaymentApplyDetailDto.setRepayterm(repaySchedule.getRepayTerm());
        repaymentApplyDetailDto.setPrintAmt(repaySchedule.getTermRetPrin().toString());
        repaymentApplyDetailDto.setIntAmt(repaySchedule.getRepayTerm());
        repaymentApplyDetailList.add(repaymentApplyDetailDto);

        repaymentApplyRepayDto.setRepayDetailList(repaymentApplyDetailList);
        repaymentApplyDto.setRepayList(repaymentApplyRepayList);

        CapitalData capitalData = capitalDataMapper.selectById(disburseData.getCapitalId());
        if (ObjectUtil.isEmpty(capitalData)) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "资方未匹配");
        }

        RepaymentApi repaymentApi = zifangFactory.getApi(capitalData.getBeanName(), RepaymentApi.class);
        // todo 回调地址和bindId
        ResponseResult<RepaymentApplyVo> repaymentApply = repaymentApi.getRepaymentApply(repaymentApplyDto);
        if (!repaymentApply.isSuccess()) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, repaymentApply.getErrorMessage());
        }
        RepaymentApplyVo repaymentApplyVo = repaymentApply.getData();
        if (!"0000".equals(repaymentApplyVo.getResponseCode())) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, repaymentApplyVo.getResponseMsg());
        }
        return ResponseResult.success();
    }

    @Override
    public ResponseResult<Void> loanRepay(BillRepayApplyDto billRepayApplyDto) {
        String sequence = CreditLoanNoUtils.getSequence("ln-rp");
        // 获取本息还款计划
        RepaySchedule repaySchedule = repayScheduleMapper.selectById(billRepayApplyDto.getRepayScheduleId());
        if (ObjectUtil.isEmpty(repaySchedule)) {
            log.error("【还款服务】还款计划不存在，repayScheduleId: {},Exception: {}",
                    billRepayApplyDto.getRepayScheduleId(), "还款计划不存在");
            return ResponseResult.error(ErrorCodeEnum.FAIL, "还款计划不存在");
        }
        repaySchedule.setRepayApplyNo(sequence);
        repayScheduleMapper.updateById(repaySchedule);
        // 获取订单
        DisburseData disburseData = disburseDataMapper.selectById(repaySchedule.getDisburseId());
        // 调用还款试算
        PreRepayApplyDto applyDto = new PreRepayApplyDto();
        applyDto.setMerserno(UUID.randomUUID().toString().replace("-", ""));
        applyDto.setLoanNo(disburseData.getLoanNo());
        // 判断还款类型
        String todayDate = DateUtil.format(new Date(), "yyyy-MM-dd");
        int date = todayDate.compareTo(repaySchedule.getRepayOwnbDate());
        applyDto.setPaytotalamt(repaySchedule.getTotalAmt().toString());
        if (date < 0) {
            applyDto.setPrePayType("2");
            applyDto.setPaytotalamt(repaySchedule.getTermRetPrin().toString());
        } else if (date == 0) {
            applyDto.setPrePayType("1");
        } else {
            applyDto.setPrePayType("5");
        }
        applyDto.setTerm(repaySchedule.getRepayTerm());
        CapitalData capitalData = capitalDataMapper.selectById(disburseData.getCapitalId());
        if (ObjectUtil.isEmpty(capitalData)) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "资方未匹配");
        }
        if (capitalData.getBeanName().equals(ZiFangBeanConstant.FENZHUAN)) {
            applyDto.setLoanNo(disburseData.getCreditNo());
        }
        RepaymentApi repaymentApi = zifangFactory.getApi(capitalData.getBeanName(), RepaymentApi.class);
        ResponseResult<PreRepayApplyVo> preRepayApply = repaymentApi.getPreRepayApply(applyDto);
        if (preRepayApply.getData().getResponseCode().equals("0000")) {
            PreRepayApplyVo data = preRepayApply.getData();
            // 本次还款本金
            BigDecimal payNormAmt = StrUtil.isNotEmpty(data.getPayNormAmt()) ? new BigDecimal(data.getPayNormAmt()) : repaySchedule.getTermRetPrin();
            // 本次还款利息
            BigDecimal payIntAmt = StrUtil.isNotEmpty(data.getPayInteAmt()) ? new BigDecimal(data.getPayInteAmt()) : repaySchedule.getTermRetInt();
            // 本次还款罚息
            BigDecimal payEnteAmt = StrUtil.isNotEmpty(data.getPayEnteAmt()) ? new BigDecimal(data.getPayEnteAmt()) : repaySchedule.getTermRetFint();
            // 手续费/违约金
            BigDecimal fee = new BigDecimal(data.getFee());
            // 本次还款总金额
            BigDecimal payTotalAmt = StrUtil.isNotEmpty(data.getPayTotalAmt()) ? new BigDecimal(data.getPayTotalAmt()) : repaySchedule.getTotalAmt();
            // 融担费
            BigDecimal guarantorAmt = StrUtil.isNotEmpty(data.getGuarantorAmt()) ? new BigDecimal(data.getGuarantorAmt()) : repaySchedule.getTermGuarantorFee();
            // 服务费
            BigDecimal serviceAmt = StrUtil.isNotEmpty(data.getServiceAmt()) ? new BigDecimal(data.getServiceAmt()) : repaySchedule.getTermServiceFee();
            // 融担罚费
            BigDecimal overdueGuarantorFee = StrUtil.isNotEmpty(data.getOverdueGuarantorFee()) ? new BigDecimal(data.getOverdueGuarantorFee()) : repaySchedule.getTermOverdueGuarantorFee();

            // 调用本息还款
            // 本息划扣
            RepaymentApplyDto repaymentApplyDto = new RepaymentApplyDto();
            repaymentApplyDto.setFundCode(disburseData.getFundCode());
            repaymentApplyDto.setRepayApplyNo(IdUtil.fastSimpleUUID());
            repaymentApplyDto.setRepayApplyNo(sequence);
            repaymentApplyDto.setUserId(disburseData.getUserId().toString());
            repaymentApplyDto.setRepayMethod(billRepayApplyDto.getRepayMethod());
            if (date < 0) {
                repaymentApplyDto.setRepayType("PREPAYMENT");
            } else if (date == 0) {
                repaymentApplyDto.setRepayType("CURRENT");
            } else {
                repaymentApplyDto.setRepayType("OVERDUE");
            }
            // 获取银行卡信息
            BackVo backVo = userBankCardMapper.queryBackByUserId(disburseData.getUserId());
            repaymentApplyDto.setBankPhoneNo(backVo.getMobile());
            repaymentApplyDto.setAmount(safeToString(payTotalAmt));
            repaymentApplyDto.setBankCardNo(backVo.getBankAccount());
            repaymentApplyDto.setAccountCardType("1");
            repaymentApplyDto.setIdNo(backVo.getIdCard());
            repaymentApplyDto.setCustomerName(backVo.getName());
            repaymentApplyDto.setBranchName(backVo.getBankName());
            repaymentApplyDto.setBankAccountType(billRepayApplyDto.getBankAccountType());

            List<RepaymentApplyRepayDto> repaymentApplyRepayList = new ArrayList<>();
            List<RepaymentApplyDetailDto> repaymentApplyDetailList = new ArrayList<>();
            RepaymentApplyRepayDto repaymentApplyRepayDto = new RepaymentApplyRepayDto();
            repaymentApplyRepayDto.setLoanNo(disburseData.getLoanNo());
            repaymentApplyRepayDto.setRepayAmt(safeToString(payTotalAmt));
            repaymentApplyRepayDto.setRepayTerm(repaySchedule.getRepayTerm());
            repaymentApplyRepayDto.setGuarantorFee(safeToString(guarantorAmt));
            repaymentApplyRepayDto.setSplitType("1");
            repaymentApplyRepayDto.setPrinAmt(safeToString(payNormAmt));
            repaymentApplyRepayDto.setIntAmt(safeToString(payIntAmt));
            repaymentApplyRepayDto.setForfeitAmt(safeToString(payEnteAmt));
            repaymentApplyRepayDto.setServiceAmt(safeToString(serviceAmt));
            repaymentApplyRepayDto.setBreachFee(safeToString(overdueGuarantorFee));
            repaymentApplyRepayList.add(repaymentApplyRepayDto);

            RepaymentApplyDetailDto repaymentApplyDetailDto = new RepaymentApplyDetailDto();
            repaymentApplyDetailDto.setRepayterm(repaySchedule.getRepayTerm());
            repaymentApplyDetailDto.setPrintAmt(safeToString(payNormAmt));
            repaymentApplyDetailDto.setIntAmt(safeToString(payIntAmt));
            repaymentApplyDetailDto.setForfeitAmt(safeToString(payEnteAmt));
            repaymentApplyDetailDto.setGuarantorFee(safeToString(guarantorAmt));
            repaymentApplyDetailDto.setServiceAmt(safeToString(serviceAmt));
            repaymentApplyDetailDto.setBreachFee(safeToString(overdueGuarantorFee));
            repaymentApplyDetailList.add(repaymentApplyDetailDto);

            repaymentApplyRepayDto.setRepayDetailList(repaymentApplyDetailList);
            repaymentApplyDto.setRepayList(repaymentApplyRepayList);
            if (ZiFangBeanConstant.FENZHUAN.equals(capitalData.getBeanName())) {
                // 查询绑卡信息
                UserRegisterResult userRegisterResult = userRegisterResultMapper
                        .queryByLoanOrderNo(disburseData.getCreditNo());
                if (ObjectUtil.isEmpty(userRegisterResult)) {
                    return ResponseResult.error(ErrorCodeEnum.FAIL, "用户未进件");
                }
                // 设置绑卡id
                repaymentApplyDto.setBindId(userRegisterResult.getBindId());
                RepaymentApplyRepayDto repayDto = repaymentApplyDto.getRepayList().get(0);
                // 设置为新资方的借款单号
                repayDto.setLoanNo(disburseData.getCreditNo());
                repaymentApplyDto.setBillPlanIds(Collections.singletonList(repaySchedule.getPlanId()));
            }
            ResponseResult<RepaymentApplyVo> repaymentApply = repaymentApi.getRepaymentApply(repaymentApplyDto);
            if (repaymentApply.getData().getResponseCode().equals("0000")) {
                RepaySchedule repayScheduleUp = new RepaySchedule();
                repayScheduleUp.setId(repaySchedule.getId());
                repayScheduleUp.setSettleFlag(SettleFlagConstant.REPAYING);
                repayScheduleUp.setRepayMethod(billRepayApplyDto.getRepayMethod());
                repayScheduleMapper.updateById(repayScheduleUp);

                return ResponseResult.success();
            } else {
                return ResponseResult.error(ErrorCodeEnum.FAIL);
            }
        } else {
            return ResponseResult.error(ErrorCodeEnum.FAIL);
        }
    }

    /**
     * 安全地将 Number (例如 BigDecimal) 转换为字符串，如果为 null 则返回 "0"。
     * 
     * @param number 数字对象
     * @return 字符串表示形式，或 "0" (如果输入为 null)
     */
    private String safeToString(Number number) {
        // 如果需要更精确的 "0.00" 或其他格式，可以在这里调整
        return number == null ? "0" : number.toString();
    }

}
