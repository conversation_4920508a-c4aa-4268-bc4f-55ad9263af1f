package com.rongchen.byh.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * api用户合同表
 * @TableName api_user_contract
 */
@TableName(value ="api_user_contract")
@Data
public class ApiUserContract implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 借款编号
     */
    private String loanNo;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 0 本息 1 赊销
     */
    private Integer type;

    /**
     * 合同信息
     */
    private String contractInfo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}