package com.rongchen.byh.app.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 用户审核状态
 * @date 2025/2/10 09:49:44
 */
public class UserAuditStatus {
    /**
     * 用户审核状态
     1-待提交初审（默认）:没有提交h5风控
     5-初审拒绝：h5风控拒绝
     10-待复审：h5风控通过，待员工审核
     15-复审拒绝：员工审核拒绝

     20-待App授信：待进入我方app授信
     25-已授信：我方app授信完成
     30-授信拒绝：我方app授信拒绝
     */
    //1-待提交初审（默认）:没有提交h5风控
    public static final Integer WAIT_SUBMIT = 1;
    //5-初审拒绝：h5风控拒绝
    public static final Integer FIRST_REJECT = 5;
    //10-待复审：h5风控通过，待员工审核
    public static final Integer WAIT_REVIEW = 10;
    //15-复审拒绝：员工审核拒绝
    public static final Integer REVIEW_REJECT = 15;
    //20-待App授信：待进入我方app授信
    public static final Integer WAIT_APP_CREDIT = 20;
    //25-已授信：我方app授信完成
    public static final Integer GRANT_CREDIT = 25;
    //30-授信拒绝：我方app授信拒绝
    public static final Integer CREDIT_REJECT = 30;

}
