package com.rongchen.byh.app.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rongchen.byh.app.dao.DisburseDataMapper;
import com.rongchen.byh.app.entity.DisburseData;
import com.rongchen.byh.common.core.enums.DisburseDataEnum;
import com.rongchen.byh.common.core.util.MDCUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 卡单检测定时任务
 * 检测授信状态为100（授信中）或300（放款中）的记录是否存在超过半小时的情况
 */
@Component
@Slf4j
public class CreditStuckJob {

    /**
     * 卡单时间阈值（毫秒），默认30分钟
     */
    private static final long STUCK_THRESHOLD_MS = 30 * 60 * 1000;
    @Resource
    private DisburseDataMapper disburseDataMapper;

    /**
     * 卡单检测定时任务
     */
    @XxlJob("creditStuckHandler")
    public void creditStuckHandler() {
        // 设置traceId
        String traceId = IdUtil.fastSimpleUUID();
        MDCUtil.setTraceId(traceId);

        log.info("开始执行卡单检测定时任务");

        try {
            // 当前时间
            Date now = new Date();

            // 查询授信状态为100（授信中）的记录，使用创建时间判断
            List<DisburseData> creditingRecords = checkStuckRecords(
                    DisburseDataEnum.CreditStatus.CREDITING.getValue(), now, true);

            // 查询授信状态为300（放款中）的记录，使用授信通过时间判断
            List<DisburseData> loaningRecords = checkStuckRecords(
                    DisburseDataEnum.CreditStatus.LOANING.getValue(), now, false);

            // 处理卡单记录
            handleStuckRecords(creditingRecords, "授信中", now, true);
            handleStuckRecords(loaningRecords, "放款中", now, false);

            log.info("卡单检测定时任务执行完成");
        } catch (Exception e) {
            log.error("卡单检测定时任务执行异常", e);
        }
    }

    /**
     * 查询指定状态的卡单记录
     *
     * @param status 授信状态
     * @param now 当前时间
     * @param useCreateTime 是否使用创建时间判断，true表示使用创建时间，false表示使用授信通过时间
     * @return 卡单记录列表
     */
    private List<DisburseData> checkStuckRecords(int status, Date now, boolean useCreateTime) {
        LambdaQueryWrapper<DisburseData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DisburseData::getCreditStatus, status);
        List<DisburseData> records = disburseDataMapper.selectList(queryWrapper);

        if (CollUtil.isEmpty(records)) {
            log.info("未找到授信状态为{}的记录", status);
            return CollUtil.newArrayList();
        }

        // 筛选出超过半小时的记录
        return records.stream()
                .filter(record -> {
                    Date timeToCheck = useCreateTime ? record.getCreateTime() : record.getCreditTime();
                    // 如果时间为空，则跳过该记录
                    if (timeToCheck == null) {
                        return false;
                    }
                    long diffMs = now.getTime() - timeToCheck.getTime();
                    return diffMs >= STUCK_THRESHOLD_MS;
                })
                .collect(Collectors.toList());
    }

    /**
     * 处理卡单记录
     *
     * @param stuckRecords 卡单记录列表
     * @param statusDesc 状态描述
     * @param now 当前时间
     * @param useCreateTime 是否使用创建时间判断，true表示使用创建时间，false表示使用授信通过时间
     */
    private void handleStuckRecords(List<DisburseData> stuckRecords, String statusDesc, Date now, boolean useCreateTime) {
        if (CollUtil.isEmpty(stuckRecords)) {
            log.info("未发现{}状态的卡单记录", statusDesc);
            return;
        }

        log.warn("发现{}条{}状态的卡单记录", stuckRecords.size(), statusDesc);

        // 构建告警消息
        String timeFieldDesc = useCreateTime ? "创建时间" : "授信通过时间";
        String message = String.format("发现%d条%s状态的卡单记录，这些记录的%s已超过30分钟未更新状态，请及时处理！",
                stuckRecords.size(), statusDesc, timeFieldDesc);

        // 格式化卡单记录信息
        String recordsInfo = stuckRecords.stream()
                .map(record -> {
                    Date timeToShow = useCreateTime ? record.getCreateTime() : record.getCreditTime();
                    return String.format("ID: %d, 用户ID: %d, 授信流水号: %s, 授信状态: %d, %s: %s",
                            record.getId(), record.getUserId(), record.getCreditNo(),
                            record.getCreditStatus(), timeFieldDesc, timeToShow);
                })
                .collect(Collectors.joining("\n"));

        // 直接使用log.error记录告警信息
        log.error("【卡单告警】{}\n卡单记录详情：\n{}", message, recordsInfo);
    }
}
