package com.rongchen.byh.app.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**

 * 项目名称：byh_java
 * 文件名称: DisburseData
 * 创建时间: 2025-04-05 14:14
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.app.entity
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */

/**
 * 支用记录
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "`disburse_data`")
public class DisburseData implements Serializable {

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    @TableField(value = "`user_id`")
    private Long userId;

    /**
     * 产品id
     */
    @TableField(value = "`product_id`")
    private Long productId;

    /**
     * 授信流水号
     */
    @TableField(value = "`credit_no`")
    private String creditNo;

    /**
     * 放款编号
     */
    @TableField(value = "`loan_no`")
    private String loanNo;

    /**
     * 赊销编号
     */
    @TableField(value = "`sale_no`")
    private String saleNo;

    /**
     * 放款金额
     */
    @TableField(value = "`credit_amount`")
    private BigDecimal creditAmount;

    /**
     * 总利息
     */
    @TableField(value = "`gross_interest`")
    private BigDecimal grossInterest;

    /**
     * 授信状态  100 授信中  200 授信失败   300 放款中  400  放款失败   500 还款中   600 已结清
     */
    @TableField(value = "`credit_status`")
    private Integer creditStatus;

    /**
     * 授信通过时间
     */
    @TableField(value = "`credit_time`")
    private Date creditTime;

    /**
     * 放款通过时间
     */
    @TableField(value = "`loan_time`")
    private Date loanTime;

    /**
     * 结清时间
     */
    @TableField(value = "`repayment_time`")
    private Date repaymentTime;

    /**
     * 放款期数 月
     */
    @TableField(value = "`periods`")
    private Integer periods;

    /**
     * 资金方编码 放款成功后写入
     */
    @TableField(value = "`fund_code`")
    private String fundCode;

    /**
     * 借款用途
     */
    @TableField(value = "`purpose_loan`")
    private String purposeLoan;

    /**
     * 年利率
     */
    @TableField(value = "`year_rete`")
    private String yearRete;

    /**
     * 还款方式   等额本息
     */
    @TableField(value = "`repayment_method`")
    private String repaymentMethod;

    /**
     * 赊销金额
     */
    @TableField(value = "`sale_repay_amount`")
    private BigDecimal saleRepayAmount;


    /**
     * 开户行
     */
    @TableField(value = "`bank_name`")
    private String bankName;

    /**
     * 创建时间
     */
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 预留手机号
     */
    @TableField(value = "`bank_phone`")
    private String bankPhone;

    /**
     * 银行卡号
     */
    @TableField(value = "`bank_account`")
    private String bankAccount;

    /**
     * 咨询费用扣款状态 0-待扣款 1-扣款成功 2-扣款失败
     */
    @TableField(value = "`consult_fee`")
    private Integer consultFee;

    /**
     * 资方授信id
     */
    @TableField(value = "`capital_record_id`")
    private Long capitalRecordId;

    /**
     * 资方id
     */
    @TableField(value = "`capital_id`")
    private Long capitalId;

    /**
     * 资方订单号
     */
    @TableField(value = "`contract_id`")
    private String contractId;

    /**
     * 行方贷款合同号
     */
    @TableField(value = "`fund_order_id`")
    private String fundOrderId;

    /**
     * 赊销订单类型
     */
    @TableField(value = "`sale_model`")
    private String saleModel;

    /**
     * 赊销供应商编码
     */
    @TableField(value = "`sale_channel`")
    private String saleChannel;

    private static final long serialVersionUID = 1L;
}