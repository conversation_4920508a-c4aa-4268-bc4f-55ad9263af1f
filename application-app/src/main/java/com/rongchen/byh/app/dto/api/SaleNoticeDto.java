package com.rongchen.byh.app.dto.api;


import lombok.Data;

@Data
public class SaleNoticeDto {

    /**
     * 请求方产品码
     */
    private String reqSysCode;

    /**
     * 产品编码
     */
    private String product;

    /**
     * 渠道来源
     */
    private String source;

    /**
     * 还款流水号
     */
    private String repayApplyNo;

    /**
     * 赊销单号
     */
    private String saleNo;

    /**
     * 还款期数，多期逗号隔开
     */
    private String repayTerm;

    /**
     * 还款金额，单位：元；
     */
    private String amt;

    /**
     * 还款方式，“01”-线上还款、“02”-线下还款
     */
    private String repayMode;

    /**
     * 还款类型，CLEAN-全部结清、OVERDUE-归还逾期、CURRENT-归还当期到期、OVER-归还到期（逾期+当期到期）、PREPAYMENT-提前还当期
     */
    private String repayType;

    /**
     * 还款状态，SUCCESS - 成功；FAIL - 失败
     */
    private String status;

    /**
     * 还款结果描述，还款失败时必填，返回失败原因
     */
    private String result;

    /**
     * 还款时间，时间格式：yyyy-MM-dd HH:mm:ss
     */
    private String repayTime;

    /**
     * 批次号，批扣还款必填
     */
    private String batchNo;

    /**
     * 申请来源，主动还款：ZDHK；批扣还款：PKHK；运营（客服）：FNYY；催收：FNCS
     */
    private String applySource;
}
