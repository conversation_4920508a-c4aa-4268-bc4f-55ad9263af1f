package com.rongchen.byh.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * @TableName user_credit_data
 */
@TableName(value ="user_credit_data")
@Data
public class UserCreditData implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 授信额度
     */
    private BigDecimal creditAmount;

    /**
     * 提现金额
     */
    private BigDecimal withdrawAmount;

    /**
     * 剩余额度
     */
    private BigDecimal residueAmount;

    /**
     * 冻结额度
     */
    private BigDecimal freezeAmount;

    /**
     * 是否过期 0 未过期  1 已过期 3空中模式冻结
     */
    private Integer statusFlag;

    /**
     * 授信有效期
     */
    private Date creditTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 来源模式 0 线上 1 线下 2 空中放款 3 线上仅注册 4 线下仅注册 5 空中仅注册
     */
    private Integer sourceMode;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}