package com.rongchen.byh.app.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class UploadFileVo {

    /**
     * 返回前端的下载url。
     */
    @Schema(description = "返回前端的下载url")
    private String downloadUri;
    /**
     * 上传文件所在路径。
     */
    @Schema(description = "上传文件所在路径")
    private String uploadPath;
    /**
     * 返回给前端的文件名。
     */
    @Schema(description = "返回给前端的文件名")
    private String filename;

    @Schema(description = "完整的文件地址")
    private String downloadUrl;
}
