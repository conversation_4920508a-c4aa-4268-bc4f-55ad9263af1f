package com.rongchen.byh.app.job;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.app.dao.RepayScheduleMapper;
import com.rongchen.byh.app.entity.RepaySchedule;
import com.rongchen.byh.common.core.util.MDCUtil;
import com.rongchen.byh.common.rabbitmq.constants.QueueConstant;
import com.rongchen.byh.common.rabbitmq.dto.RepayDto;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

/**
 * 每日查询逾期扣款的账单
 */
@Component
@Slf4j
public class AutoOverdueRepayJob {

    @Resource
    RepayScheduleMapper repayScheduleMapper;
    @Resource
    RabbitTemplate rabbitTemplate;

    @XxlJob("autoOverdueRepayJob")
    public void autoOverdueRepayJob() {

        String traceId = UUID.fastUUID().toString();
        MDCUtil.setTraceId(traceId);
        String today = DateUtil.today();

        // 查询逾期账单
        List<RepaySchedule> list = repayScheduleMapper.selectListOverdue(today);
        if (CollectionUtil.isEmpty(list)) {
            log.info("当天需要扣款逾期账单为空");
            return;
        }

        list.forEach(repaySchedule -> {
            String traceId1 = MDCUtil.generateTraceId();
            log.info("当天需要扣款逾期账单 id:{} traceId:{}",repaySchedule.getId(),traceId1);

            RepayDto repayDto = new RepayDto();
            repayDto.setDisburseId(repaySchedule.getDisburseId());
            repayDto.setRepayId(repaySchedule.getId());
            repayDto.setTraceId(traceId1);
            repayDto.setRepayType(3);
            rabbitTemplate.convertAndSend(QueueConstant.REPAY_QUEUE, JSONObject.toJSONString(repayDto));
        });


    }
}
