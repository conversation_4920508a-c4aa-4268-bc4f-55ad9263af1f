<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.dao.UserDetailMapper">

    <resultMap id="BaseResultMap" type="com.rongchen.byh.app.entity.UserDetail">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="webName" column="web_name" jdbcType="VARCHAR"/>
            <result property="webIdCard" column="web_id_card" jdbcType="VARCHAR"/>
            <result property="webTwoElements" column="web_two_elements" jdbcType="INTEGER"/>
            <result property="riskLevel" column="risk_level" jdbcType="VARCHAR"/>
            <result property="headUrl" column="head_url" jdbcType="VARCHAR"/>
            <result property="appName" column="app_name" jdbcType="VARCHAR"/>
            <result property="sex" column="sex" jdbcType="VARCHAR"/>
            <result property="nation" column="nation" jdbcType="VARCHAR"/>
            <result property="birth" column="birth" jdbcType="VARCHAR"/>
            <result property="address" column="address" jdbcType="VARCHAR"/>
            <result property="idNumber" column="id_number" jdbcType="VARCHAR"/>
            <result property="authority" column="authority" jdbcType="VARCHAR"/>
            <result property="validDate" column="valid_date" jdbcType="VARCHAR"/>
            <result property="idCardFrondUrl" column="id_card_frond_url" jdbcType="VARCHAR"/>
            <result property="idCardReverseUrl" column="id_card_reverse_url" jdbcType="VARCHAR"/>
            <result property="ocrResult" column="ocr_result" jdbcType="INTEGER"/>
            <result property="faceUrl" column="face_url" jdbcType="VARCHAR"/>
            <result property="faceScore" column="face_score" jdbcType="VARCHAR"/>
            <result property="faceConfidence" column="face_confidence" jdbcType="VARCHAR"/>
            <result property="faceSource" column="face_source" jdbcType="VARCHAR"/>
            <result property="faceTime" column="face_time" jdbcType="TIMESTAMP"/>
            <result property="faceResult" column="face_result" jdbcType="INTEGER"/>
            <result property="educationLevel" column="education_level" jdbcType="VARCHAR"/>
            <result property="maritalStatus" column="marital_status" jdbcType="VARCHAR"/>
            <result property="houseStatus" column="house_status" jdbcType="VARCHAR"/>
            <result property="custAddress" column="cust_address" jdbcType="VARCHAR"/>
            <result property="custAddressProvice" column="cust_address_provice" jdbcType="VARCHAR"/>
            <result property="custAddressCity" column="cust_address_city" jdbcType="VARCHAR"/>
            <result property="custAddressCounty" column="cust_address_county" jdbcType="VARCHAR"/>
            <result property="incomeMonth" column="income_month" jdbcType="VARCHAR"/>
            <result property="relationshipOne" column="relationship_one" jdbcType="VARCHAR"/>
            <result property="emergencyNameOne" column="emergency_name_one" jdbcType="VARCHAR"/>
            <result property="emergencyMobileOne" column="emergency_mobile_one" jdbcType="VARCHAR"/>
            <result property="relationshipTwo" column="relationship_two" jdbcType="VARCHAR"/>
            <result property="emergencyNameTwo" column="emergency_name_two" jdbcType="VARCHAR"/>
            <result property="emergencyMobileTwo" column="emergency_mobile_two" jdbcType="VARCHAR"/>
            <result property="formFlag" column="form_flag" jdbcType="INTEGER"/>
            <result property="formTime" column="form_time" jdbcType="TIMESTAMP"/>
            <result property="phoneNumber" column="phone_number" jdbcType="VARCHAR"/>
            <result property="mobileStartTime" column="mobile_start_time" jdbcType="TIMESTAMP"/>
            <result property="wifiSensitive" column="wifi_sensitive" jdbcType="VARCHAR"/>
            <result property="addressBookNum" column="address_book_num" jdbcType="INTEGER"/>
            <result property="addressBookMobileNum" column="address_book_mobile_num" jdbcType="INTEGER"/>
            <result property="addressBookSensitive" column="address_book_sensitive" jdbcType="VARCHAR"/>
            <result property="contactOperator" column="contact_operator" jdbcType="VARCHAR"/>
            <result property="overdueMessageNum" column="overdue_message_num" jdbcType="INTEGER"/>
            <result property="longitude" column="longitude" jdbcType="VARCHAR"/>
            <result property="latitude" column="latitude" jdbcType="VARCHAR"/>
            <result property="deviceBrand" column="device_brand" jdbcType="VARCHAR"/>
            <result property="networkType" column="network_type" jdbcType="VARCHAR"/>
            <result property="devAlias" column="dev_alias" jdbcType="VARCHAR"/>
            <result property="deviceId" column="device_id" jdbcType="INTEGER"/>
            <result property="clientIp" column="client_ip" jdbcType="VARCHAR"/>
            <result property="coordinateType" column="coordinate_type" jdbcType="VARCHAR"/>
            <result property="gpsCity" column="gps_city" jdbcType="VARCHAR"/>
            <result property="lbsAddress" column="lbs_address" jdbcType="VARCHAR"/>
            <result property="gpsAddress" column="gps_address" jdbcType="VARCHAR"/>
            <result property="os" column="os" jdbcType="VARCHAR"/>
            <result property="osVersion" column="os_version" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="companyName" column="company_name" jdbcType="VARCHAR"/>
            <result property="professional" column="professional" jdbcType="INTEGER"/>
    </resultMap>


    <resultMap id="UserDetailDtoResultMap" type="com.rongchen.byh.app.v2.dto.UserDetailDto">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="webName" column="web_name" jdbcType="VARCHAR"/>
        <result property="webIdCard" column="web_id_card" jdbcType="VARCHAR"/>
        <result property="webTwoElements" column="web_two_elements" jdbcType="INTEGER"/>
        <result property="riskLevel" column="risk_level" jdbcType="VARCHAR"/>
        <result property="headUrl" column="head_url" jdbcType="VARCHAR"/>
        <result property="appName" column="app_name" jdbcType="VARCHAR"/>
        <result property="sex" column="sex" jdbcType="VARCHAR"/>
        <result property="nation" column="nation" jdbcType="VARCHAR"/>
        <result property="birth" column="birth" jdbcType="VARCHAR"/>
        <result property="address" column="address" jdbcType="VARCHAR"/>
        <result property="idNumber" column="id_number" jdbcType="VARCHAR"/>
        <result property="authority" column="authority" jdbcType="VARCHAR"/>
        <result property="validDate" column="valid_date" jdbcType="VARCHAR"/>
        <result property="idCardFrondUrl" column="id_card_frond_url" jdbcType="VARCHAR"/>
        <result property="idCardReverseUrl" column="id_card_reverse_url" jdbcType="VARCHAR"/>
        <result property="ocrResult" column="ocr_result" jdbcType="INTEGER"/>
        <result property="faceUrl" column="face_url" jdbcType="VARCHAR"/>
        <result property="faceScore" column="face_score" jdbcType="VARCHAR"/>
        <result property="faceConfidence" column="face_confidence" jdbcType="VARCHAR"/>
        <result property="faceSource" column="face_source" jdbcType="VARCHAR"/>
        <result property="faceTime" column="face_time" jdbcType="TIMESTAMP"/>
        <result property="faceResult" column="face_result" jdbcType="INTEGER"/>
        <result property="educationLevel" column="education_level" jdbcType="VARCHAR"/>
        <result property="maritalStatus" column="marital_status" jdbcType="VARCHAR"/>
        <result property="houseStatus" column="house_status" jdbcType="VARCHAR"/>
        <result property="custAddress" column="cust_address" jdbcType="VARCHAR"/>
        <result property="custAddressProvice" column="cust_address_provice" jdbcType="VARCHAR"/>
        <result property="custAddressCity" column="cust_address_city" jdbcType="VARCHAR"/>
        <result property="custAddressCounty" column="cust_address_county" jdbcType="VARCHAR"/>
        <result property="incomeMonth" column="income_month" jdbcType="VARCHAR"/>
        <result property="relationshipOne" column="relationship_one" jdbcType="VARCHAR"/>
        <result property="emergencyNameOne" column="emergency_name_one" jdbcType="VARCHAR"/>
        <result property="emergencyMobileOne" column="emergency_mobile_one" jdbcType="VARCHAR"/>
        <result property="relationshipTwo" column="relationship_two" jdbcType="VARCHAR"/>
        <result property="emergencyNameTwo" column="emergency_name_two" jdbcType="VARCHAR"/>
        <result property="emergencyMobileTwo" column="emergency_mobile_two" jdbcType="VARCHAR"/>
        <result property="formFlag" column="form_flag" jdbcType="INTEGER"/>
        <result property="formTime" column="form_time" jdbcType="TIMESTAMP"/>
        <result property="phoneNumber" column="phone_number" jdbcType="VARCHAR"/>
        <result property="mobileStartTime" column="mobile_start_time" jdbcType="TIMESTAMP"/>
        <result property="wifiSensitive" column="wifi_sensitive" jdbcType="VARCHAR"/>
        <result property="addressBookNum" column="address_book_num" jdbcType="INTEGER"/>
        <result property="addressBookMobileNum" column="address_book_mobile_num" jdbcType="INTEGER"/>
        <result property="addressBookSensitive" column="address_book_sensitive" jdbcType="VARCHAR"/>
        <result property="contactOperator" column="contact_operator" jdbcType="VARCHAR"/>
        <result property="overdueMessageNum" column="overdue_message_num" jdbcType="INTEGER"/>
        <result property="longitude" column="longitude" jdbcType="VARCHAR"/>
        <result property="latitude" column="latitude" jdbcType="VARCHAR"/>
        <result property="deviceBrand" column="device_brand" jdbcType="VARCHAR"/>
        <result property="networkType" column="network_type" jdbcType="VARCHAR"/>
        <result property="devAlias" column="dev_alias" jdbcType="VARCHAR"/>
        <result property="deviceId" column="device_id" jdbcType="INTEGER"/>
        <result property="clientIp" column="client_ip" jdbcType="VARCHAR"/>
        <result property="coordinateType" column="coordinate_type" jdbcType="VARCHAR"/>
        <result property="gpsCity" column="gps_city" jdbcType="VARCHAR"/>
        <result property="lbsAddress" column="lbs_address" jdbcType="VARCHAR"/>
        <result property="gpsAddress" column="gps_address" jdbcType="VARCHAR"/>
        <result property="os" column="os" jdbcType="VARCHAR"/>
        <result property="osVersion" column="os_version" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="companyName" column="company_name" jdbcType="VARCHAR"/>
        <result property="professional" column="professional" jdbcType="INTEGER"/>

        <result property="educationLevel1" column="education_level1" jdbcType="VARCHAR"/>
        <result property="maritalStatus1" column="marital_status1" jdbcType="VARCHAR"/>
        <result property="houseStatus1" column="house_status1" jdbcType="VARCHAR"/>
        <result property="incomeMonth1" column="income_month1" jdbcType="VARCHAR"/>
        <result property="professional1" column="professional1" jdbcType="VARCHAR"/>
        <result property="relationshipOne1" column="relationship_one1" jdbcType="VARCHAR"/>
        <result property="relationshipTwo1" column="relationship_two1" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,web_name,
        web_id_card,web_two_elements,risk_level,
        head_url,app_name,sex,
        nation,birth,address,
        id_number,authority,valid_date,
        id_card_frond_url,id_card_reverse_url,ocr_result,
        face_url,face_score,face_confidence,
        face_source,face_time,face_result,
        education_level,marital_status,house_status,
        cust_address,cust_address_provice,cust_address_city,
        cust_address_county,income_month,relationship_one,
        emergency_name_one,emergency_mobile_one,relationship_two,
        emergency_name_two,emergency_mobile_two,form_flag,form_time,phone_number,
        mobile_start_time,wifi_sensitive,address_book_num,
        address_book_mobile_num,address_book_sensitive,contact_operator,
        overdue_message_num,longitude,latitude,
        device_brand,network_type,dev_alias,
        device_id,client_ip,coordinate_type,
        gps_city,lbs_address,gps_address,
        os,os_version,create_time,company_name,professional
    </sql>
    <select id="queryByUserId" resultMap="BaseResultMap">
       select
            <include refid = "Base_Column_List"/>
        from user_detail
        where user_id = #{userId}
    </select>
    <select id="queryUserFormByUserId" resultType="com.rongchen.byh.app.vo.app.UserFormVo">
        select
        mobile as mobile,
        user_id as id,
        web_name as name,
        web_id_card as webIdCard,
        ud.channel_id as channelId,
        education_level as educationLevel,
        marital_status as maritalStatus,
        house_status as houseStatus,
        cust_address as custAddress,
        cust_address_provice as custAddressProvice,
        cust_address_city as custAddressCity,
        cust_address_county as custAddressCounty,
        income_month as incomeMonth,
        relationship_one as relationshipOne,
        emergency_name_one as emergencyNameOne,
        emergency_mobile_one as emergencyMobileOne,
        relationship_two as relationshipTwo,
        emergency_name_two as emergencyNameTwo,
        emergency_mobile_two as emergencyMobileTwo,
        form_flag as formFlag,
        form_time as formTime,
        ud.create_time as createTime,
        ud2.company_name as companyName,
        ud2.professional
        from
            user_data ud
            left join user_detail ud2 on ud.id = ud2.user_id
        where ud.id = #{userId}
    </select>
    <select id="selectByLoanNo" resultType="com.rongchen.byh.app.entity.UserDetail">
        select
        <include refid = "Base_Column_List"/>
        from user_detail
        where user_id = #{userId}
    </select>
    <select id="selectByIdCard" resultType="com.rongchen.byh.app.entity.UserDetail">
        select
        <include refid = "Base_Column_List"/>
        from user_detail
        where user_id != #{userId} and (id_number = #{idNum} or web_id_card = #{idNum})
    </select>
    <select id="selectInfoByUserId" resultMap="UserDetailDtoResultMap">
        select
            id,user_id,web_name,
            web_id_card,
            app_name,sex,
            nation,birth,address,
            education_level,
            CASE education_level
                WHEN '1' THEN '大专以下'
                WHEN '2' THEN '本科'
                WHEN '3' THEN '硕士'
                WHEN '4' THEN '博士'
                WHEN '5' THEN '博士以上'
                END as education_level1,marital_status,
            CASE marital_status
                WHEN '1' THEN '未婚'
                WHEN '2' THEN '已婚'
                WHEN '3' THEN '离异'
                WHEN '4' THEN '丧偶'
                WHEN '5' THEN '其他'
                END as marital_status1,house_status,
            CASE house_status
                WHEN '1' THEN '按揭'
                WHEN '2' THEN '自置'
                WHEN '3' THEN '租房'
                WHEN '4' THEN '共有住房'
                WHEN '5' THEN '集体宿舍'
                WHEN '6' THEN '亲属楼宇'
                WHEN '7' THEN '其他'
                END as house_status1,
            cust_address,cust_address_provice,cust_address_city,
            cust_address_county,income_month,
            CASE income_month
                WHEN '1' THEN '3千以下'
                WHEN '2' THEN '3千-5千'
                WHEN '3' THEN '5千-1万'
                WHEN '4' THEN '1万-2万'
                WHEN '5' THEN '2万-3万'
                WHEN '6' THEN '3万-5万'
                WHEN '7' THEN '5万以上'
                END as income_month1,professional,
            CASE professional
                WHEN '1' THEN '上班族'
                WHEN '2' THEN '企业主'
                WHEN '3' THEN '个体户'
                WHEN '4' THEN '自由职业'
                END as professional1,relationship_one,
            CASE relationship_one
                WHEN '1' THEN '父母'
                WHEN '2' THEN '配偶'
                WHEN '3' THEN '子女'
                END as relationship_one1,
            emergency_name_one,emergency_mobile_one,relationship_two,
            CASE relationship_two
                WHEN '20' THEN '朋友'
                WHEN '30' THEN '同事'
                WHEN '10' THEN '亲属'
                WHEN '99' THEN '其他'
                END as relationship_two1,
            emergency_name_two,emergency_mobile_two,company_name
        from user_detail
        where user_id = #{userId}
    </select>
</mapper>
