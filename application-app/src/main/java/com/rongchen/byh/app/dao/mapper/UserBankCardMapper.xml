<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.dao.UserBankCardMapper">
  <resultMap id="BaseResultMap" type="com.rongchen.byh.app.entity.UserBankCard">
    <!--@mbg.generated-->
    <!--@Table `user_bank_card`-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="bank_account" jdbcType="VARCHAR" property="bankAccount" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="contract_num" jdbcType="VARCHAR" property="contractNum" />
    <result column="bao_fu_contract_num" jdbcType="VARCHAR" property="baoFuContractNum" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="consulting_mode" jdbcType="VARCHAR" property="consultingMode" />
    <result column="consulting_rate" jdbcType="VARCHAR" property="consultingRate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `id`, `user_id`, `id_card`, `bank_name`, `bank_account`, `mobile`, `contract_num`,
    `bao_fu_contract_num`, `customer_name`, `create_time`, `update_time`, `consulting_mode`,
    `consulting_rate`
  </sql>

    <update id="updateByPrimaryKey" parameterType="com.rongchen.byh.app.entity.UserBankCard">
    <!--@mbg.generated-->
    update `user_bank_card`
    set `user_id` = #{userId,jdbcType=BIGINT},
      `id_card` = #{idCard,jdbcType=VARCHAR},
      `bank_name` = #{bankName,jdbcType=VARCHAR},
      `bank_account` = #{bankAccount,jdbcType=VARCHAR},
      `mobile` = #{mobile,jdbcType=VARCHAR},
      `contract_num` = #{contractNum,jdbcType=VARCHAR},
      `bao_fu_contract_num` = #{baoFuContractNum,jdbcType=VARCHAR},
      `customer_name` = #{customerName,jdbcType=VARCHAR},
      `create_time` = #{createTime,jdbcType=TIMESTAMP},
      `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      `consulting_mode` = #{consultingMode,jdbcType=VARCHAR},
      `consulting_rate` = #{consultingRate,jdbcType=VARCHAR}
    where `id` = #{id,jdbcType=BIGINT}
  </update>

    <select id="queryBackListByUserId" resultType="com.rongchen.byh.app.dto.app.BackVo">
        select
            customer_name name,
            id_card idCard,
            bank_name bankName,
            bank_account bankAccount,
            contract_num contractNum,
            mobile
        from user_bank_card
        where user_id = #{userId}
    </select>
    <select id="queryBackByUserId" resultType="com.rongchen.byh.app.dto.app.BackVo">
        select
            customer_name name,
            id_card idCard,
            bank_name bankName,
            bank_account bankAccount,
            mobile
        from user_bank_card
        where user_id = #{userId} order by id asc limit 1
    </select>
    <select id="queryByUserId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from user_bank_card
        where user_id = #{userId} order by id asc limit 1
    </select>
</mapper>