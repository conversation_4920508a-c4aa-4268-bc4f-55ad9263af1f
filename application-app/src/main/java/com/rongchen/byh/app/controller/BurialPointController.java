package com.rongchen.byh.app.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import com.rongchen.byh.app.dto.app.BurialPointDto;
import com.rongchen.byh.app.service.BurialPointService;
import com.rongchen.byh.common.core.object.ResponseResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * @ClassName BurialPointController
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/1/8 14:22
 * @Version 1.0
 **/
@Tag(name = "埋点相关接口")
@RestController
@RequestMapping("/userApi")
public class BurialPointController {

    @Resource
    BurialPointService burialPointService;


    @Operation(summary = "新增埋点")
    @PostMapping("/burialPointAdd")
    @SaIgnore
    public ResponseResult<Void> burialPointAdd(@RequestBody BurialPointDto dto , HttpServletRequest request){
        return burialPointService.burialPointAdd(dto ,request);
    }




}
