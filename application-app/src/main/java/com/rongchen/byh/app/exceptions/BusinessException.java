package com.rongchen.byh.app.exceptions;

import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import org.springframework.stereotype.Component;


public class BusinessException extends RuntimeException {
    private ErrorCodeEnum errorCode;
    private String message;

    public BusinessException(ErrorCodeEnum errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.message = message;
    }

    public ErrorCodeEnum getErrorCode() {
        return errorCode;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setErrorCode(ErrorCodeEnum errorCode) {
        this.errorCode = errorCode;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}