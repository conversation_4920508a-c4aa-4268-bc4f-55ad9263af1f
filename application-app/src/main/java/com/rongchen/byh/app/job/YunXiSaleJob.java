package com.rongchen.byh.app.job;

import com.rongchen.byh.app.entity.SaleOrderRecord;
import com.rongchen.byh.app.service.SaleOrderRecordService;
import com.rongchen.byh.common.api.yunxi.dto.YunXiApiRspDto;
import com.rongchen.byh.common.api.yunxi.dto.req.BenefitOrderSubmitQkReqDto;
import com.rongchen.byh.common.api.yunxi.service.YunXiApiService;
import com.rongchen.byh.common.api.yunxi.vo.YunXiOrderDetailVo;
import com.rongchen.byh.common.api.zifang.dto.SaleResultDto;
import com.rongchen.byh.common.api.zifang.service.fenzhuan.FenZhuanService;
import com.rongchen.byh.common.api.zifang.vo.SaleResultVo;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.MDCUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 项目名称：byh_java
 * 文件名称: YunXiSaleJob
 * 创建时间: 2025-03-11 16:06
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.app.job
 * 文件描述: 云溪赊销订单重试任务
 * Copyright (c) 2025 byh_java All rights reserved.
 */
@Component
@Slf4j
public class YunXiSaleJob {

    private static final int MAX_RETRY_TIMES = 3;
    private static final String FAIL_STATUS = "FAIL";
    private static final String PAY_SUCCESS_STATUS = "PAY_SUCCESS";
    private static final String YUNXI_CHANNEL = "YUNXI";

    // 线程安全的日期格式化工具
    private static final ThreadLocal<SimpleDateFormat> DATE_FORMATTER = ThreadLocal
            .withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Resource
    private SaleOrderRecordService saleOrderRecordService;

    @Resource
    private FenZhuanService fenZhuanService;

    @Resource
    private YunXiApiService yunXiApiService;

    @Autowired(required = false)
    private ThreadPoolTaskExecutor taskExecutor;

    @XxlJob("yunXiOrderRetryJob")
    public void yunXiOrderRetryJob() {
        // 设置全局TraceId
        String jobTraceId = MDCUtil.generateTraceId();
        MDCUtil.put(MDCUtil.Keys.TRACE_ID, jobTraceId);
        log.info("【赊销结果重试任务】=== 开始执行 === TraceId: {}", jobTraceId);
        long startTime = System.currentTimeMillis();

        try {
            // 获取今天的开始和结束时间
            LocalDate today = LocalDate.now();
            Date startOfDay = Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant());
            Date endOfDay = Date.from(today.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant());

            // 优化查询条件
            List<SaleOrderRecord> channelsToRetry = saleOrderRecordService.lambdaQuery()
                    .eq(SaleOrderRecord::getSaleChannel, YUNXI_CHANNEL)
                    .and(wrapper -> wrapper
                            .eq(SaleOrderRecord::getOrderStatus, FAIL_STATUS)
                            .and(subWrapper -> subWrapper
                                    .isNull(SaleOrderRecord::getRetryNum)
                                    .or()
                                    .le(SaleOrderRecord::getRetryNum, MAX_RETRY_TIMES)))
                    .between(SaleOrderRecord::getCreateTime, startOfDay, endOfDay)
                    .last("ORDER BY IFNULL(retry_num, 0) ASC") // 使用IFNULL函数
                    .list();

            if (CollectionUtils.isEmpty(channelsToRetry)) {
                log.info("【赊销结果重试任务】没有待处理的订单");
                return;
            }

            log.info("【赊销结果重试任务】查询到待处理订单数量: {}", channelsToRetry.size());

            // 根据是否配置线程池选择串行或并行处理
            if (taskExecutor != null) {
                processChannelsParallel(channelsToRetry);
            } else {
                channelsToRetry.forEach(this::processChannelWithMdc);
            }

            long endTime = System.currentTimeMillis();
            log.info("【赊销结果重试任务】=== 执行完成 === 耗时: {}ms, 处理订单数: {}", (endTime - startTime), channelsToRetry.size());
        } catch (Exception e) {
            log.error("【赊销结果重试任务】执行异常", e);
        } finally {
            // 清除MDC上下文
            MDCUtil.clear();
        }
    }

    /**
     * 并行处理订单
     */
    private void processChannelsParallel(List<SaleOrderRecord> channels) {
        try {
            // 创建异步任务并等待所有任务完成
            CompletableFuture<?>[] futures = channels.stream()
                    .map(channel -> CompletableFuture.runAsync(
                            () -> processChannelWithMdc(channel),
                            taskExecutor)
                            .exceptionally(ex -> {
                                log.error("【赊销结果处理】异步处理异常 - 订单号: {}",
                                        channel.getSaleNo(), ex);
                                return null;
                            }))
                    .toArray(CompletableFuture[]::new);

            // 设置超时时间（3分钟），避免任务长时间未完成
            CompletableFuture.allOf(futures).get(3, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("【赊销结果处理】并行处理订单异常", e);
        }
    }

    /**
     * 包装MDC上下文的订单处理方法
     */
    private void processChannelWithMdc(SaleOrderRecord channel) {
        // 为每个订单设置独立的TraceId
        String orderTraceId = MDCUtil.generateTraceId();
        MDCUtil.put(MDCUtil.Keys.TRACE_ID, orderTraceId);

        try {
            processChannel(channel, orderTraceId);
        } finally {
            // 清除当前线程的MDC上下文，避免内存泄漏
            MDCUtil.clear();
        }
    }

    private void processChannel(SaleOrderRecord channel, String traceId) {
        String saleNo = channel.getSaleNo();
        String outSaleNo = channel.getOutSaleNo();
        Long retryTimes = channel.getRetryNum();

        log.info("【赊销结果处理】开始处理订单 - 赊销订单号: {}, 外部订单号: {}, 当前重试次数: {}, 订单状态: {}, 用户ID: {}, TraceId: {}",
                saleNo, outSaleNo, retryTimes, channel.getOrderStatus(), channel.getUserId(), traceId);

        try {
            // 根据渠道类型选择处理方式
            if (YUNXI_CHANNEL.equals(channel.getSaleChannel())) {
                processYunXiOrder(channel);
            } else {
                processDefaultChannel(channel);
            }
        } catch (Exception e) {
            log.error("【赊销结果处理】处理异常 - 赊销订单号: {}, 异常信息: {}", saleNo, e.getMessage(), e);
            handleRetry(channel, "处理异常: " + e.getMessage());
        }
    }

    /**
     * 处理云溪渠道订单
     */
    private void processYunXiOrder(SaleOrderRecord channel) {
        String saleNo = channel.getSaleNo();

        try {
            // 构建云溪权益订单请求
            BenefitOrderSubmitQkReqDto submitRequest = buildSubmitRequest(channel);

            log.info("【云溪订单处理】发起权益订单提交请求 - 赊销订单号: {}, 请求参数: {}", saleNo, submitRequest);

            // 发送订单提交请求
            YunXiApiRspDto<YunXiOrderDetailVo> response = yunXiApiService.submitBenefitOrderQk(submitRequest);

            if (!isSuccess(response)) {
                String errorMsg = response != null ? response.getRspMsg() : "响应数据为空";
                log.warn("【云溪订单处理】提交失败 - 赊销订单号: {}, 响应结果: {}", saleNo, response);
                handleRetry(channel, "权益订单提交失败: " + errorMsg);
                return;
            }

            YunXiOrderDetailVo orderDetail = response.getData();
            log.info("【云溪订单处理】提交成功 - 赊销订单号: {}, 订单详情: {}", saleNo, orderDetail);

            // 更新渠道记录
            updateChannelFromYunXiOrder(channel, orderDetail);

        } catch (Exception e) {
            log.error("【云溪订单处理】处理异常 - 赊销订单号: {}, 异常信息: {}", saleNo, e.getMessage(), e);
            handleRetry(channel, "云溪订单处理异常: " + e.getMessage());
        }
    }

    /**
     * 处理默认渠道订单
     */
    private void processDefaultChannel(SaleOrderRecord channel) {
        String saleNo = channel.getSaleNo();
        String outSaleNo = channel.getOutSaleNo();

        try {
            // 构建查询请求
            SaleResultDto saleResultDto = new SaleResultDto();
            saleResultDto.setSaleNo(saleNo);
            saleResultDto.setOutSaleNo(outSaleNo);

            log.info("【默认渠道处理】发起查询请求 - 赊销订单号: {}, 请求参数: {}", saleNo, saleResultDto);

            // 发送查询请求
            ResponseResult<SaleResultVo> saleResult = fenZhuanService.getSaleResult(saleResultDto);

            if (!isSuccessResult(saleResult)) {
                String errorMsg = saleResult != null ? saleResult.getErrorMessage() : "响应数据为空";
                log.warn("【默认渠道处理】查询失败 - 赊销订单号: {}, 响应结果: {}", saleNo, saleResult);
                handleRetry(channel, "查询结果失败: " + errorMsg);
                return;
            }

            SaleResultVo result = saleResult.getData();
            log.info("【默认渠道处理】查询成功 - 赊销订单号: {}, 查询结果: {}", saleNo, result);

            // 使用当前记录而不是重新查询
            SaleOrderRecord existingChannel = channel;

            log.info("【默认渠道处理】开始更新订单 - 赊销订单号: {}, 原订单状态: {}", saleNo, existingChannel.getOrderStatus());

            // 构建并更新渠道记录
            SaleOrderRecord updatedChannel = saleOrderRecordService.buildFromOrderDetail(
                    result.getYunXiOrderDetailVo(),
                    existingChannel,
                    saleNo,
                    null);

            // 更新成功，重置重试次数
            updatedChannel.setRetryNum(null);
            saleOrderRecordService.updateById(updatedChannel);

            log.info("【默认渠道处理】更新成功 - 赊销订单号: {}, 更新后状态: {}, 处理完成",
                    saleNo, updatedChannel.getOrderStatus());
        } catch (Exception e) {
            log.error("【默认渠道处理】处理异常 - 赊销订单号: {}, 异常信息: {}", saleNo, e.getMessage(), e);
            handleRetry(channel, "处理异常: " + e.getMessage());
        }
    }

    private void handleRetry(SaleOrderRecord channel, String errorMsg) {
        String saleNo = channel.getSaleNo();
        long currentRetryTimes = channel.getRetryNum() == null ? 0 : channel.getRetryNum();
        currentRetryTimes++;

        log.error("【赊销重试处理】执行失败 - 赊销订单号: {}, 外部订单号: {}, 用户ID: {}, 当前状态: {}, 重试次数: {}, 错误信息: {}",
                saleNo, channel.getOutSaleNo(), channel.getUserId(), channel.getOrderStatus(),
                currentRetryTimes, errorMsg);

        // 更新重试次数
        channel.setRetryNum(currentRetryTimes);

        if (currentRetryTimes >= MAX_RETRY_TIMES) {
            log.error("【赊销重试处理】重试次数达到上限 - 赊销订单号: {}, 最大重试次数: {}, 将发送告警",
                    saleNo, MAX_RETRY_TIMES);
            // 添加随机退避时间，避免雪崩效应
            try {
                Thread.sleep(ThreadLocalRandom.current().nextInt(100, 500));
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            sendAlarm(channel, errorMsg);
        }

        saleOrderRecordService.updateById(channel);
        log.info("【赊销重试处理】更新重试次数完成 - 赊销订单号: {}, 新重试次数: {}", saleNo, currentRetryTimes);
    }

    private void sendAlarm(SaleOrderRecord channel, String errorMsg) {
        log.error("【赊销告警通知】订单重试失败 - 赊销订单号: {}, 外部订单号: {}, 用户ID: {}, 当前状态: {}, 重试次数: {}, 错误信息: {}",
                channel.getSaleNo(), channel.getOutSaleNo(), channel.getUserId(),
                channel.getOrderStatus(), channel.getRetryNum(), errorMsg);
        // TODO: 实现告警逻辑，如发送钉钉消息、邮件等
    }

    /**
     * 构建云溪权益订单提交请求
     */
    private BenefitOrderSubmitQkReqDto buildSubmitRequest(SaleOrderRecord channel) {
        BenefitOrderSubmitQkReqDto request = new BenefitOrderSubmitQkReqDto();
        request.setCouponPackageId(channel.getCouponPackageId());
        request.setOpenId(channel.getUserId());
        request.setUserMobile(channel.getUserMobile());
        request.setPayWay(channel.getPayWay());
        request.setPaymentNo(channel.getPaymentNo());
        request.setExternalOrderNum(channel.getSaleNo());
        request.setOrderAmount(channel.getOrderAmount());
        request.setOrderPeriods(1);
        return request;
    }

    /**
     * 从云溪订单更新渠道记录
     */
    private void updateChannelFromYunXiOrder(SaleOrderRecord channel, YunXiOrderDetailVo orderDetail) {
        String saleNo = channel.getSaleNo();
        log.info("【云溪订单处理】开始更新渠道记录 - 赊销订单号: {}, 原订单状态: {}", saleNo, channel.getOrderStatus());

        try {
            // 更新订单基本信息
            channel.setOutSaleNo(orderDetail.getOrderNum()); // 权益平台订单号
            channel.setPaymentNo(orderDetail.getPaymentNo()); // 支付单号
            channel.setOrderAmount(new BigDecimal(orderDetail.getOrderAmount())); // 订单金额
            channel.setUserMobile(orderDetail.getUserMobile()); // 用户手机号
            channel.setPayWay(orderDetail.getPayWay()); // 支付方式
            channel.setCouponPackageId(orderDetail.getCouponPackageId()); // 券包号
            channel.setCouponPackageName(orderDetail.getCouponPackageName()); // 券包名

            // 更新订单状态
            channel.setOrderStatus(orderDetail.getOrderStatus()); // 订单状态
            channel.setRefundable(orderDetail.getRefundable()); // 可退状态
            channel.setReturnAmount(orderDetail.getReturnAmount()); // 退款金额

            // 更新时间信息，使用线程安全的日期解析方式
            parseAndSetDate(orderDetail.getPayTime(), channel::setPayTime);
            parseAndSetDate(orderDetail.getExpireTime(), channel::setExpireTime);
            parseAndSetDate(orderDetail.getReturnTime(), channel::setReturnTime);

            // 重置重试次数
            channel.setRetryNum(null);

            // 更新记录
            saleOrderRecordService.updateById(channel);

            log.info("【云溪订单处理】更新渠道记录成功 - 赊销订单号: {}, 更新后状态: {}, 支付状态: {}, 退款状态: {}",
                    saleNo, channel.getOrderStatus(),
                    channel.getPayTime() != null ? "已支付" : "未支付",
                    channel.getReturnTime() != null ? "已退款" : "未退款");

        } catch (Exception e) {
            log.error("【云溪订单处理】更新渠道记录异常 - 赊销订单号: {}, 异常信息: {}", saleNo, e.getMessage(), e);
            throw new RuntimeException("更新渠道记录失败: " + e.getMessage());
        }
    }

    /**
     * 线程安全地解析日期并设置
     */
    private void parseAndSetDate(String dateStr, Consumer<Date> dateSetter) {
        if (StringUtils.hasText(dateStr)) {
            try {
                // 使用Java 8的时间API解析日期
                LocalDateTime localDateTime = LocalDateTime.parse(dateStr, DATE_TIME_FORMATTER);
                Date date = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
                dateSetter.accept(date);
            } catch (Exception e) {
                // 降级到ThreadLocal的SimpleDateFormat
                try {
                    dateSetter.accept(DATE_FORMATTER.get().parse(dateStr));
                } catch (ParseException pe) {
                    log.warn("日期解析失败: {}", dateStr, pe);
                }
            }
        }
    }

    /**
     * 检查云溪API响应是否成功
     */
    private boolean isSuccess(YunXiApiRspDto<?> response) {
        return response != null && response.isSuccess() && response.getData() != null;
    }

    /**
     * 检查响应结果是否成功
     */
    private boolean isSuccessResult(ResponseResult<?> result) {
        return result != null && result.isSuccess() && result.getData() != null;
    }

    /**
     * 清理ThreadLocal资源
     */
    public void destroy() {
        DATE_FORMATTER.remove();
    }
}
