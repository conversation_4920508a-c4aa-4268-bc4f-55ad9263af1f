package com.rongchen.byh.app.mqListener.api;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.rongchen.byh.app.api.dto.credit.CreditDto;
import com.rongchen.byh.app.api.service.strategy.impl.MaYiOutApiProcess;
import com.rongchen.byh.app.constant.LoanType;
import com.rongchen.byh.app.constant.SourceMode;
import com.rongchen.byh.app.dao.ApiCreditRecordMapper;
import com.rongchen.byh.app.dao.ApiRiskFailRecordMapper;
import com.rongchen.byh.app.dao.DisburseDataMapper;
import com.rongchen.byh.app.dao.UserDataMapper;
import com.rongchen.byh.app.dao.UserDetailMapper;
import com.rongchen.byh.app.dao.UserLoanApplyMapper;
import com.rongchen.byh.app.entity.ApiCreditRecord;
import com.rongchen.byh.app.entity.ApiRiskFailRecord;
import com.rongchen.byh.app.entity.DisburseData;
import com.rongchen.byh.app.entity.UserData;
import com.rongchen.byh.app.entity.UserDetail;
import com.rongchen.byh.app.entity.UserLoanApply;
import com.rongchen.byh.app.service.NetworkFileDealService;
import com.rongchen.byh.app.utils.OssUtil;
import com.rongchen.byh.common.api.bankCredit.dto.HrzxAuthDto;
import com.rongchen.byh.common.api.beiyihua.dto.CreditPushDto;
import com.rongchen.byh.common.api.beiyihua.service.BeiYiHuaService;
import com.rongchen.byh.common.api.idCardVerify.service.IdCardVerifyService;
import com.rongchen.byh.common.api.riskControl.dto.ApiCreditPreLoanAuditDto;
import com.rongchen.byh.common.api.riskControl.dto.CrmPushDto;
import com.rongchen.byh.common.api.riskControl.service.CrmPushService;
import com.rongchen.byh.common.api.riskControl.service.RiskControlService;
import com.rongchen.byh.common.api.riskControl.vo.PreLoanAuditVo;
import com.rongchen.byh.common.api.zifang.utils.EthnicGroupUtil;
import com.rongchen.byh.common.api.zifang.utils.IdCardUtil;
import com.rongchen.byh.common.core.exception.MyRuntimeException;
import com.rongchen.byh.common.core.util.HttpUtil;
import com.rongchen.byh.common.rabbitmq.constants.QueueConstant;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 外部api授信Listener
 * @date 2025/3/20 16:17:28
 */
@Component
@Slf4j
public class MaYiCreditListener {
    @Resource
    private IdCardVerifyService idCardVerifyService;
    @Resource
    private UserDataMapper userDataMapper;
    @Resource
    private UserDetailMapper userDetailMapper;
    @Resource
    private DisburseDataMapper disburseDataMapper;
    @Resource
    private RiskControlService riskControlService;
    @Resource
    private UserLoanApplyMapper userLoanApplyMapper;
    @Resource
    private CrmPushService crmPushService;
    @Resource
    private ApiCreditRecordMapper apiCreditRecordMapper;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private MaYiOutApiProcess mayiOutApiProcess;

    @Value("${spring.profiles.active}")
    private String active;
    @Resource
    private BeiYiHuaService beiYiHuaService;
    @Resource
    private NetworkFileDealService networkFileDealService;
    @Resource
    private OssUtil ossUtil;
    @Resource
    private ApiRiskFailRecordMapper apiRiskFailRecordMapper;

    @RabbitListener(queues = QueueConstant.MAYI_API_CREDIT_QUEUE, ackMode = "MANUAL")
    public void receiver(String msg, Channel channel, Message message) {
        String traceId = UUID.fastUUID().toString();
        Map<String, String> contextMap = new HashMap<>();
        contextMap.put("traceId", traceId);
        MDC.setContextMap(contextMap);
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        log.info("【蚂蚁授信监听器】开始处理消息，RabbitMQ原始消息: {} tag: {}", msg, deliveryTag);

        // 前置校验
        JSONObject jsonObject = null;
        try {
            jsonObject = JSONObject.parseObject(msg);
        } catch (Exception e) {
            log.error("解析MQ消息失败: {}", msg, e);
            try {
                channel.basicNack(deliveryTag, false, false); // 拒绝消息，不重新入队
            } catch (IOException ioe) {
                log.error("Nack消息时发生通道异常, deliveryTag: {}", deliveryTag, ioe);
            }
            return;
        }

        CreditDto creditDto = JSONObject.parseObject(msg, CreditDto.class);
        if (ObjectUtil.isEmpty(creditDto) || ObjectUtil.isEmpty(creditDto.getCreditInfo())) {
            log.error("【蚂蚁授信监听器】creditDto 或 creditInfo 为空，消息格式错误，拒绝消息");
            try {
                channel.basicNack(deliveryTag, false, false);
            } catch (IOException ioe) {
                log.error("Nack消息时发生通道异常, deliveryTag: {}", deliveryTag, ioe);
            }
            return;
        }
        CreditDto.CreditInfo creditInfo = creditDto.getCreditInfo();
        Long innerUserId = 0L;
        String creditNo = creditInfo.getCreditNo();
        if (StrUtil.isEmpty(creditNo)) {
            log.error("【蚂蚁授信监听器】creditNo 为空，拒绝消息");
            try {
                channel.basicNack(deliveryTag, false, false);
            } catch (IOException ioe) {
                log.error("Nack消息时发生通道异常, deliveryTag: {}", deliveryTag, ioe);
            }
            return;
        }
        RLock lock = redissonClient.getLock("api_credit:" + creditNo);
        boolean isLocked = false;
        try {
            isLocked = lock.tryLock(30, TimeUnit.SECONDS);
            if (!isLocked) {
                log.error("【蚂蚁授信监听器】授信ID: {} 获取锁失败，稍后可能重试，拒绝当前消息", creditNo);
                try {
                    // 获取锁失败通常意味着有其他线程正在处理，拒绝消息让其后续重试（如果配置了死信队列等）或丢弃
                    channel.basicNack(deliveryTag, false, false);
                } catch (IOException ioe) {
                    log.error("Nack消息时发生通道异常, deliveryTag: {}", deliveryTag, ioe);
                }
                return;
            }
            ApiCreditRecord record = apiCreditRecordMapper.selectByCreditNo(creditNo);
            if (ObjectUtil.isEmpty(record)) {
                log.error("【蚂蚁授信监听器】该授信ID：{} 授信记录不存在，拒绝消息", creditNo);
                try {
                    channel.basicNack(deliveryTag, false, false); // 记录不存在，无需重试
                } catch (IOException ioe) {
                    log.error("Nack消息时发生通道异常, deliveryTag: {}", deliveryTag, ioe);
                }
                return;
            }
            // 幂等性检查：检查是否已处理
            if (record.getCreditStatus() != 1) {
                String statusDesc = "";
                switch (record.getCreditStatus()) {
                    case 2:
                        statusDesc = "授信成功";
                        break;
                    case 3:
                        statusDesc = "授信拒绝";
                        break;
                    default:
                        statusDesc = "未知状态(" + record.getCreditStatus() + ")";
                }
                log.warn("【蚂蚁授信监听器】该授信ID：{} 状态为 [{}]，非处理中，确认为重复消息或终态消息", creditNo, statusDesc);
                mayiOutApiProcess.creditResultNotify(creditNo); // 尝试通知最终结果
                try {
                    channel.basicAck(deliveryTag, false); // 确认消息，因为已经处理或处于最终状态
                } catch (IOException ioe) {
                    log.error("Ack消息时发生通道异常, deliveryTag: {}", deliveryTag, ioe);
                }
                return;
            }
            // 到这里表示 creditStatus == 1，是处理中的状态，继续执行业务逻辑

            String phoneNo = creditInfo.getPhoneNo();
            UserData userData = userDataMapper.queryByMobile(phoneNo);
            if (userData == null) {
                log.info("【蚂蚁授信监听器】手机号: {} 用户不存在，创建新用户", phoneNo);
                userData = new UserData();
                userData.setMobile(phoneNo);
                userData.setStatusFlag(1);
                userData.setAuditFlag(1);
                userData.setAuditStatus(1);
                userData.setMobileMd(DigestUtil.md5Hex(phoneNo, StandardCharsets.UTF_8.name()));
                userData.setChannelId(26L); // 假设 26 是蚂蚁渠道ID
                userData.setSourceMode(SourceMode.API_MAYI); // 来源设置为蚂蚁
                userDataMapper.insert(userData);
                log.info("【蚂蚁授信监听器】手机号: {} 新用户创建成功，用户ID: {}", phoneNo, userData.getId());
            }
            Long userId = userData.getId();
            innerUserId = userId; // 记录内部用户ID，以便异常时使用

            // 更新ApiCreditRecord中的userId (即使之前插入过，userId也可能为空)
            ApiCreditRecord apiCreditRecordUpdateUserId = new ApiCreditRecord();
            apiCreditRecordUpdateUserId.setId(record.getId());
            apiCreditRecordUpdateUserId.setUserId(userId);
            apiCreditRecordMapper.updateById(apiCreditRecordUpdateUserId); // 更新userId

            UserDetail userDetail = userDetailMapper.queryByUserId(userId);
            // 老用户检查 - 已填表单
            if (ObjectUtil.isNotEmpty(userDetail) && 1 == userDetail.getFormFlag()) {
                log.warn("【蚂蚁授信监听器】该手机号：{}，授信ID：{} 关联的用户已填写表单，视为老用户拒绝", phoneNo, creditNo);
                ApiCreditRecord apiCreditRecord = new ApiCreditRecord();
                apiCreditRecord.setId(record.getId()); // 使用查询到的记录ID
                apiCreditRecord.setCreditStatus(3);
                apiCreditRecord.setFailReason("老用户-已填表单");
                apiCreditRecordMapper.updateById(apiCreditRecord);
                mayiOutApiProcess.creditResultNotify(creditNo);
                try {
                    channel.basicAck(deliveryTag, false); // 确认消息，业务已处理（拒绝）
                } catch (IOException ioe) {
                    log.error("Ack消息时发生通道异常, deliveryTag: {}", deliveryTag, ioe);
                }
                return;
            }
            // 老用户检查 - 已支用
            DisburseData disburseData = disburseDataMapper.selectByUserId(userId);
            if (ObjectUtil.isNotEmpty(disburseData)) {
                log.warn("【蚂蚁授信监听器】该手机号：{}，授信ID：{} 关联的用户已存在支用记录，视为老用户拒绝", phoneNo, creditNo);
                ApiCreditRecord apiCreditRecord = new ApiCreditRecord();
                apiCreditRecord.setId(record.getId()); // 使用查询到的记录ID
                apiCreditRecord.setCreditStatus(3);
                apiCreditRecord.setFailReason("老用户-已支用");
                apiCreditRecordMapper.updateById(apiCreditRecord);
                mayiOutApiProcess.creditResultNotify(creditNo);
                try {
                    channel.basicAck(deliveryTag, false); // 确认消息，业务已处理（拒绝）
                } catch (IOException ioe) {
                    log.error("Ack消息时发生通道异常, deliveryTag: {}", deliveryTag, ioe);
                }
                return;
            }

            // 三要素校验
            String userName = creditInfo.getCustName();
            String idNo = creditInfo.getIdNo();
            // if ("prod".equals(active)) {
            // log.info("【蚂蚁授信监听器】生产环境，开始对手机号：{}，授信ID：{} 进行三要素校验", phoneNo, creditNo);
            // VerifyVo verifyVo = idCardVerifyService.verifyThree(idNo, userName, phoneNo);
            // if (verifyVo.getCode() != 1) {
            // log.error("【蚂蚁授信监听器】手机号：{}，授信ID：{} 三要素校验失败: {}", phoneNo, creditNo,
            // verifyVo.getMsg());
            // ApiCreditRecord apiCreditRecord = new ApiCreditRecord();
            // apiCreditRecord.setId(record.getId()); // 使用查询到的记录ID
            // apiCreditRecord.setCreditStatus(3);
            // apiCreditRecord.setFailReason("三要素校验失败");
            // apiCreditRecordMapper.updateById(apiCreditRecord);
            // mayiOutApiProcess.creditResultNotify(creditNo);
            // try {
            // channel.basicAck(deliveryTag, false); // 确认消息，业务已处理（拒绝）
            // } catch (IOException ioe) {
            // log.error("Ack消息时发生通道异常, deliveryTag: {}", deliveryTag, ioe);
            // }
            // return;
            // }
            // log.info("【蚂蚁授信监听器】手机号：{}，授信ID：{} 三要素校验成功", phoneNo, creditNo);
            // } else {
            // log.info("【蚂蚁授信监听器】非生产环境，跳过三要素校验");
            // }

            // 记录或更新用户详情
            if (ObjectUtil.isNotEmpty(userDetail)) {
                log.info("【蚂蚁授信监听器】用户ID: {} 的详情已存在，根据新信息更新旧详情。", userId);
                try {
                    userDetail = updateUserDetail(creditInfo, phoneNo, userId, userName, idNo, userDetail);
                } catch (Exception e) {
                    log.error("【蚂蚁授信监听器】用户ID: {} 用户详情更新失败", userId, e);
                }
                log.info("【蚂蚁授信监听器】用户ID: {} 用户详情更新成功。", userId);
            } else {
                log.info("【蚂蚁授信监听器】用户ID: {} 的详情不存在，开始添加用户详情。", userId);
                userDetail = addUserDetail(creditInfo, phoneNo, userId, userName, idNo);
                log.info("【蚂蚁授信监听器】用户ID: {} 用户详情添加成功。", userId);
            }

            // 文件处理
            log.info("【蚂蚁授信监听器】开始处理授信ID: {} 的文件列表", creditNo);
            List<CreditDto.FileInfo> fileList = creditInfo.getFileList();
            Optional<CreditDto.FileInfo> caFileOpt = fileList.stream()
                    .filter(item -> "个人征信查询报送授权书（蚂蚁智慧）".equals(item.getFileName())).findFirst();
            Optional<CreditDto.FileInfo> otherFileOpt = fileList.stream()
                    .filter(item -> "委托担保申请书（蚂蚁智慧）".equals(item.getFileName())).findFirst();

            if (!caFileOpt.isPresent()) {
                log.error("【蚂蚁授信监听器】授信ID: {} 缺少必要的征信授权书文件，处理失败", creditNo);
                throw new MyRuntimeException("缺少个人征信查询报送授权书（蚂蚁智慧）文件"); // 抛出异常由catch块处理
            }
            if (!otherFileOpt.isPresent()) {
                log.error("【蚂蚁授信监听器】授信ID: {} 缺少必要的委托担保申请书文件，处理失败", creditNo);
                throw new MyRuntimeException("缺少委托担保申请书（蚂蚁智慧）文件"); // 抛出异常由catch块处理
            }

            CreditDto.FileInfo caFileInfo = caFileOpt.get();
            CreditDto.FileInfo otherFileInfo = otherFileOpt.get();
            log.info("【蚂蚁授信监听器】授信ID: {} 文件定位成功，准备构建风控参数 caFileUrl: {}, otherFileUrl: {}", creditNo,
                    caFileInfo.getFileUrl(), otherFileInfo.getFileUrl());

            // 组装风控参数
            ApiCreditPreLoanAuditDto apiCreditPreLoanAuditDto = buildRiskParam(phoneNo, userDetail,
                    caFileInfo.getFileUrl(), otherFileInfo.getFileUrl());
            log.info("【蚂蚁授信监听器】授信ID: {} 风控参数构建完成，CreditId: {}", creditNo, apiCreditPreLoanAuditDto.getCredit_id());

            // 记录申请
            UserLoanApply apply = new UserLoanApply();
            apply.setUserId(userId);
            apply.setApplyType(LoanType.CHECK); // 假设 CHECK 是授信申请类型
            apply.setCreditId(apiCreditPreLoanAuditDto.getCredit_id());
            apply.setApiCreditNo(creditNo);
            apply.setOnlineType(SourceMode.API_MAYI); // 标记来源为蚂蚁API
            userLoanApplyMapper.insert(apply);
            log.info("【蚂蚁授信监听器】授信ID: {}，用户ID: {} 的授信申请记录已插入，ApplyId: {}", creditNo, userId, apply.getId());

            // 调用风控
            log.info("【蚂蚁授信监听器】开始调用风控接口进行预授信审核，授信ID: {}", creditNo);
            try {
                PreLoanAuditVo preLoanAuditVo = riskControlService.apiCreditPreLoanAudit(apiCreditPreLoanAuditDto);
                if (preLoanAuditVo.getResult() != 1) {
                    saveRiskError(record, apply, apiCreditPreLoanAuditDto);
                }
            } catch (Exception e) {
                saveRiskError(record, apply, apiCreditPreLoanAuditDto);
                log.error("【蚂蚁授信监听器】风控接口调用异常，授信ID: {}", creditNo, e);
            }

            log.info("【蚂蚁授信监听器】风控接口调用完成，授信ID: {}", creditNo); // 注意：这里只是调用完成，不代表结果

            // 更新授信记录状态为处理中（理论上它已经是1，但为了流程明确可以再更新一次或省略）
            // 当前业务逻辑是在最终回调时更新状态，这里保持不变，仅认为发起风控调用成功
            // 如果需要在这里就标记一个状态，可以增加一个字段，或者就依赖 creditStatus=1

            // ApiCreditRecord apiCreditRecord = new ApiCreditRecord();
            // apiCreditRecord.setId(record.getId());
            // apiCreditRecord.setCreditStatus(1); // 保持处理中
            // apiCreditRecord.setFailReason("已提交风控审核"); // 更新原因
            // apiCreditRecordMapper.updateById(apiCreditRecord);
            // log.info("【蚂蚁授信监听器】手机号：{}，授信ID：{} 已成功提交风控，状态更新为处理中", phoneNo, creditNo);

            // 确认消息 - 因为核心流程（到调用风控）已成功触发
            try {
                channel.basicAck(deliveryTag, false);
                log.info("【蚂蚁授信监听器】授信ID: {} 核心处理流程完成，消息已确认 (ACK)", creditNo);
            } catch (IOException ioe) {
                log.error("Ack消息时发生通道异常, deliveryTag: {}, 授信ID: {}", deliveryTag, creditNo, ioe);
                // 如果 Ack 失败，可能导致消息重发，需要下游有幂等保证
            }

        } catch (Exception e) {
            log.error("【蚂蚁授信监听器】处理授信ID: {} 时发生异常", creditNo, e);
            // 记录异常状态到数据库
            ApiCreditRecord apiCreditRecord = new ApiCreditRecord();
            apiCreditRecord.setCreditNo(creditNo); // 使用 creditNo 更新，以防 record ID 未获取到
            if (innerUserId != 0L) { // 尝试关联用户ID
                apiCreditRecord.setUserId(innerUserId);
            }
            if (StrUtil.isNotEmpty(creditInfo.getUserId())) { // 记录外部用户ID
                apiCreditRecord.setOutUserId(creditInfo.getUserId());
            }
            apiCreditRecord.setCreditStatus(3); // 标记为失败
            apiCreditRecord.setFailReason("系统内部异常"); // 设置失败原因
            // 尝试根据 creditNo 更新记录，因为 record 可能在异常前未完全加载或ID未知
            int updatedRows = apiCreditRecordMapper.updateByCreditNo(apiCreditRecord);
            if (updatedRows == 0) {
                log.error("【蚂蚁授信监听器】尝试根据 CreditNo: {} 更新异常状态失败，可能记录不存在或无匹配", creditNo);
                // 如果需要，可以尝试插入一条失败记录，但这可能与幂等性冲突，需谨慎
            } else {
                log.info("【蚂蚁授信监听器】授信ID: {} 因异常，状态已更新为失败", creditNo);
            }

            // 尝试通知外部系统处理失败
            try {
                mayiOutApiProcess.creditResultNotify(creditNo);
                log.info("【蚂蚁授信监听器】已尝试通知外部系统处理失败，授信ID: {}", creditNo);
            } catch (Exception notifyEx) {
                log.error("【蚂蚁授信监听器】通知外部系统失败时发生异常，授信ID: {}", creditNo, notifyEx);
            }

            // 拒绝消息，不重新入队
            try {
                channel.basicNack(deliveryTag, false, false);
                log.warn("【蚂蚁授信监听器】授信ID: {} 因处理异常，消息已拒绝 (NACK)，不重新入队", creditNo);
            } catch (IOException ioe) {
                log.error("Nack消息时发生通道异常, deliveryTag: {}, 授信ID: {}", deliveryTag, creditNo, ioe);
            }
        } finally {
            if (isLocked) {
                try {
                    lock.unlock();
                    log.debug("【蚂蚁授信监听器】授信ID: {} 的锁已释放", creditNo);
                } catch (Exception unlockEx) {
                    log.error("【蚂蚁授信监听器】释放授信ID: {} 的锁时发生异常", creditNo, unlockEx);
                }
            }
            MDC.clear(); // 清理MDC
        }
    }

    private void saveRiskError(ApiCreditRecord apiCreditRecord, UserLoanApply apply,
            ApiCreditPreLoanAuditDto apiCreditPreLoanAuditDto) {
        try {
            ApiRiskFailRecord record = new ApiRiskFailRecord();
            record.setApiId(apiCreditRecord.getId());
            record.setApplyId(apply.getId());
            record.setUserId(apiCreditRecord.getUserId());
            record.setCreditNo(apiCreditRecord.getCreditNo());
            record.setCreditId(apiCreditPreLoanAuditDto.getCredit_id());
            record.setRequestParam(JSONObject.toJSONString(apiCreditPreLoanAuditDto));
            record.setCreateTime(DateUtil.date());
            apiRiskFailRecordMapper.insert(record);
        } catch (Exception e) {
            log.error("【蚂蚁授信监听器】风控接口调用失败记录保存异常", e);
        }

    }

    private void pushCrm(UserData userData, String userName, String idNo, PreLoanAuditVo preLoanAuditVo) {
        CrmPushDto crmPushDto = new CrmPushDto();
        crmPushDto.setMobile(userData.getMobile());
        crmPushDto.setUserName(userName);
        crmPushDto.setIdNumber(idNo);
        crmPushDto.setCreditRating(preLoanAuditVo.getCreditRating());
        crmPushDto.setLimitAmount(
                ObjectUtil.isEmpty(preLoanAuditVo.getAmount()) ? BigDecimal.ZERO : preLoanAuditVo.getAmount());
        crmPushService.push(crmPushDto);
    }

    private void pushProduct(UserData userData, UserDetail userDetail, PreLoanAuditVo preLoanAuditVo) {
        CreditPushDto creditPushDto = new CreditPushDto();
        creditPushDto.setUsername(userDetail.getAppName());
        creditPushDto.setIdCard(userDetail.getWebIdCard());
        creditPushDto.setMobile(userData.getMobile());
        creditPushDto.setCreditAmount(
                ObjectUtil.isEmpty(preLoanAuditVo.getAmount()) ? BigDecimal.ZERO : preLoanAuditVo.getAmount());
        beiYiHuaService.push(creditPushDto);
    }

    public String getDownloadDirPath(String dirPath) {
        String date = DateUtil.format(new Date(), "yyyyMM/dd");
        return DIR_PATH + "/" + date + "/" + dirPath;
    }

    public static final String DIR_PATH = "/usr/local/file";

    @NotNull
    private ApiCreditPreLoanAuditDto buildRiskParam(String phoneNo, UserDetail userDetail, String caFileUrl,
            String otherFileUrl) {
        log.info("【蚂蚁-构建风控参数】开始构建，用户ID: {}, 手机号: {}", userDetail.getUserId(), phoneNo);
        if (ObjectUtil.isEmpty(caFileUrl)) {
            log.error("【蚂蚁-构建风控参数】征信报告文件URL为空，用户ID: {}", userDetail.getUserId());
            throw new MyRuntimeException("征信报告文件URL不能为空");
        }
        if (ObjectUtil.isEmpty(otherFileUrl)) {
            log.error("【蚂蚁-构建风控参数】委托授权协议文件URL为空，用户ID: {}", userDetail.getUserId());
            throw new MyRuntimeException("委托授权协议文件URL不能为空");
        }
        // 处理人脸照片等信息
        HrzxAuthDto hrzxAuthDto = new HrzxAuthDto();
        log.info("【蚂蚁-构建风控参数】开始处理人脸图像信息，用户ID: {}, 身份证正面URL: {}, 身份证反面URL: {}, 人脸URL: {}",
                userDetail.getUserId(), userDetail.getIdCardFrondUrl(), userDetail.getIdCardReverseUrl(),
                userDetail.getFaceUrl());

        if (networkFileDealService.dealFaceImage(userDetail, hrzxAuthDto)) {
            log.info("【蚂蚁-构建风控参数】人脸图像信息处理失败，用户ID: {}, 身份证正面文件: {}, 身份证反面文件: {}, 人脸文件: {}",
                    userDetail.getUserId(), hrzxAuthDto.getFrontIdCardFile(), hrzxAuthDto.getBackIdCardFile(),
                    hrzxAuthDto.getOtherAuthFile());
            throw new MyRuntimeException("人脸图像信息处理失败");
        }
        log.info("【蚂蚁-构建风控参数】人脸图像信息处理完成，用户ID: {}, 身份证正面文件: {}, 身份证反面文件: {}, 人脸文件: {}",
                userDetail.getUserId(), hrzxAuthDto.getFrontIdCardFile(), hrzxAuthDto.getBackIdCardFile(),
                hrzxAuthDto.getOtherAuthFile());

        ApiCreditPreLoanAuditDto creditPreLoanAuditDto = new ApiCreditPreLoanAuditDto();
        // 填充基础信息
        creditPreLoanAuditDto.setIdcard_no(userDetail.getIdNumber());
        creditPreLoanAuditDto.setMobile(phoneNo);
        creditPreLoanAuditDto.setName(userDetail.getAppName());
        creditPreLoanAuditDto.setCredit_id(IdUtil.fastSimpleUUID()); // 生成唯一授信ID
        creditPreLoanAuditDto.setCredit_time(DateUtil.now()); // 授信时间
        creditPreLoanAuditDto.setNation_ocr(IdCardUtil.nationOcr(userDetail.getNation()));
        creditPreLoanAuditDto.setIdcard_address_ocr(userDetail.getAddress());
        String validDate = userDetail.getValidDate();
        if (StrUtil.isNotBlank(validDate) && validDate.contains("-")) {
            String[] split = validDate.split("-");
            if (split.length == 2) {
                creditPreLoanAuditDto.setCert_start(split[0].replace(".", "-"));
                String endDate = split[1];
                if ("长期".equals(endDate)) {
                    endDate = "2099.12.31"; // 长期处理
                }
                creditPreLoanAuditDto.setCert_end(endDate.replace(".", "-"));
            } else {
                log.warn("【蚂蚁-构建风控参数】用户ID: {} 身份证有效期格式不正确: {}", userDetail.getUserId(), validDate);
                // 可以设置默认值或抛出异常，取决于业务要求
                // creditPreLoanAuditDto.setCert_start("YYYY-MM-DD");
                // creditPreLoanAuditDto.setCert_end("YYYY-MM-DD");
            }
        } else {
            log.warn("【蚂蚁-构建风控参数】用户ID: {} 身份证有效期为空或格式不正确: {}", userDetail.getUserId(), validDate);
            // 同上，处理无效日期
        }

        // 构造 social_credit_code 的 JSON 内容
        JSONObject socialCreditCodeJson = new JSONObject();
        socialCreditCodeJson.put("requestNo", IdUtil.fastSimpleUUID()); // 请求编号
        socialCreditCodeJson.put("queryName", userDetail.getAppName()); // 查询姓名
        socialCreditCodeJson.put("queryIdNoType", "01"); // 查询证件类型，01通常代表身份证
        socialCreditCodeJson.put("queryIdNo", userDetail.getIdNumber()); // 查询证件号码
        socialCreditCodeJson.put("queryPhone", phoneNo); // 查询手机号
        socialCreditCodeJson.put("veriFaceTime", DateUtil.now()); // 人脸核验时间
        socialCreditCodeJson.put("authTime", DateUtil.now()); // 授权时间
        socialCreditCodeJson.put("queryDate", DateUtil.format(new Date(), "yyyyMMdd")); // 查询日期

        // --- 开始文件处理计时 ---
        long overallStartTime = System.currentTimeMillis();
        log.info("【蚂蚁-构建风控参数】开始并行处理文件，用户ID: {}, 手机号: {}", userDetail.getUserId(), phoneNo);

        // 并行处理 OSS 相关文件
        log.info("【蚂蚁-构建风控参数】开始合并身份证正反面图片，用户ID: {}, 手机号: {}", userDetail.getUserId(), phoneNo);
        CompletableFuture<String> idCardFileFuture = ossUtil.mergeTwoImage(hrzxAuthDto.getFrontIdCardFile(),
                hrzxAuthDto.getBackIdCardFile(), phoneNo);

        CompletableFuture<String> caFileOssFuture = CompletableFuture.supplyAsync(() -> {
            String localCaFilePath = null;
            long downloadStartTime = System.currentTimeMillis();
            try {
                // 从URL中提取文件名，如有必要
                String originalFileName;
                try {
                    // URL首先解码，因为FastDFS URL可能被编码。
                    String decodedUrl = URLDecoder.decode(caFileUrl, StandardCharsets.UTF_8.name());
                    originalFileName = decodedUrl.substring(decodedUrl.lastIndexOf('/') + 1);
                    // 无效/长文件名的基本理智检查和后备
                    if (StrUtil.isBlank(originalFileName) || originalFileName.length() > 200
                            || originalFileName.contains("..")) {
                        log.warn("【蚂蚁-构建风控参数】从URL提取的文件名无效或过长，将使用UUID作为文件名, URL: {}", caFileUrl);
                        originalFileName = IdUtil.fastSimpleUUID() + ".pdf";
                    }
                    // 基本消毒：替换潜在的有问题的字符
                    originalFileName = originalFileName.replaceAll("[^a-zA-Z0-9.\\-_]", "_");
                } catch (Exception e) {
                    log.warn("【蚂蚁-构建风控参数】无法从URL提取或解码文件名，将使用UUID作为文件名, URL: {}", caFileUrl, e);
                    originalFileName = IdUtil.fastSimpleUUID() + ".pdf";
                }
                final String downloadDirPath = getDownloadDirPath("caFile/" + userDetail.getUserId()); // Subdirectory
                // per user
                localCaFilePath = Paths.get(downloadDirPath, originalFileName).toString(); // Use Paths.get for safer
                // path construction
                // 下载文件
                log.info("【蚂蚁-构建风控参数】开始下载征信报告，用户ID: {}, URL: {}, 保存至: {}", userDetail.getUserId(), caFileUrl,
                        localCaFilePath);
                // 确保存在目录
                Files.createDirectories(Paths.get(downloadDirPath));
                // 使用httputil进行下载 -假设下载file获取目录和
                // 单独的文件名
                HttpUtil.downloadFile(caFileUrl, downloadDirPath, originalFileName);
                long downloadEndTime = System.currentTimeMillis();
                // 在下载尝试后检查文件是否真正存在
                if (!Files.exists(Paths.get(localCaFilePath))) {
                    log.error("【蚂蚁-构建风控参数】下载征信报告后文件未找到，耗时: {} ms，用户ID: {}, URL: {}, 本地路径: {}",
                            (downloadEndTime - downloadStartTime), userDetail.getUserId(), caFileUrl, localCaFilePath);
                    return ""; // 下载隐式失败
                }

                log.info("【蚂蚁-构建风控参数】征信报告下载成功，耗时: {} ms，用户ID: {}", (downloadEndTime - downloadStartTime),
                        userDetail.getUserId());

                // 上传下载的本地文件
                log.info("【蚂蚁-构建风控参数】开始上传本地征信报告到OSS，本地路径: {}", localCaFilePath);
                // 再次检查Ossutil方法签名 -假设上传（Local Path）为
                // 正确的
                String ossPath = ossUtil.uploadOss(localCaFilePath).join(); // 假设上传返回
                                                                            // 完整的future <string>
                if (StrUtil.isEmpty(ossPath)) {
                    log.error("【蚂蚁-构建风控参数】上传本地征信报告到OSS后返回路径为空或上传失败，本地路径: {}", localCaFilePath);
                    return ""; // 表示失败
                }
                log.info("【蚂蚁-构建风控参数】本地征信报告上传OSS成功，用户ID: {}, 本地路径: {}, OSS路径: {}", userDetail.getUserId(),
                        localCaFilePath,
                        ossPath);
                return ossPath;

            } catch (IOException e) {
                long downloadEndTime = System.currentTimeMillis();
                log.error("【蚂蚁-构建风控参数】下载征信报告时发生IO异常，耗时: {} ms，用户ID: {}, URL: {}", (downloadEndTime - downloadStartTime),
                        userDetail.getUserId(), caFileUrl, e);
                return ""; // 下载失败
            } catch (Exception e) { // 在下载或上传期间捕获其他例外
                log.error("【蚂蚁-构建风控参数】处理征信报告（下载或上传）时发生未预期异常，用户ID: {}, 本地临时路径: {}",
                        userDetail.getUserId(), (localCaFilePath != null ? localCaFilePath : "N/A"), e);
                return ""; // 其他例外
            } finally {
                // 清理临时文件
                if (localCaFilePath != null) {
                    try {
                        boolean deleted = Files.deleteIfExists(Paths.get(localCaFilePath));
                        if (deleted) {
                            log.info("【蚂蚁-构建风控参数】成功删除临时征信报告文件: {}", localCaFilePath);
                        }
                        // No警告是否不存在，因为它可能在创建之前失败或
                        // 由于允许的问题，上传后。
                    } catch (IOException e) {
                        log.error("【蚂蚁-构建风控参数】删除临时征信报告文件失败: {}", localCaFilePath, e);
                    }
                }
            }
        });

        // 处理 otherFile (委托授权书)，需要先下载再压缩上传
        final String encodedFileName;
        try {
            String originalFileName = otherFileUrl.substring(otherFileUrl.lastIndexOf("/") + 1);
            encodedFileName = java.net.URLEncoder.encode(originalFileName, StandardCharsets.UTF_8.name()).replace("+",
                    "%20");
        } catch (java.io.UnsupportedEncodingException e) {
            log.error("【蚂蚁-构建风控参数】对文件名进行URL编码失败，用户ID: {}", userDetail.getUserId(), e);
            throw new MyRuntimeException("文件名编码失败", e);
        }
        final String downloadDirPath = getDownloadDirPath("apiOtherFile/" + userDetail.getUserId()); // 按用户ID分子目录
        final String fullDownloadPath = downloadDirPath + "/" + encodedFileName;

        // 使用 CompletableFuture 包装下载和后续处理
        CompletableFuture<String> otherFileOssFuture = CompletableFuture.supplyAsync(() -> {
            long downloadStartTime = System.currentTimeMillis();
            try {
                log.info("【蚂蚁-构建风控参数】开始下载委托授权书，用户ID: {}, URL: {}, 保存至: {}", userDetail.getUserId(), otherFileUrl,
                        fullDownloadPath);
                // 使用编码后的文件名进行下载
                HttpUtil.downloadFile(otherFileUrl, downloadDirPath, encodedFileName);
                long downloadEndTime = System.currentTimeMillis();
                log.info("【蚂蚁-构建风控参数】委托授权书下载成功，耗时: {} ms，用户ID: {}", (downloadEndTime - downloadStartTime),
                        userDetail.getUserId());
                // 下载成功后，异步执行 PDF 转 Zip 并上传
                return ossUtil.pdfToZip(fullDownloadPath).join(); // join 等待内部异步完成
            } catch (IOException e) {
                long downloadEndTime = System.currentTimeMillis();
                log.error("【蚂蚁-构建风控参数】下载委托授权书失败，耗时: {} ms，用户ID: {}, URL: {}", (downloadEndTime - downloadStartTime),
                        userDetail.getUserId(), otherFileUrl, e);
                return ""; // 下载失败返回空字符串
            } catch (Exception e) {
                log.error("【蚂蚁-构建风控参数】处理委托授权书（下载后压缩上传）时发生异常，用户ID: {}", userDetail.getUserId(), e);
                return ""; // 其他异常也返回空字符串
            }
        });

        String idCardFileResult = null;
        String caFileResult = null;
        String otherFileResult = null;
        try {
            // 等待所有异步文件处理操作完成
            CompletableFuture.allOf(idCardFileFuture, caFileOssFuture, otherFileOssFuture).join();

            // --- 结束文件处理计时 ---
            long overallEndTime = System.currentTimeMillis();
            log.info("【蚂蚁-构建风控参数】所有文件并行处理完成，总耗时: {} ms，用户ID: {}, 手机号: {}",
                    (overallEndTime - overallStartTime), userDetail.getUserId(), phoneNo);

            // 获取异步结果并填充到 JSON 对象
            idCardFileResult = idCardFileFuture.join();
            caFileResult = caFileOssFuture.join();
            otherFileResult = otherFileOssFuture.join();
            log.info("【蚂蚁-构建风控参数】人脸照片OSS路径: {}、身份证正面OSS路径: {}、征信报告OSS路径: {}、委托授权书OSS路径: {}",
                    otherFileResult, idCardFileResult, caFileResult, otherFileResult);

            // 检查关键文件处理结果是否为空 (除了idCardFileResult，它现在由catch处理)
            if (StrUtil.isEmpty(caFileResult)) {
                log.error("【蚂蚁-构建风控参数】征信报告文件处理失败（上传），用户ID: {}", userDetail.getUserId());
                throw new MyRuntimeException("征信报告文件处理失败");
            }
            if (StrUtil.isEmpty(otherFileResult)) {
                log.error("【蚂蚁-构建风控参数】委托授权书文件处理失败（下载、压缩或上传），用户ID: {}", userDetail.getUserId());
                throw new MyRuntimeException("委托授权书文件处理失败");
            }

        } catch (java.util.concurrent.CompletionException e) {
            // 捕获由 CompletableFuture 抛出的异常
            Throwable cause = e.getCause(); // 获取原始异常
            log.error("【蚂蚁-构建风控参数】文件并行处理时发生错误，用户ID: {}", userDetail.getUserId(), cause);
            // 抛出包含根本原因信息的新异常
            throw new MyRuntimeException("文件处理失败，根本原因: " + (cause != null ? cause.getMessage() : "未知错误"), cause);
        } catch (Exception e) {
            // 捕获其他可能的异常，例如 join() 本身可能遇到的中断等
            log.error("【蚂蚁-构建风控参数】文件并行处理等待或结果获取时发生未预期错误，用户ID: {}", userDetail.getUserId(), e);
            throw new MyRuntimeException("文件处理时发生未预期错误: " + e.getMessage(), e);
        }

        socialCreditCodeJson.put("idCardFile", idCardFileResult); // 身份证正反面合并文件OSS路径
        socialCreditCodeJson.put("otherAuthFile", userDetail.getFaceUrl()); // 人脸照片OSS路径 (这个不是异步获取的)
        socialCreditCodeJson.put("caFile", caFileResult); // 征信报告OSS路径
        socialCreditCodeJson.put("otherFile", otherFileResult); // 委托授权书Zip包OSS路径

        // 将 JSON 对象转为 Base64 编码的字符串
        String socialCreditCodeBase64 = Base64.getEncoder()
                .encodeToString(socialCreditCodeJson.toJSONString().getBytes(StandardCharsets.UTF_8));
        JSONObject companyInfoJson = new JSONObject();
        companyInfoJson.put("social_credit_code", socialCreditCodeBase64);
        creditPreLoanAuditDto.setCompany_info(companyInfoJson); // 设置到风控 DTO
        log.info("【蚂蚁-构建风控参数】构建完成，用户ID: {}, CreditId: {},参数SocialCreditCode：{}", userDetail.getUserId(),
                creditPreLoanAuditDto.getCredit_id(), socialCreditCodeJson.toJSONString());
        return creditPreLoanAuditDto;
    }

    private UserDetail addUserDetail(CreditDto.CreditInfo creditInfo, String phoneNo, Long userId, String userName,
            String idNo) {
        log.info("【添加用户详情】开始为用户ID: {} 添加详情", userId);
        UserDetail newUserDetail = new UserDetail();
        // 填充基本信息
        newUserDetail.setUserId(userId);
        newUserDetail.setWebName(userName); // 网页端姓名？可能是外部系统传入的姓名
        newUserDetail.setWebIdCard(idNo); // 网页端身份证号？同上
        newUserDetail.setWebTwoElements(1); // 标记二要素已验证？
        newUserDetail.setAppName(userName); // App内姓名
        newUserDetail.setSex("2".equals(creditInfo.getSex()) ? "女" : "男");
        newUserDetail.setNation(EthnicGroupUtil.getEthnicGroupByCode(creditInfo.getNation())); // 民族代码转名称
        newUserDetail.setBirth(creditInfo.getBirthday().replace("-", "/")); // 生日格式转换
        newUserDetail.setAddress(creditInfo.getIdAddress()); // 身份证地址
        newUserDetail.setIdNumber(idNo); // 身份证号
        newUserDetail.setAuthority(creditInfo.getSignOrganization()); // 签发机关
        // 处理有效期，兼容"长期"
        String idValidDateBegin = creditInfo.getIdValidDateBegin().replace("-", ".");
        String idValidDateEnd = creditInfo.getIdValidDateEnd().replace("-", ".");
        if ("长期".equals(idValidDateEnd)) {
            // 如果需要存"长期"，则直接赋值，如果需要转日期，则转成一个未来很远的日期
            // newUserDetail.setValidDate(idValidDateBegin + "-长期"); // 或者
            newUserDetail.setValidDate(idValidDateBegin + "-2099.12.31");
        } else {
            newUserDetail.setValidDate(idValidDateBegin + "-" + idValidDateEnd);
        }

        // 处理图片信息
        log.debug("【添加用户详情】处理图片信息，用户ID: {}", userId);
        List<CreditDto.PictureInfo> pictureList = creditInfo.getPictureList();
        Map<String, String> pictureInfoMap = pictureList.stream()
                .collect(Collectors.toMap(CreditDto.PictureInfo::getPictureType, CreditDto.PictureInfo::getPictureUrl,
                        (v1, v2) -> v1)); // 处理重复key
        newUserDetail.setIdCardFrondUrl(pictureInfoMap.get("0")); // 身份证正面
        newUserDetail.setIdCardReverseUrl(pictureInfoMap.get("1")); // 身份证反面
        newUserDetail.setOcrResult(1); // 标记OCR成功？
        newUserDetail.setFaceUrl(pictureInfoMap.get("2")); // 人脸照片
        newUserDetail.setFaceScore(creditInfo.getFaceScore()); // 人脸识别分数
        newUserDetail.setFaceConfidence(creditInfo.getFaceScore()); // 人脸置信度？复用分数？
        newUserDetail.setFaceSource(creditInfo.getFaceSource()); // 人脸来源
        newUserDetail.setFaceTime(creditInfo.getFaceTime()); // 人脸识别时间
        newUserDetail.setFaceResult(1); // 标记人脸识别成功？

        // 补充其他信息 (许多是默认值或固定值)
        newUserDetail.setEducationLevel("0"); // 学历，默认未选择？
        newUserDetail.setMaritalStatus("0"); // 婚姻状况，默认未选择？
        newUserDetail.setHouseStatus("0"); // 住房状况，默认未选择？
        newUserDetail.setCustAddress(creditInfo.getLiveAddress()); // 居住地址
        newUserDetail.setCustAddressProvice(creditInfo.getLiveProvince()); // 省
        newUserDetail.setCustAddressCity(creditInfo.getLiveCity()); // 市
        newUserDetail.setCustAddressCounty(creditInfo.getLiveArea()); // 区/县
        newUserDetail.setIncomeMonth(creditInfo.getIncome()); // 月收入

        // 处理联系人信息
        log.debug("【添加用户详情】处理联系人信息，用户ID: {}", userId);
        List<CreditDto.ContactInfo> contactList = creditInfo.getContactList();
        if (contactList != null && !contactList.isEmpty()) {
            CreditDto.ContactInfo contactInfoFirst = contactList.get(0);
            newUserDetail.setRelationshipOne(contactInfoFirst.getContactRelation()); // 关系1
            newUserDetail.setEmergencyNameOne(contactInfoFirst.getContactName()); // 姓名1
            newUserDetail.setEmergencyMobileOne(contactInfoFirst.getContactPhoneNo()); // 手机1
            if (contactList.size() > 1) {
                CreditDto.ContactInfo contactInfoSecond = contactList.get(1);
                newUserDetail.setRelationshipTwo(contactInfoSecond.getContactRelation()); // 关系2
                newUserDetail.setEmergencyNameTwo(contactInfoSecond.getContactName()); // 姓名2
                newUserDetail.setEmergencyMobileTwo(contactInfoSecond.getContactPhoneNo()); // 手机2
            }
        } else {
            log.warn("【添加用户详情】用户ID: {} 的联系人列表为空", userId);
        }

        // 设置表单标记和时间
        newUserDetail.setFormFlag(1); // 标记表单已完成
        newUserDetail.setFormTime(DateUtil.now()); // 表单完成时间
        newUserDetail.setPhoneNumber(phoneNo); // 手机号

        // 设备和定位信息
        newUserDetail.setWifiSensitive("N"); // WiFi敏感信息？默认为N？
        newUserDetail.setLongitude(creditInfo.getLng()); // 经度
        newUserDetail.setLatitude(creditInfo.getLat()); // 纬度
        newUserDetail.setDeviceBrand(creditInfo.getDeviceBrand()); // 设备品牌
        newUserDetail.setNetworkType("wifi"); // 网络类型，这里固定为wifi？应根据实际情况？
        newUserDetail.setDevAlias(creditInfo.getDevAlias()); // 设备别名
        newUserDetail.setDeviceId(creditInfo.getDeviceId()); // 设备ID
        newUserDetail.setClientIp(creditInfo.getClientIp()); // 客户端IP
        newUserDetail.setGpsCity(creditInfo.getGpsCity()); // GPS城市
        newUserDetail.setLbsAddress(creditInfo.getLbsAddress()); // LBS地址
        newUserDetail.setGpsAddress(creditInfo.getGpsAddress()); // GPS地址
        newUserDetail.setOs(creditInfo.getOs()); // 操作系统
        newUserDetail.setOsVersion(creditInfo.getOsVersion()); // 系统版本

        // 插入数据库
        userDetailMapper.insert(newUserDetail);
        log.info("【添加用户详情】用户ID: {} 详情信息插入数据库成功", userId);
        return newUserDetail;
    }

    private UserDetail updateUserDetail(CreditDto.CreditInfo creditInfo, String phoneNo, Long userId, String userName,
            String idNo, UserDetail userDetail) {
        log.info("【更新用户详情】开始为用户ID: {} 更新详情", userId);

        // 填充基本信息 (不更新userId)
        userDetail.setWebName(userName); // 网页端姓名？可能是外部系统传入的姓名
        userDetail.setWebIdCard(idNo); // 网页端身份证号？同上
        userDetail.setWebTwoElements(1); // 标记二要素已验证？
        userDetail.setAppName(userName); // App内姓名
        userDetail.setSex("2".equals(creditInfo.getSex()) ? "女" : "男");
        userDetail.setNation(EthnicGroupUtil.getEthnicGroupByCode(creditInfo.getNation())); // 民族代码转名称
        userDetail.setBirth(creditInfo.getBirthday().replace("-", "/")); // 生日格式转换
        userDetail.setAddress(creditInfo.getIdAddress()); // 身份证地址
        userDetail.setIdNumber(idNo); // 身份证号 (如果需要更新) - 注意: 身份证号通常不应随意更改，但这里根据addUserDetail逻辑保持一致
        userDetail.setAuthority(creditInfo.getSignOrganization()); // 签发机关
        // 处理有效期，兼容"长期"
        String idValidDateBegin = creditInfo.getIdValidDateBegin().replace("-", ".");
        String idValidDateEnd = creditInfo.getIdValidDateEnd().replace("-", ".");
        if ("长期".equals(idValidDateEnd)) {
            userDetail.setValidDate(idValidDateBegin + "-2099.12.31");
        } else {
            userDetail.setValidDate(idValidDateBegin + "-" + idValidDateEnd);
        }

        // 处理图片信息
        log.info("【更新用户详情】处理图片信息，用户ID: {}", userId);
        List<CreditDto.PictureInfo> pictureList = creditInfo.getPictureList();
        Map<String, String> pictureInfoMap = pictureList.stream()
                .collect(Collectors.toMap(CreditDto.PictureInfo::getPictureType, CreditDto.PictureInfo::getPictureUrl,
                        (v1, v2) -> v1)); // 处理重复key
        userDetail.setIdCardFrondUrl(pictureInfoMap.get("0")); // 身份证正面
        userDetail.setIdCardReverseUrl(pictureInfoMap.get("1")); // 身份证反面
        userDetail.setOcrResult(1); // 标记OCR成功？
        userDetail.setFaceUrl(pictureInfoMap.get("2")); // 人脸照片
        userDetail.setFaceScore(creditInfo.getFaceScore()); // 人脸识别分数
        userDetail.setFaceConfidence(creditInfo.getFaceScore()); // 人脸置信度？复用分数？
        userDetail.setFaceSource(creditInfo.getFaceSource()); // 人脸来源
        userDetail.setFaceTime(creditInfo.getFaceTime()); // 人脸识别时间
        userDetail.setFaceResult(1); // 标记人脸识别成功？

        // 补充其他信息 (按需更新，或保持addUserDetail逻辑覆盖)
        // userDetail.setEducationLevel("0"); // 学历，如果需要更新则取消注释
        // userDetail.setMaritalStatus("0"); // 婚姻状况
        // userDetail.setHouseStatus("0"); // 住房状况
        userDetail.setCustAddress(creditInfo.getLiveAddress()); // 居住地址
        userDetail.setCustAddressProvice(creditInfo.getLiveProvince()); // 省
        userDetail.setCustAddressCity(creditInfo.getLiveCity()); // 市
        userDetail.setCustAddressCounty(creditInfo.getLiveArea()); // 区/县
        userDetail.setIncomeMonth(creditInfo.getIncome()); // 月收入

        // 处理联系人信息
        log.debug("【更新用户详情】处理联系人信息，用户ID: {}", userId);
        List<CreditDto.ContactInfo> contactList = creditInfo.getContactList();
        if (contactList != null && !contactList.isEmpty()) {
            CreditDto.ContactInfo contactInfoFirst = contactList.get(0);
            userDetail.setRelationshipOne(contactInfoFirst.getContactRelation()); // 关系1
            userDetail.setEmergencyNameOne(contactInfoFirst.getContactName()); // 姓名1
            userDetail.setEmergencyMobileOne(contactInfoFirst.getContactPhoneNo()); // 手机1
            if (contactList.size() > 1) {
                CreditDto.ContactInfo contactInfoSecond = contactList.get(1);
                userDetail.setRelationshipTwo(contactInfoSecond.getContactRelation()); // 关系2
                userDetail.setEmergencyNameTwo(contactInfoSecond.getContactName()); // 姓名2
                userDetail.setEmergencyMobileTwo(contactInfoSecond.getContactPhoneNo()); // 手机2
            } else {
                // 如果新信息只有一个联系人，是否需要清除第二个联系人信息？根据业务决定
                // userDetail.setRelationshipTwo(null);
                // userDetail.setEmergencyNameTwo(null);
                // userDetail.setEmergencyMobileTwo(null);
            }
        } else {
            log.warn("【更新用户详情】用户ID: {} 的新联系人列表为空，旧联系人信息将保留", userId);
            // 是否需要清除旧联系人信息？根据业务决定
            // userDetail.setRelationshipOne(null); ...
        }

        // 设置表单标记和时间
        userDetail.setFormFlag(1); // 标记表单已完成 (或根据业务逻辑判断是否真的完成了更新)
        userDetail.setFormTime(DateUtil.now()); // 更新表单时间
        userDetail.setPhoneNumber(phoneNo); // 更新手机号 (如果允许更新)
        
        // 设备和定位信息
        userDetail.setWifiSensitive("N"); // WiFi敏感信息？默认为N？
        userDetail.setLongitude(creditInfo.getLng()); // 经度
        userDetail.setLatitude(creditInfo.getLat()); // 纬度
        userDetail.setDeviceBrand(creditInfo.getDeviceBrand()); // 设备品牌
        userDetail.setNetworkType("wifi"); // 网络类型，这里固定为wifi？应根据实际情况？
        userDetail.setDevAlias(creditInfo.getDevAlias()); // 设备别名
        userDetail.setDeviceId(creditInfo.getDeviceId()); // 设备ID
        userDetail.setClientIp(creditInfo.getClientIp()); // 客户端IP
        userDetail.setGpsCity(creditInfo.getGpsCity()); // GPS城市
        userDetail.setLbsAddress(creditInfo.getLbsAddress()); // LBS地址
        userDetail.setGpsAddress(creditInfo.getGpsAddress()); // GPS地址
        userDetail.setOs(creditInfo.getOs()); // 操作系统
        userDetail.setOsVersion(creditInfo.getOsVersion()); // 系统版本

        // 更新数据库,如果id不存在就用user_id更新
        if (userDetail.getId() != null) {
            userDetailMapper.updateById(userDetail); 
        } else {
            //先查询userId是否存在
            UserDetail userDetailTmp = userDetailMapper.queryByUserId(userId);
            if (userDetailTmp != null) {
                userDetail.setId(userDetailTmp.getId());
                userDetailMapper.updateById(userDetail);
            } else {
                userDetailMapper.insert(userDetail);
            }
        }
        
        log.info("【更新用户详情】用户ID: {} 详情信息更新数据库成功", userId);
        return userDetail;
    }

}
