package com.rongchen.byh.app.dao;

import com.rongchen.byh.app.entity.ApiCreditRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【api_credit_record】的数据库操作Mapper
* @createDate 2025-03-20 16:04:29
* @Entity com.rongchen.byh.app.entity.ApiCreditRecord
*/
@Mapper
public interface ApiCreditRecordMapper extends BaseMapper<ApiCreditRecord> {

    ApiCreditRecord selectByCreditNo(String creditNo);

    int updateByCreditNo(ApiCreditRecord record);

    List<ApiCreditRecord> selectInProcessList(Integer hour);

    int batchFail(List<ApiCreditRecord> list);
}




