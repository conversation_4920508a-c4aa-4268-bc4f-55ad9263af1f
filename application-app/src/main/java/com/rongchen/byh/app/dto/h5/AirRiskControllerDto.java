package com.rongchen.byh.app.dto.h5;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 空中放款模式风控dto
 * @date 2025/2/21 10:22:56
 */
@Data
public class AirRiskControllerDto {
    @Schema(description = "用户姓名")
    private String userName;

    @Schema(description = "身份证号")
    private String idNumber;

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "模式 0 线上 1 线下 2 空中放款 3线上仅注册 4线下仅注册 5空中仅注册")
    private Integer type;

    private Long applyId;

    private String traceId;
}
