<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.dao.BurialPointLogMapper">

    <resultMap id="BaseResultMap" type="com.rongchen.byh.app.entity.BurialPointLog">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="ip" column="ip" jdbcType="VARCHAR"/>
            <result property="channelId" column="channel_id" jdbcType="BIGINT"/>
            <result property="productId" column="product_id" jdbcType="BIGINT"/>
            <result property="pageKey" column="page_key" jdbcType="VARCHAR"/>
            <result property="pageName" column="page_name" jdbcType="VARCHAR"/>
            <result property="buttonKey" column="button_key" jdbcType="VARCHAR"/>
            <result property="buttonName" column="button_name" jdbcType="VARCHAR"/>
            <result property="other" column="other" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,ip,
        channel_id,product_id,page_key,
        page_name,button_key,button_name,
        other,create_time
    </sql>
</mapper>
