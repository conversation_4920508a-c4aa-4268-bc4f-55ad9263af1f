package com.rongchen.byh.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 线下模式注册或者登录dto
 * @date 2025/2/7 12:21:09
 */
@Data
public class OfflineRegOrLoginDto extends YzmCodeDto{
    @Schema(description = "邀请码")
    @NotEmpty(message = "邀请码不能为空")
    private String inviteCode;

    @Schema(description = "验证码")
    @NotEmpty(message = "验证码不能为空")
    private String code;

    @Schema(description = "渠道")
    @NotEmpty(message = "渠道不能为空")
    private String channel;

}
