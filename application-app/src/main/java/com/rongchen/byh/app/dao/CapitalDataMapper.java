package com.rongchen.byh.app.dao;

import com.rongchen.byh.app.entity.CapitalData;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【capital_data(资方信息表)】的数据库操作Mapper
* @createDate 2025-02-18 16:45:04
* @Entity com.rongchen.byh.app.model.CapitalData
*/
@Mapper
public interface CapitalDataMapper extends BaseMapper<CapitalData> {
    CapitalData selectByDisburseId(@Param("capitalRecordId") Long capitalRecordId);
}




