package com.rongchen.byh.app.dto.api;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName FenZhuanCreditNoticeDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/10 16:52
 * @Version 1.0
 **/
@Data
public class FenZhuanCreditNoticeDto {

    /**
     * 进件单号
     */
    private String orderNo;

    /**
     * 审核状态
     */
    private String orderStatus;

    /**
     * 审核时间，格式：yyyy-MM-dd HH:mm:ss
     */
    private String auditTime;

    /**
     * 审批金额，单位：元。审核通过时必填
     */
    private Long auditAmount;

    /**
     * 拒绝原因，不通过时必填
     */
    private String remark;

    /**
     * 年综合费率
     */
    private BigDecimal yearRate;
}
