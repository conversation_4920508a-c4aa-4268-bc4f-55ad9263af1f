package com.rongchen.byh.app.api.dto.loan;

import lombok.Data;

import java.util.List;

/**
 * 用信息通知
 */
@Data
public class loanInfoDto {
    /**
     * 贷款编号
     */
    private String loanNo;

    /**
     * 身份证
     */
    private String idNo;

    /**
     * 授信流水号
     */
    private String creditNo;

    /**
     * 申请金额
     */
    private String applyAmount;

    /**
     * 申请期限
     */
    private String applyTerm;

    /**
     * 借款用途
     */
    private String applyUse;

    /**
     * 银行卡号
     */
    private String bankCardNum;

    /**
     * 银行预留手机号
     */
    private String bankPhone;

    /**
     * 银行编码
     */
    private String bankCode;

    /**
     * 申请时间
     */
    private String applyTime;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 合作模式
     */
    private String saleType;
    /**
     * 赊销订单列表
     */
    private List<SaleInfoDto> saleInfoList;

    /**
     * 用信结果
     */
    private loanResultInfoDto loanResultInfo;
}
