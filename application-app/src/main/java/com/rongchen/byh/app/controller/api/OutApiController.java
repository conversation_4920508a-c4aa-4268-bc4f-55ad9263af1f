package com.rongchen.byh.app.controller.api;


import cn.dev33.satoken.annotation.SaIgnore;
import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.rongchen.byh.app.dto.api.BindBankCardDto;
import com.rongchen.byh.app.dto.api.RiskCallBackDto;
import com.rongchen.byh.app.service.OutApiService;
import com.rongchen.byh.common.core.annotation.DisableDataFilter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 外部调用api接口
 * @date 2024/12/11 10:37:37
 */
@ApiSupport(order = 1)
@Tag(name = "外部调用api接口")
@DisableDataFilter
@Slf4j
@RestController
@RequestMapping("/out")
public class OutApiController {
    @Resource
    private OutApiService outApiService;

    @PostMapping("/bindBankCard")
    @Operation(summary = "绑卡接口")
    @SaIgnore
    public String bindBankCard(@RequestBody BindBankCardDto bindBankCardDto) {
        return outApiService.bindBankCard(bindBankCardDto);
    }

    @PostMapping("/riskCallBack")
    @Operation(summary = "风控回调接口")
    @SaIgnore
    public JSONObject riskCallBack(@RequestBody RiskCallBackDto riskCallBackDto) {
        return outApiService.riskCallBack(riskCallBackDto);
    }
}
