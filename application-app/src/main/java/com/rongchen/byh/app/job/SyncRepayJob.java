package com.rongchen.byh.app.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rongchen.byh.app.config.ZifangFactory;
import com.rongchen.byh.app.constant.ZiFangBeanConstant;
import com.rongchen.byh.app.dao.CapitalDataMapper;
import com.rongchen.byh.app.dao.DisburseDataMapper;
import com.rongchen.byh.app.dao.RepayScheduleMapper;
import com.rongchen.byh.app.entity.CapitalData;
import com.rongchen.byh.app.entity.DisburseData;
import com.rongchen.byh.app.entity.RepaySchedule;
import com.rongchen.byh.app.service.RepayScheduleService;
import com.rongchen.byh.app.utils.NumberUtil;
import com.rongchen.byh.common.api.zifang.dto.RepaymentPlanQueryDto;
import com.rongchen.byh.common.api.zifang.service.RepaymentApi;
import com.rongchen.byh.common.api.zifang.vo.RepaymentPlanQueryPkgVo;
import com.rongchen.byh.common.api.zifang.vo.RepaymentPlanQueryVo;
import com.rongchen.byh.common.core.constant.SettleFlagConstant;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.MDCUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 同步订单任务
 * xxl-job手动执行
 */
@Component
@Slf4j
public class SyncRepayJob {

    @Resource
    ZifangFactory zifangFactory;
    @Resource
    RepayScheduleMapper repayScheduleMapper;
    @Resource
    DisburseDataMapper disburseDataMapper;
    @Resource
    CapitalDataMapper capitalDataMapper;
    @Resource
    RepayScheduleService repayScheduleService;
    @Resource
    SyncRepayJob syncRepayJob;

    private static final String JOB_NAME = "xxl同步订单";

    /**
     * 手动执行同步订单任务
     */
    @XxlJob("syncRepayJobHandler")
    public void syncRepayJobHandler() {
        try {
            MDCUtil.setTraceId();
            // 需要更新的disburse_id，使用逗号拼接
            // 格式 1,2,3
            String param = XxlJobHelper.getJobParam();
            List<Long> list = null;
            // disburse_id 为空，查询所有订单
            if (StrUtil.isEmpty(param)) {
                list = repayScheduleMapper.selectDisburseIdAll(1);
            } else {
                List<String> split = StrUtil.split(param, ",");
                list = split.stream().map(Long::parseLong).collect(Collectors.toList());
            }
            if (CollectionUtil.isEmpty(list)) {
                log.info("【{}】未提供或查询到需要同步的 disburse_id 列表数据为空", JOB_NAME);
                return;
            }
            LambdaQueryWrapper<DisburseData> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(DisburseData::getId, list);
            List<DisburseData> disburseData = disburseDataMapper.selectList(queryWrapper);

            log.info("【{}】查询到 {} 条支用记录需要处理。", JOB_NAME, disburseData.size());
            disburseData.forEach(data -> {
                if (data.getCreditStatus() == 500) {
                    this.syncProcessV2(data);
                }
            });
            log.info("【{}】手动同步任务处理完成。", JOB_NAME);

        } catch (Exception e) {
            log.error("【{}】执行异常", JOB_NAME, e);
        } finally {
            // 清除MDC上下文
            MDCUtil.clear();
        }

    }

    private void syncProcessV2(DisburseData disburseData) {
        String loanNo = disburseData.getLoanNo();
        try {
            log.info("【{}】开始处理借款编号: {}", JOB_NAME, loanNo);

            // --- API 调用逻辑 ---
            RepaymentPlanQueryDto queryDto = new RepaymentPlanQueryDto();
            queryDto.setUserId(disburseData.getUserId() + "");
            queryDto.setLoanNo(loanNo);
            CapitalData capitalData = capitalDataMapper.selectById(disburseData.getCapitalId());
            if (ObjectUtil.isEmpty(capitalData)) {
                log.warn("【{}】借款编号 [{}] 资方未匹配 (Capital ID: {}), 跳过处理", JOB_NAME, loanNo, disburseData.getCapitalId());
                return;
            }
            if (capitalData.getBeanName().equals(ZiFangBeanConstant.FENZHUAN)) {
                queryDto.setLoanNo(disburseData.getCreditNo());
            }
            RepaymentApi repaymentApi = zifangFactory.getApi(capitalData.getBeanName(), RepaymentApi.class);
            ResponseResult<RepaymentPlanQueryVo> result = repaymentApi.getRepaymentPlanQuery(queryDto);
            if (!result.isSuccess()) {
                log.error("【{}】借款编号 [{}] 查询资方还款计划接口失败: {}", JOB_NAME, loanNo, result.getErrorMessage());
                return;
            }
            RepaymentPlanQueryVo data = result.getData();
            log.info("【{}】借款编号 [{}] 资方返回结果：{}", JOB_NAME, loanNo, data);
            if (!"0000".equals(data.getResponseCode()) || data.getPkgList() == null) {
                log.error("【{}】借款编号 [{}] 资方返回结果错误或无数据：{}", JOB_NAME, loanNo, data.getResponseMsg());
                return;
            }
            List<RepaymentPlanQueryPkgVo> pkgList = data.getPkgList();
            if (pkgList.isEmpty()) {
                log.info("【{}】借款编号 [{}] 资方返回的还款计划列表为空，无需处理。", JOB_NAME, loanNo);
                return;
            }

            /**
             * 暂时先不考虑异常情况
             * 1. 查询有多条重复账单数据
             */

            // 1. 查询现有数据库记录
            List<RepaySchedule> existingSchedulesList = repayScheduleService.lambdaQuery()
                    .eq(RepaySchedule::getDisburseId, disburseData.getId())
                    .list();

//            if (existingSchedulesList.size() < 12) {
//                log.info("【{}】借款编号 [{}] 账单提前结清，不是12期，无需处理。", JOB_NAME, loanNo);
//                return;
//            }

            // 资方返回账单数据不足12条，说明是提前结清
            int size = pkgList.size();
            if (size < 12) {
                syncRepayJob.cleanOrders(existingSchedulesList,pkgList,disburseData);
            } else {
                syncRepayJob.syncOrders(existingSchedulesList,pkgList,disburseData);
            }
            log.info("【{}】借款编号 [{}] 同步处理成功完成。", JOB_NAME, loanNo);
        } catch (Exception e) {
             log.error("【{}】借款编号 [{}] 同步处理异常", JOB_NAME, loanNo, e);
        }

    }

    /**
     * 同步订单
     * @param scheduleList
     * @param pkgList
     * @param disburseData
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncOrders(List<RepaySchedule> scheduleList, List<RepaymentPlanQueryPkgVo> pkgList,DisburseData disburseData) {
        Map<String, RepaymentPlanQueryPkgVo> collect = pkgList.stream().collect(Collectors.toMap(RepaymentPlanQueryPkgVo::getRepayTerm, V -> V));
        List<RepaySchedule> upList = new ArrayList<>(12);
        BigDecimal totalAmt = zero;
        for (RepaySchedule schedule : scheduleList) {
            String repayTerm = schedule.getRepayTerm();
            RepaymentPlanQueryPkgVo pkgVo = collect.get(repayTerm);
            RepaySchedule upData = new RepaySchedule();
            upData.setId(schedule.getId());
            upData.setTotalAmt(NumberUtil.safeParseBigDecimal(pkgVo.getTotalAmt()));
            upData.setTermRetPrin(NumberUtil.safeParseBigDecimal(pkgVo.getTermRetPrin()));
            upData.setTermRetInt(NumberUtil.safeParseBigDecimal(pkgVo.getTermRetInt()));
            this.processData(upData, pkgVo);
            upData.setTermStatus(pkgVo.getTermStatus());
            upData.setSettleFlag(pkgVo.getSettleFlag());
            if (StrUtil.isNotEmpty(pkgVo.getDatePay())) {
                upData.setPayTime(pkgVo.getDatePay());
            }
            if (StrUtil.isEmpty(schedule.getDatePayTime())) {
                upData.setDatePay(pkgVo.getDatePay());
            }
            totalAmt = totalAmt.add(upData.getTotalAmt());
            upList.add(upData);
        }

        BigDecimal subtract = totalAmt.subtract(disburseData.getCreditAmount());

        repayScheduleService.updateBatchById(upList);

        // 更新总利息数据
        DisburseData data = new DisburseData();
        data.setId(disburseData.getId());
        data.setGrossInterest(subtract);
        disburseDataMapper.updateById(data);

    }

    static BigDecimal zero = new BigDecimal("0.00");

    /**
     * 结清订单逻辑
     * @param scheduleList
     * @param pkgList
     */
    @Transactional(rollbackFor = Exception.class)
    public void cleanOrders(List<RepaySchedule> scheduleList, List<RepaymentPlanQueryPkgVo> pkgList,DisburseData disburseData) {
        Map<String, RepaymentPlanQueryPkgVo> collect = pkgList.stream().collect(Collectors.toMap(RepaymentPlanQueryPkgVo::getRepayTerm, V -> V));
        List<RepaySchedule> upList = new ArrayList<>(12);
        // 还款总金额
        BigDecimal totalAmt = zero;
        for (RepaySchedule schedule : scheduleList) {
            String repayTerm = schedule.getRepayTerm();
            RepaySchedule upData = new RepaySchedule();
            upData.setId(schedule.getId());
            if (collect.containsKey(repayTerm)) {
                RepaymentPlanQueryPkgVo pkgVo = collect.get(repayTerm);
                upData.setTotalAmt(NumberUtil.safeParseBigDecimal(pkgVo.getTotalAmt()));
                upData.setTermRetPrin(NumberUtil.safeParseBigDecimal(pkgVo.getTermRetPrin()));
                upData.setTermRetInt(NumberUtil.safeParseBigDecimal(pkgVo.getTermRetInt()));
                this.processData(upData, pkgVo);
                upData.setTermStatus(pkgVo.getTermStatus());
                upData.setSettleFlag(pkgVo.getSettleFlag());
                if (StrUtil.isNotEmpty(pkgVo.getDatePay())) {
                    upData.setPayTime(pkgVo.getDatePay());
                }
                if (StrUtil.isEmpty(schedule.getDatePayTime())) {
                    upData.setDatePay(pkgVo.getDatePay());
                }

            } else {
                upData.setTotalAmt(zero);
                upData.setTermRetPrin(zero);
                upData.setTermRetInt(zero);
                upData.setTermGuarantorFee(zero);
                upData.setTermRetFint(zero);
                upData.setTermOverdueGuarantorFee(zero);
                upData.setPrinAmt(zero);
                upData.setNoRetAmt(zero);
                upData.setIntAmt(zero);
                upData.setNoRetInt(zero);
                upData.setTermFintFinish(zero);
                upData.setNoRetFin(zero);
                upData.setGuarantorFee(zero);
                upData.setNoGuarantorFee(zero);
                upData.setTermServiceFee(zero);
                upData.setServiceFee(zero);
                upData.setNoServiceFee(zero);
                upData.setOverdueGuarantorFee(zero);
                upData.setNoOverdueGuarantorFee(zero);
                upData.setSettleFlag(SettleFlagConstant.CLOSE);
            }
            totalAmt = totalAmt.add(upData.getTotalAmt());
            upList.add(upData);
        }
        // 总利息
        BigDecimal subtract = totalAmt.subtract(disburseData.getCreditAmount());

        boolean b = repayScheduleService.updateBatchById(upList);

        DisburseData data = new DisburseData();
        data.setId(disburseData.getId());
        data.setCreditStatus(600);
        data.setGrossInterest(subtract);
        if (disburseData.getRepaymentTime() == null) {
            data.setRepaymentTime(new Date());
        }
        disburseDataMapper.updateById(data);
    }

    private void syncProcess(DisburseData disburseData) {
        String loanNo = disburseData.getLoanNo();
        log.info("【{}】开始处理借款编号: {}", JOB_NAME, loanNo);

        // --- API 调用逻辑 ---
        RepaymentPlanQueryDto queryDto = new RepaymentPlanQueryDto();
        queryDto.setUserId(disburseData.getUserId() + "");
        queryDto.setLoanNo(loanNo);
        CapitalData capitalData = capitalDataMapper.selectById(disburseData.getCapitalId());
        if (ObjectUtil.isEmpty(capitalData)) {
            log.warn("【{}】借款编号 [{}] 资方未匹配 (Capital ID: {}), 跳过处理", JOB_NAME, loanNo, disburseData.getCapitalId());
            return;
        }
        if (capitalData.getBeanName().equals(ZiFangBeanConstant.FENZHUAN)) {
            queryDto.setLoanNo(disburseData.getCreditNo());
        }
        RepaymentApi repaymentApi = zifangFactory.getApi(capitalData.getBeanName(), RepaymentApi.class);
        ResponseResult<RepaymentPlanQueryVo> result = repaymentApi.getRepaymentPlanQuery(queryDto);
        if (!result.isSuccess()) {
            log.error("【{}】借款编号 [{}] 查询资方还款计划接口失败: {}", JOB_NAME, loanNo, result.getErrorMessage());
            return;
        }
        RepaymentPlanQueryVo data = result.getData();
        log.info("【{}】借款编号 [{}] 资方返回结果：{}", JOB_NAME, loanNo, data);
        if (!"0000".equals(data.getResponseCode()) || data.getPkgList() == null) {
            log.error("【{}】借款编号 [{}] 资方返回结果错误或无数据：{}", JOB_NAME, loanNo, data.getResponseMsg());
            return;
        }
        List<RepaymentPlanQueryPkgVo> pkgList = data.getPkgList();
        if (pkgList.isEmpty()) {
            log.info("【{}】借款编号 [{}] 资方返回的还款计划列表为空，无需处理。", JOB_NAME, loanNo);
            return;
        }

        // --- Upsert Logic ---

        // 1. 查询现有数据库记录
        List<RepaySchedule> existingSchedulesList = repayScheduleService.lambdaQuery()
                .eq(RepaySchedule::getDisburseId, disburseData.getId())
                .list();
        Map<Integer, RepaySchedule> existingScheduleMap = existingSchedulesList.stream()
                .collect(Collectors.toMap(
                        schedule -> {
                            try {
                                return Integer.parseInt(schedule.getRepayTerm());
                            } catch (NumberFormatException e) {
                                log.error("【{}】借款编号 [{}] 在 {} 中解析数据库已有记录的期数失败: {}", JOB_NAME, loanNo, JOB_NAME,
                                        schedule.getRepayTerm(), e);
                                return -1; // 错误标记值
                            }
                        },
                        schedule -> schedule,
                        (existing, replacement) -> {
                            log.warn("【{}】借款编号 [{}] 数据库中发现放款ID {} 的重复期数: {}. 保留第一个遇到的记录。", JOB_NAME, loanNo,
                                    disburseData.getId(), existing.getRepayTerm());
                            return existing; // 保留第一个遇到的
                        }));
        existingScheduleMap.remove(-1); // 移除错误标记产生的条目
        log.info("【{}】借款编号 [{}] 查询到数据库已有 {} 条还款计划记录。", JOB_NAME, loanNo, existingScheduleMap.size());

        // 2. 初始化列表和总利息
        List<RepaySchedule> schedulesToInsert = new ArrayList<>();
        List<RepaySchedule> schedulesToUpdate = new ArrayList<>();
        BigDecimal totalRet = BigDecimal.ZERO;

        // 3. 遍历 API 数据，比较并分组
        for (RepaymentPlanQueryPkgVo pkgVo : pkgList) {
            Integer term;
            try {
                term = Integer.parseInt(pkgVo.getRepayTerm());
            } catch (NumberFormatException e) {
                log.error("【{}】借款编号 [{}] 无法解析资方返回的期数: {}, 跳过此条记录。", JOB_NAME, loanNo, pkgVo.getRepayTerm(), e);
                continue;
            }

            RepaySchedule dbSchedule = existingScheduleMap.get(term);
            RepaySchedule scheduleToSave = null;

            if (dbSchedule != null) { // --- 更新逻辑 ---
                // 内联 needsUpdate 逻辑
                boolean changed = false;
                String beanName = capitalData.getBeanName(); // 获取 beanName 用于比较
                changed |= !Objects.equals(pkgVo.getRepayOwnbDate(), dbSchedule.getRepayOwnbDate());
                changed |= !Objects.equals(pkgVo.getRepayOwneDate(), dbSchedule.getRepayOwneDate());
                changed |= !Objects.equals(pkgVo.getRepayIntbDate(), dbSchedule.getRepayIntbDate());
                changed |= !Objects.equals(pkgVo.getRepayInteDate(), dbSchedule.getRepayInteDate());
                BigDecimal zero = BigDecimal.ZERO;
                BigDecimal pkgTotalAmt = StrUtil.isBlank(pkgVo.getTotalAmt()) ? zero
                        : new BigDecimal(pkgVo.getTotalAmt());
                changed |= pkgTotalAmt
                        .compareTo(dbSchedule.getTotalAmt() == null ? zero : dbSchedule.getTotalAmt()) != 0;
                BigDecimal pkgTermRetPrin = StrUtil.isBlank(pkgVo.getTermRetPrin()) ? zero
                        : new BigDecimal(pkgVo.getTermRetPrin());
                changed |= pkgTermRetPrin
                        .compareTo(dbSchedule.getTermRetPrin() == null ? zero : dbSchedule.getTermRetPrin()) != 0;
                BigDecimal pkgTermRetInt = StrUtil.isBlank(pkgVo.getTermRetInt()) ? zero
                        : new BigDecimal(pkgVo.getTermRetInt());
                changed |= pkgTermRetInt
                        .compareTo(dbSchedule.getTermRetInt() == null ? zero : dbSchedule.getTermRetInt()) != 0;
                changed |= !Objects.equals(pkgVo.getTermStatus(), dbSchedule.getTermStatus());
                changed |= !Objects.equals(pkgVo.getSettleFlag(), dbSchedule.getSettleFlag());
                changed |= !Objects.equals(pkgVo.getDatePay(), dbSchedule.getDatePay());
                if (ZiFangBeanConstant.FENZHUAN.equals(beanName)) {
                    changed |= !Objects.equals(pkgVo.getBillId(), dbSchedule.getBillId());
                    changed |= !Objects.equals(pkgVo.getPlanId(), dbSchedule.getPlanId());
                }

                if (changed) {
                    // 内联 updateScheduleFromPkgVo 逻辑
                    dbSchedule.setRepayOwnbDate(pkgVo.getRepayOwnbDate());
                    dbSchedule.setRepayOwneDate(pkgVo.getRepayOwneDate());
                    dbSchedule.setRepayIntbDate(pkgVo.getRepayIntbDate());
                    dbSchedule.setRepayInteDate(pkgVo.getRepayInteDate());
                    dbSchedule.setTotalAmt(pkgTotalAmt);
                    dbSchedule.setTermRetPrin(pkgTermRetPrin);
                    dbSchedule.setTermRetInt(pkgTermRetInt);

                    processData(dbSchedule,pkgVo);

                    dbSchedule.setTermStatus(pkgVo.getTermStatus());
                    dbSchedule.setSettleFlag(pkgVo.getSettleFlag());
                    if (ZiFangBeanConstant.FENZHUAN.equals(beanName)) {
                        dbSchedule.setBillId(pkgVo.getBillId());
                        dbSchedule.setPlanId(pkgVo.getPlanId());
                    }
                    if (StrUtil.isNotEmpty(pkgVo.getDatePay())) {
                        dbSchedule.setDatePay(pkgVo.getDatePay());
                        dbSchedule.setDatePayTime(pkgVo.getDatePay());
                    }

                    schedulesToUpdate.add(dbSchedule);
                    log.debug("【{}】借款编号 [{}] 期数 {} 标记为更新。", JOB_NAME, loanNo, term);
                } else {
                    log.debug("【{}】借款编号 [{}] 期数 {} 数据匹配，无需更新。", JOB_NAME, loanNo, term);
                }
                scheduleToSave = dbSchedule;
            } else { // --- 插入逻辑 ---
                // 内联 createScheduleFromPkgVo 逻辑
                RepaySchedule newSchedule = new RepaySchedule();
                try {
                    newSchedule.setRepayTerm(pkgVo.getRepayTerm());
                } catch (Exception e) {
                    log.error("【{}】借款编号 [{}] 创建期间设置期数 {} 时出错。", JOB_NAME, loanNo, pkgVo.getRepayTerm(), e);
                    continue; // 跳过此条记录
                }
                newSchedule.setDisburseId(disburseData.getId());
                newSchedule.setUserId(disburseData.getUserId());
                newSchedule.setRepayOwnbDate(pkgVo.getRepayOwnbDate());
                newSchedule.setRepayOwneDate(pkgVo.getRepayOwneDate());
                newSchedule.setRepayIntbDate(pkgVo.getRepayIntbDate());
                newSchedule.setRepayInteDate(pkgVo.getRepayInteDate());
                BigDecimal newTotalAmt = StrUtil.isBlank(pkgVo.getTotalAmt()) ? BigDecimal.ZERO
                        : new BigDecimal(pkgVo.getTotalAmt());
                newSchedule.setTotalAmt(newTotalAmt);

                processData(newSchedule,pkgVo);

                newSchedule.setTermStatus(pkgVo.getTermStatus());
                newSchedule.setSettleFlag(pkgVo.getSettleFlag());
                if (ZiFangBeanConstant.FENZHUAN.equals(capitalData.getBeanName())) {
                    newSchedule.setBillId(pkgVo.getBillId());
                    newSchedule.setPlanId(pkgVo.getPlanId());
                }

                schedulesToInsert.add(newSchedule);
                log.debug("【{}】借款编号 [{}] 期数 {} 标记为插入。", JOB_NAME, loanNo, term);
                scheduleToSave = newSchedule;
            }

            // 累加利息
            if (scheduleToSave != null) {
                BigDecimal termInt = scheduleToSave.getTermRetInt() == null ? BigDecimal.ZERO
                        : scheduleToSave.getTermRetInt();
                BigDecimal guarantorFee = scheduleToSave.getTermGuarantorFee() == null ? BigDecimal.ZERO
                        : scheduleToSave.getTermGuarantorFee();
                BigDecimal overdueFee = scheduleToSave.getTermOverdueGuarantorFee() == null ? BigDecimal.ZERO
                        : scheduleToSave.getTermOverdueGuarantorFee();
                totalRet = totalRet.add(termInt).add(guarantorFee).add(overdueFee);
            }
        }

        // 4. 执行批量数据库操作
        boolean insertSuccess = true;
        boolean updateSuccess = true;

        try {
            if (!schedulesToInsert.isEmpty()) {
                log.info("【{}】借款编号 [{}] 准备插入 {} 条新的还款计划。", JOB_NAME, loanNo, schedulesToInsert.size());
                insertSuccess = repayScheduleService.saveBatch(schedulesToInsert);
                log.info("【{}】借款编号 [{}] 插入 {} 条新纪录 {}。", JOB_NAME, loanNo, schedulesToInsert.size(),
                        insertSuccess ? "成功" : "失败");
            }
        } catch (Exception e) {
            log.error("【{}】借款编号 [{}] 批量插入还款计划时出错: {}", JOB_NAME, loanNo, e.getMessage(), e);
            insertSuccess = false;
        }

        try {
            if (!schedulesToUpdate.isEmpty()) {
                log.info("【{}】借款编号 [{}] 准备更新 {} 条现有的还款计划。", JOB_NAME, loanNo, schedulesToUpdate.size());
                updateSuccess = repayScheduleService.updateBatchById(schedulesToUpdate);
                log.info("【{}】借款编号 [{}] 更新 {} 条记录 {}。", JOB_NAME, loanNo, schedulesToUpdate.size(),
                        updateSuccess ? "成功" : "失败");
            }
        } catch (Exception e) {
            log.error("【{}】借款编号 [{}] 批量更新还款计划时出错: {}", JOB_NAME, loanNo, e.getMessage(), e);
            updateSuccess = false;
        }

        // 5. 更新总利息
        log.info("【{}】借款编号 [{}] 基于同步后数据计算的总利息: {}", JOB_NAME, loanNo, totalRet);
        DisburseData disburseUpdate = new DisburseData();
        disburseUpdate.setId(disburseData.getId());
        disburseUpdate.setGrossInterest(totalRet);
        int updateResult = disburseDataMapper.updateById(disburseUpdate);
        log.info("【{}】借款编号 [{}] 更新支用记录总利息 {}。", JOB_NAME, loanNo, updateResult > 0 ? "成功" : "失败");

        if (!insertSuccess || !updateSuccess) {
            log.error("【{}】借款编号 [{}] 同步处理完成，但存在失败的操作 (插入: {}, 更新: {})。", JOB_NAME, loanNo, insertSuccess,
                    updateSuccess);
        } else {
            log.info("【{}】借款编号 [{}] 同步处理成功完成。", JOB_NAME, loanNo);
        }
    }


    public void processData(RepaySchedule data,RepaymentPlanQueryPkgVo pkgVo) {
        data.setTermGuarantorFee(NumberUtil.safeParseBigDecimal(pkgVo.getTermGuarantorFee()));
        data.setTermRetFint(NumberUtil.safeParseBigDecimal(pkgVo.getTermRetFint()));
        data.setTermOverdueGuarantorFee(NumberUtil.safeParseBigDecimal(pkgVo.getTermOverdueGuarantorFee()));
        data.setPrinAmt(NumberUtil.safeParseBigDecimal(pkgVo.getPrinAmt()));
        data.setNoRetAmt(NumberUtil.safeParseBigDecimal(pkgVo.getNoRetAmt()));
        data.setIntAmt(NumberUtil.safeParseBigDecimal(pkgVo.getIntAmt()));
        data.setNoRetInt(NumberUtil.safeParseBigDecimal(pkgVo.getNoRetInt()));
        data.setTermFintFinish(NumberUtil.safeParseBigDecimal(pkgVo.getTermFintFinish()));
        data.setNoRetFin(NumberUtil.safeParseBigDecimal(pkgVo.getNoRetFin()));
        data.setGuarantorFee(NumberUtil.safeParseBigDecimal(pkgVo.getGuarantorFee()));
        data.setNoGuarantorFee(NumberUtil.safeParseBigDecimal(pkgVo.getNoGuarantorFee()));
        data.setTermServiceFee(NumberUtil.safeParseBigDecimal(pkgVo.getTermServiceFee()));
        data.setServiceFee(NumberUtil.safeParseBigDecimal(pkgVo.getServiceFee()));
        data.setNoServiceFee(NumberUtil.safeParseBigDecimal(pkgVo.getNoServiceFee()));
        data.setOverdueGuarantorFee(NumberUtil.safeParseBigDecimal(pkgVo.getOverdueGuarantorFee()));
        data.setNoOverdueGuarantorFee(NumberUtil.safeParseBigDecimal(pkgVo.getNoOverdueGuarantorFee()));
    }
}
