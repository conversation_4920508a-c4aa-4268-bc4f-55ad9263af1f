package com.rongchen.byh.app.dao;

import com.rongchen.byh.app.dto.api.FenZhuanCreditNoticeDto;
import com.rongchen.byh.app.entity.UserRegisterResult;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【user_register_result(用户进件结果表)】的数据库操作Mapper
* @createDate 2025-03-10 14:11:50
* @Entity com.rongchen.byh.app.entity.UserRegisterResult
*/
public interface UserRegisterResultMapper extends BaseMapper<UserRegisterResult> {

    UserRegisterResult queryByOrderNo(FenZhuanCreditNoticeDto dto);

    UserRegisterResult selectByUserId(@Param("userId")Long userId);

    UserRegisterResult queryByLoanOrderNo(String loanOrderNo);
}




