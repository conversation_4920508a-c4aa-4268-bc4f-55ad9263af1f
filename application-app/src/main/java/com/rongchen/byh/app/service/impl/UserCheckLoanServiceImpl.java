package com.rongchen.byh.app.service.impl;

import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rongchen.byh.app.constant.LoanType;
import com.rongchen.byh.app.constant.SourceMode;
import com.rongchen.byh.app.constant.UserAuditStatus;
import com.rongchen.byh.app.dao.*;
import com.rongchen.byh.app.dto.app.BackVo;
import com.rongchen.byh.app.dto.h5.AirRiskControllerDto;
import com.rongchen.byh.app.entity.UserCreditData;
import com.rongchen.byh.app.entity.UserLoanApply;
import com.rongchen.byh.app.entity.UserDetail;
import com.rongchen.byh.app.dto.h5.UserCheckLoanApplyDto;
import com.rongchen.byh.app.entity.UserData;
import com.rongchen.byh.app.service.UserCheckLoanService;
import com.rongchen.byh.app.utils.UserTokenUtil;
import com.rongchen.byh.app.vo.h5.UserCheckLoanApplyVo;
import com.rongchen.byh.common.api.bankCredit.dto.HrzxAuthDto;
import com.rongchen.byh.common.api.idCardVerify.service.IdCardVerifyService;
import com.rongchen.byh.common.api.idCardVerify.vo.VerifyVo;
import com.rongchen.byh.common.api.riskControl.dto.CrmPushDto;
import com.rongchen.byh.common.api.riskControl.dto.PreLoanAuditDto;
import com.rongchen.byh.common.api.riskControl.service.CrmPushService;
import com.rongchen.byh.common.api.riskControl.service.RiskControlService;
import com.rongchen.byh.common.api.riskControl.vo.CrmPushVo;
import com.rongchen.byh.common.api.riskControl.vo.PreLoanAuditVo;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.rabbitmq.constants.QueueConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 用户初筛相关业务
 * @date 2024/12/11 10:26:05
 */
@Service
@Slf4j
public class UserCheckLoanServiceImpl implements UserCheckLoanService {
    @Resource
    private IdCardVerifyService idCardVerifyService;
    @Resource
    private UserLoanApplyMapper userLoanApplyMapper;
    @Resource
    private RiskControlService riskControlService;
    @Resource
    private CrmPushService crmPushService;
    @Resource
    private UserDataMapper userDataMapper;
    @Resource
    private UserDetailMapper userDetailMapper;
    @Resource
    private UserCreditDataMapper userCreditDataMapper;
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    private UserBankCardMapper userBankCardMapper;
    @Value("${spring.profiles.active}")
    private String active;

    @Override
    public synchronized ResponseResult<UserCheckLoanApplyVo> apply(UserCheckLoanApplyDto userCheckLoanApplyDto) {
        if (userCheckLoanApplyDto != null && userCheckLoanApplyDto.getIdNumber() != null){
            userCheckLoanApplyDto.setIdNumber(userCheckLoanApplyDto.getIdNumber().toUpperCase());
        }
        // 前置校验
        if (!IdcardUtil.isValidCard(userCheckLoanApplyDto.getIdNumber())) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "身份证号格式错误");
        }
        // 是否已经提交过一次申请，是则提示已经提交无需再次提交
        Long userId = UserTokenUtil.getUserId();
        UserData userData = userDataMapper.selectById(userId);
        if (ObjectUtil.isEmpty(userData)) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "用户不存在");
        }

        UserData userDataUp = new UserData();
        userDataUp.setId(userData.getId());
        //查看用户是否是线上初筛申请
        UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userId, LoanType.CHECK, 0);
        UserCheckLoanApplyVo userCheckLoanApplyVo = new UserCheckLoanApplyVo();
        if (userLoanApply != null) {
            UserCheckLoanApplyVo result = userCheckLoanApplyVo;
            result.setUserId(userId);
            result.setAuditStatus(userLoanApply.getAuditsStatus());
            return ResponseResult.success(result);
        }
        // 二要素校验----->修改为三要素校验
//        VerifyVo verifyResult = idCardVerifyService.verify(userCheckLoanApplyDto.getIdNumber(),
//                userCheckLoanApplyDto.getUserName());
//        if (verifyResult.getCode() != 0) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL, verifyResult.getMsg());
//        }
        if ("prod".equals(active)) {
            VerifyVo verifyResult = idCardVerifyService.verifyThree(userCheckLoanApplyDto.getIdNumber(),
                    userCheckLoanApplyDto.getUserName(), userData.getMobile());
            if (verifyResult.getCode() != 1) {
                log.info("三要素校验失败:{}", userId);
                return ResponseResult.error(ErrorCodeEnum.FAIL, "您的姓名，身份证号，手机号不一致，请核对");
            }
        }
        // 根据身份证号查询用户
        UserDetail detail = userDetailMapper.selectOne(new LambdaQueryWrapper<UserDetail>().eq(UserDetail::getWebIdCard, userCheckLoanApplyDto.getIdNumber()));
        if (ObjectUtil.isNotEmpty(detail)) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "身份证号已存在，请勿重复申请");
        }
        // 记录申请
        UserLoanApply apply = new UserLoanApply();
        apply.setUserId(userId);
        apply.setApplyType(LoanType.CHECK);
        apply.setUserApplyInfo(JSONObject.toJSONString(userCheckLoanApplyDto));
        userLoanApplyMapper.insert(apply);

        // 记录用户身份证号和姓名
        UserDetail userDetail = new UserDetail();
        userDetail.setUserId(userId);
        userDetail.setWebName(userCheckLoanApplyDto.getUserName());
        userDetail.setWebIdCard(userCheckLoanApplyDto.getIdNumber());
        userDetail.setWebTwoElements(1);
        userDetailMapper.insert(userDetail);

        // 风控
        PreLoanAuditDto preLoanAuditDto = new PreLoanAuditDto();
        preLoanAuditDto.setApplyId(String.valueOf(apply.getId()));
        preLoanAuditDto.setMobile(userData.getMobile());
        preLoanAuditDto.setName(userCheckLoanApplyDto.getUserName());
        preLoanAuditDto.setIdNumber(userCheckLoanApplyDto.getIdNumber());
        PreLoanAuditVo preLoanAuditVo = riskControlService.preLoanAudit(preLoanAuditDto);
        Integer result = preLoanAuditVo.getResult();
        if (result == -1) {
            apply.setAuditsStatus(2);
            userLoanApplyMapper.updateById(apply);
            // 更新用户审核状态
            userDataUp.setAuditStatus(UserAuditStatus.FIRST_REJECT);
            userDataMapper.updateById(userDataUp);
            return ResponseResult.success(null);
        }else {
            if (result == 0 || result == 1) {
                apply.setAuditsStatus(1);
                // 同步crm
                CrmPushDto crmPushDto = new CrmPushDto();
                crmPushDto.setMobile(userData.getMobile());
                crmPushDto.setUserName(userCheckLoanApplyDto.getUserName());
                crmPushDto.setIdNumber(userCheckLoanApplyDto.getIdNumber());
                crmPushDto.setCreditRating(preLoanAuditVo.getCreditRating());
//                CrmPushVo push = crmPushService.push(crmPushDto);
//                if (push.getResult() != 0) {
//                    apply.setAuditsStatus(2);
//                    userLoanApplyMapper.updateById(apply);
//                    return ResponseResult.success(null);
//                }
            } else {
                apply.setAuditsStatus(2);
                userLoanApplyMapper.updateById(apply);
                // 更新用户审核状态
                userDataUp.setAuditStatus(UserAuditStatus.FIRST_REJECT);
                userDataMapper.updateById(userDataUp);
                return ResponseResult.success(null);
            }
        }
        // 更新用户风险等级
        userDetail.setRiskLevel(preLoanAuditVo.getCreditRating());
        userDetailMapper.updateById(userDetail);

        userLoanApplyMapper.updateById(apply);
        // 更新用户审核状态
        userDataUp.setAuditStatus(UserAuditStatus.WAIT_APP_CREDIT);
        userDataUp.setAuditFlag(1);
        userDataUp.setSourceMode(SourceMode.ONLINE);
        userDataMapper.updateById(userDataUp);
        // 保存授信额度
        UserCreditData userCreditData = new UserCreditData();
        userCreditData.setUserId(userId);
        userCreditData.setCreditAmount(ObjectUtil.isEmpty(preLoanAuditVo.getAmount()) ? BigDecimal.ZERO
                : preLoanAuditVo.getAmount());
        // 风控通过，剩余额度和授信额度保持一致
        userCreditData.setResidueAmount(userCreditData.getCreditAmount());
        userCreditDataMapper.insert(userCreditData);
        userCheckLoanApplyVo.setUserId(userId);
        userCheckLoanApplyVo.setAuditStatus(apply.getAuditsStatus());
        return ResponseResult.success(userCheckLoanApplyVo);
    }

    @Override
    public ResponseResult<UserCheckLoanApplyVo> queryResult() {
        Long userId = UserTokenUtil.getUserId();
        UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userId, LoanType.CHECK,  0);
        UserCheckLoanApplyVo userCheckLoanApplyVo = new UserCheckLoanApplyVo();
        userCheckLoanApplyVo.setUserId(userId);
        userCheckLoanApplyVo.setBindCardStatus(0);
        if (ObjectUtil.isEmpty(userLoanApply)) {
            userCheckLoanApplyVo.setAuditStatus(0);
            return ResponseResult.success(userCheckLoanApplyVo);
        }
        UserCreditData userCreditData = userCreditDataMapper.queryByUserId(userId);
        userCheckLoanApplyVo.setAuditStatus(userLoanApply.getAuditsStatus());
        if (ObjectUtil.isNotEmpty(userCreditData)){
            userCheckLoanApplyVo.setCreditAmount(userCreditData.getCreditAmount());
        }
        BackVo backVo = userBankCardMapper.queryBackByUserId(userId);
        if (ObjectUtil.isNotEmpty(backVo)){
            userCheckLoanApplyVo.setBindCardStatus(1);
        }
        return ResponseResult.success(userCheckLoanApplyVo);
    }

    @Override
    public ResponseResult<UserCheckLoanApplyVo> flyApply(UserCheckLoanApplyDto userCheckLoanApplyDto) {
        if (userCheckLoanApplyDto != null && userCheckLoanApplyDto.getIdNumber() != null){
            userCheckLoanApplyDto.setIdNumber(userCheckLoanApplyDto.getIdNumber().toUpperCase());
        }
        // 前置校验
        if (!IdcardUtil.isValidCard(userCheckLoanApplyDto.getIdNumber())) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "身份证号格式错误");
        }
        // 是否已经提交过一次申请，是则提示已经提交无需再次提交
        Long userId = UserTokenUtil.getUserId();
        UserData userData = userDataMapper.selectById(userId);
        if (ObjectUtil.isEmpty(userData)) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "用户不存在");
        }

        UserData userDataUp = new UserData();
        userDataUp.setId(userData.getId());

        UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userId, LoanType.CHECK, 2);
        UserCheckLoanApplyVo userCheckLoanApplyVo = new UserCheckLoanApplyVo();
        if (userLoanApply != null) {
            UserCheckLoanApplyVo result = userCheckLoanApplyVo;
            result.setUserId(userId);
            result.setAuditStatus(userLoanApply.getAuditsStatus());
            return ResponseResult.success(result);
        }
        // 二要素校验----->修改为三要素校验
//        VerifyVo verifyResult = idCardVerifyService.verify(userCheckLoanApplyDto.getIdNumber(),
//                userCheckLoanApplyDto.getUserName());
//        if (verifyResult.getCode() != 0) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL, verifyResult.getMsg());
//        }
        if ("prod".equals(active)) {
            VerifyVo verifyResult = idCardVerifyService.verifyThree(userCheckLoanApplyDto.getIdNumber(),
                    userCheckLoanApplyDto.getUserName(), userData.getMobile());
            if (verifyResult.getCode() != 1) {
                log.info("三要素校验失败:{}", userId);
                return ResponseResult.error(ErrorCodeEnum.FAIL, "您的姓名，身份证号，手机号不一致，请核对");
            }
        }
        // 根据身份证号查询用户
        UserDetail detail = userDetailMapper.selectOne(new LambdaQueryWrapper<UserDetail>().eq(UserDetail::getWebIdCard, userCheckLoanApplyDto.getIdNumber()));
        if (ObjectUtil.isNotEmpty(detail)) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "身份证号已存在，请勿重复申请");
        }
        // 记录申请
        UserLoanApply apply = new UserLoanApply();
        apply.setUserId(userId);
        apply.setApplyType(LoanType.CHECK);
        apply.setOnlineType(2);
        apply.setUserApplyInfo(JSONObject.toJSONString(userCheckLoanApplyDto));
        userLoanApplyMapper.insert(apply);

        // 记录用户身份证号和姓名
        UserDetail userDetail = new UserDetail();
        userDetail.setUserId(userId);
        userDetail.setWebName(userCheckLoanApplyDto.getUserName());
        userDetail.setWebIdCard(userCheckLoanApplyDto.getIdNumber());
        userDetail.setWebTwoElements(1);
        userDetailMapper.insert(userDetail);

        // 风控
        PreLoanAuditDto preLoanAuditDto = new PreLoanAuditDto();
        preLoanAuditDto.setApplyId(String.valueOf(apply.getId()));
        preLoanAuditDto.setMobile(userData.getMobile());
        preLoanAuditDto.setName(userCheckLoanApplyDto.getUserName());
        preLoanAuditDto.setIdNumber(userCheckLoanApplyDto.getIdNumber());
        PreLoanAuditVo preLoanAuditVo = riskControlService.flyH5PreLoanAudit(preLoanAuditDto);
        Integer result = preLoanAuditVo.getResult();
        if (result == -1) {
            apply.setAuditsStatus(2);
            userLoanApplyMapper.updateById(apply);
            // 更新用户审核状态
            userDataUp.setAuditStatus(UserAuditStatus.FIRST_REJECT);
            userDataMapper.updateById(userDataUp);
            return ResponseResult.success(null);
        }else {
            if (result == 0) {
                apply.setAuditsStatus(1);
                UserData userData1 = new UserData();
                userData1.setId(userId);
                userData1.setAuditFlag(1);
                //更新审核状态
                userDataMapper.updateById(userData1);
            }else {
                apply.setAuditsStatus(4);

                // 同步crm
                // todo 同步失败是否需要重试
                // TODO 暂时不推送数据到crm,上线后打开
                CrmPushDto crmPushDto = new CrmPushDto();
                crmPushDto.setMobile(userData.getMobile());
                crmPushDto.setUserName(userCheckLoanApplyDto.getUserName());
                crmPushDto.setIdNumber(userCheckLoanApplyDto.getIdNumber());
                crmPushDto.setCreditRating(preLoanAuditVo.getCreditRating());
//                CrmPushVo push = crmPushService.push(crmPushDto);
//                if (push.getResult() != 0) {
//                    apply.setAuditsStatus(2);
//                    userLoanApplyMapper.updateById(apply);
//                    return ResponseResult.success(null);
//                }
            }
        }
        // 更新用户风险等级
        userDetail.setRiskLevel(preLoanAuditVo.getCreditRating());
        userDetailMapper.updateById(userDetail);

        userLoanApplyMapper.updateById(apply);
        // 更新用户审核状态
        userDataUp.setAuditStatus(UserAuditStatus.WAIT_APP_CREDIT);
        userDataMapper.updateById(userDataUp);
        // 保存授信额度
        UserCreditData userCreditData = new UserCreditData();
        userCreditData.setUserId(userId);
        userCreditData.setCreditAmount(ObjectUtil.isEmpty(preLoanAuditVo.getAmount()) ? BigDecimal.ZERO
                : preLoanAuditVo.getAmount());
        // 风控通过，剩余额度和授信额度保持一致
        userCreditData.setResidueAmount(userCreditData.getCreditAmount());
        userCreditDataMapper.insert(userCreditData);


        userCheckLoanApplyVo.setUserId(userId);
        userCheckLoanApplyVo.setAuditStatus(apply.getAuditsStatus());
        return ResponseResult.success(userCheckLoanApplyVo);
    }

    @Override
    public ResponseResult<UserCheckLoanApplyVo> flyQueryResult() {
        Long userId = UserTokenUtil.getUserId();
        UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userId, LoanType.CHECK,  2);
        UserCheckLoanApplyVo userCheckLoanApplyVo = new UserCheckLoanApplyVo();
        userCheckLoanApplyVo.setUserId(userId);
        userCheckLoanApplyVo.setBindCardStatus(0);
        if (ObjectUtil.isEmpty(userLoanApply)) {
            userCheckLoanApplyVo.setAuditStatus(0);
            return ResponseResult.success(userCheckLoanApplyVo);
        }
        UserCreditData userCreditData = userCreditDataMapper.queryByUserId(userId);
        userCheckLoanApplyVo.setAuditStatus(userLoanApply.getAuditsStatus());
        if (ObjectUtil.isNotEmpty(userCreditData)){
            userCheckLoanApplyVo.setCreditAmount(userCreditData.getCreditAmount());
        }
        BackVo backVo = userBankCardMapper.queryBackByUserId(userId);
        if (ObjectUtil.isNotEmpty(backVo)){
            userCheckLoanApplyVo.setBindCardStatus(1);
        }
        return ResponseResult.success(userCheckLoanApplyVo);
    }

    @Override
    public ResponseResult<Void> applyNew() {
        // todo 发送mq，通知风控
        Long userId = UserTokenUtil.getUserId();
        UserData userData = userDataMapper.selectById(userId);
        if (ObjectUtil.isEmpty(userData)) {
            log.error("用户不存在");
            return ResponseResult.error(ErrorCodeEnum.FAIL, "用户不存在");
        }
        UserDetail userDetail = userDetailMapper.queryByUserId(userId);
        if (ObjectUtil.isEmpty(userDetail)) {
            log.error("用户信息不存在");
            return ResponseResult.error(ErrorCodeEnum.FAIL, "用户信息不存在");
        }
        // 前置校验
        if (!IdcardUtil.isValidCard(userDetail.getIdNumber())) {
            log.error("身份证号格式错误");
            return ResponseResult.error(ErrorCodeEnum.FAIL, "身份证号格式错误");
        }
        // 是否已经提交过一次申请，是则提示已经提交无需再次提交
        UserData userDataUp = new UserData();
        userDataUp.setId(userData.getId());

        UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userId, LoanType.CHECK, 2);
        UserCheckLoanApplyVo userCheckLoanApplyVo = new UserCheckLoanApplyVo();
        if (userLoanApply != null) {
            UserCheckLoanApplyVo result = userCheckLoanApplyVo;
            result.setUserId(userId);
            result.setAuditStatus(userLoanApply.getAuditsStatus());
            log.error("{}已经提交过一次申请，无需再次提交", userId);
            return ResponseResult.error(ErrorCodeEnum.FAIL, "已经提交过一次申请，无需再次提交");
        }
        // 记录申请
        UserLoanApply apply = new UserLoanApply();
        apply.setUserId(userId);
        apply.setApplyType(LoanType.CHECK);
        apply.setOnlineType(2);
        userLoanApplyMapper.insert(apply);

        AirRiskControllerDto airRiskControllerDto = new AirRiskControllerDto();
        airRiskControllerDto.setUserName(userDetail.getAppName());
        airRiskControllerDto.setIdNumber(userDetail.getIdNumber());
        airRiskControllerDto.setUserId(userId);
        airRiskControllerDto.setType(SourceMode.AIR);
        airRiskControllerDto.setApplyId(apply.getId());
        rabbitTemplate.convertAndSend(QueueConstant.RISK_CONTROL_QUEUE, JSONObject.toJSONString(airRiskControllerDto));
        return ResponseResult.success();
    }
}
