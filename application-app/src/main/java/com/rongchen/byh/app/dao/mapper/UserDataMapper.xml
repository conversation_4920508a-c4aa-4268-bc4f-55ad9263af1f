<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.dao.UserDataMapper">

    <resultMap id="BaseResultMap" type="com.rongchen.byh.app.entity.UserData">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
        <result property="statusFlag" column="status_flag" jdbcType="INTEGER"/>
        <result property="auditFlag" column="audit_flag" jdbcType="INTEGER"/>
        <result property="auditStatus" column="audit_status" jdbcType="INTEGER"/>
        <result property="sourceMode" column="source_mode" jdbcType="INTEGER"/>
        <result property="mobileMd" column="mobile_md" jdbcType="VARCHAR"/>
        <result property="channelId" column="channel_id" jdbcType="BIGINT"/>
        <result property="ip" column="ip" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,mobile,status_flag,
        audit_flag,audit_status,mobile_md,
        channel_id,ip,create_time,source_mode
    </sql>


    <select id="queryByMobile" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from user_data
        where mobile = #{mobile}
    </select>
    <select id="selectSaleRecordByMobile" resultType="java.lang.Integer">
        select staff_audit_record.id
        from user_data
                 left join user_staff on user_data.id = user_staff.user_id
                 left join staff_audit_record on user_staff.id = staff_audit_record.user_staff_id
        where user_data.mobile = #{mobile}
    </select>


</mapper>
