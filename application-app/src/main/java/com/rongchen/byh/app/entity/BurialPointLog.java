package com.rongchen.byh.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 埋点记录表
 * @TableName burial_point_log
 */
@TableName(value ="burial_point_log")
@Data
public class BurialPointLog implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 访问ip
     */
    private String ip;

    /**
     * 渠道id
     */
    private Long channelId;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 页面key
     */
    private String pageKey;

    /**
     * 页面名称
     */
    private String pageName;

    /**
     * 按钮key
     */
    private String buttonKey;

    /**
     * 按钮名称
     */
    private String buttonName;

    /**
     * 其他
     */
    private String other;

    /**
     * 创建时间
     */
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}