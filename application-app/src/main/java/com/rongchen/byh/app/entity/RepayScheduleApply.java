package com.rongchen.byh.app.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**

 * 项目名称：byh_java
 * 文件名称: RepayScheduleApply
 * 创建时间: 2025-04-05 18:22
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.app.entity
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */

/**
 * 本息还款计划申请表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "`repay_schedule_apply`")
public class RepayScheduleApply implements Serializable {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 账单还款流水号
     */
    @TableField(value = "`repay_apply_no`")
    private String repayApplyNo;

    /**
     * 用户id
     */
    @TableField(value = "`user_id`")
    private Long userId;

    /**
     * 还款计划id
     */
    @TableField(value = "`repay_schedule_id`")
    private Long repayScheduleId;

    /**
     * 还款类型
     1-主动提交还款
     2-自动跑批扣款
     3-逾期自动跑批扣款
     4-内部线下还款发起
     */
    @TableField(value = "`repay_type`")
    private Integer repayType;

    /**
     * 还款状态 0 待审核 1 还款成功 2 还款失败'
     */
    @TableField(value = "`repay_status`")
    private Integer repayStatus;

    /**
     * 失败原因
     */
    @TableField(value = "`reason`")
    private String reason;

    /**
     * 创建时间
     */
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 响应时间
     */
    @TableField(value = "`response_time`")
    private Date responseTime;

    private static final long serialVersionUID = 1L;
}