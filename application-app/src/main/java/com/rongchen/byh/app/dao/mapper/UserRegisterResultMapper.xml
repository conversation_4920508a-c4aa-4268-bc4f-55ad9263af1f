<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.dao.UserRegisterResultMapper">

    <resultMap id="BaseResultMap" type="com.rongchen.byh.app.entity.UserRegisterResult">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
            <result property="bindRequestNo" column="bind_request_no" jdbcType="VARCHAR"/>
            <result property="bindId" column="bind_id" jdbcType="VARCHAR"/>
            <result property="loanOrderNo" column="loan_order_no" jdbcType="VARCHAR"/>
            <result property="capitalId" column="capital_id" jdbcType="BIGINT"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,order_no,bind_request_no,bind_id,
        capital_id,status,loan_order_no,create_time,
        update_time
    </sql>
    <select id="selectByUserId" resultType="com.rongchen.byh.app.entity.UserRegisterResult">
        select <include refid="Base_Column_List"/>
        from user_register_result
        where user_id = #{userId} order by id desc limit 1
    </select>
    <select id="queryByOrderNo" resultType="com.rongchen.byh.app.entity.UserRegisterResult">
        select
            <include refid="Base_Column_List"/>
        from
            user_register_result
        where
            order_no = #{orderNo}
    </select>
    <select id="queryByLoanOrderNo" resultType="com.rongchen.byh.app.entity.UserRegisterResult">
        select
            *
        from
            user_register_result
        where
            loan_order_no = #{loanOrderNo}
    </select>
</mapper>
