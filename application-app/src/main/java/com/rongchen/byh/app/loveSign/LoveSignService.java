package com.rongchen.byh.app.loveSign;


import com.ancun.netsign.client.NetSignClient;
import com.ancun.netsign.model.*;
import com.rongchen.byh.app.exceptions.UserException;
import com.rongchen.byh.app.loveSign.strategy.SignStrategy;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class LoveSignService {


    @Resource
    NetSignClient client;
    @Resource
    LoveSignProperties loveSignProperties;
    @Resource
    List<SignStrategy> signStrategies;
    @Resource
    private RedissonClient redissonClient;




//    @Async(AsyncConfig.ASYNC_EXECUTOR_THREAD_NAME)
    public ResponseResult<Void> signAll(SignDto signDto,String templateNo) {
        try {
            // 加入分布式锁，防止前端同时请求，导致添加个人用户失败
            RLock lock = redissonClient.getLock("aiSign:" + signDto.getUserId());
            boolean isLocked = false;
            try {
                isLocked = lock.tryLock(10, TimeUnit.SECONDS);
                if (!isLocked) {
                    return ResponseResult.error(ErrorCodeEnum.FAIL, "系统繁忙，请稍后再试！");
                }

                // 1.查询用户是否存在
                SignUserOutput getUser = this.getUser(signDto);
                if (getUser == null) {
                    // 1.添加个人用户（V2）
                    UserSeal userSeal = this.addUser(signDto);
                }
            } catch (Exception e) {
                return ResponseResult.error(ErrorCodeEnum.FAIL, "系统异常");
            } finally {
                if (isLocked) {
                    lock.unlock();
                }
            }
            // 2.上传待签署文件
            ContractOutput contract = this.createContract(signDto,templateNo);
            List<ContractUserInput> inputs = new ArrayList<>();
            for (SignStrategy signStrategy : signStrategies) {
                if (signStrategy.match(templateNo)) {
                    inputs = signStrategy.getInputList(signDto);
                    break;
                }
            }
            ContractOutput contractOutput = this.addSigner(inputs);

            // 预览链接
            String previewUrl = contractOutput.getPreviewUrl();



            return ResponseResult.success();
        } catch (UserException userException) {
            log.error("自定义错误",userException);
            return ResponseResult.error(ErrorCodeEnum.FAIL,userException.getMessage());
        } catch (Exception e) {
            log.error("未知错误",e);
            return ResponseResult.error(ErrorCodeEnum.FAIL,"未知错误");
        }
    }

    public SignUserOutput getUser(SignDto dto) {
        ApiRespBody<SignUserOutput> respBody = client.getUser(dto.getAccount());
        if (respBody.getCode() != 100000) {
            return null;
        }
        return respBody.getData();

    }

    private ContractOutput addSigner(List<ContractUserInput> inputs) {
        ApiRespBody<ContractOutput> respBody = client.addSigner(inputs);
        if (respBody.getCode() != 100000) {
            throw new UserException(respBody.getMsg());
        }
        return respBody.getData();
    }

    private ContractOutput createContract(SignDto dto,String templateNo) {
        ContractInput input = new ContractInput();
        input.setContractNo(dto.getContractNo());
        input.setContractName(dto.getContractName());
        input.setValidityTime(1);
        input.setSignOrder(2);
        List<ContractInput.Template> templates = new ArrayList<>(1);
        ContractInput.Template template1 = new ContractInput.Template();
        template1.setTemplateNo(templateNo);
        Map<String,String> fillData = new HashMap<>();
        dto.getRules().forEach(item -> {
            fillData.put(item.getKey(),item.getValue());
        });
        template1.setFillData(fillData);
        templates.add(template1);
        input.setTemplates(templates);
        ApiRespBody<ContractOutput> respBody = client.createContract(input);
        if (respBody.getCode() != 100000) {
            throw new UserException(respBody.getMsg());
        }
        return respBody.getData();
    }

    public UserSeal addUser(SignDto dto) {
        UserInput input = new UserInput();
        input.setAccount(dto.getAccount());
        input.setName(dto.getName());
        input.setIdCard(dto.getIdCard());
        input.setIdCardType(1);
        input.setMobile(dto.getAccount());
        input.setIsSignPwdNotice(0);
        input.setIsNotice(0);
        ApiRespBody<UserSeal> respBody = client.addPersonalUserV2(input);
        if (respBody.getCode() != 100000) {
            throw new UserException(respBody.getMsg());
        }
        return respBody.getData();
    }

    public DownloadContractOutput contractDownLoad(String mobile, String contractId) {
        SignDto dto = new SignDto();
        dto.setContractNo(contractId);
        dto.setAccount(mobile);
        DownloadContractOutput output = downloadContract(dto);
        return output;
    }

    private DownloadContractOutput downloadContract(SignDto dto) {
        ApiRespBody<DownloadContractOutput> respBody = client.downloadContract(dto.getContractNo(), "");
        if (respBody.getCode() != 100000) {
            throw new UserException(respBody.getMsg());
        }
        return respBody.getData();
    }
}
