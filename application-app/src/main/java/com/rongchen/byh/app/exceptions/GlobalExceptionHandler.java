package com.rongchen.byh.app.exceptions;

import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(BusinessException.class)
    public ResponseResult handleBusinessException(BusinessException e) {
        return ResponseResult.error(e.getErrorCode(), e.getMessage());
    }

    @ExceptionHandler(Exception.class)
    public ResponseResult handleOtherExceptions(Exception e) {
        return ResponseResult.error(ErrorCodeEnum.FAIL, "系统异常");
    }
}