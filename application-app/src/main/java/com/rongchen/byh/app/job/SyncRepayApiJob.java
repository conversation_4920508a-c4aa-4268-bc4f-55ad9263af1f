package com.rongchen.byh.app.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rongchen.byh.app.config.ZifangFactory;
import com.rongchen.byh.app.constant.ZiFangBeanConstant;
import com.rongchen.byh.app.dao.CapitalDataMapper;
import com.rongchen.byh.app.dao.DisburseDataMapper;
import com.rongchen.byh.app.dao.RepayScheduleMapper;
import com.rongchen.byh.app.entity.CapitalData;
import com.rongchen.byh.app.entity.DisburseData;
import com.rongchen.byh.app.entity.RepaySchedule;
import com.rongchen.byh.app.service.RepayScheduleService;
import com.rongchen.byh.app.utils.NumberUtil;
import com.rongchen.byh.common.api.zifang.dto.RepaymentPlanQueryDto;
import com.rongchen.byh.common.api.zifang.service.RepaymentApi;
import com.rongchen.byh.common.api.zifang.vo.RepaymentPlanQueryPkgVo;
import com.rongchen.byh.common.api.zifang.vo.loan.RespRepayPlanPkgVo;
import com.rongchen.byh.common.api.zifang.vo.loan.RespRepayPlanVo;
import com.rongchen.byh.common.core.constant.SettleFlagConstant;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.MDCUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 同步订单任务
 * xxl-job手动执行
 */
@Component
@Slf4j
public class SyncRepayApiJob {

    @Resource
    ZifangFactory zifangFactory;
    @Resource
    RepayScheduleMapper repayScheduleMapper;
    @Resource
    DisburseDataMapper disburseDataMapper;
    @Resource
    CapitalDataMapper capitalDataMapper;
    @Resource
    RepayScheduleService repayScheduleService;
    @Resource
    SyncRepayApiJob syncRepayApiJob;

    private static final String JOB_NAME = "xxl同步api订单";

    /**
     * 手动执行同步订单任务
     */
    @XxlJob("syncRepayApiJobHandler")
    public void syncRepayJobHandler() {
        try {
            MDCUtil.setTraceId();
            // 需要更新的disburse_id，使用逗号拼接
            // 格式 1,2,3
            String param = XxlJobHelper.getJobParam();
            List<Long> list = null;
            // disburse_id 为空，查询所有订单
            if (StrUtil.isEmpty(param)) {
                list = repayScheduleMapper.selectDisburseIdAll(0);
            } else {
                List<String> split = StrUtil.split(param, ",");
                list = split.stream().map(Long::parseLong).collect(Collectors.toList());
            }
            if (CollectionUtil.isEmpty(list)) {
                log.info("【{}】未提供或查询到需要同步的 disburse_id 列表数据为空", JOB_NAME);
                return;
            }
            LambdaQueryWrapper<DisburseData> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(DisburseData::getId, list);
            List<DisburseData> disburseData = disburseDataMapper.selectList(queryWrapper);

            log.info("【{}】查询到 {} 条支用记录需要处理。", JOB_NAME, disburseData.size());
            disburseData.forEach(data -> {
                if (data.getCreditStatus() == 500) {
                    this.syncOrderProcess(data);
                }
            });
            log.info("【{}】手动同步任务处理完成。", JOB_NAME);

        } catch (Exception e) {
            log.error("【{}】执行异常", JOB_NAME, e);
        } finally {
            // 清除MDC上下文
            MDCUtil.clear();
        }

    }

    private void syncOrderProcess(DisburseData disburseData) {
        String loanNo = disburseData.getLoanNo();
        try {
            log.info("【{}】开始处理借款编号: {}", JOB_NAME, loanNo);

            // --- API 调用逻辑 ---
            RepaymentPlanQueryDto queryDto = new RepaymentPlanQueryDto();
            queryDto.setUserId(disburseData.getUserId() + "");
            queryDto.setLoanNo(loanNo);
            CapitalData capitalData = capitalDataMapper.selectById(disburseData.getCapitalId());
            if (ObjectUtil.isEmpty(capitalData)) {
                log.warn("【{}】借款编号 [{}] 资方未匹配 (Capital ID: {}), 跳过处理", JOB_NAME, loanNo, disburseData.getCapitalId());
                return;
            }
            if (capitalData.getBeanName().equals(ZiFangBeanConstant.FENZHUAN)) {
                queryDto.setLoanNo(disburseData.getCreditNo());
            }
            RepaymentApi repaymentApi = zifangFactory.getApi(capitalData.getBeanName(), RepaymentApi.class);
            ResponseResult<RespRepayPlanVo> result = repaymentApi.getApiRepaymentPlanQuery(queryDto);
            if (!result.isSuccess()) {
                log.error("【{}】借款编号 [{}] 查询资方还款计划接口失败: {}", JOB_NAME, loanNo, result.getErrorMessage());
                return;
            }
            RespRepayPlanVo data = result.getData();
            log.info("【{}】借款编号 [{}] 资方返回结果：{}", JOB_NAME, loanNo, data);
            if (!"0000".equals(data.getResponseCode()) || data.getPkgList() == null) {
                log.error("【{}】借款编号 [{}] 资方返回结果错误或无数据：{}", JOB_NAME, loanNo, data.getResponseMsg());
                return;
            }
            List<RespRepayPlanPkgVo> pkgList = data.getPkgList();
            if (pkgList.isEmpty()) {
                log.info("【{}】借款编号 [{}] 资方返回的还款计划列表为空，无需处理。", JOB_NAME, loanNo);
                return;
            }

            /**
             * 暂时先不考虑异常情况
             * 1. 查询有多条重复账单数据
             */

            // 1. 查询现有数据库记录
            List<RepaySchedule> schedulesList = repayScheduleService.lambdaQuery()
                    .eq(RepaySchedule::getDisburseId, disburseData.getId())
                    .list();
//            if (schedulesList.size() < 12) {
//                log.info("【{}】借款编号 [{}] 账单提前结清，不是12期，无需处理。", JOB_NAME, loanNo);
//                return;
//            }

            // 资方返回账单数据不足12条，说明是提前结清
            int size = pkgList.size();
            if (size < 12) {
                syncRepayApiJob.cleanOrders(schedulesList,pkgList,disburseData);
            } else {
                syncRepayApiJob.syncOrders(schedulesList,pkgList,disburseData);
            }
            log.info("【{}】借款编号 [{}] 同步处理成功完成。", JOB_NAME, loanNo);
        } catch (Exception e) {
             log.error("【{}】借款编号 [{}] 同步处理异常", JOB_NAME, loanNo, e);
        }

    }

    /**
     * 同步订单
     * @param scheduleList
     * @param pkgList
     * @param disburseData
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncOrders(List<RepaySchedule> scheduleList, List<RespRepayPlanPkgVo> pkgList,DisburseData disburseData) {
        Map<String, RespRepayPlanPkgVo> collect = pkgList.stream().collect(Collectors.toMap(RespRepayPlanPkgVo::getRepayTerm, V -> V));
        List<RepaySchedule> upList = new ArrayList<>(12);
        BigDecimal totalAmt = zero;
        for (RepaySchedule schedule : scheduleList) {
            String repayTerm = schedule.getRepayTerm();
            RespRepayPlanPkgVo pkgVo = collect.get(repayTerm);
            RepaySchedule upData = new RepaySchedule();
            upData.setId(schedule.getId());
            upData.setTotalAmt(NumberUtil.safeParseBigDecimal(pkgVo.getTotalAmt()));
            upData.setTermRetPrin(NumberUtil.safeParseBigDecimal(pkgVo.getTermRetPrin()));
            upData.setTermRetInt(NumberUtil.safeParseBigDecimal(pkgVo.getTermRetInt()));
            this.processData(upData, pkgVo);
            upData.setTermStatus(pkgVo.getTermStatus());
            upData.setSettleFlag(pkgVo.getSettleFlag());
            if (StrUtil.isNotEmpty(pkgVo.getDatePay())) {
                upData.setPayTime(pkgVo.getDatePay());
            }
            if (StrUtil.isEmpty(schedule.getDatePayTime())) {
                upData.setDatePay(pkgVo.getDatePay());
            }
            totalAmt = totalAmt.add(upData.getTotalAmt());
            upList.add(upData);
        }

        BigDecimal subtract = totalAmt.subtract(disburseData.getCreditAmount());

        repayScheduleService.updateBatchById(upList);

        // 更新总利息数据
        DisburseData data = new DisburseData();
        data.setId(disburseData.getId());
        data.setGrossInterest(subtract);
        disburseDataMapper.updateById(data);

    }

    static BigDecimal zero = new BigDecimal("0.00");

    /**
     * 结清订单逻辑
     * @param scheduleList
     * @param pkgList
     */
    @Transactional(rollbackFor = Exception.class)
    public void cleanOrders(List<RepaySchedule> scheduleList, List<RespRepayPlanPkgVo> pkgList,DisburseData disburseData) {
        Map<String, RespRepayPlanPkgVo> collect = pkgList.stream().collect(Collectors.toMap(RespRepayPlanPkgVo::getRepayTerm, V -> V));
        List<RepaySchedule> upList = new ArrayList<>(12);
        // 还款总金额
        BigDecimal totalAmt = zero;
        for (RepaySchedule schedule : scheduleList) {
            String repayTerm = schedule.getRepayTerm();
            RepaySchedule upData = new RepaySchedule();
            upData.setId(schedule.getId());
            if (collect.containsKey(repayTerm)) {
                RespRepayPlanPkgVo pkgVo = collect.get(repayTerm);
                upData.setTotalAmt(NumberUtil.safeParseBigDecimal(pkgVo.getTotalAmt()));
                upData.setTermRetPrin(NumberUtil.safeParseBigDecimal(pkgVo.getTermRetPrin()));
                upData.setTermRetInt(NumberUtil.safeParseBigDecimal(pkgVo.getTermRetInt()));
                this.processData(upData, pkgVo);
                upData.setTermStatus(pkgVo.getTermStatus());
                upData.setSettleFlag(pkgVo.getSettleFlag());
                if (StrUtil.isNotEmpty(pkgVo.getDatePay())) {
                    upData.setPayTime(pkgVo.getDatePay());
                }
                if (StrUtil.isEmpty(schedule.getDatePayTime())) {
                    upData.setDatePay(pkgVo.getDatePay());
                }

            } else {
                upData.setTotalAmt(zero);
                upData.setTermRetPrin(zero);
                upData.setTermRetInt(zero);
                upData.setTermGuarantorFee(zero);
                upData.setTermRetFint(zero);
                upData.setTermOverdueGuarantorFee(zero);
                upData.setPrinAmt(zero);
                upData.setNoRetAmt(zero);
                upData.setIntAmt(zero);
                upData.setNoRetInt(zero);
                upData.setTermFintFinish(zero);
                upData.setNoRetFin(zero);
                upData.setGuarantorFee(zero);
                upData.setNoGuarantorFee(zero);
                upData.setTermServiceFee(zero);
                upData.setServiceFee(zero);
                upData.setNoServiceFee(zero);
                upData.setOverdueGuarantorFee(zero);
                upData.setNoOverdueGuarantorFee(zero);
                upData.setSettleFlag(SettleFlagConstant.CLOSE);
            }
            totalAmt = totalAmt.add(upData.getTotalAmt());
            upList.add(upData);
        }
        // 总利息
        BigDecimal subtract = totalAmt.subtract(disburseData.getCreditAmount());

        boolean b = repayScheduleService.updateBatchById(upList);

        DisburseData data = new DisburseData();
        data.setId(disburseData.getId());
        data.setCreditStatus(600);
        data.setGrossInterest(subtract);
        if (disburseData.getRepaymentTime() == null) {
            data.setRepaymentTime(new Date());
        }
        disburseDataMapper.updateById(data);
    }




    public void processData(RepaySchedule data,RespRepayPlanPkgVo pkgVo) {
        data.setTermGuarantorFee(NumberUtil.safeParseBigDecimal(pkgVo.getTermGuarantorFee()));
        data.setTermRetFint(NumberUtil.safeParseBigDecimal(pkgVo.getTermRetFint()));
        data.setPrinAmt(NumberUtil.safeParseBigDecimal(pkgVo.getPrinAmt()));
        data.setNoRetAmt(NumberUtil.safeParseBigDecimal(pkgVo.getNoRetAmt()));
        data.setIntAmt(NumberUtil.safeParseBigDecimal(pkgVo.getIntAmt()));
        data.setNoRetInt(NumberUtil.safeParseBigDecimal(pkgVo.getNoRetInt()));
        data.setTermFintFinish(NumberUtil.safeParseBigDecimal(pkgVo.getTermFintFinish()));
        data.setNoRetFin(NumberUtil.safeParseBigDecimal(pkgVo.getNoRetFin()));
        data.setGuarantorFee(NumberUtil.safeParseBigDecimal(pkgVo.getGuarantorFee()));
        data.setNoGuarantorFee(NumberUtil.safeParseBigDecimal(pkgVo.getNoGuarantorFee()));
        data.setTermServiceFee(NumberUtil.safeParseBigDecimal(pkgVo.getTermServiceFee()));
        data.setServiceFee(NumberUtil.safeParseBigDecimal(pkgVo.getServiceFee()));
        data.setNoServiceFee(NumberUtil.safeParseBigDecimal(pkgVo.getNoServiceFee()));

    }
}
