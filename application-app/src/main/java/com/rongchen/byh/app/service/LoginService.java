package com.rongchen.byh.app.service;

import com.rongchen.byh.app.dto.OfflineRegOrLoginDto;
import com.rongchen.byh.app.dto.RegOrLoginDto;
import com.rongchen.byh.app.dto.YzmCodeDto;
import com.rongchen.byh.app.vo.RegOrLoginVo;
import com.rongchen.byh.common.core.object.ResponseResult;

public interface LoginService {

    /**
     * 注册或登录
     * @param regOrLoginDto
     * @return
     */
    ResponseResult<RegOrLoginVo> regOrLogin(RegOrLoginDto regOrLoginDto);

    /**
     * 发送验证码
     * @param yzmCodeDto
     * @return
     */
    ResponseResult<Void> sendCode(YzmCodeDto yzmCodeDto);

    /**
     * 线下模式注册或登录
     * @param dto
     * @return
     */
    ResponseResult<RegOrLoginVo> offlineRegOrLogin(OfflineRegOrLoginDto dto);

    /**
     * 线下模式校验邀请码是否正确
     * @param dto
     * @return
     */
    ResponseResult<Void> checkInviteCode(OfflineRegOrLoginDto dto);


    ResponseResult<RegOrLoginVo> flyRegOrLogin(RegOrLoginDto regOrLoginDto);
}
