package com.rongchen.byh.app.utils;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Base64;

public class FileUtil {

    private FileUtil() {}

    /**
     * base64转MultipartFile
     * @param base64String base64字符串
     * @return
     * @throws IOException
     */
    public static MultipartFile base64ToMultipartFile(String base64String ) {
        // 解码base64字符串 （如果base64有前缀如：data:image/jpg;base64,则需要去掉前缀，我这里有前缀，所以需要去掉）
        String fileName = UUID.fastUUID().toString();
        String base64Str = "";
        // 创建字符串
        String suffix = ".png"; // 文件名
        String contentType = "image/png"; // 内容类型
        if (base64String.startsWith("data:image")) {
            String[] split = base64String.split(",");
            base64Str = split[1];
            String prx = split[0];
            String replaceData = StrUtil.replace(prx, "data:", "");
            contentType = StrUtil.replace(replaceData, ";base64", "");
            String[] split1 = contentType.split("/");
            suffix = "." + split1[1];
        } else {
            base64Str = base64String;
        }
        //有前缀
        //byte[] decode = Base64.getDecoder().decode();
        //无前缀
        byte[] decode = Base64.getDecoder().decode(base64Str);

        return new MockMultipartFile(fileName, fileName+suffix, contentType, decode);
    }
}
