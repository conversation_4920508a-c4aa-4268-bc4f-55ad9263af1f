package com.rongchen.byh.app.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rongchen.byh.app.entity.RepaySchedule;
import com.rongchen.byh.app.entity.SalePayVo;
import com.rongchen.byh.app.entity.SaleSchedule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectKey;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【sale_schedule(赊销账单表)】的数据库操作Mapper
* @createDate 2024-12-15 12:00:55
* @Entity com.rongchen.byh.app.entity.SaleSchedule
*/
@Mapper
public interface SaleScheduleMapper extends BaseMapper<SaleSchedule> {


    int insertBatch(List<SaleSchedule> list);

    SaleSchedule selectByDisburseIdAndTerm(@Param("disburseId") Long disburseId,@Param("repayTerm") String repayTerm);

    SaleSchedule selectByRepayApplyNo(String repayApplyNo);

    List<SaleSchedule> selectListOverdue(String today);

    @Select("SELECT count(*) FROM sale_schedule WHERE disburse_id = #{disburseId} AND settle_flag = 'REPAYING'")
    int payingCount(Long disburseId);

    SaleSchedule selectLast(SaleSchedule saleSchedule);

    List<SalePayVo> repaying();

    List<SaleSchedule> selectNotRepayList();

    List<Long> selectDisburseIdList(@Param("autoRepay") int autoRepay);

    List<SaleSchedule> selectByDisburseId(@Param("disburseId") Long disburseId);
}
