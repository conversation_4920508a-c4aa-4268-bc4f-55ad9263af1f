<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.dao.SaleScheduleMapper">

    <resultMap id="BaseResultMap" type="com.rongchen.byh.app.entity.SaleSchedule">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="disburseId" column="disburse_id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="repayApplyNo" column="repay_apply_no" jdbcType="VARCHAR"/>
            <result property="repayTerm" column="repay_term" jdbcType="VARCHAR"/>
            <result property="repayOwnbDate" column="repay_ownb_date" jdbcType="VARCHAR"/>
            <result property="repayOwneDate" column="repay_owne_date" jdbcType="VARCHAR"/>
            <result property="repayIntbDate" column="repay_intb_date" jdbcType="VARCHAR"/>
            <result property="repayInteDate" column="repay_inte_date" jdbcType="VARCHAR"/>
            <result property="totalAmt" column="total_amt" jdbcType="DECIMAL"/>
            <result property="termRetPrin" column="term_ret_prin" jdbcType="DECIMAL"/>
            <result property="termRetInt" column="term_ret_int" jdbcType="DECIMAL"/>
            <result property="termRetFint" column="term_ret_fint" jdbcType="DECIMAL"/>
            <result property="termStatus" column="term_status" jdbcType="VARCHAR"/>
            <result property="settleFlag" column="settle_flag" jdbcType="VARCHAR"/>
            <result property="datePay" column="date_pay" jdbcType="VARCHAR"/>
            <result property="payTime" column="pay_time" jdbcType="VARCHAR"/>
            <result property="datePayTime" column="date_pay_time" jdbcType="VARCHAR"/>
            <result property="vipDeratePrin" column="vip_derate_prin" jdbcType="DECIMAL"/>
            <result property="deratePrin" column="derate_prin" jdbcType="DECIMAL"/>
            <result property="autoRepay" column="auto_repay" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,disburse_id,user_id,
        repay_apply_no,repay_term,repay_ownb_date,
        repay_owne_date,repay_intb_date,repay_inte_date,
        total_amt,term_ret_prin,term_ret_int,
        term_ret_fint,term_status,settle_flag,pay_time
        date_pay,date_pay_time,auto_repay,create_time,
        update_time
    </sql>
    <insert id="insertBatch">
        INSERT INTO sale_schedule (
            disburse_id,user_id,
            repay_apply_no,repay_term,repay_ownb_date,
            repay_owne_date,repay_intb_date,repay_inte_date,
            total_amt,term_ret_prin,term_ret_int,
            term_ret_fint,term_status,settle_flag,
            date_pay,date_pay_time,auto_repay
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.disburseId}, #{item.userId}, #{item.repayApplyNo}, #{item.repayTerm}, #{item.repayOwnbDate}, #{item.repayOwneDate},
            #{item.repayIntbDate}, #{item.repayInteDate}, #{item.totalAmt}, #{item.termRetPrin}, #{item.termRetInt}, #{item.termRetFint},
            #{item.termStatus}, #{item.settleFlag}, #{item.datePay}, #{item.datePayTime},#{item.autoRepay})
        </foreach>
    </insert>
    <select id="selectByDisburseIdAndTerm" resultType="com.rongchen.byh.app.entity.SaleSchedule">
        select <include refid="Base_Column_List"/>
        from sale_schedule
        where disburse_id = #{disburseId} and repay_term = #{repayTerm}
    </select>
    <select id="selectByRepayApplyNo" resultType="com.rongchen.byh.app.entity.SaleSchedule">
        select <include refid="Base_Column_List"/>
        from sale_schedule
        where repay_apply_no = #{repayApplyNo}
    </select>
    <select id="selectListOverdue" resultType="com.rongchen.byh.app.entity.SaleSchedule">
        select *
        from sale_schedule
        where repay_owne_date &lt; #{today} and settle_flag = 'RUNNING' and auto_repay = 1
    </select>
    <select id="selectLast" resultType="com.rongchen.byh.app.entity.SaleSchedule">
        select *
        from sale_schedule
       <where>
           <if test="userId != null"> and user_id = #{userId}</if>
           <if test="repayTerm != null"> and repay_term = #{repayTerm}</if>
       </where>
        order by id desc limit 1
    </select>
    <select id="repaying" resultType="com.rongchen.byh.app.entity.SalePayVo">
        select
            sale_schedule.id as saleScheduleId,
            sale_schedule.disburse_id as disburseId,
            sale_schedule.repay_apply_no as repayApplyNo,
            disburse_data.sale_no as saleNo,
            disburse_data.capital_id as capitalId
        from sale_schedule
        left join disburse_data on sale_schedule.disburse_id = disburse_data.id
        where sale_schedule.settle_flag = 'REPAYING'
    </select>
    <select id="selectNotRepayList" resultType="com.rongchen.byh.app.entity.SaleSchedule">
        select ss.*
        from sale_schedule ss
                 inner join (select disburse_id, repay_term from repay_schedule where settle_flag = 'CLOSE') rs on ss.disburse_id = rs.disburse_id and ss.repay_term = rs.repay_term
                 inner join (select id from disburse_data where capital_id = 1) dd on ss.disburse_id = dd.id
        where ss.repay_owne_date = CURDATE() and ss.settle_flag = 'RUNNING' and ss.auto_repay = 1
    </select>

    <select id="selectByDisburseId" resultType="com.rongchen.byh.app.entity.SaleSchedule">
        select *
        from sale_schedule
        where disburse_id = #{disburseId}
    </select>
    <select id="selectDisburseIdList" resultType="java.lang.Long">
        select distinct disburse_id
        from sale_schedule
        where auto_repay = #{autoRepay}
    </select>
</mapper>
