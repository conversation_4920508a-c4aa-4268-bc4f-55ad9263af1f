package com.rongchen.byh.app.api.service.strategy.process;

import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 外部api流程
 * @date 2025/3/20 11:39:02
 */
public abstract class OutApiAbstractProcess implements CreditProcess, LoanProcess, RepayProcess, SaleProcess {
    public abstract String getChannel();

    protected abstract JSONObject parseParam(JSONObject param);

    protected abstract JSONObject buildResult(JSONObject result);
}
