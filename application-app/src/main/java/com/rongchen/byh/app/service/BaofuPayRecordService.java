package com.rongchen.byh.app.service;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rongchen.byh.app.entity.BaofuPayRecord;
import com.rongchen.byh.app.dao.BaofuPayRecordMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;

/**
 * 
 * 项目名称：byh_java
 * 文件名称: BaofuPayRecordService
 * 创建时间: 2025-03-12 16:09
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.app.service
 * 文件描述: 宝付支付记录服务
 * Copyright (c) 2025 byh_java All rights reserved.
 */
@Service
@Slf4j
public class BaofuPayRecordService extends ServiceImpl<BaofuPayRecordMapper, BaofuPayRecord> {

    /**
     * 根据主键更新
     * 
     * @param record 记录
     * @return 更新结果
     */
    public int updateByPrimaryKey(BaofuPayRecord record) {
        return baseMapper.updateByPrimaryKey(record);
    }

    /**
     * 根据用户ID和交易ID查询支付记录
     *
     * @param userId  用户ID
     * @param transId 交易ID
     * @return 支付记录，如果不存在则返回null
     */
    public BaofuPayRecord getByUserIdAndTransId(String userId, String transId) {
        BaofuPayRecord record = null;

        try {
            // 先尝试使用自定义mapper方法
            record = this.lambdaQuery()
                    .eq(BaofuPayRecord::getUserId, userId)
                    .eq(BaofuPayRecord::getTransId, transId)
                    .one();
        } catch (Exception e) {
            // 如果自定义方法不存在或出错，记录日志
            log.warn("调用selectByUserIdAndTransId方法失败，将使用通用查询：{}", e.getMessage());
        }

        // 如果自定义方法返回null或异常，使用通用查询
        if (record == null) {
            LambdaQueryWrapper<BaofuPayRecord> queryWrapper = new LambdaQueryWrapper<BaofuPayRecord>()
                    .eq(BaofuPayRecord::getUserId, userId)
                    .eq(BaofuPayRecord::getTransId, transId);
            record = this.getOne(queryWrapper);
        }

        return record;
    }
}
