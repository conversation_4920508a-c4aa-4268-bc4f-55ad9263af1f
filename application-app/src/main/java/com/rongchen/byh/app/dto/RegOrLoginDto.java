package com.rongchen.byh.app.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;


@Schema(description = "注册登录")
@Data
public class RegOrLoginDto extends YzmCodeDto{

    @Schema(description = "验证码")
    @NotEmpty(message = "验证码不能为空")
    private String code;

    @Schema(description = "渠道")
    @NotEmpty(message = "渠道不能为空")
    private String channel;

    @Schema(description = "推荐码")
    private String inviteCode;

}
