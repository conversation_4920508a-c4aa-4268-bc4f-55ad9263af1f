package com.rongchen.byh.app.service;

import com.rongchen.byh.app.entity.UserDetail;
import com.rongchen.byh.common.api.bankCredit.dto.HrzxAuthDto;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 网络文件处理
 * @date 2025/2/24 12:26:14
 */
public interface NetworkFileDealService {
    boolean dealFaceImage(UserDetail detail, HrzxAuthDto hrzxAuthDto);

    boolean dealProtocolFile(UserDetail detail, HrzxAuthDto hrzxAuthDto);

    boolean checkFaceImage(UserDetail detail, HrzxAuthDto hrzxAuthDto);

    boolean checkProtocolFile(UserDetail detail, HrzxAuthDto hrzxAuthDto);
}
