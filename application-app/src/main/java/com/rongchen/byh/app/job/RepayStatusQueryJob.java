package com.rongchen.byh.app.job;

import com.rongchen.byh.app.dao.RepayScheduleApplyMapper;
import com.rongchen.byh.app.dto.RepayApplyWithCapitalDto;
import com.rongchen.byh.app.entity.CapitalData;
import com.rongchen.byh.app.entity.RepayScheduleApply;
import com.rongchen.byh.app.service.RepayScheduleApplyService;
import com.rongchen.byh.common.core.util.MDCUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.time.LocalDateTime;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 账单还款状态查询定时任务 - (Refactored)
 */
@Component
@Slf4j
public class RepayStatusQueryJob {

    // 查询间隔，单位：分钟
    private static final int QUERY_INTERVAL_MINUTES = 5;

    @Resource
    private RepayScheduleApplyMapper repayScheduleApplyMapper;
    @Resource
    private RepayScheduleApplyService repayScheduleApplyService;

    @XxlJob("repayQueryHandler")
    public void repayQueryHandler() {
        String traceId = MDCUtil.setTraceId();
        MDCUtil.setTraceId(traceId);
        log.info("【还款状态查询任务】开始执行... Trace ID: {}", traceId);

        try {
            LocalDateTime cutoffTime = LocalDateTime.now().minusMinutes(QUERY_INTERVAL_MINUTES);
            log.info("【还款状态查询任务】查询 {} 之前创建的状态为待处理(0)的还款申请记录", cutoffTime);

            List<RepayApplyWithCapitalDto> pendingApplies;
            try {
                pendingApplies = repayScheduleApplyMapper.selectPendingAppliesWithCapital(cutoffTime);
            } catch (Exception e) {
                log.error("【还款状态查询任务】查询待处理还款申请记录时发生数据库异常", e);
                return;
            }

            if (CollectionUtils.isEmpty(pendingApplies)) {
                log.info("【还款状态查询任务】未查询到 {} 分钟前创建的待处理还款申请记录", QUERY_INTERVAL_MINUTES);
                return;
            }

            log.info("【还款状态查询任务】查询到 {} 条待处理的还款申请记录，准备处理...", pendingApplies.size());

            int successCount = 0;
            int failureCount = 0;

            for (RepayApplyWithCapitalDto dto : pendingApplies) {
                RepayScheduleApply applyRecord = dto.getApplyRecord();
                CapitalData capitalData = dto.getCapitalData();

                if (applyRecord == null || capitalData == null) {
                    log.error("【还款状态查询任务】查询到的 DTO 缺少必要信息，跳过处理。DTO: {}", dto);
                    failureCount++;
                    continue;
                }

                String repayApplyNo = applyRecord.getRepayApplyNo();
                try {
                    log.info("【还款状态查询任务】开始处理还款申请号: {}", repayApplyNo);
                    repayScheduleApplyService.queryAndUpdateRepaymentStatus(applyRecord, capitalData);
                    log.info("【还款状态查询任务】处理还款申请号: {} 完成", repayApplyNo);
                    successCount++;
                } catch (Exception e) {
                    log.error("【还款状态查询任务】处理还款申请号: {} 时发生异常", repayApplyNo, e);
                    failureCount++;
                }
            }

            log.info("【还款状态查询任务】处理完成。成功处理: {} 条，失败/跳过: {} 条", successCount, failureCount);

        } finally {
            log.info("【还款状态查询任务】执行结束。Trace ID: {}", traceId);
            MDCUtil.remove(MDCUtil.Keys.TRACE_ID);
        }
    }
}
