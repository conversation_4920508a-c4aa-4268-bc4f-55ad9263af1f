package com.rongchen.byh.app.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rongchen.byh.app.dao.RepayScheduleMapper;
import com.rongchen.byh.app.entity.RepayPayVo;
import com.rongchen.byh.app.entity.RepaySchedule;
import java.util.List;
import org.springframework.stereotype.Service;
/**

 * 项目名称：byh_java
 * 文件名称: RepayScheduleService
 * 创建时间: 2025-04-05 15:42
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.app.service
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
@Service
public class RepayScheduleService extends ServiceImpl<RepayScheduleMapper, RepaySchedule> {

    
    public int insertSelective(RepaySchedule record) {
        return baseMapper.insertSelective(record);
    }
    
    public int updateByPrimaryKeySelective(RepaySchedule record) {
        return baseMapper.updateByPrimaryKeySelective(record);
    }
    
    public int updateByPrimaryKey(RepaySchedule record) {
        return baseMapper.updateByPrimaryKey(record);
    }
    
    public int updateBatch(List<RepaySchedule> list) {
        return baseMapper.updateBatch(list);
    }
    
    public int updateBatchSelective(List<RepaySchedule> list) {
        return baseMapper.updateBatchSelective(list);
    }
    
    public int batchInsert(List<RepaySchedule> list) {
        return baseMapper.batchInsert(list);
    }
    

    
    public int batchInsertOrUpdate(List<RepaySchedule> list) {
        return baseMapper.batchInsertOrUpdate(list);
    }

   public List<RepayPayVo> repaying() {
        return baseMapper.repaying();
    }
}
