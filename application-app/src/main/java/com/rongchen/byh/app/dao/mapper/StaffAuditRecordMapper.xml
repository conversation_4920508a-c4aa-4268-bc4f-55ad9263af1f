<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.dao.StaffAuditRecordMapper">

    <resultMap id="BaseResultMap" type="com.rongchen.byh.app.entity.StaffAuditRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userStaffId" column="user_staff_id" jdbcType="BIGINT"/>
            <result property="submitTime" column="submit_time" jdbcType="TIMESTAMP"/>
            <result property="sesameImages" column="sesame_images" jdbcType="VARCHAR"/>
            <result property="wechatImages" column="wechat_images" jdbcType="VARCHAR"/>
            <result property="reportImages" column="report_images" jdbcType="VARCHAR"/>
            <result property="auditStatus" column="audit_status" jdbcType="INTEGER"/>
            <result property="creditAmount" column="credit_amount" jdbcType="INTEGER"/>
            <result property="refuseReason" column="refuse_reason" jdbcType="VARCHAR"/>
            <result property="auditTime" column="audit_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_staff_id,submit_time,
        sesame_images,wechat_images,report_images,
        audit_status,credit_amount,refuse_reason,
        audit_time,create_time,update_time
    </sql>
</mapper>
