package com.rongchen.byh.app.controller;


import cn.dev33.satoken.annotation.SaIgnore;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.google.common.base.Charsets;
import com.google.common.hash.Hashing;
import com.rongchen.byh.app.dao.SysConfigMapper;
import com.rongchen.byh.app.dto.SignGenDto;
import com.rongchen.byh.app.entity.SysConfig;
import com.rongchen.byh.common.core.annotation.DisableDataFilter;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.HttpUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;

@ApiSupport(order = 1)
@Tag(name = "签名生成")
@DisableDataFilter
@Slf4j
@RestController
@RequestMapping("/userApi")
public class SignGenController {

    @Resource
    SysConfigMapper sysConfigMapper;

    @PostMapping("/signGen")
    @SaIgnore
    public ResponseResult<String> signGen(@RequestBody SignGenDto signGenDto) {
        if (signGenDto.getValues() == null) {
            throw new NullPointerException("values is null");
        }
        List<String> values = signGenDto.getValues();
        String ticket = signGenDto.getTicket();
        values.removeAll(Collections.singleton(null));// remove null
        values.add(ticket);
        java.util.Collections.sort(values);

        StringBuilder sb = new StringBuilder();
        for (String s : values) {
            sb.append(s);
        }
        String res = Hashing.sha1().hashString(sb, Charsets.UTF_8).toString().toUpperCase();

        return ResponseResult.success(res);
    }

    /**
     * 查询app是否需要开启客服
     * @return
     * 1-开启
     * 0-关闭
     */
    @PostMapping("/queryCustomerOpen")
    @SaIgnore
    public ResponseResult<String> queryCustomerOpen() {
        String flag = sysConfigMapper.selectByConfigKey("customerOpenFlag");
        String s = Optional.ofNullable(flag).orElse("1");
        return ResponseResult.success(s);
    }

    public static void main(String[] args) {
        String APPID = "IDAAoO3M";
        String SECRET = "FshEBgEqp3dYJmSmxeic1B4UdzCCui0MPGe9cvmoVVzGIVbApEU2dhXzLenXbE3g";
        String VERSION = "1.0.0";
        String s = HttpUtil.get("https://kyc1.qcloud.com/api/oauth2/access_token?appId="+APPID+"&secret="+SECRET+"&grant_type=client_credential&version=1.0.0");
        JSONObject jsonObject = JSONObject.parseObject(s);
        String token = jsonObject.getString("access_token");
        String s1 = HttpUtil.get("https://kyc1.qcloud.com/api/oauth2/api_ticket?appId="+APPID+"&access_token="+token+"&type=SIGN&version=1.0.0");
        JSONObject jsonObject1 = JSONObject.parseObject(s1);
        Object tickets = jsonObject1.getJSONArray("tickets").get(0);
        JSONObject jsonObject2 = JSONObject.parseObject(tickets.toString());
        String ticket = jsonObject2.getString("value");
        List<String> list = new ArrayList<>();
        list.add(APPID);
        list.add("tkySzS9dXDSoi17jmA3TxYXMf6ae2NUi");
        list.add(VERSION);
        list.add(ticket);
        String nonce = UUID.randomUUID().toString().replace("-", "");
        System.out.println();
        list.add(nonce);
        if (list == null) {
            throw new NullPointerException("values is null");
        }
        list.removeAll(Collections.singleton(null));// remove null
        java.util.Collections.sort(list);
        StringBuilder sb = new StringBuilder();
        for (String str : list) {
            sb.append(str);
        }
        String res = Hashing.sha1().hashString(sb, Charsets.UTF_8).toString().toUpperCase();
        System.out.println(res);
        JSONObject param = new JSONObject();
        param.put("appId", APPID);
        param.put("version", VERSION);
        param.put("nonce", nonce);
        param.put("orderNo", "tkySzS9dXDSoi17jmA3TxYXMf6ae2NUi");
        param.put("sign", res);
        param.put("getFile", "2");
        String ret = HttpUtil.postJson("https://kyc1.qcloud.com/api/v2/base/queryfacerecord?orderNo=tkySzS9dXDSoi17jmA3TxYXMf6ae2NUi", param);
        System.out.println(ret);
    }
}
