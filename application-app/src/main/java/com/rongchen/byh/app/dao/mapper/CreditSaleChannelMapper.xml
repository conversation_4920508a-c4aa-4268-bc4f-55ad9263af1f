<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.dao.SaleOrderRecordMapper">
  <resultMap id="BaseResultMap" type="com.rongchen.byh.app.entity.SaleOrderRecord">
    <!--@mbg.generated-->
    <!--@Table `sale_order_record`-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sale_channel" jdbcType="VARCHAR" property="saleChannel" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="sale_no" jdbcType="VARCHAR" property="saleNo" />
    <result column="out_sale_no" jdbcType="VARCHAR" property="outSaleNo" />
    <result column="payment_no" jdbcType="VARCHAR" property="paymentNo" />
    <result column="order_amount" jdbcType="DECIMAL" property="orderAmount" />
    <result column="user_mobile" jdbcType="VARCHAR" property="userMobile" />
    <result column="pay_way" jdbcType="VARCHAR" property="payWay" />
    <result column="coupon_package_id" jdbcType="VARCHAR" property="couponPackageId" />
    <result column="coupon_package_name" jdbcType="VARCHAR" property="couponPackageName" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime" />
    <result column="order_status" jdbcType="VARCHAR" property="orderStatus" />
    <result column="refundable" jdbcType="BOOLEAN" property="refundable" />
    <result column="return_amount" jdbcType="DECIMAL" property="returnAmount" />
    <result column="retry_num" jdbcType="BIGINT" property="retryNum" />
    <result column="return_time" jdbcType="TIMESTAMP" property="returnTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `id`, `sale_channel`, `user_id`, `sale_no`, `out_sale_no`, `payment_no`, `order_amount`, 
    `user_mobile`, `pay_way`, `coupon_package_id`, `coupon_package_name`, `pay_time`, 
    `expire_time`, `order_status`, `refundable`, `return_amount`, `retry_num`, `return_time`, 
    `create_time`, `update_time`
  </sql>
  <update id="updateByPrimaryKey" parameterType="com.rongchen.byh.app.entity.SaleOrderRecord">
    <!--@mbg.generated-->
    update `sale_order_record`
    set `sale_channel` = #{saleChannel,jdbcType=VARCHAR},
      `user_id` = #{userId,jdbcType=VARCHAR},
      `sale_no` = #{saleNo,jdbcType=VARCHAR},
      `out_sale_no` = #{outSaleNo,jdbcType=VARCHAR},
      `payment_no` = #{paymentNo,jdbcType=VARCHAR},
      `order_amount` = #{orderAmount,jdbcType=DECIMAL},
      `user_mobile` = #{userMobile,jdbcType=VARCHAR},
      `pay_way` = #{payWay,jdbcType=VARCHAR},
      `coupon_package_id` = #{couponPackageId,jdbcType=VARCHAR},
      `coupon_package_name` = #{couponPackageName,jdbcType=VARCHAR},
      `pay_time` = #{payTime,jdbcType=TIMESTAMP},
      `expire_time` = #{expireTime,jdbcType=TIMESTAMP},
      `order_status` = #{orderStatus,jdbcType=VARCHAR},
      `refundable` = #{refundable,jdbcType=BOOLEAN},
      `return_amount` = #{returnAmount,jdbcType=DECIMAL},
      `retry_num` = #{retryNum,jdbcType=BIGINT},
      `return_time` = #{returnTime,jdbcType=TIMESTAMP},
      `create_time` = #{createTime,jdbcType=TIMESTAMP},
      `update_time` = #{updateTime,jdbcType=TIMESTAMP}
    where `id` = #{id,jdbcType=BIGINT}
  </update>
</mapper>