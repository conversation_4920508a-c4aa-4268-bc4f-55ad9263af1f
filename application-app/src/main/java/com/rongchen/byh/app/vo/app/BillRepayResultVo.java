package com.rongchen.byh.app.vo.app;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 账单还款结果vo
 * @date 2024/12/14 17:11:54
 */
@Data
public class BillRepayResultVo {
    @Schema(description = "本息还款主键id")
    private Long repayScheduleId;

    @Schema(description = "赊销还款主键id")
    private Long saleScheduleId;

    @Schema(description = "0 还款中 1 还款成功 2 还款失败")
    private Integer repaymentResult;

    @Schema(description = "还款总金额")
    private BigDecimal repaymentAmount;

    @Schema(description = "还款发起时间")
    private String payTime;

    @Schema(description = "实际还款日期")
    private String datePayTime;

    @Schema(description = "还款账户")
    private String bankName;

    @Schema(description = "银行卡卡号")
    private String bankAccount;


}
