<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.dao.ApiRiskFailRecordMapper">

    <resultMap id="BaseResultMap" type="com.rongchen.byh.app.entity.ApiRiskFailRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="apiId" column="api_id" jdbcType="BIGINT"/>
            <result property="applyId" column="apply_id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="creditNo" column="credit_no" jdbcType="VARCHAR"/>
            <result property="creditId" column="credit_id" jdbcType="VARCHAR"/>
            <result property="requestParam" column="request_param" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,api_id,apply_id,
        user_id,credit_no,credit_id,
        request_param,create_time
    </sql>
    <select id="getInProcessList" resultType="com.rongchen.byh.app.entity.ApiRiskFailRecord">
        select
        <include refid="Base_Column_List"/>
        from api_risk_fail_record where apply_id in (select id from user_loan_apply where audits_status = 0 and create_time > DATE_SUB(CURDATE(), INTERVAL #{day} DAY) and credit_id is not null)
    </select>
</mapper>
