package com.rongchen.byh.app.api.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.app.api.service.strategy.factory.OutApiFactory;
import com.rongchen.byh.app.api.service.strategy.process.OutApiAbstractProcess;
import com.rongchen.byh.common.core.annotation.DisableDataFilter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 *  api用信通知接口
 */
@Tag(name = "api用信通知接口")
@DisableDataFilter
@Slf4j
@RestController
@RequestMapping("/outApi")
@SaIgnore
public class ApiLoanController {
    @Resource
    private OutApiFactory outApiFactory;
    /**
     * 资方用信结果通知接口
     */
    @PostMapping("/{channel}/loan")
    public JSONObject loanNotice(@PathVariable("channel") String channel, @RequestBody String request) {
        try {
            log.info("api用信通知,渠道：{}，请求参数：{}", channel, request);
            JSONObject bizMap = JSONObject.parseObject(request);
            OutApiAbstractProcess outApiAbstractProcess = outApiFactory.getOutApiAbstractProcess(channel);
            if (ObjectUtil.isEmpty(outApiAbstractProcess)) {
                log.error("未找到对应的渠道");
                JSONObject jsonObject = new JSONObject();
                return jsonObject;
            }
            return outApiAbstractProcess.loan(bizMap);
        } catch (Exception e) {
            log.error("【api授信解耦】 失败", e);
        }
        return new JSONObject();
    }
}
