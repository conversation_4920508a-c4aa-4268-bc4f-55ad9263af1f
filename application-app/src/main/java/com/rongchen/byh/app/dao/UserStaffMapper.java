package com.rongchen.byh.app.dao;

import com.rongchen.byh.app.entity.UserStaff;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【user_staff(用户员工绑定关系表)】的数据库操作Mapper
* @createDate 2025-02-07 16:14:18
* @Entity com.rongchen.byh.app.entity.UserStaff
*/
@Mapper
public interface UserStaffMapper extends BaseMapper<UserStaff> {
    UserStaff selectByUserId(@Param("userId") Long userId);
}




