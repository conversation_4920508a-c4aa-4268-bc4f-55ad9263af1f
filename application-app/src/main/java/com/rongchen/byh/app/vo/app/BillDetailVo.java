package com.rongchen.byh.app.vo.app;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 账单详情vo
 * @date 2024/12/14 16:41:00
 */
@Data
public class  BillDetailVo {

    @Schema(description = "剩余代还款金额")
    private BigDecimal creditAmount;

    @Schema(description = "待还款笔数")
    private Integer repayNum;

    @Schema(description = "本金总金额")
    private BigDecimal principalAmount;

    @Schema(description = "利息总金额")
    private BigDecimal interestAmount;

    @Schema(description = "赊销总金额")
    private BigDecimal saleAmount;

    @Schema(description = "已还款列表")
    List<PlanVo> repaidList;

    @Schema(description = "未还款列表")
    List<PlanVo> unRepaidList;

}
