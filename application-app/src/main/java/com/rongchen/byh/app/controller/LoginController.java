package com.rongchen.byh.app.controller;


import cn.dev33.satoken.annotation.SaIgnore;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.rongchen.byh.app.dto.RegOrLoginDto;
import com.rongchen.byh.app.dto.YzmCodeDto;
import com.rongchen.byh.app.service.LoginService;
import com.rongchen.byh.app.vo.RegOrLoginVo;
import com.rongchen.byh.common.core.annotation.DisableDataFilter;
import com.rongchen.byh.common.core.object.ResponseResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@ApiSupport(order = 1)
@Tag(name = "用户登录注册相关接口")
@DisableDataFilter
@Slf4j
@RestController
@RequestMapping("/userApi")
public class LoginController {

    @Resource
    private LoginService loginService;

    @Operation(summary = "用户注册或登录")
    @SaIgnore
    @PostMapping("/regOrLogin")
    public ResponseResult<RegOrLoginVo> regOrLogin(@RequestBody @Validated RegOrLoginDto regOrLoginDto) {
        return loginService.regOrLogin(regOrLoginDto);
    }


    @Operation(summary = "短信验证码获取")
    @SaIgnore
    @PostMapping("/sendCode")
    public ResponseResult<Void> sendCode(@RequestBody @Validated YzmCodeDto yzmCodeDto) {
        return loginService.sendCode(yzmCodeDto);
    }

    @Operation(summary = "空中飞行模式用户注册或登录")
    @SaIgnore
    @PostMapping("/fly/regOrLogin")
    public ResponseResult<RegOrLoginVo> flyRegOrLogin(@RequestBody @Validated RegOrLoginDto regOrLoginDto) {
        return loginService.flyRegOrLogin(regOrLoginDto);
    }
}
