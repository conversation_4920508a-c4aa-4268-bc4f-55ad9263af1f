package com.rongchen.byh.app.loveSign.strategy;

import com.ancun.netsign.model.ContractUserInput;
import com.ancun.netsign.model.UserSignStrategyInput;
import com.rongchen.byh.app.loveSign.LoveSignProperties;
import com.rongchen.byh.app.loveSign.SignDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/12/13 16:40:15
 */
@Service
public class PersonSignStrategy implements SignStrategy{
    @Resource
    private LoveSignProperties properties;
    @Override
    public List<ContractUserInput> getInputList(SignDto dto) {
        List<ContractUserInput> inputs = new ArrayList<>(2);
        ContractUserInput inputUser = new ContractUserInput();
        inputUser.setContractNo(dto.getContractNo());
        inputUser.setAccount(dto.getAccount());
        inputUser.setSignType(2);
        List<UserSignStrategyInput> signStrategyList = new ArrayList<>(1);
        UserSignStrategyInput strategyInput = new UserSignStrategyInput();
        strategyInput.setAttachNo(1);
        strategyInput.setLocationMode(4);
        strategyInput.setCanDrag(11);

        strategyInput.setSignKey("signUserNameA");
        strategyInput.setSignType(1);
        signStrategyList.add(strategyInput);
        UserSignStrategyInput strategyDateInput = new UserSignStrategyInput();
        strategyDateInput.setAttachNo(1);
        strategyDateInput.setLocationMode(4);
        strategyDateInput.setCanDrag(11);

        strategyDateInput.setSignKey("signDateA");
        strategyDateInput.setSignType(1);
        signStrategyList.add(strategyDateInput);

        UserSignStrategyInput strategyUserInput = new UserSignStrategyInput();
        strategyUserInput.setAttachNo(1);
        strategyUserInput.setLocationMode(4);
        strategyUserInput.setCanDrag(11);
        strategyUserInput.setSignKey("signUserNameB");
        strategyUserInput.setSignType(1);
        signStrategyList.add(strategyUserInput);

        UserSignStrategyInput strategyDateAInput = new UserSignStrategyInput();
        strategyDateAInput.setAttachNo(1);
        strategyDateAInput.setLocationMode(4);
        strategyDateAInput.setCanDrag(11);
        // signDate
        strategyDateAInput.setSignKey("signDateB");
        strategyDateAInput.setSignType(1);
        signStrategyList.add(strategyDateAInput);


        inputUser.setSignStrategyList(signStrategyList);
        inputs.add(inputUser);

//        ContractUserInput inputCompany = new ContractUserInput();
//        inputCompany.setContractNo(dto.getContractNo());
//        inputCompany.setAccount(properties.getCompanyAccount());
//        inputCompany.setSignType(2);
//        List<UserSignStrategyInput> signStrategyListCompany = new ArrayList<>(2);
//
//        UserSignStrategyInput strategyCompany2 = new UserSignStrategyInput();
//        strategyCompany2.setAttachNo(1);
//        strategyCompany2.setLocationMode(4);
//        strategyCompany2.setCanDrag(11);
//        strategyCompany2.setSignKey("company");
//        signStrategyListCompany.add(strategyCompany2);
//        inputCompany.setSignStrategyList(signStrategyListCompany);
        return inputs;
    }

    @Override
    public boolean match(String templateNo) {
        return properties.getPerson().equals(templateNo);
    }
}
