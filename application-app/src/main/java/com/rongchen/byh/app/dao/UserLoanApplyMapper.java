package com.rongchen.byh.app.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rongchen.byh.app.entity.UserLoanApply;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface UserLoanApplyMapper extends BaseMapper<UserLoanApply> {

    UserLoanApply selectByUserIdAndType(Long userId, Integer applyType, Integer onlineType);

    UserLoanApply selectByUserIdAndTypeStatus(Long userId, Integer applyType, String onlineType);

    UserLoanApply selectByCreditId(String creditId);

    List<UserLoanApply> selectInProcessList(Integer day);
}