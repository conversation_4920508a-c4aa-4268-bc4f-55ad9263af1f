package com.rongchen.byh.app.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "注册登录VO视图对象")
@Data
public class RegOrLoginVo {

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "token信息")
    private String token;

    @Schema(description = "线上模式：是否提交申请 0 否  1 是 空中放款：状态 0 新用户 1 通过身份证 2 通过OCR ")
    private Integer applyStatus;

    @Schema(description = "渠道id")
    private Long channelId;
}
