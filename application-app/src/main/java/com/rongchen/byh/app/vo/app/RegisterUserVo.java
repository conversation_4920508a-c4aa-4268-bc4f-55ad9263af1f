package com.rongchen.byh.app.vo.app;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class RegisterUserVo {

    @Schema(description = "用户token")
    private String token;

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "渠道id")
    private Long channelId;

    @Schema(description = "是否授信 0 未授信 1 已授信 ")
    private Integer isCredit;

    @Schema(description = "授信额度 ")
    private BigDecimal creditAmount;

    @Schema(description = "用户手机号")
    private String mobile;

    @Schema(description = "产品code")
    private String productCode;



}
