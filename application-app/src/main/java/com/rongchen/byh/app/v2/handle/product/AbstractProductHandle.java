package com.rongchen.byh.app.v2.handle.product;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.ancun.netsign.client.NetSignClient;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.rongchen.byh.app.config.FileUtilService;
import com.rongchen.byh.app.constant.LoanType;
import com.rongchen.byh.app.dao.*;
import com.rongchen.byh.app.dto.app.*;
import com.rongchen.byh.app.dto.h5.AirRiskControllerDto;
import com.rongchen.byh.app.dto.h5.UserCheckLoanApplyDto;
import com.rongchen.byh.app.entity.*;
import com.rongchen.byh.app.exceptions.BusinessException;
import com.rongchen.byh.app.loveSign.LoveSignProperties;
import com.rongchen.byh.app.loveSign.LoveSignService;
import com.rongchen.byh.app.loveSign.SignDto;
import com.rongchen.byh.app.loveSign.SignRulesDto;
import com.rongchen.byh.app.service.AppUserDetailService;
import com.rongchen.byh.app.service.CreditService;
import com.rongchen.byh.app.service.NetworkFileDealService;
import com.rongchen.byh.app.utils.FileUtil;
import com.rongchen.byh.app.utils.UserTokenUtil;
import com.rongchen.byh.app.v2.async.AsyncMethod;
import com.rongchen.byh.app.v2.common.FinalParam;
import com.rongchen.byh.app.v2.common.IdCardUtil;
import com.rongchen.byh.app.v2.common.RedisEnum;
import com.rongchen.byh.app.v2.dao.ProductDataMapper;
import com.rongchen.byh.app.v2.dao.ProductPageMenuMapper;
import com.rongchen.byh.app.v2.dao.UserProgressMapper;
import com.rongchen.byh.app.v2.entity.AgreementData;
import com.rongchen.byh.app.v2.entity.ProductPageMenu;
import com.rongchen.byh.app.vo.RegOrLoginVo;
import com.rongchen.byh.app.vo.app.SubmitUserFormVo;
import com.rongchen.byh.app.vo.h5.UserCheckLoanApplyVo;
import com.rongchen.byh.common.api.idCardVerify.dto.OcrDto;
import com.rongchen.byh.common.api.idCardVerify.service.IdCardOcrService;
import com.rongchen.byh.common.api.idCardVerify.service.IdCardVerifyService;
import com.rongchen.byh.common.api.idCardVerify.vo.OcrVo;
import com.rongchen.byh.common.api.idCardVerify.vo.VerifyVo;
import com.rongchen.byh.common.api.riskControl.service.RiskControlService;
import com.rongchen.byh.common.api.sms.service.SmsService;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.MyModelUtil;
import com.rongchen.byh.common.rabbitmq.constants.QueueConstant;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public abstract class AbstractProductHandle implements ProductHandleFactory {

    @Resource
    protected UserStaffMapper userStaffMapper;
    @Resource
    protected StaffDataMapper staffDataMapper;

    @Resource
    protected UserLoanApplyMapper userLoanApplyMapper;

    @Resource
    protected UserDetailMapper userDetailMapper;

    @Resource
    protected UserDataMapper userDataMapper;

    @Resource
    protected IdCardVerifyService idCardVerifyService;

    @Resource
    protected RabbitTemplate rabbitTemplate;

    @Resource
    protected CreditService creditService;

    @Resource
    protected HrzxCreditLogMapper hrzxCreditLogMapper;
    @Resource
    protected RedissonClient redissonClient;

    @Resource
    protected DisburseDataMapper disburseDataMapper;

    @Resource
    protected NetworkFileDealService networkFileDealService;

    @Resource
    protected UserCreditDataMapper userCreditDataMapper;
    @Resource
    protected RiskControlService riskControlService;
    @Resource
    protected ProductPageMenuMapper menuMapper;

    @Resource
    protected StaffAuditRecordMapper staffAuditRecordMapper;
    @Resource
    protected SmsService smsService;
    @Resource
    protected ChannelDataMapper channelDataMapper;
    @Resource
    protected ProductDataMapper productDataMapper;
    @Resource
    protected IdCardOcrService idCardOcrService;

    @Resource
    protected UserBankCardMapper userBankCardMapper;

    @Resource
    FileUtilService fileUtilService;
    @Resource
    LoveSignProperties loveSignProperties;
    @Resource
    NetSignClient client;
    @Resource
    LoveSignService loveSignService;
    @Resource
    AppUserDetailService appUserDetailService;
    @Resource
    UserLoveLogMapper userLoveLogMapper;
    @Resource
    protected RedisTemplate<String,Object> redisTemplate;
    @Resource
    IdCardUtil idCardUtil;

    @Autowired
    protected UserProgressMapper userProgressMapper;

    @Autowired
    protected AsyncMethod asyncMethod;

    @Resource
    private CapitalDataMapper capitalDataMapper;


    private static final String MOBILE_PREFIX = "mb:";
    private static final String IP_PREFIX = "ip:";
    private static final String YZM_PREFIX = "yzm:";


    @Override
    public ResponseResult<UserCheckLoanApplyVo> queryLoanResult(Object parame) {
        Long userId = UserTokenUtil.getUserId();
        Integer productId = UserTokenUtil.getProductId();
        UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userId, LoanType.CHECK, productId);
        UserCheckLoanApplyVo userCheckLoanApplyVo = new UserCheckLoanApplyVo();
        userCheckLoanApplyVo.setUserId(userId);
        userCheckLoanApplyVo.setBindCardStatus(0);
        //给额度一个初始值
        userCheckLoanApplyVo.setCreditAmount(new BigDecimal(0));
        if (ObjectUtil.isEmpty(userLoanApply)) {
            userCheckLoanApplyVo.setAuditStatus(0);
            return ResponseResult.success(userCheckLoanApplyVo);
        }
        UserCreditData userCreditData = userCreditDataMapper.queryByUserId(userId);
        userCheckLoanApplyVo.setAuditStatus(userLoanApply.getAuditsStatus());
        if (ObjectUtil.isNotEmpty(userCreditData)){
            userCheckLoanApplyVo.setCreditAmount(userCreditData.getCreditAmount());
        }
        BackVo backVo = userBankCardMapper.queryBackByUserId(userId);
        if (ObjectUtil.isNotEmpty(backVo)){
            userCheckLoanApplyVo.setBindCardStatus(1);
        }
        return ResponseResult.success(userCheckLoanApplyVo);
    }



    /**
     * 提交二要素表单前置校验
     * @param userCheckLoanApplyDto
     * @return
     */
    public ResponseResult<UserCheckLoanApplyVo> preCheckByTwoElementsData(UserCheckLoanApplyDto userCheckLoanApplyDto) {
        Long userId = UserTokenUtil.getUserId();
        // 前置校验
        if (!IdcardUtil.isValidCard(userCheckLoanApplyDto.getIdNumber())) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "身份证号格式错误");
        }
        // 是否已经提交过一次申请，是则提示已经提交无需再次提交
        UserData userData = userDataMapper.selectById(userId);
        if (ObjectUtil.isEmpty(userData)) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "用户不存在");
        }
        // 根据身份证号查询用户
//        UserDetail detail = userDetailMapper.selectOne(new LambdaQueryWrapper<UserDetail>().eq(UserDetail::getWebIdCard, userCheckLoanApplyDto.getIdNumber()));
        UserDetail detail = userDetailMapper.selectByIdCard(userId,userCheckLoanApplyDto.getIdNumber());
        if (ObjectUtil.isNotEmpty(detail)) {
            //如果查询出来的用户id和该用户不是一个用户则不让申请，因为一个身份证号只能用唯一的手机号进行申请
//            if (!userId.equals(detail.getUserId())) {
                return ResponseResult.error(ErrorCodeEnum.FAIL, "该身份证号已绑定它手机号，请勿重复申请");
//            }
        }
        return ResponseResult.success(null);
    }
    @Override
    public ResponseResult<UserCheckLoanApplyVo> submitTwoElementsData(UserCheckLoanApplyDto userCheckLoanApplyDto) {
        Long userId = UserTokenUtil.getUserId();
        String lockKey = RedisEnum.SUBMIT_TWOELEMENTS_DATA + userId;
        RLock lock = redissonClient.getLock(lockKey);
        try {
            // 尝试获取锁，最多等待10秒，锁自动释放时间为5秒
            boolean isLocked = lock.tryLock(0,5, TimeUnit.SECONDS);
            if (!isLocked) {
                return ResponseResult.error(ErrorCodeEnum.FAIL, "请勿频繁提交，稍后再试");
            }
        }
        catch(Exception e){
            return ResponseResult.error(ErrorCodeEnum.FAIL, "提交失败");
        }
        return ResponseResult.success(new UserCheckLoanApplyVo());
    }

    @Override
    public ResponseResult<OcrVo> submitOcrData(IdCardVerifyDto verifyDto, HttpServletRequest request) {
        Long userId = UserTokenUtil.getUserId();
        UserDetail detail = userDetailMapper.queryByUserId(userId);
        if (detail == null) {
            detail = new UserDetail();
            detail.setUserId(userId);
            userDetailMapper.insert(detail);
        }
        log.info("提交身份证正反面信息开始");
        final UserDetail fdetail = detail;
        CompletableFuture<ResponseResult<OcrVo>> front = CompletableFuture.supplyAsync(() -> {
            return idCardUtil.getFrontIdCard(fdetail, verifyDto); // 异步执行任务---处理身份证正面
        });
        log.info("提交身份证正反面信息结束");
        CompletableFuture<ResponseResult<OcrVo>> back = CompletableFuture.supplyAsync(() -> {
            return idCardUtil.getBackIdCard(fdetail, verifyDto);  // 异步执行任务---处理身份证反面
        });
        CompletableFuture.allOf(front, back).join();
        try {
            ResponseResult<OcrVo> frontRes = front.get();
            ResponseResult<OcrVo> backRes = back.get();

            if(frontRes.isSuccess() && backRes.isSuccess()){
                //身份证正反面验证都是通过的时候才执行下面的流程
                log.info("身份证正反面验证结果成功");
                ResponseResult<OcrVo> voidResponseResult = idCardUtil.updateUserDetail(detail, frontRes.getData(), backRes.getData(), verifyDto, request);
                return voidResponseResult;
            }else if(!frontRes.isSuccess()){
                return frontRes;
            }else if(!backRes.isSuccess()){
                return backRes;
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
        return ResponseResult.error(ErrorCodeEnum.FAIL,"身份证校验失败");

    }


    /**
     * 记录申请、记录用户身份证号和姓名、mq风控
     * @param userCheckLoanApplyDto
     * @param userId
     * @param productId
     * @return
     */
    protected ResponseResult<UserCheckLoanApplyVo> saveTwoElementsMq(UserCheckLoanApplyDto userCheckLoanApplyDto, Long userId,Integer productId) {
        // 记录申请
        UserLoanApply apply = new UserLoanApply();
        apply.setUserId(userId);
        apply.setApplyType(LoanType.CHECK);
        apply.setUserApplyInfo(JSONObject.toJSONString(userCheckLoanApplyDto));
        apply.setOnlineType(productId);
        userLoanApplyMapper.insert(apply);

        // 记录用户身份证号和姓名
        UserDetail userDetail = userDetailMapper.queryByUserId(userId);
        if (ObjectUtil.isNotEmpty(userDetail)){
            userDetail.setWebName(userCheckLoanApplyDto.getUserName());
            userDetail.setWebIdCard(userCheckLoanApplyDto.getIdNumber());
            userDetail.setWebTwoElements(1);
            userDetailMapper.updateById(userDetail);
        }else {
            userDetail = new UserDetail();
            userDetail.setWebName(userCheckLoanApplyDto.getUserName());
            userDetail.setWebIdCard(userCheckLoanApplyDto.getIdNumber());
            userDetail.setWebTwoElements(1);
            userDetail.setUserId(userId);
            userDetailMapper.insert(userDetail);
        }

        AirRiskControllerDto airRiskControllerDto = new AirRiskControllerDto();
        airRiskControllerDto.setUserName(userDetail.getWebName());
        airRiskControllerDto.setIdNumber(userDetail.getWebIdCard());
        airRiskControllerDto.setUserId(userId);
        airRiskControllerDto.setType(productId);
        airRiskControllerDto.setApplyId(apply.getId());
//        rabbitTemplate.convertAndSend(QueueConstant.RISK_CONTROL_QUEUE, JSONObject.toJSONString(airRiskControllerDto));
        rabbitTemplate.convertAndSend(QueueConstant.RISK_CONTROL_QUEUE_NEW, JSONObject.toJSONString(airRiskControllerDto));
        return ResponseResult.success(null);
    }

    /**
     * ocr身份证校验
     * @param verifyDto
     * @return
     */
    public ResponseResult<Void> ocrDataVerify(IdCardVerifyDto verifyDto) {
        UserDetail detail = userDetailMapper.queryByUserId(UserTokenUtil.getUserId());
        if (detail == null) {
            detail = new UserDetail();
            detail.setUserId(UserTokenUtil.getUserId());
            userDetailMapper.insert(detail);
        }

        // 正
        OcrDto ocrDto = new OcrDto();
        ocrDto.setImageUrl(verifyDto.getIdCardFrondUrl());
        if (StrUtil.isNotEmpty(verifyDto.getIdCardFrondUrl()) && verifyDto.getIdCardFrondUrl().startsWith("data:image")) {
            ocrDto.setImageBase64(verifyDto.getIdCardFrondBase64());
            ocrDto.setImageUrl(null);
            verifyDto.setIdCardFrondUrl(null);
        }
        ocrDto.setCardSide("FRONT");

        OcrDto finalOcrDto = ocrDto;
        CompletableFuture<OcrVo> front = CompletableFuture.supplyAsync(() -> {
            return idCardOcrService.idCardOcr(finalOcrDto);
        });

        // 反
        ocrDto = new OcrDto();
        ocrDto.setImageUrl(verifyDto.getIdCardReverseUrl());
        if (StrUtil.isNotEmpty(verifyDto.getIdCardReverseUrl()) && verifyDto.getIdCardReverseUrl().startsWith("data:image")) {
            ocrDto.setImageBase64(verifyDto.getIdCardReverseBase64());
            ocrDto.setImageUrl(null);
            verifyDto.setIdCardReverseUrl(null);
        }
        ocrDto.setCardSide("BACK");
        OcrDto finalOcrBackDto = ocrDto;
        CompletableFuture<OcrVo> back = CompletableFuture.supplyAsync(() -> {
            return idCardOcrService.idCardOcr(finalOcrBackDto);
        });
        CompletableFuture.allOf(front, back).join();
        try {
            OcrVo frontRes = front.get();
            OcrVo backRes = back.get();

            if(frontRes.getCode() != 0){
                return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,frontRes.getMsg());
            }
            if(backRes.getCode() != 0){
                return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,backRes.getMsg());
            }

            if (StrUtil.isNotEmpty(detail.getWebIdCard())) {
                if (!frontRes.getIdCardResult().getName().equals(detail.getWebName()) || !frontRes.getIdCardResult().getIdNum().equals(detail.getWebIdCard())){
                    return ResponseResult.error(ErrorCodeEnum.FAIL,"姓名或身份证号不一致");
                }
            }
            Long count = userDetailMapper.selectCount(new LambdaQueryWrapper<UserDetail>().eq(UserDetail::getIdNumber, frontRes.getIdCardResult().getIdNum()));
            if (count > 0){
                return ResponseResult.error(ErrorCodeEnum.FAIL,"身份证号已被注册");
            }
            if (backRes.getIdCardResult().getValidDate() == null){
                return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"有效期不能为空");
            }
            String[] split = backRes.getIdCardResult().getValidDate().split("-");
            if (DateUtil.now().compareTo(split[1]) == 1){
                return ResponseResult.error(ErrorCodeEnum.FAIL,"身份证有效期已过期,请更换身份证");
            }
            Long userId = UserTokenUtil.getUserId();
            UserDetail userDetail = new UserDetail();
            try {
                if (StrUtil.isEmpty(verifyDto.getIdCardFrondUrl())) {
                    userDetail.setIdCardFrondUrl(fileUtilService.upResult(FileUtil.base64ToMultipartFile(verifyDto.getIdCardFrondBase64()),null).getData().getDownloadUrl());
                } else {
                    userDetail.setIdCardFrondUrl(verifyDto.getIdCardFrondUrl());
                }
                if (StrUtil.isEmpty(verifyDto.getIdCardReverseUrl())) {
                    userDetail.setIdCardReverseUrl(fileUtilService.upResult(FileUtil.base64ToMultipartFile(verifyDto.getIdCardReverseBase64()),null).getData().getDownloadUrl());
                } else {
                    userDetail.setIdCardReverseUrl(verifyDto.getIdCardReverseUrl());
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            userDetail.setUserId(userId);
            if (StrUtil.isEmpty(detail.getWebIdCard())) {
                userDetail.setWebName(frontRes.getIdCardResult().getName());
                userDetail.setWebIdCard(frontRes.getIdCardResult().getIdNum());
                userDetail.setWebTwoElements(1);
            }
            userDetail.setAppName(frontRes.getIdCardResult().getName());
            userDetail.setSex(frontRes.getIdCardResult().getSex());
            userDetail.setNation(frontRes.getIdCardResult().getNation());
            userDetail.setBirth(frontRes.getIdCardResult().getBirth());
            userDetail.setAddress(frontRes.getIdCardResult().getAddress());
            userDetail.setIdNumber(frontRes.getIdCardResult().getIdNum());
            userDetail.setAuthority(frontRes.getIdCardResult().getAuthority());
            userDetail.setValidDate(frontRes.getIdCardResult().getValidDate());
            userDetail.setOcrResult(1);
            UpdateWrapper<UserDetail> updateWrapper = new UpdateWrapper();
            updateWrapper.eq("user_id",userId);
            int update = userDetailMapper.update(userDetail, updateWrapper);
            if (update == 1){
                return ResponseResult.success();
            }
            return ResponseResult.error(ErrorCodeEnum.DATA_SAVE_FAILED);
        }  catch (Exception e) {
            return ResponseResult.error(ErrorCodeEnum.DATA_SAVE_FAILED);
        }
    }

    /**
     * 三要素校验
     * @return
     */
    public ResponseResult<Void> verifyThree(String idCard, String name,String mobile) {
        Long userId = UserTokenUtil.getUserId();
        UserDetail userDetail = userDetailMapper.queryByUserId(userId);
        if (idCard==null || name==null || mobile==null){
            UserData userData = userDataMapper.selectById(userId);
            name = userDetail.getAppName();
            idCard = userDetail.getIdNumber();
            mobile = userData.getMobile();
        }

        VerifyVo verifyResult = idCardVerifyService.verifyThree(idCard, name,mobile);
        if (verifyResult.getCode() != 1) {
            log.info("三要素校验失败:{}", userId);
            return ResponseResult.error(ErrorCodeEnum.FAIL, "三要素校验失败");
        }
        return ResponseResult.success();
    }

    /**
     * 人脸提交的方案二
     * */
    @Override
    public ResponseResult<Void> submitFaceData(FaceVerifyDto verifyDto){
        Long userId = UserTokenUtil.getUserId();
        //限制用户不能重复提交
        String lockKey = RedisEnum.SUBMIT_OCR_DATA+ userId;
        RLock lock = redissonClient.getLock(lockKey);
        try {
            // 尝试获取锁，最多等待10秒，锁自动释放时间为5秒
            boolean isLocked = lock.tryLock(0, 5, TimeUnit.SECONDS);
            if (!isLocked) {
                return ResponseResult.error(ErrorCodeEnum.FAIL, "请勿频繁提交，稍后再试");
            }
        }
        catch(Exception e){
            return ResponseResult.error(ErrorCodeEnum.FAIL, "提交失败");
        }
        //查询人脸识别结果
        String bizId = verifyDto.getBizId();
        if (StrUtil.isEmpty(bizId)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "bizId不能为空");
        }
        ResponseResult<Void> faceRes = appUserDetailService.faceLoveVerifyQuery(bizId);
        if(!faceRes.isSuccess()){
            return ResponseResult.error(ErrorCodeEnum.FAIL, faceRes.getErrorMessage());
        }
        //1.调用爱签签
        //1.1获取ocr+人脸页面的协议ids
        ProductPageMenu menu = (ProductPageMenu)redisTemplate.opsForValue().get(RedisEnum.PAGE_USER_ID + userId);//人脸页面协议
        //查询身份证页面的协议
        menu.setSort(menu.getSort()-1);
        menu.setCurPage(null);
        ProductPageMenu befMenu = menuMapper.selectNext(menu);
        befMenu.getAgreementDataList().addAll(menu.getAgreementDataList());
        //3.2调用爱签签进行协议签署
        ResponseResult<Void> signResult = aiQianSign(userId, befMenu.getAgreementDataList());
        return signResult;
    }

    /**
     * 签署当前页面协议
     * @return
     */
    public ResponseResult<Void> signPageProtocol(Long userId) {
        ProductPageMenu menu = (ProductPageMenu)redisTemplate.opsForValue().get(RedisEnum.PAGE_USER_ID + userId);

        // 调用爱签签进行协议签署
        ResponseResult<Void> signResult = aiQianSign(userId, menu.getAgreementDataList());
        return signResult;
    }

    @Override
    public ResponseResult<UserCheckLoanApplyVo> apply(UserCheckLoanApplyDto userCheckLoanApplyDto, Object params) {
        return null;
    }

    @Override
    public void loanApply(LoanApplyDto loanApplyDto, Object params) {

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<SubmitUserFormVo> submitUserForm(UserFormDto dto) {
        //联系人手机号与当前手机号进行校验
        Long userId = UserTokenUtil.getUserId();
        UserData userData = userDataMapper.selectById(userId);
        if (userData.getMobile().equals(dto.getEmergencyMobileOne()) || userData.getMobile().equals(dto.getEmergencyMobileTwo())){
            return ResponseResult.error(ErrorCodeEnum.FAIL,"紧急联系人手机号与当前手机号不能一致");
        }
        if (dto.getEmergencyMobileOne().equals(dto.getEmergencyMobileTwo())) {
            return ResponseResult.error(ErrorCodeEnum.FAIL,"紧急联系人手机号不能重复");
        }
        UserDetail detail = userDetailMapper.queryByUserId(userId);
        if (detail.getWebName().equals(dto.getEmergencyNameOne()) || detail.getWebName().equals(dto.getEmergencyNameTwo())){
            return ResponseResult.error(ErrorCodeEnum.FAIL,"紧急联系人姓名与用户姓名不能一致");
        }
        if (dto.getEmergencyNameOne().equals(dto.getEmergencyNameTwo())) {
            return ResponseResult.error(ErrorCodeEnum.FAIL,"紧急联系人姓名不能重复");
        }
        UserDetail userDetail = MyModelUtil.copyTo(dto, UserDetail.class);
        if (dto.getFormFlag() == 1){
            userDetail.setFormTime(DateUtil.now());
        }
        userDetail.setUserId(userId);
        userDetail.setFormFlag(1);
        UpdateWrapper<UserDetail> updateWrapper = new UpdateWrapper();
        updateWrapper.eq("user_id",userId);
        int update = userDetailMapper.update(userDetail, updateWrapper);
        SubmitUserFormVo vo = new SubmitUserFormVo();
        vo.setFlag(0);
        if (update == 1){
            // 记录申请
            UserLoanApply apply = new UserLoanApply();
            apply.setUserId(userId);
            apply.setApplyType(LoanType.LOAN);
            apply.setAuditsStatus(1);
            apply.setCreateTime(new Date());
            apply.setOnlineType(userData.getSourceMode());
            userLoanApplyMapper.insert(apply);
            // 电子签？
            log.info("用户:"+userId+"马上签署电子签："+System.currentTimeMillis());
            this.signPageProtocol(userId);
            log.info("用户:"+userId+"签署电子签结束，马上进件："+System.currentTimeMillis());
            // 向新资方提交进件
            CapitalData capitalData = capitalDataMapper.selectById(2);
            if (ObjectUtil.isNotEmpty(capitalData) && capitalData.getStatus() == 1) {
                creditService.creditApply(capitalData, userId);
            }
            log.info("用户:"+userId+"进件结束时间："+System.currentTimeMillis());
            return ResponseResult.success(vo);
        }
        vo.setFlag(1);

        return ResponseResult.success(vo);
    }



    /**
     * 绑定销售
     * @param userId
     * @return
     */
    public ResponseResult<RegOrLoginVo> longinBindStaff(Long userId,String inviteCode){
        if (StrUtil.isEmpty(inviteCode)) {
//            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"邀请码不能为空");
            throw new BusinessException(ErrorCodeEnum.DATA_VALIDATED_FAILED,"邀请码不能为空");
        }
        UserStaff userStaff = userStaffMapper.selectOne(new LambdaQueryWrapper<UserStaff>().eq(UserStaff::getUserId, userId));
        if (ObjectUtil.isEmpty(userStaff)) {
            StaffData staffData = staffDataMapper.selectByInviteCode(inviteCode);
            if (ObjectUtil.isEmpty(staffData)) {
//                return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"邀请码错误");
                throw new BusinessException(ErrorCodeEnum.DATA_VALIDATED_FAILED,"邀请码错误");
            }
            if (staffData.getInviteFlag() == 1) {
//                return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"邀请码已失效");
                throw new BusinessException(ErrorCodeEnum.DATA_VALIDATED_FAILED,"邀请码已失效");
            }
            userStaff = new UserStaff();
            userStaff.setUserId(userId);
            userStaff.setStaffId(staffData.getId());
            userStaff.setCreateTime(new Date());
            userStaffMapper.insert(userStaff);
        }
        return ResponseResult.success(new RegOrLoginVo());
    }
    public ResponseResult<Void> aiQianSign(Long userId,List<AgreementData> agreements){
        UserData userData = userDataMapper.selectById(userId);
        UserDetail detail = userDetailMapper.queryByUserId(userId);
        SignDto signDto = new SignDto();
        signDto.setName(detail.getWebName());
        signDto.setAccount(userData.getMobile());
        signDto.setIdCard(detail.getWebIdCard());
        signDto.setUserId(userId);
        List<SignRulesDto> rules = new ArrayList<>();
        ResponseResult<Void> result = ResponseResult.success();
        for (AgreementData one:agreements
             ) {
            Integer id = one.getId();
            if(id.equals(FinalParam.FACE_AGREEAMENT)){
                signDto.setContractNo(IdUtil.fastSimpleUUID());
                signDto.setContractName("人脸识别授权书");
                List<SignRulesDto> rules1 = createRules(userData, detail, id);
                signDto.setRules(rules1);
                result = loveSignService.signAll(signDto, loveSignProperties.getFace());
                loveSign(signDto,result);
                if(!result.isSuccess()){
                    return ResponseResult.error(ErrorCodeEnum.FAIL,"人脸识别授权书签名失败");
                }
            }
            else if(id.equals(FinalParam.INFO_SHARE_AGREEAMENT)){
                signDto.setContractNo(IdUtil.fastSimpleUUID());
                signDto.setRules(rules);
                signDto.setContractName("个人信息共享授权书");
                result = loveSignService.signAll(signDto,loveSignProperties.getPerson());
                loveSign(signDto,result);
                if(!result.isSuccess()){
                    return ResponseResult.error(ErrorCodeEnum.FAIL,"个人信息共享授权书签名失败");
                }
            }
            else if(id.equals(FinalParam.ELECTRONIC_AGREEAMENT)){
                signDto.setContractNo(IdUtil.fastSimpleUUID());
                signDto.setContractName("电子签授权书");
                result = loveSignService.signAll(signDto,loveSignProperties.getSign());
                loveSign(signDto,result);
                if(!result.isSuccess()){
                    return ResponseResult.error(ErrorCodeEnum.FAIL,"电子签授权书签名失败");
                }

            }
            else if(id.equals(FinalParam.Credit_AGREEAMENT)){
                signDto.setContractNo(IdUtil.fastSimpleUUID());
                signDto.setContractName("征信查询授权书");
                List<SignRulesDto> rules1 = createRules(userData, detail, id);
                signDto.setRules(rules1);
                result = loveSignService.signAll(signDto, loveSignProperties.getCredit());
                loveSign(signDto, result);
                if(!result.isSuccess()){
                    return ResponseResult.error(ErrorCodeEnum.FAIL,"征信查询授权书签名失败");
                }
            }
            else if(id.equals(FinalParam.ENTRUST_AGREEAMENT)){
                signDto.setContractNo(IdUtil.fastSimpleUUID());
                signDto.setContractName("委托担保申请书");
                List<SignRulesDto> rules1 = createRules(userData, detail, id);
                signDto.setRules(rules1);
                result = loveSignService.signAll(signDto, loveSignProperties.getEntrusted());
                loveSign(signDto, result);
                if(!result.isSuccess()){
                    return ResponseResult.error(ErrorCodeEnum.FAIL,"委托担保申请书签名失败");
                }
            }
        }
        return result;
    }
    public void loveSign(SignDto dto , ResponseResult<Void> result){
        UserLoveLog log = new UserLoveLog();
        log.setUserId(UserTokenUtil.getUserId());
        log.setContractName(dto.getContractName());
        log.setContractNo(dto.getContractNo());
        if (result.isSuccess()){
            log.setContractStatus(1);
        }else {
            log.setContractStatus(2);
        }
        userLoveLogMapper.insert(log);
    }

    public List<SignRulesDto> createRules(UserData userData,UserDetail detail,Integer id){
        List<SignRulesDto> rules = new ArrayList<>();
        if(id.equals(FinalParam.FACE_AGREEAMENT)){
            SignRulesDto rulesDto = new SignRulesDto();
            rulesDto.setKey("platformName");
            rulesDto.setValue("七叶草");
            rules.add(rulesDto);
            return rules;
        } else if(id.equals(FinalParam.Credit_AGREEAMENT) || id.equals(FinalParam.ENTRUST_AGREEAMENT)){
            SignRulesDto signRulesDto1 = new SignRulesDto();
            SignRulesDto signRulesDto2 = new SignRulesDto();
            SignRulesDto signRulesDto3 = new SignRulesDto();
            SignRulesDto signRulesDto4 = new SignRulesDto();
            SignRulesDto signRulesDto5 = new SignRulesDto();
            SignRulesDto signRulesDto6 = new SignRulesDto();
            signRulesDto1.setKey("idNumber");
            signRulesDto1.setValue(detail.getWebIdCard());
            signRulesDto2.setKey("signUserName");
            signRulesDto2.setValue(detail.getWebName());
            signRulesDto3.setKey("mobile");
            signRulesDto3.setValue(userData.getMobile());
            signRulesDto4.setKey("address");
            signRulesDto4.setValue(detail.getAddress());
            signRulesDto5.setKey("signDate");
            signRulesDto5.setValue(new SimpleDateFormat("yyyy 年 MM 月 dd 日").format(new Date()));
            signRulesDto6.setKey("type");
            signRulesDto6.setValue("身份证");
            rules.add(signRulesDto1);
            rules.add(signRulesDto2);
            rules.add(signRulesDto3);
            rules.add(signRulesDto4);
            rules.add(signRulesDto5);
            rules.add(signRulesDto6);
            return rules;
        }else{
            return rules;
        }

    }


    public ResponseResult<Void> flyApplyVerify(UserData userData,UserDetail userDetail){
        Long userId = userData.getId();
        if (ObjectUtil.isEmpty(userData)) {
            log.error("用户不存在");
            return ResponseResult.error(ErrorCodeEnum.FAIL, "用户不存在");
        }

        if (ObjectUtil.isEmpty(userDetail)) {
            log.error("用户信息不存在");
            return ResponseResult.error(ErrorCodeEnum.FAIL, "用户信息不存在");
        }
        // 前置校验
        if (!IdcardUtil.isValidCard(userDetail.getIdNumber())) {
            log.error("身份证号格式错误");
            return ResponseResult.error(ErrorCodeEnum.FAIL, "身份证号格式错误");
        }
        // 是否已经提交过一次申请，是则提示已经提交无需再次提交
        UserData userDataUp = new UserData();
        userDataUp.setId(userData.getId());

        UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userId, LoanType.CHECK, userData.getSourceMode());
        UserCheckLoanApplyVo userCheckLoanApplyVo = new UserCheckLoanApplyVo();
        if (userLoanApply != null) {
            UserCheckLoanApplyVo result = userCheckLoanApplyVo;
            result.setUserId(userId);
            result.setAuditStatus(userLoanApply.getAuditsStatus());
            log.error("{}已经提交过一次申请，无需再次提交", userId);
            return ResponseResult.error(ErrorCodeEnum.FAIL, "已经提交过一次申请，无需再次提交");
        }
        return ResponseResult.success();
    }



    @Override
    public ResponseResult<ProductPageMenu> getPage(Long userId, String curPage, Integer productId) {
        return null;
    }
}
