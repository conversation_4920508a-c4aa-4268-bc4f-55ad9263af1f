package com.rongchen.byh.app.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 用户贷款申请
 * @TableName user_loan_apply
 */
@TableName(value ="user_loan_apply")
@Data
public class UserLoanApply implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 授信id
     */
    private String creditId;

    /**
     * api授信编号
     */
    private String apiCreditNo;

    /**
     * 申请类型 0 初筛申请 1 授信申请
     */
    private Integer applyType;

    /**
     * 线上线下类型 0 线上 1 线下
     */
    private Integer onlineType;

    /**
     * 审核状态 0 待审核 1 审核通过 2 审核不通过
     */
    private Integer auditsStatus;

    /**
     * 提交申请信息
     */
    private String userApplyInfo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;



    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}