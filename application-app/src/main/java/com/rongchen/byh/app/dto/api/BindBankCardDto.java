package com.rongchen.byh.app.dto.api;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 绑定银行卡dto
 * @date 2024/12/11 19:49:25
 */
@Data
public class BindBankCardDto {
    @Schema(description = "签名")
    @NotEmpty(message = "签名不能为空")
    private String DataSign;

    @Schema(description = "时间戳")
    @NotEmpty(message = "时间戳不能为空")
    private String Timestamp;

    @Schema(description = "data")
    private BindBankCardDataDto Data;
}
