package com.rongchen.byh.app.vo.app;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class RepaymentDetailVo {

    @Schema(description = "还款起日")
    private String repayOwnbDate;

    @Schema(description = "本期应还总金额 元")
    private BigDecimal totalAmt;

    @Schema(description = "本期应还本金")
    private BigDecimal termRetPrin;

    @Schema(description = "本期应还利息")
    private BigDecimal termRetInt;

    @Schema(description = "本期应还融担费")
    private BigDecimal termGuarantorFee;

    @Schema(description = "本期应还罚息")
    private BigDecimal termRetFint;

    @Schema(description = "结清标志 可选值：RUNNING - 未结 ，CLOSE - 已结 ,REPAYING-还款中")
    private String settleFlag;

    @Schema(description = "期数")
    private String repayTerm;

    @Schema(description = "赊销总费用")
    private BigDecimal saleTotalAmt;

    @Schema(description = "还款账户")
    private String bankName;

    @Schema(description = "银行卡卡号")
    private String bankAccount;



}
