package com.rongchen.byh.app.vo.app;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 用户账单列表dto
 * @date 2024/12/14 16:28:26
 */
@Data
public class BillListVo {
    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "剩余待还金额")
    private BigDecimal creditAmount;

    @Schema(description = "还款到期日")
    private String repaymentDate;

    @Schema(description = "还款状态 100 授信中  200 授信失败   300 放款中  400  放款失败   500 还款中   600 已结清")
    private Integer creditStatus;
}
