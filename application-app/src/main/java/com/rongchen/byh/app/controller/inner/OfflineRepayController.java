package com.rongchen.byh.app.controller.inner;


import cn.dev33.satoken.annotation.SaIgnore;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.rongchen.byh.app.dto.inner.InnerRepayDto;
import com.rongchen.byh.app.service.AppUserBillService;
import com.rongchen.byh.common.core.annotation.DisableDataFilter;
import com.rongchen.byh.common.core.object.ResponseResult;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@ApiSupport(order = 1)
@Tag(name = "内部 线下还款模式-还款信息流推送到资方")
@DisableDataFilter
@Slf4j
@RestController
@RequestMapping("/inner")
public class OfflineRepayController {

    @Resource
    AppUserBillService appUserBillService;

    /**
     * 内部  线下还款发起
     * @return
     */
    @PostMapping("/repay")
    @SaIgnore
    public ResponseResult<Void> repay(@RequestBody InnerRepayDto repayDto) {

        return appUserBillService.innerRepay(repayDto);
    }

    /**
     * 内部  线下还款发起
     * @return
     */
    @PostMapping("/repaySale")
    @SaIgnore
    public ResponseResult<Void> repaySale(@RequestBody InnerRepayDto repayDto) {

        return appUserBillService.innerRepaySale(repayDto);
    }
}
