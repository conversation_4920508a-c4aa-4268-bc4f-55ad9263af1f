package com.rongchen.byh.app.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**

 * 项目名称：byh_java
 * 文件名称: BaofuPayDetail
 * 创建时间: 2025-03-13 15:34
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.app.entity
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */

/**
 * 宝付支付详情表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "baofu_pay_detail")
public class BaofuPayDetail implements Serializable {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商户订单号
     */
    @TableField(value = "trans_id")
    private String transId;

    /**
     * 用户ID
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 签约协议号
     */
    @TableField(value = "protocol_no")
    private String protocolNo;

    /**
     * 交易金额(单位:分)
     */
    @TableField(value = "txn_amt")
    private String txnAmt;

    /**
     * 支付状态：SUCCESS-成功 FAIL-失败
     */
    @TableField(value = "pay_status")
    private String payStatus;

    /**
     * 宝付订单号
     */
    @TableField(value = "ou_pay_order_on")
    private String ouPayOrderOn;

    /**
     * 失败后返回的错误代码
     */
    @TableField(value = "msg_code")
    private String msgCode;

    /**
     * 失败后返回的错误消息
     */
    @TableField(value = "message")
    private String message;

    /**
     * 支付成功时间
     */
    @TableField(value = "succeed_time")
    private Date succeedTime;

    /**
     * 成功金额(单位:分)
     */
    @TableField(value = "succeed_amt")
    private String succeedAmt;

    /**
     * 风控参数
     */
    @TableField(value = "risk_item")
    private String riskItem;

    /**
     * 备注信息
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}