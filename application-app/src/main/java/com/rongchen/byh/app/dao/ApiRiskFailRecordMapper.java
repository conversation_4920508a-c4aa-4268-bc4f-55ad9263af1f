package com.rongchen.byh.app.dao;

import com.rongchen.byh.app.entity.ApiRiskFailRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【api_risk_fail_record(api风控失败记录表)】的数据库操作Mapper
* @createDate 2025-04-08 19:30:33
* @Entity com.rongchen.byh.app.entity.ApiRiskFailRecord
*/
@Mapper
public interface ApiRiskFailRecordMapper extends BaseMapper<ApiRiskFailRecord> {
    List<ApiRiskFailRecord> getInProcessList(Integer day);
}




