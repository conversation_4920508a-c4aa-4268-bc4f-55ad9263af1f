package com.rongchen.byh.app.controller.app;

import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.rongchen.byh.app.service.DisburseService;
import com.rongchen.byh.common.api.zifang.dto.BindBankSmsDto;
import com.rongchen.byh.common.api.zifang.dto.VerifyBindBankSmsDto;
import com.rongchen.byh.common.api.zifang.vo.BindBankSmsVo;
import com.rongchen.byh.common.api.zifang.vo.LoanElementVo;
import com.rongchen.byh.common.api.zifang.vo.VerifyBindBankSmsVo;
import com.rongchen.byh.common.core.object.ResponseResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Tag(name = "app更换银行卡相关新接口")
@ApiSupport(order = 3)
@RestController
@RequestMapping("/userAppApi")
public class AppDisburseController {

    @Resource
    DisburseService disburseService;

    @Operation(summary = "换卡-支付通道")
    @PostMapping("/replaceBindBackPay")
    public ResponseResult<LoanElementVo> bindBackPay(){
        return disburseService.newBindBackPay();
    }


    @Operation(summary = "换卡-获取验证码")
    @PostMapping("/replaceBindSendCode")
    public ResponseResult<BindBankSmsVo> bindSendCode(@RequestBody BindBankSmsDto dto){
        return disburseService.newBindSendCode(dto);
    }


    @Operation(summary = "换卡-提交验证码")
    @PostMapping("/replaceVerifyBindSend")
    public ResponseResult<VerifyBindBankSmsVo> verifyBindSend(@RequestBody VerifyBindBankSmsDto dto){
        return disburseService.newVerifyBindSend(dto);
    }
}
