package com.rongchen.byh.app.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rongchen.byh.app.dao.DisburseDataMapper;
import com.rongchen.byh.app.dao.RepaySaleApplyMapper;
import com.rongchen.byh.app.dao.RepayScheduleApplyMapper;
import com.rongchen.byh.app.dao.RepayScheduleMapper;
import com.rongchen.byh.app.dao.SaleScheduleMapper;
import com.rongchen.byh.app.dao.UserBankCardMapper;
import com.rongchen.byh.app.dto.RepaymentDto;
import com.rongchen.byh.app.dto.app.BackVo;
import com.rongchen.byh.app.dto.app.BillDetailDto;
import com.rongchen.byh.app.dto.app.BillRepayApplyDto;
import com.rongchen.byh.app.dto.inner.InnerRepayDto;
import com.rongchen.byh.app.entity.DisburseData;
import com.rongchen.byh.app.entity.RepaySaleApply;
import com.rongchen.byh.app.entity.RepaySchedule;
import com.rongchen.byh.app.entity.RepayScheduleApply;
import com.rongchen.byh.app.entity.SaleSchedule;
import com.rongchen.byh.app.service.AppUserBillService;
import com.rongchen.byh.app.service.PrincipalRepayService;
import com.rongchen.byh.app.service.RepayPlanUpdateService;
import com.rongchen.byh.app.service.SaleRepayService;
import com.rongchen.byh.app.utils.UserTokenUtil;
import com.rongchen.byh.app.vo.app.BillDetailVo;
import com.rongchen.byh.app.vo.app.BillListVo;
import com.rongchen.byh.app.vo.app.BillRepayResultVo;
import com.rongchen.byh.app.vo.app.PlanVo;
import com.rongchen.byh.app.vo.app.RepaymentDetailVo;
import com.rongchen.byh.common.api.zifang.service.OtherApi;
import com.rongchen.byh.common.api.zifang.service.RepaymentApi;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.constant.SettleFlagConstant;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.rabbitmq.constants.QueueConstant;
import com.rongchen.byh.common.rabbitmq.dto.RepayDto;
import com.rongchen.byh.common.rabbitmq.dto.SaleDto;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/12/14 16:27:29
 */
@Service
@Slf4j
public class AppUserBillServiceImpl implements AppUserBillService {
    @Resource
    private DisburseDataMapper disburseDataMapper;
    @Resource
    private RepayScheduleMapper repayScheduleMapper;
    @Resource
    private UserBankCardMapper userBankCardMapper;
    @Resource
    private SaleRepayService saleRepayService;
    @Resource
    private PrincipalRepayService principalRepayService;
    @Resource
    private RepaySaleApplyMapper repaySaleApplyMapper;
    @Resource
    private RepayScheduleApplyMapper repayScheduleApplyMapper;
    @Resource
    private SaleScheduleMapper saleScheduleMapper;
    @Resource
    private OtherApi otherApi;
    @Resource
    private RepaymentApi repaymentApi;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    RabbitTemplate rabbitTemplate;
    @Resource
    private RepayPlanUpdateService asyncRepayPlanUpdateService;

    @Resource(name = "taskExecutor") // 注入自定义的线程池 Bean，用于执行异步任务
    private Executor taskExecutor;

    @Override
    public ResponseResult<List<BillListVo>> billList() {
        Long userId = UserTokenUtil.getUserId();

        try {
            long startTime = System.currentTimeMillis();
            log.info("【账单列表】触发异步还款计划更新检查，userId: {}", userId);
            asyncRepayPlanUpdateService.triggerRepayPlanUpdate(userId);
            long endTime = System.currentTimeMillis();
            log.info("【账单列表】异步还款计划更新检查完成，userId: {}, 耗时: {}ms", userId, endTime - startTime);
        } catch (Exception e) {
            log.error("【账单列表】提交异步还款计划更新任务失败，userId: {}, error: ", userId, e);
        }

        List<Integer> statusList = new ArrayList<>(2);
        statusList.add(500);
        statusList.add(600);

        LambdaQueryWrapper<DisburseData> wrapper = new LambdaQueryWrapper<DisburseData>()
                .eq(DisburseData::getUserId, userId)
                .in(DisburseData::getCreditStatus, statusList);
        List<DisburseData> list = disburseDataMapper.selectList(wrapper);
        if (CollectionUtil.isEmpty(list)) {
            return ResponseResult.success(new ArrayList<>());
        }
        Map<Long, DisburseData> disburseDataMap = list.stream().collect(Collectors.toMap(DisburseData::getId, k -> k));
        List<Long> disburseId = list.stream().map(DisburseData::getId).collect(Collectors.toList());
        // 查询还款计划
        List<RepaySchedule> repayScheduleList = repayScheduleMapper.selectList(new LambdaQueryWrapper<RepaySchedule>()
                .in(RepaySchedule::getDisburseId, disburseId).orderBy(true, true, RepaySchedule::getId));
        Map<Long, List<RepaySchedule>> disburseRepayMap = repayScheduleList.stream()
                .collect(Collectors.groupingBy(RepaySchedule::getDisburseId));
        // 查询赊销
        List<SaleSchedule> saleScheduleList = saleScheduleMapper.selectList(new LambdaQueryWrapper<SaleSchedule>()
                .in(SaleSchedule::getDisburseId, disburseId).orderBy(true, true, SaleSchedule::getId));
        Map<Long, List<SaleSchedule>> disburseSaleMap = saleScheduleList.stream()
                .collect(Collectors.groupingBy(SaleSchedule::getDisburseId));

        List<BillListVo> result = new ArrayList<>();
        disburseDataMap.forEach((k, v) -> {
            DisburseData disburseData = disburseDataMap.get(k);
            // 账单总的金额 本金+利息+赊销
            BigDecimal totalAmount = disburseData.getCreditAmount().add(disburseData.getGrossInterest())
                    .add(disburseData.getSaleRepayAmount());
            List<RepaySchedule> listRepay = disburseRepayMap.get(k);
            // 已经还了的总金额
            BigDecimal repayedAmount = listRepay.stream()
                    .filter(s -> SettleFlagConstant.CLOSE.equals(s.getSettleFlag())).map(RepaySchedule::getTotalAmt)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // listRepay.sort((o1, o2) -> {
            // long a = o1.getId() - o2.getId();
            // return (int) a;
            // });

            totalAmount = totalAmount.subtract(repayedAmount);
            // 已经还了的总赊销金额
            List<SaleSchedule> listSale = disburseSaleMap.get(k);
            BigDecimal saledAmount = listSale.stream().filter(s -> SettleFlagConstant.CLOSE.equals(s.getSettleFlag()))
                    .map(SaleSchedule::getTotalAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            totalAmount = totalAmount.subtract(saledAmount);

            BillListVo billListVo = new BillListVo();
            billListVo.setId(disburseData.getId());
            billListVo.setCreditAmount(totalAmount);
            billListVo.setCreditStatus(disburseData.getCreditStatus());

            // 最近还款日
            List<RepaySchedule> collect = listRepay.stream()
                    .filter(s -> SettleFlagConstant.RUNNING.equals(s.getSettleFlag())).collect(Collectors.toList());
            if (!CollectionUtil.isEmpty(collect)) {
                RepaySchedule repaySchedule = collect.get(0);
                String repayOwnbDate = repaySchedule.getRepayOwnbDate();
                billListVo.setRepaymentDate(repayOwnbDate);
            } else {
                billListVo.setRepaymentDate("");
            }
            result.add(billListVo);
        });
        return ResponseResult.success(result);
    }

    @Override
    public ResponseResult<BillDetailVo> billDetail(BillDetailDto billDetailDto) {
        Long userId = UserTokenUtil.getUserId();
        DisburseData disburseData = disburseDataMapper.selectById(billDetailDto.getId());
        if (ObjectUtil.isEmpty(disburseData)) {
            log.info("[{}] 账单详情 账单不存在", billDetailDto.getId());
            return ResponseResult.error(ErrorCodeEnum.FAIL, "账单不存在");
        }
        if (!disburseData.getUserId().equals(userId)) {
            log.info("[{}] 账单详情 账单不属于当前用户", billDetailDto.getId());
            return ResponseResult.error(ErrorCodeEnum.FAIL, "账单不属于当前用户");
        }

        BillDetailVo result = new BillDetailVo();

        // 本息还款计划
        List<RepaySchedule> repayScheduleList = repayScheduleMapper.selectList(new LambdaQueryWrapper<RepaySchedule>()
                .eq(RepaySchedule::getDisburseId, billDetailDto.getId()).orderBy(true, true, RepaySchedule::getId));
        // 赊销还款计划
        List<SaleSchedule> saleScheduleList = saleScheduleMapper.selectList(new LambdaQueryWrapper<SaleSchedule>()
                .eq(SaleSchedule::getDisburseId, billDetailDto.getId()).orderBy(true, true, SaleSchedule::getId));

        // 将赊销计划，按照 期限转成map
        Map<String, SaleSchedule> saleTermMap = saleScheduleList.stream()
                .collect(Collectors.toMap(SaleSchedule::getRepayTerm, k -> k));

        // 已还款列表
        List<PlanVo> repaidList = new ArrayList<>();
        // 未还款列表
        List<PlanVo> unRepaidList = new ArrayList<>();

        // 剩余还款金额
        BigDecimal creditAmount = new BigDecimal("0.00");
        // 剩余还款笔数
        int repayNum = 0;
        // 本金总金额
        BigDecimal principalAmount = new BigDecimal("0.00");
        // 利息总金额
        BigDecimal interestAmount = new BigDecimal("0.00");
        // 赊销总金额
        BigDecimal saleAmount = new BigDecimal("0.00");
        for (RepaySchedule schedule : repayScheduleList) {
            PlanVo plan = new PlanVo();
            plan.setId(schedule.getId());
            plan.setDisburseId(schedule.getDisburseId());
            plan.setRepayApplyNo(schedule.getRepayApplyNo());
            plan.setRepayOwnbDate(schedule.getRepayOwnbDate());
            plan.setTotalAmt(schedule.getTotalAmt());
            plan.setTermRetPrin(schedule.getTermRetPrin());
            plan.setTermRetInt(schedule.getTermRetInt());
            plan.setTermGuarantorFee(schedule.getTermGuarantorFee());
            plan.setTermRetFint(schedule.getTermRetFint());
            plan.setTermStatus(schedule.getTermStatus());
            plan.setSettleFlag(schedule.getSettleFlag());
            plan.setRepayTerm(schedule.getRepayTerm());

            // 还款中
            if (SettleFlagConstant.RUNNING.equals(schedule.getSettleFlag())
                    || SettleFlagConstant.REPAYING.equals(schedule.getSettleFlag())) {
                creditAmount = creditAmount.add(schedule.getTotalAmt());
                if (saleTermMap.containsKey(schedule.getRepayTerm())) {
                    SaleSchedule saleSchedule = saleTermMap.get(schedule.getRepayTerm());
                    plan.setSaleTotalAmt(saleSchedule.getTotalAmt());
                    plan.setTotalAmt(schedule.getTotalAmt().add(saleSchedule.getTotalAmt()));
                    saleAmount = saleAmount.add(saleSchedule.getTotalAmt());
                    creditAmount = creditAmount.add(saleSchedule.getTotalAmt());
                } else {
                    plan.setSaleTotalAmt(null);
                }
                unRepaidList.add(plan);
                repayNum = repayNum + 1;
                principalAmount = principalAmount.add(schedule.getTermRetPrin());
                interestAmount = interestAmount.add(schedule.getTermRetInt());
            } else {
                if (saleTermMap.containsKey(schedule.getRepayTerm())) {
                    SaleSchedule saleSchedule = saleTermMap.get(schedule.getRepayTerm());
                    plan.setSaleTotalAmt(saleSchedule.getTotalAmt());
                    // 判断是否赊销还款完成,如果账单还款完成,但是赊销没有还款完成,那么放到未完成里面,本金利息等设置0
                    if (saleSchedule.getSettleFlag().equals(SettleFlagConstant.CLOSE)) {
                        plan.setTotalAmt(schedule.getTotalAmt().add(saleSchedule.getTotalAmt()));
                        repaidList.add(plan);
                    } else {
                        plan.setTotalAmt(saleSchedule.getTotalAmt());
                        plan.setTermRetPrin(new BigDecimal("0.00"));
                        plan.setTermRetInt(new BigDecimal("0.00"));
                        plan.setTermGuarantorFee(new BigDecimal("0.00"));
                        plan.setTermRetFint(new BigDecimal("0.00"));
                        unRepaidList.add(plan);
                    }
                } else {
                    repaidList.add(plan);
                }
            }
        }
        result.setCreditAmount(creditAmount);
        result.setRepayNum(repayNum);
        result.setPrincipalAmount(principalAmount);
        result.setInterestAmount(interestAmount);
        result.setSaleAmount(saleAmount);
        result.setRepaidList(repaidList);
        result.setUnRepaidList(unRepaidList);

        return ResponseResult.success(result);
    }

    /**
     * 是否允许重复提交？
     *
     * @param repaymentDto
     * @return
     */
    @Override
    public ResponseResult<Void> billApply(RepaymentDto repaymentDto) {
        Long userId = UserTokenUtil.getUserId();
        Map<String, String> contextMap = MDC.getCopyOfContextMap();
        String traceId = contextMap.getOrDefault("traceId", UUID.fastUUID().toString());

        RLock lock = redissonClient.getLock("bill" + userId);
        boolean isLocked = false;
        try {
            isLocked = lock.tryLock();
            if (!isLocked) {
                log.info("[{}] 账单申请 请勿重复操作", repaymentDto.getDisburseId());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "请勿重复操作");
            }
            // 需要判断是本金账单还是赊销账单

            Long disburseId = repaymentDto.getDisburseId();
            // 校验是不是有其他账单
            String today = DateUtil.today();

            RepaySchedule repaySchedule = repayScheduleMapper.selectById(repaymentDto.getId());
            if (repaySchedule.getSettleFlag().equals(SettleFlagConstant.RUNNING)) {
                RepaySchedule lastOne = repayScheduleMapper.selectLastOne(disburseId, today);
                if (lastOne == null) {
                    log.info("[{}] 账单申请 暂无可还款账单", repaymentDto.getDisburseId());
                    return ResponseResult.error(ErrorCodeEnum.FAIL, "暂无可还款账单");
                }
                if (!lastOne.getId().equals(repaymentDto.getId())) {
                    log.info("[{}] 账单申请 还款账单不正确", repaymentDto.getDisburseId());
                    return ResponseResult.error(ErrorCodeEnum.FAIL, "还款账单不正确");
                }

                RepayDto repayDto = new RepayDto();
                repayDto.setDisburseId(disburseId);
                repayDto.setRepayId(repaySchedule.getId());
                repayDto.setTraceId(traceId);
                repayDto.setRepayType(1);
                rabbitTemplate.convertAndSend(QueueConstant.REPAY_QUEUE, JSONObject.toJSONString(repayDto));

                return ResponseResult.success();
            } else if (repaySchedule.getSettleFlag().equals(SettleFlagConstant.REPAYING)) {
                log.info("[{}] 账单申请 账单还款中", repaymentDto.getDisburseId());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "账单还款中");
            } else if (repaySchedule.getSettleFlag().equals(SettleFlagConstant.CLOSE)) {
                int term = Integer.parseInt(repaySchedule.getRepayTerm());
                if (term <= 3) {
                    SaleSchedule saleSchedule = saleScheduleMapper
                            .selectByDisburseIdAndTerm(repaymentDto.getDisburseId(), repaySchedule.getRepayTerm());
                    if (saleSchedule.getSettleFlag().equals(SettleFlagConstant.REPAYING)) {
                        log.info("[{}] 账单申请 赊销还款中", repaymentDto.getDisburseId());
                        return ResponseResult.error(ErrorCodeEnum.FAIL, "赊销还款中");
                    } else if (saleSchedule.getSettleFlag().equals(SettleFlagConstant.CLOSE)) {
                        log.info("[{}] 账单申请 赊销还款完成", repaymentDto.getDisburseId());
                        return ResponseResult.error(ErrorCodeEnum.FAIL, "赊销还款完成");
                    } else {
                        // 发送赊销还款
                        log.info("[{}] 账单申请 发送赊销还款", repaymentDto.getDisburseId());
                        SaleDto saleDto = new SaleDto();
                        saleDto.setDisburseId(repaySchedule.getDisburseId());
                        saleDto.setTerm(repaySchedule.getRepayTerm());
                        saleDto.setTraceId(traceId);
                        saleDto.setRepayType(1);
                        rabbitTemplate.convertAndSend(QueueConstant.SALE_QUEUE, JSONObject.toJSONString(saleDto));
                    }
                }

            }
        } finally {
            if (isLocked) {
                lock.unlock();
            }
        }

        return ResponseResult.success();
    }

    @Override
    public ResponseResult<BillRepayResultVo> getBillRepaymentResult(RepaymentDto repaymentDto) {
        // 还款为异步操作，休眠1.5秒
        ThreadUtil.sleep(RandomUtil.randomInt(1000, 2000));
        BillRepayResultVo resultVo = new BillRepayResultVo();
        // 本息还款成功就算成功?
        Long userId = UserTokenUtil.getUserId();
        RepaySchedule repaySchedule = repayScheduleMapper.selectById(repaymentDto.getId());
        resultVo.setRepaymentAmount(repaySchedule.getTotalAmt());
        resultVo.setPayTime(repaySchedule.getPayTime());
        resultVo.setDatePayTime(repaySchedule.getDatePayTime());
        // 查询银行卡信息
        BackVo backVo = userBankCardMapper.queryBackByUserId(userId);
        resultVo.setBankName(backVo.getBankName());
        String bankAccount = backVo.getBankAccount();
        String substring = bankAccount.substring(bankAccount.length() - 4);
        resultVo.setBankAccount(substring);

        int term = Integer.parseInt(repaySchedule.getRepayTerm());
        SaleSchedule saleSchedule = null;
        if (term <= 3) {
            saleSchedule = saleScheduleMapper.selectByDisburseIdAndTerm(repaymentDto.getDisburseId(),
                    repaySchedule.getRepayTerm());
        }
        if (repaySchedule.getSettleFlag().equals(SettleFlagConstant.CLOSE)) {
            // 查询赊销是否还完,赊销和本金还完才显示完成
            if (saleSchedule != null) {
                resultVo.setRepaymentAmount(repaySchedule.getTotalAmt().add(saleSchedule.getTotalAmt()));
                if (saleSchedule.getSettleFlag().equals(SettleFlagConstant.CLOSE)) {
                    resultVo.setRepaymentResult(1);
                } else {
                    resultVo.setRepaymentResult(0);
                }
            } else {
                resultVo.setRepaymentResult(1);
            }
        } else if (repaySchedule.getSettleFlag().equals(SettleFlagConstant.REPAYING)) {
            resultVo.setRepaymentResult(0);
        } else {
            if (saleSchedule != null) {
                resultVo.setRepaymentAmount(repaySchedule.getTotalAmt().add(saleSchedule.getTotalAmt()));
            }
            resultVo.setRepaymentResult(2);
        }
        return ResponseResult.success(resultVo);
    }

    @Override
    public ResponseResult<RepaymentDetailVo> repaymentDetail(RepaymentDto repaymentDto) {
        RepaymentDetailVo vo = new RepaymentDetailVo();
        Long userId = UserTokenUtil.getUserId();
        BackVo backVo = userBankCardMapper.queryBackByUserId(userId);
        vo.setBankName(backVo.getBankName());
        String bankAccount = backVo.getBankAccount();
        String substring = bankAccount.substring(bankAccount.length() - 4);
        vo.setBankAccount(substring);

        RepaySchedule repaySchedule = repayScheduleMapper.selectById(repaymentDto.getId());
        int term = Integer.parseInt(repaySchedule.getRepayTerm());
        SaleSchedule saleSchedule = null;
        if (term <= 3) {
            saleSchedule = saleScheduleMapper.selectByDisburseIdAndTerm(repaymentDto.getDisburseId(),
                    repaySchedule.getRepayTerm());
            vo.setSaleTotalAmt(saleSchedule.getTotalAmt());
        } else {
            vo.setSaleTotalAmt(new BigDecimal("0.00"));
        }
        vo.setRepayOwnbDate(repaySchedule.getRepayOwnbDate());
        vo.setRepayTerm(repaySchedule.getRepayTerm());
        vo.setTermRetPrin(repaySchedule.getTermRetPrin());
        vo.setTermRetInt(repaySchedule.getTermRetInt());
        vo.setTermGuarantorFee(repaySchedule.getTermGuarantorFee());
        vo.setTermRetFint(repaySchedule.getTermRetFint());
        vo.setTotalAmt(vo.getSaleTotalAmt().add(repaySchedule.getTotalAmt()));
        vo.setSettleFlag(repaySchedule.getSettleFlag());
        if (repaySchedule.getSettleFlag().equals(SettleFlagConstant.CLOSE)) {
            if (saleSchedule != null) {
                if (!saleSchedule.getSettleFlag().equals(SettleFlagConstant.CLOSE)) {
                    vo.setRepayTerm(repaySchedule.getRepayTerm());
                    vo.setTermRetPrin(new BigDecimal("0.00"));
                    vo.setTermRetInt(new BigDecimal("0.00"));
                    vo.setTermGuarantorFee(new BigDecimal("0.00"));
                    vo.setTermRetFint(new BigDecimal("0.00"));
                    vo.setTotalAmt(vo.getSaleTotalAmt());
                    vo.setSettleFlag(saleSchedule.getSettleFlag());
                }
            }
        }
        return ResponseResult.success(vo);
    }

    @Override
    public ResponseResult<Void> innerRepay(InnerRepayDto innerRepayDto) {

        Long RepayScheduleId = innerRepayDto.getBillRepayNo();

        if (RepayScheduleId == null) {
            log.info("[{}]-{} 内部还款 账单ID不能为空", innerRepayDto.getRepayMethod(), innerRepayDto.getBillRepayNo());
            return ResponseResult.error(ErrorCodeEnum.FAIL, "账单ID不能为空");
        }

        RLock lock = redissonClient.getLock("Repay" + RepayScheduleId);
        boolean isLocked = false;
        try {
            isLocked = lock.tryLock();
            if (!isLocked) {
                log.info("[{}]-{} 内部还款 请勿重复操作", innerRepayDto.getRepayMethod(), innerRepayDto.getBillRepayNo());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "请勿重复操作");
            }
            RepaySchedule repaySchedule = repayScheduleMapper.selectById(RepayScheduleId);
            if (repaySchedule == null) {
                log.info("[{}]-{} 内部还款 账单不存在", innerRepayDto.getRepayMethod(), innerRepayDto.getBillRepayNo());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "账单不存在");
            }
            if (!repaySchedule.getSettleFlag().equals(SettleFlagConstant.RUNNING)) {
                log.info("[{}]-{} 内部还款 账单状态不正确", innerRepayDto.getRepayMethod(), innerRepayDto.getBillRepayNo());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "账单状态不正确");
            }

            // 若一笔扣款正在进行中（无论是用户主动还款还是跑批扣款），尚未拿到最终结果，则不允许再付发起第二笔。
            int i = repayScheduleMapper.payingCount(repaySchedule.getDisburseId());
            if (i > 0) {
                log.info("[{}]-{} 内部还款 有一笔账单扣款在进行中", innerRepayDto.getRepayMethod(), innerRepayDto.getBillRepayNo());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "有一笔账单扣款在进行中");
            }

            BillRepayApplyDto billRepayApplyDto = new BillRepayApplyDto();
            billRepayApplyDto.setRepayScheduleId(repaySchedule.getId());
            billRepayApplyDto.setRepayMethod("1");
            billRepayApplyDto.setBankAccountType("1");
            Integer repayMethod = innerRepayDto.getRepayMethod();
            if (repayMethod != null && repayMethod == 2) {
                billRepayApplyDto.setRepayMethod("0");
                billRepayApplyDto.setBankAccountType("0");
            }
            // 调用还款接口
            ResponseResult<Void> repayResult = principalRepayService.loanRepay(billRepayApplyDto);
            if (!repayResult.isSuccess()) {
                log.info("[{}]-{} 内部还款 资方还款请求失败", innerRepayDto.getRepayMethod(), innerRepayDto.getBillRepayNo());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "资方还款请求失败");
            }

            RepaySchedule schedule = repayScheduleMapper.selectById(RepayScheduleId);

            String repayApplyNo = schedule.getRepayApplyNo();
            // 根据 repayApplyNo 查询是否已存在记录
            RepayScheduleApply existingApply = repayScheduleApplyMapper.selectByRepayApplyNo(repayApplyNo);

            if (existingApply == null) {
                // 不存在，插入新记录
                RepayScheduleApply repayScheduleApply = new RepayScheduleApply();
                repayScheduleApply.setRepayApplyNo(repayApplyNo);
                repayScheduleApply.setUserId(schedule.getUserId());
                repayScheduleApply.setRepayScheduleId(schedule.getId());
                repayScheduleApply.setRepayType(4); // 固定为 4
                repayScheduleApply.setRepayStatus(0);
                repayScheduleApply.setCreateTime(new Date());
                repayScheduleApply.setUpdateTime(new Date());
                repayScheduleApplyMapper.insert(repayScheduleApply);
                log.info("[{}]-{} 内部还款 申请记录插入成功，repayApplyNo: {}", innerRepayDto.getRepayMethod(), innerRepayDto.getBillRepayNo(), repayApplyNo); 
            } else {
                log.info("[{}]-{} 内部还款 申请记录已存在，repayApplyNo: {} type: {} status: {}", innerRepayDto.getRepayMethod(), innerRepayDto.getBillRepayNo(), repayApplyNo, existingApply.getRepayType(), existingApply.getRepayStatus());
                // 已存在，更新记录
                existingApply.setRepayType(4); // 固定为 4
                existingApply.setRepayStatus(0); // 重置状态或根据业务逻辑更新
                existingApply.setUpdateTime(new Date()); 
                repayScheduleApplyMapper.updateById(existingApply);
                log.info("[{}]-{} 内部还款 申请记录更新成功，repayApplyNo: {}", innerRepayDto.getRepayMethod(), innerRepayDto.getBillRepayNo()  , repayApplyNo); 
            }

        } finally {
            if (isLocked) {
                lock.unlock();
            }
        }
        return ResponseResult.success();
    }

    @Override
    public ResponseResult<Void> innerRepaySale(InnerRepayDto repayDto) {
        Long saleScheduleId = repayDto.getBillRepayNo();

        RLock lock = redissonClient.getLock("Sale" + saleScheduleId);
        boolean isLocked = false;
        try {
            isLocked = lock.tryLock();
            if (!isLocked) {
                log.info("[{}]-{} 内部还款 请勿重复操作", repayDto.getRepayMethod(), repayDto.getBillRepayNo());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "请勿重复操作");
            }
            SaleSchedule saleSchedule = saleScheduleMapper.selectById(saleScheduleId);
            if (!saleSchedule.getSettleFlag().equals(SettleFlagConstant.RUNNING)) {
                log.info("[{}]-{} 内部还款 账单状态不正确", repayDto.getRepayMethod(), repayDto.getBillRepayNo());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "账单状态不正确");
            }

            // 若一笔扣款正在进行中（无论是用户主动还款还是跑批扣款），尚未拿到最终结果，则不允许再付发起第二笔。
            int i = saleScheduleMapper.payingCount(saleSchedule.getDisburseId());
            if (i > 0) {
                log.info("[{}]-{} 内部还款 有一笔账单扣款在进行中", repayDto.getRepayMethod(), repayDto.getBillRepayNo());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "有一笔账单扣款在进行中");
            }

            BillRepayApplyDto billRepayApplyDto = new BillRepayApplyDto();
            // billRepayApplyDto.setRepayScheduleId(saleSchedule.getId());
            billRepayApplyDto.setRepaySaleId(saleSchedule.getId());
            billRepayApplyDto.setRepayMethod("1");
            billRepayApplyDto.setBankAccountType("1");
            Integer repayMethod = repayDto.getRepayMethod();
            if (repayMethod != null && repayMethod == 2) {
                billRepayApplyDto.setRepayMethod("0");
                billRepayApplyDto.setBankAccountType("0");
            }
            // 调用还款接口
            ResponseResult<Void> repayResult = saleRepayService.saleRepayNew(billRepayApplyDto);
            if (!repayResult.isSuccess()) {
                log.info("[{}]-{} 内部还款 资方还款请求失败", repayDto.getRepayMethod(), repayDto.getBillRepayNo());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "资方还款请求失败");
            }

            SaleSchedule schedule = saleScheduleMapper.selectById(saleScheduleId);

            RepaySaleApply repayScheduleApply = new RepaySaleApply();
            repayScheduleApply.setRepayApplyNo(schedule.getRepayApplyNo());
            repayScheduleApply.setUserId(saleSchedule.getUserId());
            repayScheduleApply.setSaleScheduleId(schedule.getId());
            repayScheduleApply.setRepayType(4);
            repayScheduleApply.setRepayStatus(0);
            repaySaleApplyMapper.insert(repayScheduleApply);
            log.info("[{}]-{} 内部还款 申请记录插入成功，repayApplyNo: {}", repayDto.getRepayMethod(), repayDto.getBillRepayNo(), schedule.getRepayApplyNo());
        } finally {
            if (isLocked) {
                lock.unlock();
            }
        }
        return ResponseResult.success();
    }
}
