package com.rongchen.byh.app.dto.app;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName FaceVerifyDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/11 12:10
 * @Version 1.0
 **/
@Data
public class FaceVerifyDto {

    @Schema(description = "人脸图片url")
    private String faceImageUrl;

    @Schema(description = "人脸视频url")
    private String faceImageBase64;

    @Schema(description = "人脸识别分数")
    private BigDecimal faceScore;

    @Schema(description = "人脸校验置信区间")
    private String faceConfidence;

    @Schema(description = "人脸校验置信区间")
    private String orderNo;

    @Schema(description = "业务ID")
    private String bizId;


}
