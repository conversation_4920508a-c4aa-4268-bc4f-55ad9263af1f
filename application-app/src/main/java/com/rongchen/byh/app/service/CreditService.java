package com.rongchen.byh.app.service;

import com.rongchen.byh.app.dto.app.BackVo;
import com.rongchen.byh.app.entity.CapitalData;
import com.rongchen.byh.common.core.object.ResponseResult;

import java.util.List;

/**
 * @ClassName CreditService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/15 14:34
 * @Version 1.0
 **/
public interface CreditService {

    ResponseResult<Void> creditApply(CapitalData capitalData, Long userId);

    ResponseResult<Void> loanApply(Long userId);
}
