package com.rongchen.byh.app.mqListener;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rabbitmq.client.Channel;
import com.rongchen.byh.app.dao.RepayScheduleApplyMapper;
import com.rongchen.byh.app.dao.RepayScheduleMapper;
import com.rongchen.byh.app.dto.app.BillRepayApplyDto;
import com.rongchen.byh.app.entity.DisburseData;
import com.rongchen.byh.app.entity.RepaySchedule;
import com.rongchen.byh.app.entity.RepayScheduleApply;
import com.rongchen.byh.app.service.DisburseDataService;
import com.rongchen.byh.app.service.DisburseRecordService;
import com.rongchen.byh.app.service.PrincipalRepayService;
import com.rongchen.byh.app.service.RepayPlanUpdateService;
import com.rongchen.byh.common.core.constant.SettleFlagConstant;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.MDCUtil;
import com.rongchen.byh.common.rabbitmq.constants.QueueConstant;
import com.rongchen.byh.common.rabbitmq.dto.RepayDto;
import com.rongchen.byh.common.redis.util.CommonRedisUtil;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

/**
 * 还款请求通知监听
 */
@Component
@Slf4j
public class RepayListener {

    @Resource
    private RepayScheduleMapper repayScheduleMapper;

    @Resource
    private PrincipalRepayService principalRepayService;
    @Resource
    DisburseRecordService disburseRecordService;
    @Resource
    RepayScheduleApplyMapper repayScheduleApplyMapper;

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private CommonRedisUtil commonRedisUtil;

    @Resource
    private RepayPlanUpdateService repayPlanUpdateService;

    @Resource
    private DisburseDataService disburseDataService;


    @RabbitListener(queues = QueueConstant.REPAY_QUEUE, ackMode = "MANUAL")
    public void receiver(String msg, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        
        RepayDto repayDto = null;
        String traceId = "未知";
        Long repayId = null;
        String lockKey = "未知";

        Map<String, String> contextMap = new HashMap<>();
        RLock lock = null;

        try {
            try {
                repayDto = JSONObject.parseObject(msg, RepayDto.class);
            } catch (Exception e) {
                log.error("【还款监听器】消息解析失败，原始消息: {}, 错误: {}", msg, e.getMessage(), e);
                channel.basicNack(deliveryTag, false, false);
                return;
            }

            if (repayDto == null || repayDto.getRepayId() == null) {
                log.error("【还款监听器】接收到的 DTO 为空或缺少关键字段 (repayId)，消息: {}", msg);
                channel.basicNack(deliveryTag, false, false);
                return;
            }

            traceId = StrUtil.isNotBlank(repayDto.getTraceId()) ? repayDto.getTraceId() : UUID.fastUUID().toString(true);
            MDCUtil.setTraceId(traceId);
            log.info("【还款监听器】开始处理消息，RabbitMQ原始消息: {} tag: {}", msg, deliveryTag);
            repayId = repayDto.getRepayId();

            lockKey = String.format("lock:repay:%d", repayId);
            lock = redissonClient.getLock(lockKey);

            boolean locked = false;
            try {
                locked = lock.tryLock(0, 60, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                log.warn("【还款监听器】获取分布式锁时被中断，锁键: {}. 消息将重新排队。", lockKey, e);
                Thread.currentThread().interrupt();
                channel.basicNack(deliveryTag, false, true);
                return;
            }

            if (!locked) {
                log.warn("【还款监听器】未能获取还款处理锁（可能已有其他实例在处理），锁键: {}. 消息将重新排队。", lockKey);
                channel.basicNack(deliveryTag, false, true);
                return;
            }

            log.info("【还款监听器】成功获取还款处理锁，开始处理还款消息，锁键: {}, 原始消息: {}", lockKey, msg);
            try {
                DisburseData disburseData = disburseDataService.lambdaQuery()
                    .eq(DisburseData::getId, repayDto.getDisburseId())
                    .one();
                if(disburseData == null){
                    log.error("【还款监听器】[锁内] 未找到对应的支用账单，disburseId: {}，消息将被确认。", repayDto.getDisburseId());
                    channel.basicAck(deliveryTag, false);
                    return;
                }
                //同步更新一下还款计划
                repayPlanUpdateService.triggerRepayPlanUpdate(disburseData.getUserId());
                log.info("【还款监听器】[锁内] 更新还款计划成功，disburseId: {}", repayDto.getDisburseId());
            } catch (IOException e) {
               log.error("【还款监听器】[锁内] 更新还款计划失败，disburseId: {}，错误: {}", repayDto.getDisburseId(), e.getMessage(), e);
            }

            try {
                //获取还款计划
                RepaySchedule repaySchedule = repayScheduleMapper.selectById(repayId);
                if (repaySchedule == null) {
                    log.warn("【还款监听器】[锁内] 未找到对应的还款计划，repayId: {}，消息将被确认。", repayId);
                    channel.basicAck(deliveryTag, false);
                    return;
                }

                if (!repaySchedule.getSettleFlag().equals(SettleFlagConstant.RUNNING)) {
                    log.info("【还款监听器】[锁内] 还款校验 账单状态不正确 (非RUNNING)，账单：{}，状态：{}。消息将被确认。", repayId,
                            repaySchedule.getSettleFlag());
                    channel.basicAck(deliveryTag, false);
                    return;
                }
                
                //校验账单是否一笔扣款在进行中
                int i = repayScheduleMapper.payingCount(repaySchedule.getDisburseId());
                if (i > 0) {
                    log.info("【还款监听器】[锁内] 还款校验 账单一笔扣款在进行中，账单：{}，状态：{}。消息将被确认。", repayId, repaySchedule.getSettleFlag());
                    channel.basicAck(deliveryTag, false);
                    return;
                }

                BillRepayApplyDto billRepayApplyDto = new BillRepayApplyDto();
                billRepayApplyDto.setRepayScheduleId(repaySchedule.getId());
                billRepayApplyDto.setRepayMethod("0");
                billRepayApplyDto.setBankAccountType("0");
                ResponseResult<Void> repayResult = principalRepayService.loanRepay(billRepayApplyDto);

                if (!repayResult.isSuccess()) {
                    log.info("【还款监听器】[锁内] 还款校验 资方还款失败，账单：{}。错误码: {}, 错误信息: {}. 消息将被确认。",
                            repaySchedule.getId(), repayResult.getErrorCode(), repayResult.getErrorMessage());
                    disburseRecordService.saveRecord(2, "账单(repaySchedule)id:" + repaySchedule.getId(), traceId,
                            "资方本金还款失败，错误码: " + repayResult.getErrorCode() + ", 错误信息: " + repayResult.getErrorMessage());
                    channel.basicAck(deliveryTag, false);
                    return;
                }

                RepaySchedule schedule = repayScheduleMapper.selectById(repayId);
                if (schedule == null) {
                    log.error("【还款监听器】[锁内] 资方还款成功后，重新查询还款计划失败，repayId: {}。消息将拒绝并不重排队。", repayId);
                    channel.basicNack(deliveryTag, false, false);
                    return;
                }

                Integer repayType = repayDto.getRepayType();
                String repayApplyNo = schedule.getRepayApplyNo();

                if (repayApplyNo == null || repayApplyNo.isEmpty()) {
                    log.error("【还款监听器】[锁内] 获取到的 repayApplyNo 为空，无法处理还款申请记录，repayId: {}。消息将拒绝并不重排队。", repayId);
                    channel.basicNack(deliveryTag, false, false);
                    return;
                }

                List<RepayScheduleApply> existingApplyList = repayScheduleApplyMapper
                        .selectList(new LambdaQueryWrapper<RepayScheduleApply>()
                                .eq(RepayScheduleApply::getRepayApplyNo, repayApplyNo));

                if (existingApplyList.isEmpty()) {
                    RepayScheduleApply repayScheduleApply = new RepayScheduleApply();
                    repayScheduleApply.setRepayApplyNo(repayApplyNo);
                    repayScheduleApply.setUserId(schedule.getUserId());
                    repayScheduleApply.setRepayScheduleId(schedule.getId());
                    repayScheduleApply.setRepayType(repayType);
                    repayScheduleApply.setRepayStatus(0);
                    repayScheduleApply.setCreateTime(new Date());
                    repayScheduleApply.setUpdateTime(new Date());
                    repayScheduleApplyMapper.insert(repayScheduleApply);
                    log.info("【还款监听器】[锁内] 未找到还款申请记录，已插入新记录，repayApplyNo: {}", repayApplyNo);
                } else {
                    log.info("【还款监听器】[锁内] 找到 {} 条已存在的还款申请记录，repayApplyNo: {}。将全部更新类型和状态。",
                            existingApplyList.size(), repayApplyNo);

                    for (RepayScheduleApply applyToUpdate : existingApplyList) {
                        log.debug("【还款监听器】[锁内] 正在更新记录 ID: {}, 原状态: {}, 原类型: {}",
                                applyToUpdate.getId(), applyToUpdate.getRepayStatus(), applyToUpdate.getRepayType());

                        applyToUpdate.setRepayType(repayType);
                        applyToUpdate.setRepayStatus(0);
                        applyToUpdate.setUpdateTime(new Date());
                        repayScheduleApplyMapper.updateById(applyToUpdate);
                    }
                    log.info("【还款监听器】[锁内] 更新 {} 条还款申请记录成功，repayApplyNo: {}", existingApplyList.size(), repayApplyNo);
                }

                log.info("【还款监听器】还款消息处理成功，锁键: {}", lockKey);
                channel.basicAck(deliveryTag, false);
                log.debug("【还款监听器】消息已确认 (ACK)，锁键: {}, deliveryTag: {}", lockKey, deliveryTag);

            } catch (Exception e) {
                log.error("【还款监听器】[锁内] 处理业务逻辑时发生异常，锁键: {}, 错误: {}", lockKey, e.getMessage(), e);
                channel.basicNack(deliveryTag, false, false);
                log.warn("【还款监听器】业务处理异常，消息已拒绝 (NACK)，锁键: {}, deliveryTag: {}", lockKey, deliveryTag);
            } finally {
                if (lock != null && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                    log.debug("【还款监听器】分布式锁已释放，锁键: {}", lockKey);
                }
            }

        } catch (IOException e) {
            log.error("【还款监听器】手动确认/拒绝消息时发生 IO 错误, 锁键(可能未知): {}, deliveryTag: {}. 错误: {}",
                    (lockKey != null ? lockKey : "未知"), deliveryTag, e.getMessage(), e);
        } catch (Exception e) {
            log.error("【还款监听器】处理消息外层发生未知错误, 锁键(可能未知): {}, deliveryTag: {}. 错误: {}",
                    (lockKey != null ? lockKey : "未知"), deliveryTag, e.getMessage(), e);
            try {
                channel.basicNack(deliveryTag, false, false);
                log.warn("【还款监听器】外层未知错误，消息已尝试拒绝 (NACK)，锁键(可能未知): {}, deliveryTag: {}",
                        (lockKey != null ? lockKey : "未知"), deliveryTag);
            } catch (IOException ioEx) {
                log.error("【还款监听器】外层未知错误，尝试拒绝消息时发生 IO 错误, 锁键(可能未知): {}, deliveryTag: {}. 错误: {}",
                        (lockKey != null ? lockKey : "未知"), deliveryTag, ioEx.getMessage(), ioEx);
            }
        } finally {
            MDC.clear();
            if (lock != null && lock.isHeldByCurrentThread()) {
                try {
                    lock.unlock();
                    log.warn("【还款监听器】分布式锁在最外层 finally 中被释放（可能表示有异常发生在业务 try 块之前），锁键: {}", lockKey);
                } catch (Exception unlockEx) {
                    log.error("【还款监听器】在最外层 finally 中释放锁时发生异常, 锁键: {}", lockKey, unlockEx);
                }
            }
        }
    }

}
