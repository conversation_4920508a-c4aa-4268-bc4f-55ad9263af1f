package com.rongchen.byh.app.config;


import cn.hutool.core.util.StrUtil;
import com.rongchen.byh.app.vo.UploadFileVo;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.upload.BaseUpDownloader;
import com.rongchen.byh.common.core.upload.UpDownloaderFactory;
import com.rongchen.byh.common.core.upload.UploadResponseInfo;
import com.rongchen.byh.common.core.upload.UploadStoreTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

@Component
public class FileUtilService {


    @Autowired
    private UpDownloaderFactory upDownloaderFactory;
    public static final String domain = "https://12qyc-1328155466.cos.ap-guangzhou.myqcloud.com/image/";


    public ResponseResult<UploadFileVo> upResult(MultipartFile file, HttpServletRequest request) throws IOException {
        BaseUpDownloader upDownloader = upDownloaderFactory.get(UploadStoreTypeEnum.QCLOUD_COS_SYTEM);
        UploadResponseInfo responseInfo = upDownloader.doUpload(null, "qyc", "image", file);
        if (responseInfo.getUploadFailed()) {
            return ResponseResult.error(ErrorCodeEnum.FAIL,responseInfo.getErrorMessage());
        }
        String uploadPath = responseInfo.getUploadPath();
        String filename = responseInfo.getFilename();
        String downloadUri = responseInfo.getDownloadUri();
        UploadFileVo vo = new UploadFileVo();
        vo.setDownloadUri(downloadUri);
        vo.setUploadPath(uploadPath);
        vo.setFilename(filename);

//        String requestURI = request.getRequestURI();
//        StringBuffer requestURL = request.getRequestURL();
//        String domain = StrUtil.removeSuffix(requestURL, requestURI);
//        String domain = StrUtil.removeSuffix(requestURL, requestURI);
        String url = domain + filename;
        String replace = StrUtil.replace(url, "http://", "https://");
        vo.setDownloadUrl(replace);
        return ResponseResult.success(vo);
    }
}
