package com.rongchen.byh.app.service;

import com.rongchen.byh.common.core.object.ResponseResult;

/**
 * 用信相关接口
 */
public interface LoanService {


    /**
     * 用信申请
     * @return
     */
    ResponseResult<Void> loanApply(Long userId);

    /**
     * 查询还款计划
     * @return
     */
    ResponseResult<Void> queryRepaymentSchedule(String loanNo);
    /**
     * api流程查询还款计划
     * @return
     */
    ResponseResult<Void> queryApiRepaymentSchedule(String loanNo);

    /**
     * 赊销订单生成
     * @return
     */
    ResponseResult<Void> createSaleApply(String loanNo);

    /**
     * api流程赊销订单生成
     * @return
     */
    ResponseResult<Void> createApiSaleApply(String loanNo);
}
