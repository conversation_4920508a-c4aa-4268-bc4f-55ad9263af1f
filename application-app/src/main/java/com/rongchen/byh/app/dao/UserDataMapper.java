package com.rongchen.byh.app.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rongchen.byh.app.entity.UserData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【user_data(用户基础信息表)】的数据库操作Mapper
* @createDate 2024-12-11 11:28:46
* @Entity generator.domain.UserData
*/
@Mapper
public interface UserDataMapper extends BaseMapper<UserData> {


    UserData queryByMobile(String mobile);

    Integer selectSaleRecordByMobile(String mobile);
}
