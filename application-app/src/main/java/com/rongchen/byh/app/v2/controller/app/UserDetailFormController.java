package com.rongchen.byh.app.v2.controller.app;

import com.rongchen.byh.app.entity.UserDetail;
import com.rongchen.byh.app.service.AppUserDetailService;
import com.rongchen.byh.app.v2.dto.UserDetailDto;
import com.rongchen.byh.app.v2.entity.UserDetailForm;
import com.rongchen.byh.app.v2.service.IUserDetailFormService;
import com.rongchen.byh.common.core.object.ResponseResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "用户表单类接口")
@RestController
@RequestMapping("/v2/userFormApi")
public class UserDetailFormController {
    @Autowired
    private IUserDetailFormService userDetailFormService;
    @Autowired
    private AppUserDetailService userDetailService;
    @Operation(summary = "获取基本信息表单")
    @GetMapping("/getUserDetailForm")
    public ResponseResult<List<UserDetailForm>> getUserDetailForm(Integer capitalId) {
      return ResponseResult.success(userDetailFormService.selectByCapitalId(capitalId));
    }
    @Operation(summary = "表单回显")
    @GetMapping("/getInfoForm")
    public ResponseResult<UserDetailDto> getInfoForm() {
        return ResponseResult.success(userDetailService.getInfoForm());
    }

}