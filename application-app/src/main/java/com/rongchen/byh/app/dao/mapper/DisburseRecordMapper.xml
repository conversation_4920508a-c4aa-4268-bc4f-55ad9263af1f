<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.dao.DisburseRecordMapper">

    <resultMap id="BaseResultMap" type="com.rongchen.byh.app.entity.DisburseRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="loanNo" column="loan_no" jdbcType="VARCHAR"/>
            <result property="traceId" column="trace_id" jdbcType="VARCHAR"/>
            <result property="location" column="location" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,type,loan_no,
        trace_id,location,create_time,
        update_time
    </sql>
</mapper>
