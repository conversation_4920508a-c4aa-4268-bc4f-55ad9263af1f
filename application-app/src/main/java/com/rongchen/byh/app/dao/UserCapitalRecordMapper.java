package com.rongchen.byh.app.dao;

import com.rongchen.byh.app.entity.UserCapitalRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【user_capital_record(员工资方授信状态表)】的数据库操作Mapper
* @createDate 2025-02-18 16:38:22
* @Entity com.rongchen.byh.app.model.UserCapitalRecord
*/
@Mapper
public interface UserCapitalRecordMapper extends BaseMapper<UserCapitalRecord> {

    List<UserCapitalRecord> selectListByUserId(Long userId);
}




