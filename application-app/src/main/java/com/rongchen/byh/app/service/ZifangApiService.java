package com.rongchen.byh.app.service;

import com.rongchen.byh.app.dto.api.CreditNoticeDto;
import com.rongchen.byh.app.dto.api.LoanNoticeDto;
import com.rongchen.byh.app.dto.api.RepayNoticeDto;
import com.rongchen.byh.app.dto.api.SaleNoticeDto;
import com.rongchen.byh.common.api.zifang.vo.BaseVo;

public interface ZifangApiService {

    /**
     * 授信回调
     * @param creditNoticeDto
     * @return
     */
    BaseVo creditNotice(CreditNoticeDto creditNoticeDto);

    /**
     * 用信结果通知
     * @param loanNoticeDto
     * @return
     */
    BaseVo loanNotice(LoanNoticeDto loanNoticeDto);

    /**
     * 还款接口通知
     * @param repayNoticeDto
     * @return
     */
    BaseVo repayNotice(RepayNoticeDto repayNoticeDto);

    /**
     * 赊销接口溶质
     * @param saleNoticeDto
     * @return
     */
    BaseVo saleNotice(SaleNoticeDto saleNoticeDto);

}
