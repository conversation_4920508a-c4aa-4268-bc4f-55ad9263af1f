package com.rongchen.byh.app.vo.app;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName UserFormVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/11 16:47
 * @Version 1.0
 **/
@Data
public class UserFormVo {

    @Schema(description = "用户id")
    private Long id;

    @Schema(description = "用户手机号")
    private String mobile;

    @Schema(description = "用户姓名")
    private String name;

    @Schema(description = "身份证")
    private String webIdCard;

    @Schema(description = "渠道id")
    private Integer channelId;

    @Schema(description = "教育程度")
    private String educationLevel;

    @Schema(description = "婚姻状态")
    private String maritalStatus;

    @Schema(description = "住房情况")
    private String houseStatus;

    @Schema(description = "个人住址（详细）")
    private String custAddress;

    @Schema(description = "个人住址（省）")
    private String custAddressProvice;

    @Schema(description = "个人住址（市）")
    private String custAddressCity;

    @Schema(description = "个人住址（区）")
    private String custAddressCounty;
    @Schema(description = "个人月收入")
    private String incomeMonth;

    @Schema(description = "紧急联系人关系1")
    private String relationshipOne;

    @Schema(description = "紧急联系人姓名1")
    private String emergencyNameOne;

    @Schema(description = "紧急联系人手机号码1")
    private String emergencyMobileOne;

    @Schema(description = "紧急联系人关系2")
    private String relationshipTwo;

    @Schema(description = "紧急联系人姓名2")
    private String emergencyNameTwo;

    @Schema(description = "紧急联系人手机号码2")
    private String emergencyMobileTwo;

    @Schema(description = "是否已完成表单 0 未完成  1 已完成")
    private Integer formFlag;

    @Schema(description = "表单完成时间")
    private String formTime;

    @Schema(description = "注册时间")
    private String createTime;

    @Schema(description = "公司名称")
    private String companyName;

    @Schema(description = "职业 1: 上班族2：企业主3：个体户4：自由职业")
    private Integer professional;

}
