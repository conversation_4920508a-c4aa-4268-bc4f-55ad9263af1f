package com.rongchen.byh.app.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rongchen.byh.app.config.ZifangFactory;
import com.rongchen.byh.app.constant.ZiFangBeanConstant;
import com.rongchen.byh.app.dao.CapitalDataMapper;
import com.rongchen.byh.app.dao.DisburseDataMapper;
import com.rongchen.byh.app.dao.RepayScheduleMapper;
import com.rongchen.byh.app.dao.SaleScheduleMapper;
import com.rongchen.byh.app.dao.UserBankCardMapper;
import com.rongchen.byh.app.dao.UserDataMapper;
import com.rongchen.byh.app.dao.UserDetailMapper;
import com.rongchen.byh.app.dto.app.BackVo;
import com.rongchen.byh.app.entity.CapitalData;
import com.rongchen.byh.app.entity.DisburseData;
import com.rongchen.byh.app.entity.RepaySchedule;
import com.rongchen.byh.app.entity.SaleSchedule;
import com.rongchen.byh.app.entity.UserData;
import com.rongchen.byh.app.entity.UserDetail;
import com.rongchen.byh.app.service.LoanService;
import com.rongchen.byh.app.service.RepayScheduleService;
import com.rongchen.byh.app.service.SaleOrderRecordService;
import com.rongchen.byh.common.api.zifang.dto.LoanApplyDto;
import com.rongchen.byh.common.api.zifang.dto.RepaymentPlanQueryDto;
import com.rongchen.byh.common.api.zifang.dto.SaleApplyDto;
import com.rongchen.byh.common.api.zifang.dto.SaleApplyPkgDto;
import com.rongchen.byh.common.api.zifang.service.LoanApi;
import com.rongchen.byh.common.api.zifang.service.OtherApi;
import com.rongchen.byh.common.api.zifang.service.RepaymentApi;
import com.rongchen.byh.common.api.zifang.vo.BaseVo;
import com.rongchen.byh.common.api.zifang.vo.CreditApplyVo;
import com.rongchen.byh.common.api.zifang.vo.RepaymentPlanQueryPkgVo;
import com.rongchen.byh.common.api.zifang.vo.RepaymentPlanQueryVo;
import com.rongchen.byh.common.api.zifang.vo.SaleApplyVo;
import com.rongchen.byh.common.api.zifang.vo.loan.RepayPlanListVo;
import com.rongchen.byh.common.api.zifang.vo.loan.RespRepayPlanPkgVo;
import com.rongchen.byh.common.api.zifang.vo.loan.RespRepayPlanVo;
import com.rongchen.byh.common.api.zifang.vo.loan.RespSaleRepayPlanVo;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.MyModelUtil;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class LoanServiceImpl implements LoanService {

    @Resource
    LoanApi loanApi;
    @Resource
    RepaymentApi repaymentApi;
    @Resource
    UserDataMapper userDataMapper;
    @Resource
    UserDetailMapper userDetailMapper;
    @Resource
    DisburseDataMapper disburseDataMapper;
    @Resource
    UserBankCardMapper userBankCardMapper;
    @Resource
    RepayScheduleMapper repayScheduleMapper;
    @Resource
    RepayScheduleService repayScheduleService;
    @Resource
    OtherApi otherApi;
    @Resource
    SaleScheduleMapper saleScheduleMapper;
    @Resource
    ZifangFactory zifangFactory;
    @Resource
    CapitalDataMapper capitalDataMapper;
    @Resource
    private SaleOrderRecordService saleOrderRecordService;

    @Deprecated
    @Override
    public ResponseResult<Void> loanApply(Long userId) {
        UserDetail userDetail = userDetailMapper.queryByUserId(userId);
        DisburseData disburseData = disburseDataMapper.selectByUserId(userId);
        BackVo backVo = userBankCardMapper.queryBackByUserId(userId);
        LoanApplyDto loanApplyDto = new LoanApplyDto();
        loanApplyDto.setLoanNo(disburseData.getLoanNo());
        loanApplyDto.setIdNo(userDetail.getIdNumber());
        loanApplyDto.setSerialNo(disburseData.getSaleNo());
        loanApplyDto.setCreditNo(disburseData.getCreditNo());
        loanApplyDto.setApplyAmount(disburseData.getCreditAmount().toString());
        loanApplyDto.setApplyTerm(disburseData.getPeriods() + "");
        loanApplyDto.setTermType("02");
        loanApplyDto.setApplyUse(transUse(disburseData.getPurposeLoan()));
        // loanApplyDto.setOtherPurpose("");
        loanApplyDto.setUserId(userId + "");
        loanApplyDto.setPayWay("R9926");
        loanApplyDto.setBankCardNum(backVo.getIdCard());
        loanApplyDto.setBankPhone(backVo.getMobile());
        loanApplyDto.setBankName(backVo.getBankName());
        loanApplyDto.setApplyTime(DateUtil.now());
        CapitalData capitalData = capitalDataMapper.selectById(disburseData.getCapitalId());
        if (ObjectUtil.isEmpty(capitalData)) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "资方未匹配");
        }
        LoanApi loanApi = zifangFactory.getApi(capitalData.getBeanName(), LoanApi.class);
        ResponseResult<CreditApplyVo> responseResult = loanApi.loanApply(loanApplyDto);
        if (responseResult.isSuccess()) {
            BaseVo data = responseResult.getData();
            if ("0000".equals(data.getResponseCode())) {
                DisburseData updateDate = new DisburseData();
                updateDate.setId(disburseData.getId());
                updateDate.setLoanNo(disburseData.getLoanNo());
                disburseDataMapper.updateById(updateDate);
                return ResponseResult.success();
            }

        }
        return ResponseResult.error(ErrorCodeEnum.FAILED_TO_INVOKE_THIRDPARTY_URL);
    }

    private String transUse(String purposeLoan) {
        String use = "";
        switch (purposeLoan) {
            case "个人日常消费":
                use = "HEA";
                break;
            case "装修":
                use = "DEC";
                break;
            case "教育":
                use = "EDU";
                break;
            case "手机数码":
                use = "MOD";
                break;
            case "电器":
                use = "HEA";
                break;
            case "家具家居":
                use = "HEA";
                break;
            case "医疗":
                use = "MED";
                break;
            case "租房":
                use = "REN";
                break;
            case "旅游":
                use = "TRA";
                break;
            case "婚庆":
                use = "MAR";
                break;
        }
        return use;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> queryRepaymentSchedule(String loanNo) {
        log.info("[{}] 还款计划同步(标准流程) 开始", loanNo);
        DisburseData disburseData = disburseDataMapper.selectByLoanNo(loanNo);
        if (ObjectUtil.isEmpty(disburseData)) {
            log.warn("[{}] 未找到对应的放款记录", loanNo);
            return ResponseResult.success();
        }

        // 检查总期数是否存在，避免空指针 (保留此检查)
        Integer totalPeriods = disburseData.getPeriods();
        if (totalPeriods == null || totalPeriods <= 0) {

            log.warn("[{}] 支用记录的总期数未设置或无效，跳过还款计划同步。", loanNo);
            return ResponseResult.error(ErrorCodeEnum.FAIL, "支用记录总期数无效");
        }

        // 调用 API 获取还款计划
        RepaymentPlanQueryDto queryDto = new RepaymentPlanQueryDto();
        queryDto.setUserId(disburseData.getUserId() + "");
        queryDto.setLoanNo(disburseData.getLoanNo());
        CapitalData capitalData = capitalDataMapper.selectById(disburseData.getCapitalId());
        if (ObjectUtil.isEmpty(capitalData)) {
            log.error("[{}] Exception:还款计划同步失败: 资方未匹配 (Capital ID: {}),", loanNo,
                disburseData.getCapitalId());
            return ResponseResult.error(ErrorCodeEnum.FAIL, "资方未匹配");
        }
        // 如有需要，针对特定资方的特殊处理
        if (ZiFangBeanConstant.FENZHUAN.equals(capitalData.getBeanName())) {
            queryDto.setLoanNo(disburseData.getCreditNo());
        }

        RepaymentApi specificRepaymentApi = zifangFactory.getApi(capitalData.getBeanName(), RepaymentApi.class);
        ResponseResult<RepaymentPlanQueryVo> result = specificRepaymentApi.getRepaymentPlanQuery(queryDto);

        if (!result.isSuccess()) {
            log.error("【还款计划同步】借款单号：{} 查询资方还款计划接口失败: {}", loanNo, result.getErrorMessage());
            return ResponseResult.error(ErrorCodeEnum.FAILED_TO_INVOKE_THIRDPARTY_URL, "查询资方还款计划接口失败");
        }
        RepaymentPlanQueryVo data = result.getData();
        log.info("[{}] 还款计划查询 资方返回结果：{}", loanNo, data); // 生产环境请谨慎记录敏感数据
        if (!"0000".equals(data.getResponseCode()) || data.getPkgList() == null) {
            log.error("【还款计划同步】借款单号：{} 资方返回结果错误或无数据：{}", loanNo, data.getResponseMsg());
            return ResponseResult.error(ErrorCodeEnum.FAILED_TO_INVOKE_THIRDPARTY_URL, data.getResponseMsg());
        }

        List<RepaymentPlanQueryPkgVo> pkgList = data.getPkgList();
        if (pkgList.isEmpty()) {
            log.warn("[{}] 资方返回的还款计划列表为空，无需处理。", loanNo);
            // 如果计划为空，考虑是否仍需更新 DisburseData 的总利息
            return ResponseResult.success();
        }

        // 查询现有数据库记录
        Map<Integer, RepaySchedule> existingScheduleMap = queryExistingSchedules(disburseData.getId(), loanNo,
            "queryRepaymentSchedule(标准流程)");

        List<RepaySchedule> schedulesToInsert = new ArrayList<>();
        List<RepaySchedule> schedulesToUpdate = new ArrayList<>();
        BigDecimal totalRet = BigDecimal.ZERO; // 初始化总利息

        // 遍历 API 数据并比较
        for (RepaymentPlanQueryPkgVo pkgVo : pkgList) {
            Integer term;
            try {
                term = Integer.parseInt(pkgVo.getRepayTerm());
            } catch (NumberFormatException e) {
                log.error("[{}] 无法解析资方返回的期数: {}, 跳过此条记录。", loanNo, pkgVo.getRepayTerm(), e);
                continue; // 跳过此记录
            }

            RepaySchedule dbSchedule = existingScheduleMap.get(term);
            RepaySchedule scheduleToSave; // 代表此期数的最终状态

            if (dbSchedule != null) { // --- 更新逻辑 ---
                if (needsUpdate(pkgVo, dbSchedule, capitalData.getBeanName())) {
                    updateScheduleFromPkgVo(pkgVo, dbSchedule, capitalData.getBeanName());
                    schedulesToUpdate.add(dbSchedule);
                    log.debug("[{}] 期数 {} 标记为更新。", loanNo, term);
                } else {
                    log.debug("[{}] 期数 {} 数据匹配，无需更新。", loanNo, term);
                }
                // 使用（可能已更新的）dbSchedule 进行利息计算
                scheduleToSave = dbSchedule;
            } else { // --- 插入逻辑 ---
                RepaySchedule newSchedule = createScheduleFromPkgVo(pkgVo, disburseData.getId(),
                    disburseData.getUserId(), capitalData.getBeanName(), loanNo);
                if (newSchedule != null) { // 检查创建是否成功（例如，期数解析）
                    schedulesToInsert.add(newSchedule);
                    log.debug("[{}] 期数 {} 标记为插入。", loanNo, term);
                    scheduleToSave = newSchedule;
                } else {
                    scheduleToSave = null; // 表明处理此期数失败
                }
            }

            // 基于此期数的最终状态累加利息
            if (scheduleToSave != null) {
                totalRet = totalRet.add(safeAdd(scheduleToSave.getTermRetInt())) // 安全地加，null视为0
                    .add(safeAdd(scheduleToSave.getTermGuarantorFee()))
                    .add(safeAdd(scheduleToSave.getTermOverdueGuarantorFee())); // 使用最终值
            }
        }

        // -- 已移除旧的过滤和单一 saveList 逻辑 --

        // 执行批量数据库操作
        boolean insertSuccess = true;
        boolean updateSuccess = true;
        int insertedCount = 0;
        int updatedCount = 0;

        try {
            if (!schedulesToInsert.isEmpty()) {
                log.info("[{}] 准备插入 {} 条新的还款计划。", loanNo, schedulesToInsert.size());
                insertSuccess = repayScheduleService.saveBatch(schedulesToInsert);
                if (insertSuccess) {
                    insertedCount = schedulesToInsert.size();
                }
            }
        } catch (Exception e) {
            log.error("[{}] 批量插入还款计划时出错: {}", loanNo, e.getMessage(), e);
            insertSuccess = false;
            // 考虑是立即返回错误还是尝试执行更新
        }

        try {
            if (!schedulesToUpdate.isEmpty()) {
                log.info("[{}] 准备更新 {} 条现有的还款计划。", loanNo, schedulesToUpdate.size());
                // 假设 updateBatchById 的行为与 saveBatch 类似（关于返回值）
                // 注意: 如果部分更新失败，updateBatchById 可能返回 false，需要确认具体行为
                updateSuccess = repayScheduleService.updateBatchById(schedulesToUpdate);
                if (updateSuccess) {
                    updatedCount = schedulesToUpdate.size();
                }
            }
        } catch (Exception e) {
            log.error("[{}] 批量更新还款计划时出错: {}", loanNo, e.getMessage(), e);
            updateSuccess = false;
        }

        // 更新总利息
        // 如果需要，检查基于最终状态的计算与原始计算是否有显著差异，并记录日志
        log.info("[{}] 基于同步后数据计算的总利息: {}", loanNo, totalRet);
        DisburseData disburseUpdate = new DisburseData();
        disburseUpdate.setId(disburseData.getId());
        disburseUpdate.setGrossInterest(totalRet);
        disburseDataMapper.updateById(disburseUpdate); // 仅更新利息

        if (insertSuccess && updateSuccess) {
            log.info("[{}] 还款计划同步成功. 新增: {}, 更新: {}", loanNo, insertedCount, updatedCount);
            return ResponseResult.success();
        } else {
            log.error("[{}] 还款计划同步部分或完全失败. 插入成功: {}, 更新成功: {}", loanNo, insertSuccess,
                updateSuccess);
            // 如果可能，提供更具体的错误信息
            return ResponseResult.error(ErrorCodeEnum.FAIL,
                "还款计划同步操作失败 (插入: " + insertSuccess + ", 更新: " + updateSuccess + ")");
        }
    }

    /**
     * api流程查询还款计划
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> queryApiRepaymentSchedule(String loanNo) {
        log.info("[{}] 还款计划同步(API流程) 开始", loanNo);
        DisburseData disburseData = disburseDataMapper.selectByLoanNo(loanNo);
        if (ObjectUtil.isEmpty(disburseData)) {
            log.warn("[{}] 未找到对应的放款记录 (API流程)", loanNo);
            return ResponseResult.success();
        }

        // 检查总期数是否存在
        Integer totalPeriods = disburseData.getPeriods();
        if (totalPeriods == null || totalPeriods <= 0) {
            log.warn("[{}] 支用记录的总期数未设置或无效，跳过还款计划同步 (API流程)", loanNo);
            return ResponseResult.error(ErrorCodeEnum.FAIL, "支用记录总期数无效");
        }

        // 获取 CapitalData (针对API流程调整了查询)
        CapitalData capitalData = capitalDataMapper.selectById(disburseData.getCapitalId());
        if (ObjectUtil.isEmpty(capitalData)) {
            log.error("[{}] 还款计划同步失败 (API流程): 资方未匹配 (CapitalRecord ID: {})", loanNo,
                disburseData.getCapitalRecordId());
            return ResponseResult.error(ErrorCodeEnum.FAIL, "资方未匹配");
        }

        // 检查此资方是否使用API流程 (根据原始代码似乎是多余的检查?)
        if (capitalData.getBeanName().equals(ZiFangBeanConstant.FENZHUAN)) {
            log.warn("[{}] 资方 {} 不支持API用信流程的还款计划同步", loanNo, capitalData.getBeanName());
            return ResponseResult.error(ErrorCodeEnum.FAIL, "新资方无api用信流程");
        }

        // 调用 API 获取还款计划
        RepaymentPlanQueryDto queryDto = new RepaymentPlanQueryDto();
        queryDto.setUserId(disburseData.getUserId() + "");
        queryDto.setLoanNo(disburseData.getLoanNo());

        RepaymentApi specificRepaymentApi = zifangFactory.getApi(capitalData.getBeanName(), RepaymentApi.class);
        ResponseResult<RespRepayPlanVo> result = specificRepaymentApi.getApiRepaymentPlanQuery(queryDto);

        if (!result.isSuccess()) {
            log.error("【还款计划同步(API)】借款单号：{} 查询资方还款计划接口失败: {}", loanNo, result.getErrorMessage());
            return ResponseResult.error(ErrorCodeEnum.FAILED_TO_INVOKE_THIRDPARTY_URL, "查询资方(API)还款计划接口失败");
        }
        RespRepayPlanVo data = result.getData();
        // 谨慎记录数据
        log.info("[{}] 还款计划查询 (API流程) 资方返回结果：{}", loanNo, data);
        if (!"0000".equals(data.getResponseCode()) || data.getPkgList() == null) {
            log.error("【还款计划同步(API)】借款单号：{} 资方返回结果错误或无数据：{}", loanNo, data.getResponseMsg());
            return ResponseResult.error(ErrorCodeEnum.FAILED_TO_INVOKE_THIRDPARTY_URL, data.getResponseMsg());
        }

        List<RespRepayPlanPkgVo> pkgList = data.getPkgList();
        if (pkgList.isEmpty()) {
            log.info("[{}] 资方返回的还款计划列表为空 (API流程)，无需处理。", loanNo);
            return ResponseResult.success();
        }

        // 查询现有数据库记录
        Map<Integer, RepaySchedule> existingScheduleMap = queryExistingSchedules(disburseData.getId(), loanNo,
            "queryApiRepaymentSchedule(API流程)");

        List<RepaySchedule> schedulesToInsert = new ArrayList<>();
        List<RepaySchedule> schedulesToUpdate = new ArrayList<>();
        BigDecimal totalRet = BigDecimal.ZERO; // 初始化总利息

        // 遍历 API 数据并比较
        for (RespRepayPlanPkgVo pkgVo : pkgList) {
            Integer term;
            try {
                term = Integer.parseInt(pkgVo.getRepayTerm());
            } catch (NumberFormatException e) {
                log.error("[{}] 无法解析资方返回的期数 (API流程): {}, 跳过此条记录。", loanNo, pkgVo.getRepayTerm(), e);
                continue; // 跳过此记录
            }

            RepaySchedule dbSchedule = existingScheduleMap.get(term);
            RepaySchedule scheduleToSave;

            if (dbSchedule != null) { // --- 更新逻辑 ---
                if (needsUpdateApi(pkgVo, dbSchedule)) {
                    updateScheduleFromApiPkgVo(pkgVo, dbSchedule);
                    schedulesToUpdate.add(dbSchedule);
                    log.debug("[{}] 期数 {} (API) 标记为更新。", loanNo, term);
                } else {
                    log.debug("[{}] 期数 {} (API) 数据匹配，无需更新。", loanNo, term);
                }
                scheduleToSave = dbSchedule;
            } else { // --- 插入逻辑 ---
                RepaySchedule newSchedule = createScheduleFromApiPkgVo(pkgVo, disburseData.getId(),
                    disburseData.getUserId(), loanNo);
                if (newSchedule != null) {
                    schedulesToInsert.add(newSchedule);
                    log.debug("[{}] 期数 {} (API) 标记为插入。", loanNo, term);
                    scheduleToSave = newSchedule;
                } else {
                    scheduleToSave = null;
                }
            }

            // 基于此期数的最终状态累加利息
            if (scheduleToSave != null) {
                totalRet = totalRet.add(safeAdd(scheduleToSave.getTermRetInt()))
                    .add(safeAdd(scheduleToSave.getTermGuarantorFee()))
                    .add(safeAdd(scheduleToSave.getTermOverdueGuarantorFee()));
            }
        }

        // 执行批量数据库操作
        boolean insertSuccess = true;
        boolean updateSuccess = true;
        int insertedCount = 0;
        int updatedCount = 0;

        try {
            if (!schedulesToInsert.isEmpty()) {
                log.info("[{}] 准备插入 {} 条新的还款计划 (API)。", loanNo, schedulesToInsert.size());
                insertSuccess = repayScheduleService.saveBatch(schedulesToInsert);
                if (insertSuccess) {
                    insertedCount = schedulesToInsert.size();
                }
            }
        } catch (Exception e) {
            log.error("[{}] 批量插入还款计划时出错 (API): {}", loanNo, e.getMessage(), e);
            insertSuccess = false;
        }

        try {
            if (!schedulesToUpdate.isEmpty()) {
                log.info("[{}] 准备更新 {} 条现有的还款计划 (API)。", loanNo, schedulesToUpdate.size());
                updateSuccess = repayScheduleService.updateBatchById(schedulesToUpdate);
                if (updateSuccess) {
                    updatedCount = schedulesToUpdate.size();
                }
            }
        } catch (Exception e) {
            log.error("[{}] 批量更新还款计划时出错 (API): {}", loanNo, e.getMessage(), e);
            updateSuccess = false;
        }

        // 更新总利息
        log.info("[{}] 基于同步后数据计算的总利息 (API): {}", loanNo, totalRet);
        DisburseData disburseUpdate = new DisburseData();
        disburseUpdate.setId(disburseData.getId());
        disburseUpdate.setGrossInterest(totalRet);
        disburseDataMapper.updateById(disburseUpdate);

        if (insertSuccess && updateSuccess) {
            log.info("[{}] 还款计划同步成功 (API流程). 新增: {}, 更新: {}", loanNo, insertedCount, updatedCount);
            return ResponseResult.success();
        } else {
            log.error("[{}] 还款计划同步部分或完全失败 (API流程). 插入成功: {}, 更新成功: {}", loanNo, insertSuccess,
                updateSuccess);
            return ResponseResult.error(ErrorCodeEnum.FAIL,
                "还款计划同步操作失败 (API) (插入: " + insertSuccess + ", 更新: " + updateSuccess + ")");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> createSaleApply(String loanNo) {
        DisburseData disburseData = disburseDataMapper.selectByLoanNo(loanNo);
        if (ObjectUtil.isEmpty(disburseData)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }

        // 添加重复生成检查 - 赊销计划
        long existingSaleSchedules = saleScheduleMapper.selectCount(
            new QueryWrapper<SaleSchedule>().lambda()
                .eq(SaleSchedule::getDisburseId, disburseData.getId()));

        // 如果已存在3期或以上，则不再生成
        if (existingSaleSchedules >= 3) {
            log.warn("[{}] 赊销计划已存在 {} 期，跳过重复生成。", loanNo, existingSaleSchedules);
            return ResponseResult.success();
        }

        CapitalData capitalData = capitalDataMapper.selectById(disburseData.getCapitalId());
        if (ObjectUtil.isEmpty(capitalData)) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "资方未匹配");
        }
        // 计算赊销金额
        BigDecimal creditAmount = disburseData.getCreditAmount();
        // 总共可以收取的利息 本金利息+赊销利息
        BigDecimal multiply = creditAmount.multiply(new BigDecimal("0.3599"));
        // 总的赊销金额
        BigDecimal saleRepayAmount = multiply.subtract(disburseData.getGrossInterest());

        // 分转赊销计算
        if (capitalData.getBeanName().equalsIgnoreCase("fenzhua")) {
            // 1. 计算APR36的利息 = 贷款本金 * 36%
            BigDecimal apr36Interest = creditAmount.multiply(new BigDecimal("0.36"));
            log.info("【分转赊销】计算APR36的利息：{}", apr36Interest);
            // 2. 获取IRR24%计算出来的利息（已经在disburseData.getGrossInterest()中）
            BigDecimal irr24Interest = disburseData.getGrossInterest();
            log.info("【分转赊销】获取IRR24%计算出来的利息：{}", irr24Interest);
            // 3. 获取融担费总额（从repaySchedule中获取）
            List<RepaySchedule> scheduleList = repayScheduleService
                .lambdaQuery()
                .in(RepaySchedule::getDisburseId, disburseData.getId())
                .list();
            BigDecimal totalGuarantorFee = BigDecimal.ZERO;
            for (RepaySchedule schedule : scheduleList) {
                totalGuarantorFee = totalGuarantorFee.add(schedule.getTermGuarantorFee());
            }
            log.info("【分转赊销】获取融担费总额：{}", totalGuarantorFee);
            // 4. 计算商品权益总金额 = 贷款本金 * 36% - IRR24%计算出来的利息 + 融担费，即赊销金额
            saleRepayAmount = apr36Interest.subtract(irr24Interest).add(totalGuarantorFee);
            log.info("【分转赊销】计算商品权益总金额：{}", saleRepayAmount);
        }

        // 修改赊销金额
        DisburseData up = new DisburseData();
        up.setId(disburseData.getId());
        up.setSaleRepayAmount(saleRepayAmount);
        disburseDataMapper.updateById(up);

        SaleApplyDto saleApplyDto = new SaleApplyDto();
        saleApplyDto.setLoanNo(disburseData.getLoanNo());
        saleApplyDto.setSaleNo(disburseData.getSaleNo());
        saleApplyDto.setApplyAmt(saleRepayAmount.toString());
        saleApplyDto.setApplyTerm("3");
        // saleApplyDto.setGoodsName("");
        // saleApplyDto.setGoodsCode("");
        BigDecimal saleRate = saleRepayAmount.divide(creditAmount, 2, RoundingMode.HALF_UP);
        saleApplyDto.setSaleRate(saleRate.toString());
        saleApplyDto.setSaleModel("ZKBBL");

        // 每一期的金额
        BigDecimal divide = saleRepayAmount.divide(new BigDecimal("3"), 2, RoundingMode.HALF_UP);

        List<RepaySchedule> list = repayScheduleMapper.selectListByDisburseId(disburseData.getId());
        RepaySchedule schedule1 = list.get(0);
        RepaySchedule schedule2 = list.get(1);
        RepaySchedule schedule3 = list.get(2);
        List<SaleApplyPkgDto> pkg = new ArrayList<>();
        List<SaleSchedule> saveList = new ArrayList<>();

        Long userId = disburseData.getUserId();
        UserData userData = userDataMapper.selectById(userId);
        if (ObjectUtil.isEmpty(userData)) {
            log.info("【下款通知】生成赊销账单 借款单号：{} 用户不存在{}", loanNo, userId);
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SaleApplyPkgDto sale1 = buildSaleApplyPkgDto(schedule1, "1", divide);
        SaleSchedule saleSchedule1 = MyModelUtil.copyTo(sale1, SaleSchedule.class);
        saleSchedule1.setDisburseId(disburseData.getId());
        saleSchedule1.setUserId(userId);
        saleSchedule1.setRepayTerm("1");
        saleSchedule1.setTermStatus("N");
        saleSchedule1.setSettleFlag("RUNNING");
        saveList.add(saleSchedule1);
        pkg.add(sale1);

        SaleApplyPkgDto sale2 = buildSaleApplyPkgDto(schedule2, "2", divide);
        SaleSchedule saleSchedule2 = MyModelUtil.copyTo(sale2, SaleSchedule.class);
        saleSchedule2.setDisburseId(disburseData.getId());
        saleSchedule2.setUserId(userId);
        saleSchedule2.setRepayTerm("2");
        saleSchedule2.setTermStatus("N");
        saleSchedule2.setSettleFlag("RUNNING");
        saveList.add(saleSchedule2);
        pkg.add(sale2);

        BigDecimal subtract = saleRepayAmount.subtract(divide).subtract(divide);
        SaleApplyPkgDto sale3 = buildSaleApplyPkgDto(schedule3, "3", subtract);
        SaleSchedule saleSchedule3 = MyModelUtil.copyTo(sale3, SaleSchedule.class);
        saleSchedule3.setDisburseId(disburseData.getId());
        saleSchedule3.setUserId(userId);
        saleSchedule3.setRepayTerm("3");
        saleSchedule3.setTermStatus("N");
        saleSchedule3.setSettleFlag("RUNNING");
        saveList.add(saleSchedule3);
        pkg.add(sale3);

        saleApplyDto.setPkgList(pkg);
        saleApplyDto.setUserId(String.valueOf(userId));
        saleApplyDto.setMobile(userData.getMobile());

        OtherApi otherApi = zifangFactory.getApi(capitalData.getBeanName(), OtherApi.class);
        ResponseResult<SaleApplyVo> saleApply = otherApi.getSaleApply(saleApplyDto);
        if (!saleApply.isSuccess()) {
            log.info("【下款通知】生成赊销账单 借款单号：{} 资方返回结果异常：{}", loanNo, saleApply.getErrorMessage());
            return ResponseResult.error(ErrorCodeEnum.FAIL);
        }

        SaleApplyVo data = saleApply.getData();
        if (!"0000".equals(data.getResponseCode())) {
            log.info("【下款通知】生成赊销账单 借款单号：{} 资方返回结果错误：{}", loanNo, data.getResponseMsg());
            return ResponseResult.error(ErrorCodeEnum.FAIL);
        }

        // 保存赊销记录
        saleScheduleMapper.insertBatch(saveList);

        return ResponseResult.success();
    }

    /**
     * api流程生成赊销记录
     *
     * @param loanNo
     * @return
     */
    @Override
    public ResponseResult<Void> createApiSaleApply(String loanNo) {
        DisburseData disburseData = disburseDataMapper.selectByLoanNo(loanNo);
        if (ObjectUtil.isEmpty(disburseData)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        // 添加重复生成检查
        long existingSaleSchedules = saleScheduleMapper.selectCount(
            new QueryWrapper<SaleSchedule>().lambda()
                .eq(SaleSchedule::getDisburseId, disburseData.getId()));

        // 如果已存在3期或以上，则不再生成
        if (existingSaleSchedules >= 3) {
            log.warn("[{}] 赊销计划已存在 {} 期，跳过重复生成。", loanNo, existingSaleSchedules);
            return ResponseResult.success();
        }

        CapitalData capitalData = capitalDataMapper.selectById(disburseData.getCapitalId());
        if (ObjectUtil.isEmpty(capitalData)) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "资方未匹配");
        }

        if (capitalData.getBeanName().equalsIgnoreCase("fenzhua")) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "新资方无api方式用信");
        }

        SaleApplyDto saleApplyDto = new SaleApplyDto();
        saleApplyDto.setLoanNo(disburseData.getLoanNo());
        saleApplyDto.setSaleNo(disburseData.getSaleNo());

        OtherApi otherApi = zifangFactory.getApi(capitalData.getBeanName(), OtherApi.class);
        ResponseResult<RespSaleRepayPlanVo> saleApply = otherApi.getApiSaleApply(saleApplyDto);
        if (!saleApply.isSuccess()) {
            log.info("【下款通知】查询赊销账单 借款单号：{} 资方返回结果异常：{}", loanNo, saleApply.getErrorMessage());
            return ResponseResult.error(ErrorCodeEnum.FAIL);
        }

        RespSaleRepayPlanVo data = saleApply.getData();
        if (!"0000".equals(data.getResponseCode())) {
            log.info("【下款通知】查询赊销账单 借款单号：{} 资方返回结果错误：{}", loanNo, data.getResponseMsg());
            return ResponseResult.error(ErrorCodeEnum.FAIL);
        }
        List<RepayPlanListVo> repayPlanList = data.getRepayPlanList();
        List<SaleSchedule> saveList = new ArrayList<>(repayPlanList.size());
        BigDecimal saleRepayAmount = new BigDecimal("0.00");
        for (RepayPlanListVo li : repayPlanList) {
            SaleSchedule entity = new SaleSchedule();
            entity.setDisburseId(disburseData.getId());
            entity.setUserId(disburseData.getUserId());
            entity.setRepayTerm(li.getRepayTerm());
            entity.setRepayOwnbDate(li.getRepayOwnbDate());
            entity.setRepayOwneDate(li.getRepayOwneDate());
            entity.setRepayIntbDate(li.getRepayIntbDate());
            entity.setRepayInteDate(li.getRepayInteDate());
            entity.setTotalAmt(new BigDecimal(li.getTotalAmt()));
            entity.setTermRetPrin(new BigDecimal(li.getTermRetPrin()));
            // 本期应还利息
            entity.setTermRetInt(BigDecimal.ZERO);
            // 本期应还罚息
            entity.setTermRetFint(BigDecimal.ZERO);
            entity.setTermStatus("N");
            entity.setSettleFlag("1".equals(li.getStatus()) ? "CLOSE" : "RUNNING");
            entity.setAutoRepay(0);

            saveList.add(entity);
            saleRepayAmount = saleRepayAmount.add(new BigDecimal(li.getTotalAmt()));
            if (ObjectUtil.isNotEmpty(li.getRepaySuccTime())) {
                entity.setDatePay(li.getRepaySuccTime());
                entity.setDatePayTime(li.getRepaySuccTime());
            }
        }
        // 保存赊销记录
        saleScheduleMapper.insertBatch(saveList);

        // 修改赊销金额
        DisburseData up = new DisburseData();
        up.setId(disburseData.getId());
        up.setSaleRepayAmount(saleRepayAmount);
        disburseDataMapper.updateById(up);

        return ResponseResult.success();
    }

    private SaleApplyPkgDto buildSaleApplyPkgDto(RepaySchedule schedule, String term, BigDecimal subtract) {
        SaleApplyPkgDto sale = new SaleApplyPkgDto();
        sale.setRepayTerm(term);
        sale.setRepayOwnbDate(schedule.getRepayOwnbDate());
        sale.setRepayOwneDate(schedule.getRepayOwnbDate());
        sale.setRepayIntbDate(schedule.getRepayOwnbDate());
        sale.setRepayInteDate(schedule.getRepayOwnbDate());
        sale.setTotalAmt(subtract.toString());
        sale.setTermRetPrin(subtract.toString());
        sale.setTermRetInt("0");
        sale.setTermRetFint("0");
        return sale;
    }

    // --- Helper Methods Start ---

    /**
     * 查询指定放款ID已存在的还款计划。
     */
    private Map<Integer, RepaySchedule> queryExistingSchedules(Long disburseId, String loanNo, String flowContext) {
        List<RepaySchedule> existingSchedulesList = repayScheduleService.lambdaQuery()
            .eq(RepaySchedule::getDisburseId, disburseId)
            .list();
        Map<Integer, RepaySchedule> map = existingSchedulesList.stream()
            .collect(Collectors.toMap(
                schedule -> {
                    try {
                        return Integer.parseInt(schedule.getRepayTerm());
                    } catch (NumberFormatException e) {
                        log.error("[{}] 在 {} 中解析数据库已有记录的期数失败: {}", loanNo, flowContext,
                            schedule.getRepayTerm(),
                            e);
                        return -1; // 错误标记值
                    }
                },
                schedule -> schedule,
                (existing, replacement) -> {
                    log.warn("[{}] 数据库中发现放款ID {} 的重复期数: {}. 保留第一个遇到的记录。", loanNo, disburseId,
                        existing.getRepayTerm());
                    return existing; // 保留第一个遇到的
                }));
        // 移除可能由解析错误产生的条目
        map.remove(-1);
        log.info("[{}] 在 {} 中查询到数据库已有 {} 条还款计划记录。", loanNo, map.size(), flowContext);
        return map;
    }

    /**
     * 安全地从字符串解析 BigDecimal，如果为 null/空/无效，则返回 BigDecimal.ZERO。
     */
    private BigDecimal safeParseBigDecimal(String value) {
        if (StrUtil.isBlank(value)) {
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(value);
        } catch (NumberFormatException e) {
            log.warn("解析 BigDecimal 失败: '{}', 返回 ZERO。", value, e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 安全地相加 BigDecimal，将 null 视为 BigDecimal.ZERO。
     */
    private BigDecimal safeAdd(BigDecimal value) {
        return value == null ? BigDecimal.ZERO : value;
    }

    /**
     * 检查 RepaySchedule 是否需要根据 RepaymentPlanQueryPkgVo (标准流程) 进行更新。
     * 只要有一个字段发生改变就会开始更新，这样的好处是可以避免不必要的数据库 UPDAT
     */
    private boolean needsUpdate(RepaymentPlanQueryPkgVo pkgVo, RepaySchedule dbSchedule, String beanName) {
        // 比较相关字段，处理 null 值，使用 compareTo 比较 BigDecimal
        boolean changed = false;
        changed |= !Objects.equals(pkgVo.getRepayOwnbDate(), dbSchedule.getRepayOwnbDate());
        changed |= !Objects.equals(pkgVo.getRepayOwneDate(), dbSchedule.getRepayOwneDate());
        changed |= !Objects.equals(pkgVo.getRepayIntbDate(), dbSchedule.getRepayIntbDate());
        changed |= !Objects.equals(pkgVo.getRepayInteDate(), dbSchedule.getRepayInteDate());
        changed |= safeParseBigDecimal(pkgVo.getTotalAmt()).compareTo(safeAdd(dbSchedule.getTotalAmt())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getTermRetPrin()).compareTo(safeAdd(dbSchedule.getTermRetPrin())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getTermRetInt()).compareTo(safeAdd(dbSchedule.getTermRetInt())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getTermGuarantorFee())
            .compareTo(safeAdd(dbSchedule.getTermGuarantorFee())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getTermRetFint()).compareTo(safeAdd(dbSchedule.getTermRetFint())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getTermOverdueGuarantorFee())
            .compareTo(safeAdd(dbSchedule.getTermOverdueGuarantorFee())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getPrinAmt()).compareTo(safeAdd(dbSchedule.getPrinAmt())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getNoRetAmt()).compareTo(safeAdd(dbSchedule.getNoRetAmt())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getIntAmt()).compareTo(safeAdd(dbSchedule.getIntAmt())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getNoRetInt()).compareTo(safeAdd(dbSchedule.getNoRetInt())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getTermFintFinish())
            .compareTo(safeAdd(dbSchedule.getTermFintFinish())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getNoRetFin()).compareTo(safeAdd(dbSchedule.getNoRetFin())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getGuarantorFee()).compareTo(safeAdd(dbSchedule.getGuarantorFee())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getNoGuarantorFee())
            .compareTo(safeAdd(dbSchedule.getNoGuarantorFee())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getTermServiceFee())
            .compareTo(safeAdd(dbSchedule.getTermServiceFee())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getServiceFee()).compareTo(safeAdd(dbSchedule.getServiceFee())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getNoServiceFee()).compareTo(safeAdd(dbSchedule.getNoServiceFee())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getOverdueGuarantorFee())
            .compareTo(safeAdd(dbSchedule.getOverdueGuarantorFee())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getNoOverdueGuarantorFee())
            .compareTo(safeAdd(dbSchedule.getNoOverdueGuarantorFee())) != 0;
        changed |= !Objects.equals(pkgVo.getTermStatus(), dbSchedule.getTermStatus());
        changed |= !Objects.equals(pkgVo.getSettleFlag(), dbSchedule.getSettleFlag());
        // 特定资方的特殊字段
        if (ZiFangBeanConstant.FENZHUAN.equals(beanName)) {
            changed |= !Objects.equals(pkgVo.getBillId(), dbSchedule.getBillId());
            changed |= !Objects.equals(pkgVo.getPlanId(), dbSchedule.getPlanId());
        }

        // 如果需要，添加更多字段比较

        return changed;
    }

    /**
     * 从 RepaymentPlanQueryPkgVo (标准流程) 更新一个已存在的 RepaySchedule 实体。
     */
    private void updateScheduleFromPkgVo(RepaymentPlanQueryPkgVo pkgVo, RepaySchedule dbSchedule, String beanName) {
        dbSchedule.setRepayOwnbDate(pkgVo.getRepayOwnbDate());
        dbSchedule.setRepayOwneDate(pkgVo.getRepayOwneDate());
        dbSchedule.setRepayIntbDate(pkgVo.getRepayIntbDate());
        dbSchedule.setRepayInteDate(pkgVo.getRepayInteDate());
        dbSchedule.setTotalAmt(safeParseBigDecimal(pkgVo.getTotalAmt()));
        dbSchedule.setTermRetPrin(safeParseBigDecimal(pkgVo.getTermRetPrin()));
        dbSchedule.setTermRetInt(safeParseBigDecimal(pkgVo.getTermRetInt()));
        dbSchedule.setTermGuarantorFee(safeParseBigDecimal(pkgVo.getTermGuarantorFee()));
        dbSchedule.setTermRetFint(safeParseBigDecimal(pkgVo.getTermRetFint()));
        dbSchedule.setTermOverdueGuarantorFee(safeParseBigDecimal(pkgVo.getTermOverdueGuarantorFee()));
        dbSchedule.setPrinAmt(safeParseBigDecimal(pkgVo.getPrinAmt()));
        dbSchedule.setNoRetAmt(safeParseBigDecimal(pkgVo.getNoRetAmt()));
        dbSchedule.setIntAmt(safeParseBigDecimal(pkgVo.getIntAmt()));
        dbSchedule.setNoRetInt(safeParseBigDecimal(pkgVo.getNoRetInt()));
        dbSchedule.setTermFintFinish(safeParseBigDecimal(pkgVo.getTermFintFinish()));
        dbSchedule.setNoRetFin(safeParseBigDecimal(pkgVo.getNoRetFin()));
        dbSchedule.setGuarantorFee(safeParseBigDecimal(pkgVo.getGuarantorFee()));
        dbSchedule.setNoGuarantorFee(safeParseBigDecimal(pkgVo.getNoGuarantorFee()));
        dbSchedule.setTermServiceFee(safeParseBigDecimal(pkgVo.getTermServiceFee()));
        dbSchedule.setServiceFee(safeParseBigDecimal(pkgVo.getServiceFee()));
        dbSchedule.setNoServiceFee(safeParseBigDecimal(pkgVo.getNoServiceFee()));
        dbSchedule.setOverdueGuarantorFee(safeParseBigDecimal(pkgVo.getOverdueGuarantorFee()));
        dbSchedule.setNoOverdueGuarantorFee(safeParseBigDecimal(pkgVo.getNoOverdueGuarantorFee()));
        dbSchedule.setTermStatus(pkgVo.getTermStatus());
        dbSchedule.setSettleFlag(pkgVo.getSettleFlag());
        // 保持 repayTerm 不变 (它是键)
        // 保持 disburseId 和 userId 不变

        // 特定资方的特殊字段
        if (ZiFangBeanConstant.FENZHUAN.equals(beanName)) {
            dbSchedule.setBillId(pkgVo.getBillId());
            dbSchedule.setPlanId(pkgVo.getPlanId());
        }
        // 注意: 这里不更新像 autoRepay 这样的字段，因为它们很可能是本地管理的
    }

    /**
     * 从 RepaymentPlanQueryPkgVo (标准流程) 创建一个新的 RepaySchedule 实体。
     */
    private RepaySchedule createScheduleFromPkgVo(RepaymentPlanQueryPkgVo pkgVo, Long disburseId, Long userId,
        String beanName, String loanNo) {
        RepaySchedule entity = new RepaySchedule();
        try {
            entity.setRepayTerm(pkgVo.getRepayTerm()); // 首先设置期数，用于可能的日志/错误上下文
        } catch (Exception e) {
            // 这里应该已经被更早的逻辑捕获，但作为安全措施
            log.error("[{}] 创建期间设置期数 {} 时出错。", loanNo, pkgVo.getRepayTerm(), e);
            return null;
        }

        entity.setDisburseId(disburseId);
        entity.setUserId(userId);
        entity.setRepayOwnbDate(pkgVo.getRepayOwnbDate());
        entity.setRepayOwneDate(pkgVo.getRepayOwneDate());
        entity.setRepayIntbDate(pkgVo.getRepayIntbDate());
        entity.setRepayInteDate(pkgVo.getRepayInteDate());
        entity.setTotalAmt(safeParseBigDecimal(pkgVo.getTotalAmt()));
        entity.setTermRetPrin(safeParseBigDecimal(pkgVo.getTermRetPrin()));
        entity.setTermRetInt(safeParseBigDecimal(pkgVo.getTermRetInt()));
        entity.setTermGuarantorFee(safeParseBigDecimal(pkgVo.getTermGuarantorFee()));
        entity.setTermRetFint(safeParseBigDecimal(pkgVo.getTermRetFint()));
        entity.setTermOverdueGuarantorFee(safeParseBigDecimal(pkgVo.getTermOverdueGuarantorFee()));
        entity.setPrinAmt(safeParseBigDecimal(pkgVo.getPrinAmt()));
        entity.setNoRetAmt(safeParseBigDecimal(pkgVo.getNoRetAmt()));
        entity.setIntAmt(safeParseBigDecimal(pkgVo.getIntAmt()));
        entity.setNoRetInt(safeParseBigDecimal(pkgVo.getNoRetInt()));
        entity.setTermFintFinish(safeParseBigDecimal(pkgVo.getTermFintFinish()));
        entity.setNoRetFin(safeParseBigDecimal(pkgVo.getNoRetFin()));
        entity.setGuarantorFee(safeParseBigDecimal(pkgVo.getGuarantorFee()));
        entity.setNoGuarantorFee(safeParseBigDecimal(pkgVo.getNoGuarantorFee()));
        entity.setTermServiceFee(safeParseBigDecimal(pkgVo.getTermServiceFee()));
        entity.setServiceFee(safeParseBigDecimal(pkgVo.getServiceFee()));
        entity.setNoServiceFee(safeParseBigDecimal(pkgVo.getNoServiceFee()));
        entity.setOverdueGuarantorFee(safeParseBigDecimal(pkgVo.getOverdueGuarantorFee()));
        entity.setNoOverdueGuarantorFee(safeParseBigDecimal(pkgVo.getNoOverdueGuarantorFee()));
        entity.setTermStatus(pkgVo.getTermStatus());
        entity.setSettleFlag(pkgVo.getSettleFlag());

        // 特定资方的特殊字段
        if (ZiFangBeanConstant.FENZHUAN.equals(beanName)) {
            entity.setBillId(pkgVo.getBillId());
            entity.setPlanId(pkgVo.getPlanId());
        }
        // 如果需要，初始化本地管理的字段
        entity.setAutoRepay(0); // 基于之前逻辑的默认值

        return entity;
    }

    // --- API 流程辅助方法 ---

    /**
     * 检查 RepaySchedule 是否需要根据 RespRepayPlanPkgVo (API 流程) 进行更新。
     */
    private boolean needsUpdateApi(RespRepayPlanPkgVo pkgVo, RepaySchedule dbSchedule) {
        boolean changed = false;
        changed |= !Objects.equals(pkgVo.getRepayOwnbDate(), dbSchedule.getRepayOwnbDate());
        changed |= !Objects.equals(pkgVo.getRepayOwneDate(), dbSchedule.getRepayOwneDate());
        changed |= !Objects.equals(pkgVo.getRepayIntbDate(), dbSchedule.getRepayIntbDate());
        changed |= !Objects.equals(pkgVo.getRepayInteDate(), dbSchedule.getRepayInteDate());
        changed |= safeParseBigDecimal(pkgVo.getTotalAmt()).compareTo(safeAdd(dbSchedule.getTotalAmt())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getTermRetPrin()).compareTo(safeAdd(dbSchedule.getTermRetPrin())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getTermRetInt()).compareTo(safeAdd(dbSchedule.getTermRetInt())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getTermGuarantorFee())
            .compareTo(safeAdd(dbSchedule.getTermGuarantorFee())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getTermRetFint()).compareTo(safeAdd(dbSchedule.getTermRetFint())) != 0;
        // API 流程 DTO 可能没有 TermOverdueGuarantorFee，检查 DTO 定义
        // 假设根据原始代码它不存在或总是零:
        // changed |=
        // BigDecimal.ZERO.compareTo(safeAdd(dbSchedule.getTermOverdueGuarantorFee()))
        // != 0; // Check if DB should be zeroed
        changed |= safeParseBigDecimal(pkgVo.getPrinAmt()).compareTo(safeAdd(dbSchedule.getPrinAmt())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getNoRetAmt()).compareTo(safeAdd(dbSchedule.getNoRetAmt())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getIntAmt()).compareTo(safeAdd(dbSchedule.getIntAmt())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getNoRetInt()).compareTo(safeAdd(dbSchedule.getNoRetInt())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getTermFintFinish())
            .compareTo(safeAdd(dbSchedule.getTermFintFinish())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getNoRetFin()).compareTo(safeAdd(dbSchedule.getNoRetFin())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getGuarantorFee()).compareTo(safeAdd(dbSchedule.getGuarantorFee())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getNoGuarantorFee())
            .compareTo(safeAdd(dbSchedule.getNoGuarantorFee())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getTermServiceFee())
            .compareTo(safeAdd(dbSchedule.getTermServiceFee())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getServiceFee()).compareTo(safeAdd(dbSchedule.getServiceFee())) != 0;
        changed |= safeParseBigDecimal(pkgVo.getNoServiceFee()).compareTo(safeAdd(dbSchedule.getNoServiceFee())) != 0;
        // API 流程 DTO 可能没有 OverdueGuarantorFee / NoOverdueGuarantorFee
        // 假设根据原始代码为零:
        // changed |=
        // BigDecimal.ZERO.compareTo(safeAdd(dbSchedule.getOverdueGuarantorFee())) != 0;
        // changed |=
        // BigDecimal.ZERO.compareTo(safeAdd(dbSchedule.getNoOverdueGuarantorFee())) !=
        // 0;
        changed |= !Objects.equals(pkgVo.getTermStatus(), dbSchedule.getTermStatus());
        changed |= !Objects.equals(pkgVo.getSettleFlag(), dbSchedule.getSettleFlag());

        return changed;
    }

    /**
     * 从 RespRepayPlanPkgVo (API 流程) 更新一个已存在的 RepaySchedule 实体。
     */
    private void updateScheduleFromApiPkgVo(RespRepayPlanPkgVo pkgVo, RepaySchedule dbSchedule) {
        dbSchedule.setRepayOwnbDate(pkgVo.getRepayOwnbDate());
        dbSchedule.setRepayOwneDate(pkgVo.getRepayOwneDate());
        dbSchedule.setRepayIntbDate(pkgVo.getRepayIntbDate());
        dbSchedule.setRepayInteDate(pkgVo.getRepayInteDate());
        dbSchedule.setTotalAmt(safeParseBigDecimal(pkgVo.getTotalAmt()));
        dbSchedule.setTermRetPrin(safeParseBigDecimal(pkgVo.getTermRetPrin()));
        dbSchedule.setTermRetInt(safeParseBigDecimal(pkgVo.getTermRetInt()));
        dbSchedule.setTermGuarantorFee(safeParseBigDecimal(pkgVo.getTermGuarantorFee()));
        dbSchedule.setTermRetFint(safeParseBigDecimal(pkgVo.getTermRetFint()));
        // 根据原始注释代码，这些在 API 流程中似乎设置为 0
        dbSchedule.setTermOverdueGuarantorFee(BigDecimal.ZERO);
        dbSchedule.setPrinAmt(safeParseBigDecimal(pkgVo.getPrinAmt()));
        dbSchedule.setNoRetAmt(safeParseBigDecimal(pkgVo.getNoRetAmt()));
        dbSchedule.setIntAmt(safeParseBigDecimal(pkgVo.getIntAmt()));
        dbSchedule.setNoRetInt(safeParseBigDecimal(pkgVo.getNoRetInt()));
        dbSchedule.setTermFintFinish(safeParseBigDecimal(pkgVo.getTermFintFinish()));
        dbSchedule.setNoRetFin(safeParseBigDecimal(pkgVo.getNoRetFin()));
        dbSchedule.setGuarantorFee(safeParseBigDecimal(pkgVo.getGuarantorFee()));
        dbSchedule.setNoGuarantorFee(safeParseBigDecimal(pkgVo.getNoGuarantorFee()));
        dbSchedule.setTermServiceFee(safeParseBigDecimal(pkgVo.getTermServiceFee()));
        dbSchedule.setServiceFee(safeParseBigDecimal(pkgVo.getServiceFee()));
        dbSchedule.setNoServiceFee(safeParseBigDecimal(pkgVo.getNoServiceFee()));
        // 根据原始注释代码，这些在 API 流程中似乎设置为 0
        dbSchedule.setOverdueGuarantorFee(BigDecimal.ZERO);
        dbSchedule.setNoOverdueGuarantorFee(BigDecimal.ZERO);
        dbSchedule.setTermStatus(pkgVo.getTermStatus());
        dbSchedule.setSettleFlag(pkgVo.getSettleFlag());
        if (ObjectUtil.isNotEmpty(pkgVo.getDatePay())) {
            dbSchedule.setDatePay(pkgVo.getDatePay());
            dbSchedule.setDatePayTime(pkgVo.getDatePay());
        }
        // 保持 repayTerm, disburseId, userId
        // 保持本地管理的字段如 autoRepay
    }

    /**
     * 从 RespRepayPlanPkgVo (API 流程) 创建一个新的 RepaySchedule 实体。
     */
    private RepaySchedule createScheduleFromApiPkgVo(RespRepayPlanPkgVo pkgVo, Long disburseId, Long userId,
        String loanNo) {
        RepaySchedule entity = new RepaySchedule();
        try {
            entity.setRepayTerm(pkgVo.getRepayTerm());
        } catch (Exception e) {
            log.error("[{}] 创建期间设置期数 {} (API) 时出错。", loanNo, pkgVo.getRepayTerm(), e);
            return null;
        }

        entity.setDisburseId(disburseId);
        entity.setUserId(userId);
        entity.setRepayOwnbDate(pkgVo.getRepayOwnbDate());
        entity.setRepayOwneDate(pkgVo.getRepayOwneDate());
        entity.setRepayIntbDate(pkgVo.getRepayIntbDate());
        entity.setRepayInteDate(pkgVo.getRepayInteDate());
        entity.setTotalAmt(safeParseBigDecimal(pkgVo.getTotalAmt()));
        entity.setTermRetPrin(safeParseBigDecimal(pkgVo.getTermRetPrin()));
        entity.setTermRetInt(safeParseBigDecimal(pkgVo.getTermRetInt()));
        entity.setTermGuarantorFee(safeParseBigDecimal(pkgVo.getTermGuarantorFee()));
        entity.setTermRetFint(safeParseBigDecimal(pkgVo.getTermRetFint()));
        // 根据原始注释代码，这些在 API 流程中似乎设置为 0
        entity.setTermOverdueGuarantorFee(BigDecimal.ZERO);
        entity.setPrinAmt(safeParseBigDecimal(pkgVo.getPrinAmt()));
        entity.setNoRetAmt(safeParseBigDecimal(pkgVo.getNoRetAmt()));
        entity.setIntAmt(safeParseBigDecimal(pkgVo.getIntAmt()));
        entity.setNoRetInt(safeParseBigDecimal(pkgVo.getNoRetInt()));
        entity.setTermFintFinish(safeParseBigDecimal(pkgVo.getTermFintFinish()));
        entity.setNoRetFin(safeParseBigDecimal(pkgVo.getNoRetFin()));
        entity.setGuarantorFee(safeParseBigDecimal(pkgVo.getGuarantorFee()));
        entity.setNoGuarantorFee(safeParseBigDecimal(pkgVo.getNoGuarantorFee()));
        entity.setTermServiceFee(safeParseBigDecimal(pkgVo.getTermServiceFee()));
        entity.setServiceFee(safeParseBigDecimal(pkgVo.getServiceFee()));
        entity.setNoServiceFee(safeParseBigDecimal(pkgVo.getNoServiceFee()));
        // 根据原始注释代码，这些在 API 流程中似乎设置为 0
        entity.setOverdueGuarantorFee(BigDecimal.ZERO);
        entity.setNoOverdueGuarantorFee(BigDecimal.ZERO);
        entity.setTermStatus(pkgVo.getTermStatus());
        entity.setSettleFlag(pkgVo.getSettleFlag());
        if (ObjectUtil.isNotEmpty(pkgVo.getDatePay())) {
            entity.setDatePay(pkgVo.getDatePay());
            entity.setDatePayTime(pkgVo.getDatePay());
        }
        // 初始化本地管理的字段
        entity.setAutoRepay(0);

        return entity;
    }

    // --- Helper Methods End ---

}
