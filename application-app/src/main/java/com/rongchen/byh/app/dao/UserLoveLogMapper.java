package com.rongchen.byh.app.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rongchen.byh.app.entity.UserLoveLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【user_love_log(爱签签署记录表)】的数据库操作Mapper
* @createDate 2024-12-13 18:03:58
* @Entity com.rongchen.byh.app.entity.UserLoveLog
*/
@Mapper
public interface UserLoveLogMapper extends BaseMapper<UserLoveLog> {

    UserLoveLog getByUserIdAndContractName(@Param("userId") Long userId, @Param("contractName") String contractName);
}
