package com.rongchen.byh.app.config;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rongchen.byh.app.constant.ZiFangBeanConstant;
import com.rongchen.byh.app.dao.*;
import com.rongchen.byh.app.entity.*;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Configuration
public class ZifangFactory {

    @Resource
    ApplicationContext applicationContext;
    @Resource
    UserCapitalRecordMapper userCapitalRecordMapper;
    @Resource
    CapitalDataMapper capitalDataMapper;
    @Resource
    SysConfigMapper sysConfigMapper;
    @Resource
    UserDetailMapper userDetailMapper;
    @Resource
    DisburseDataMapper disburseDataMapper;
    @Resource
    private UserRegisterResultMapper userRegisterResultMapper;

    /**
     * 通过bean名称获取接口bean
     * @param beanName
     * @param clazz
     * @return
     * @param <T>
     */
    public <T> T getApi(String beanName, Class<T> clazz) {
        Map<String, T> beansOfType = applicationContext.getBeansOfType(clazz);

        T t = beansOfType.get(beanName);
        if (t != null) {
            return t;
        }
        return null;
    }


    /**
     * 授信前选择路由资方
     * @param userId
     * @return
     */
    public CapitalData matchCapitalByUserId(Long userId) {
//        return capitalDataMapper.selectById(2);
        // 新资方授信失败默认走老资方
        UserRegisterResult userRegisterResult = userRegisterResultMapper.selectByUserId(userId);
        if (userRegisterResult == null || userRegisterResult.getStatus() == 2) {
            return capitalDataMapper.selectById(1);
        }
        // 第一次支用走新资方，第二次支用调整为走老资方
        List<UserCapitalRecord> userCapitalRecords = userCapitalRecordMapper.selectListByUserId(userId);
        if (CollectionUtil.isNotEmpty(userCapitalRecords)) {
            long count = userCapitalRecords.stream().filter(userCapitalRecord -> userCapitalRecord.getCapitalId() == 2).count();
            if (count > 0) {
                return capitalDataMapper.selectById(1);
            }
        }
        String capitalNum = sysConfigMapper.selectByConfigKey("capitalNum");

        //如果大于5次就不让支用
        List<UserCapitalRecord> records = userCapitalRecordMapper.selectListByUserId(userId);
        if (CollectionUtil.isNotEmpty(records)) {
            if (records.size() >= Integer.parseInt(Optional.ofNullable(capitalNum).orElse("0"))) {
                return null;
            }
        }

        // 查询用户支用数据，将已经支用了的资方过滤掉
        LambdaQueryWrapper<DisburseData> disWrapper = new LambdaQueryWrapper<>();
        disWrapper.eq(DisburseData::getUserId,userId);
        List<DisburseData> dataList = disburseDataMapper.selectList(disWrapper);


        // 查询可用资方
        LambdaQueryWrapper<CapitalData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CapitalData::getStatus, 1);
        List<Long> disCapitalIds = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(dataList)) {
            dataList.forEach(data -> {
                // 100 授信中  200 授信失败   300 放款中  400  放款失败   500 还款中   600 已结清
                Long capitalId = data.getCapitalId();
                if (data.getCreditStatus() == 200) {
                    disCapitalIds.add(capitalId);
                }
            });
        }
        if (CollectionUtil.isNotEmpty(disCapitalIds)) {
            wrapper.notIn(CapitalData::getId, disCapitalIds);
        }
        List<CapitalData> list = capitalDataMapper.selectList(wrapper);
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }

        ListUtil.sortByProperty(list,"sort");

        return list.get(0);
    }
}
