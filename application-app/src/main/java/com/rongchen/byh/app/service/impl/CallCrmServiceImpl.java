package com.rongchen.byh.app.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.app.constant.DaiKouProperties;
import com.rongchen.byh.app.constant.SourceMode;
import com.rongchen.byh.app.dao.*;
import com.rongchen.byh.app.entity.DisburseData;
import com.rongchen.byh.app.entity.UserBankCard;
import com.rongchen.byh.app.entity.UserData;
import com.rongchen.byh.app.service.CallCrmService;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.HttpUtil;
import java.math.BigDecimal;
import java.util.Date;
import java.util.TreeMap;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CallCrmServiceImpl implements CallCrmService {
    @Resource
    UserDetailMapper userDetailMapper;
    @Resource
    DaiKouProperties daiKouProperties;
    @Resource
    UserBankCardMapper userBankCardMapper;
    @Resource
    DisburseDataMapper disburseDataMapper;
    @Resource
    SysConfigMapper sysConfigMapper;
    @Resource
    UserLoanApplyMapper userLoanApplyMapper;

    @Resource
    UserDataMapper userDataMapper;

    public static String CreateSign(String ua, String uaKey, String jsonData) {
        // 签名生成规则：
        // 1、合作方需要在平台获取唯一的UA(合作方唯一标识)和Key（请求签名密钥）
        // 2、签名生成规则：
        // （1）、利用UA和Key拼装生成signKey $SignKey = "{$UA}{$Key}{$UA}"
        // （2）、将请求的数据转换成json字符串 $Data = json_encode(rquestArgs)
        // （3）、结合SignKey、Data生成签名： $Sign = md5("{$SignKey}{Data}{SignKey}")
        String result = "";
        // string ua = ConfigHelper.GetValue("UA");
        // string uaKey = ConfigHelper.GetValue("UAKey");
        String signKey = ua + uaKey + ua;
        String signSource = signKey + jsonData + signKey;
        result = SecureUtil.md5(signSource);
        return result;
    }

    // private static final String CreditOrgID =
    // "1FA60D63-1F97-4161-B1F0-66EB5499E779";
    // private static final String ProductID =
    // "17BADE5D-B2D8-442B-92D5-2AF88DADEAA5";
    //
    // private static final String UA = "6473770011";
    // private static final String UAKey =
    // "910000$BAA42DA9-15E6-4FE7-ACA8-7CD66ED8A1DF$6473770011";
    //
    // private static final String URL =
    // "https://testopenapi.hefu-tech.cn/loan/sendloan";
    @Override
    public ResponseResult<Void> sendLoan(String loanNo) {

        DisburseData disburseData = disburseDataMapper.selectByLoanNo(loanNo);

        Long userId = disburseData.getUserId();

        //查看当前用户模式是空中还是空中仅注册，如果不是则直接返回，无需走下面的扣咨询费流程
        UserData userData = userDataMapper.selectById(userId);
        if (!userData.getSourceMode().equals(SourceMode.AIR_ONLY_REGISTER) || !userData.getSourceMode().equals(SourceMode.AIR)) {
            return ResponseResult.success();
        }

        // 判断是否扣款控制
        // 线上模式暂时不管扣费，线下模式需要判断是否开启扣费
        // 根据初筛表通过判断
        String offlineFee = sysConfigMapper.selectByConfigKey("offlineFee");
        if (!StrUtil.equals(offlineFee, "1")) {
            log.info("[资产方] 下款成功通知 自动扣费关闭 用户id：{}", userId);
            return ResponseResult.success();
        }
        UserBankCard userBankCard = userBankCardMapper.queryByUserId(userId);
        String consultingMode = userBankCard.getConsultingMode();
        // 使用 disburseData.getCreditAmount() * ConsultingRate;如果 ConsultingMode 不是 比例
        // ，不处理
        if (consultingMode == null || (consultingMode != null && ("1").equals(consultingMode))) {
            log.info("[资产方] 下款成功通知  consultingMode值为null,或者consultingMode不是比例,不处理 用户id：{}", userId);
            return ResponseResult.success();
        }
        if (userBankCard.getContractNum() != null
                && !"".equals(userBankCard.getContractNum())
                && "webNoContractNum".equals(userBankCard.getContractNum())) {
            log.info("[资产方] 下款成功通知 用户无合同号不扣费 用户id：{}", userId);
            return ResponseResult.success();
        }

        TreeMap<String, Object> data = new TreeMap<>();
        data.put("FKID", disburseData.getId() + "");
        data.put("ContractNum", userBankCard.getContractNum());
        data.put("CustomerName", userBankCard.getCustomerName());
        data.put("IDCard", userBankCard.getIdCard());
        data.put("LoanMoney", disburseData.getCreditAmount().toString());
        data.put("Term", disburseData.getPeriods().toString());
        BigDecimal rateFee = disburseData.getCreditAmount().multiply(new BigDecimal(userBankCard.getConsultingRate()));
        data.put("ConsultingFee", rateFee.toString());
        Date loanTime = disburseData.getLoanTime();
        data.put("LoanDate", DateUtil.format(loanTime, "yyyyMMddHHmmss"));
        data.put("CreditOrgID", "system_default_creditorg");
        data.put("ProductID", "system_default_product");
        data.put("IsEnd", "1");
        String jsonString = JSONObject.toJSONString(data);
        JSONObject param = new JSONObject();
        param.put("UA", daiKouProperties.getUa());
        param.put("TimeStamp", System.currentTimeMillis() / 1000);
        param.put("Data", data);
        param.put("Sign", CreateSign(daiKouProperties.getUa(), daiKouProperties.getUaKey(), jsonString));

        String res = doRequest(param);
        JSONObject resJson = JSONObject.parseObject(res);
        String status = resJson.getString("Status");

        DisburseData up = new DisburseData();
        up.setId(disburseData.getId());
        if (StrUtil.equals(status, "1")) { // 成功
            JSONObject resultDat = resJson.getJSONObject("ResultData");
            // 10：扣款成功
            // 00：扣款处理中
            // 01：扣款失败
            String payState = resultDat.getString("PayState");
            if (payState.equals("10")) {
                up.setConsultFee(1);
            } else {
                up.setConsultFee(2);
            }
        } else {
            up.setConsultFee(2);
        }
        disburseDataMapper.updateById(up);

        if (StrUtil.equals(status, "1")) {
            return ResponseResult.success();
        }
        return ResponseResult.error(ErrorCodeEnum.FAIL);
    }

    private String doRequest(JSONObject param) {
        log.info("[资产方] 下款成功通知 请求参数：{}", param);
        String res = HttpUtil.postJson(daiKouProperties.getUrl(), param);
        log.info("[资产方] 下款成功通知 响应参数：{}", res);
        return res;
    }
}
