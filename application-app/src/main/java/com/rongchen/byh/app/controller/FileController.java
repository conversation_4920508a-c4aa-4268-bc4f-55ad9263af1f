package com.rongchen.byh.app.controller;


import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import com.rongchen.byh.app.config.FileUtilService;
import com.rongchen.byh.app.dto.UploadBase64Dto;
import com.rongchen.byh.app.utils.FileUtil;
import com.rongchen.byh.app.vo.UploadFileVo;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.upload.BaseUpDownloader;
import com.rongchen.byh.common.core.upload.UpDownloaderFactory;
import com.rongchen.byh.common.core.upload.UploadResponseInfo;
import com.rongchen.byh.common.core.upload.UploadStoreTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Base64;


@Tag(name = "文件上传下载接口")
@Slf4j
@RestController
@RequestMapping("/file")
@SaIgnore
public class FileController {

    @Autowired
    private UpDownloaderFactory upDownloaderFactory;
    @Resource
    FileUtilService fileUtilService;

    @Operation(summary = "文件上传")
    @PostMapping("/upload")
    public ResponseResult<UploadFileVo> upload(@RequestParam("file") MultipartFile file, HttpServletRequest request) throws IOException {
//        BaseUpDownloader upDownloader = upDownloaderFactory.get(UploadStoreTypeEnum.QCLOUD_COS_SYTEM);
//        UploadResponseInfo responseInfo = upDownloader.doUpload(null, "qyc", "image", file);
//        if (responseInfo.getUploadFailed()) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL,responseInfo.getErrorMessage());
//        }
//        String uploadPath = responseInfo.getUploadPath();
//        String filename = responseInfo.getFilename();
//        String downloadUri = responseInfo.getDownloadUri();
//        UploadFileVo vo = new UploadFileVo();
//        vo.setDownloadUri(downloadUri);
//        vo.setUploadPath(uploadPath);
//        vo.setFilename(filename);
//
//        String requestURI = request.getRequestURI();
//        StringBuffer requestURL = request.getRequestURL();
//        String domain = StrUtil.removeSuffix(requestURL, requestURI);
//        String url = domain+"/api"+downloadUri+"?filename="+filename;
//        String replace = StrUtil.replace(url, "http://", "https://");
//        vo.setDownloadUrl(replace);
//        return ResponseResult.success(vo);

        return fileUtilService.upResult(file, request);
    }


    @Operation(summary = "文件上传base64")
    @PostMapping("/uploadBase64")
    public ResponseResult<UploadFileVo> uploadBase64(@RequestBody UploadBase64Dto uploadBase64Dto, HttpServletRequest request) throws IOException {
        MultipartFile file = FileUtil.base64ToMultipartFile(uploadBase64Dto.getBase64Str());
        return fileUtilService.upResult(file, request);
    }



    @Operation(summary = "文件下载")
    @GetMapping("/download")
    @SaIgnore
    public void download(String filename, HttpServletResponse response) throws IOException {
        BaseUpDownloader baseUpDownloader = upDownloaderFactory.get(UploadStoreTypeEnum.QCLOUD_COS_SYTEM);
        baseUpDownloader.doDownload("qyc", "image", filename, response);
    }
}
