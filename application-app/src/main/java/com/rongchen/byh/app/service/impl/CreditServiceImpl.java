package com.rongchen.byh.app.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.rongchen.byh.app.config.ZifangFactory;
import com.rongchen.byh.app.constant.ZiFangBeanConstant;
import com.rongchen.byh.app.dao.*;
import com.rongchen.byh.app.dto.app.BackVo;
import com.rongchen.byh.app.entity.*;
import com.rongchen.byh.app.service.CreditService;
import com.rongchen.byh.app.utils.CreditLoanNoUtils;
import com.rongchen.byh.common.api.zifang.dto.ApplyCheckDto;
import com.rongchen.byh.common.api.zifang.dto.CreditApplyDto;
import com.rongchen.byh.common.api.zifang.dto.LoanApplyDto;
import com.rongchen.byh.common.api.zifang.service.CapitalApi;
import com.rongchen.byh.common.api.zifang.service.LoanApi;
import com.rongchen.byh.common.api.zifang.vo.ApplyCheckVo;
import com.rongchen.byh.common.api.zifang.vo.BaseVo;
import com.rongchen.byh.common.api.zifang.vo.CreditApplyVo;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @ClassName CreditServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/15 14:34
 * @Version 1.0
 **/
@Service
@Slf4j
public class CreditServiceImpl implements CreditService {

    @Resource
    CapitalApi capitalApi;
    @Resource
    LoanApi loanApi;
    @Resource
    UserDataMapper userDataMapper;
    @Resource
    UserDetailMapper userDetailMapper;
    @Resource
    DisburseDataMapper disburseDataMapper;
    @Resource
    UserBankCardMapper userBankCardMapper;
    @Resource
    ZifangFactory zifangFactory;
    @Resource
    CapitalDataMapper capitalDataMapper;
    @Resource
    UserRegisterResultMapper userRegisterResultMapper;
    @Resource
    UserCreditDataMapper userCreditDataMapper;

    @Override
    public ResponseResult<Void> creditApply(CapitalData capitalData, Long userId) {
        UserData userData = userDataMapper.selectById(userId);
        UserDetail detail = userDetailMapper.queryByUserId(userId);
        ApplyCheckDto dto = new ApplyCheckDto();
        dto.setSerialNo(CreditLoanNoUtils.getSequence("s"));
        dto.setIdNo(detail.getIdNumber());
        dto.setCustName(detail.getAppName());
        dto.setPhoneNo(userData.getMobile());
        log.info("用户:"+userId+"进件进资方了,时间是："+System.currentTimeMillis());
        CapitalApi capitalApi = zifangFactory.getApi(capitalData.getBeanName(), CapitalApi.class);
        if (!ZiFangBeanConstant.FENZHUAN.equals(capitalData.getBeanName())) {
            ResponseResult<ApplyCheckVo> responseResult = capitalApi.applyCheck(dto);
            if (!responseResult.getData().getResponseCode().equals("0000")){
                return ResponseResult.error(ErrorCodeEnum.FAIL);
            }
        }
        log.info("用户:"+userId+"进件从资方出来了,时间是："+System.currentTimeMillis());
        DisburseData data = disburseDataMapper.selectByUserId(userId);
        CreditApplyDto applyDto = new CreditApplyDto();
        if (ObjectUtil.isNotEmpty(data)) {
            applyDto.setCreditNo(data.getCreditNo());
            applyDto.setCreditAmount(data.getCreditAmount().toString());
        } else {
            UserCreditData userCreditData = userCreditDataMapper.queryByUserId(userId);
            if (ObjectUtil.isNotEmpty(userCreditData)){
                applyDto.setCreditAmount(userCreditData.getCreditAmount().toString());
            }
        }
        applyDto.setUserId(userId.toString());
        applyDto.setCustName(detail.getAppName());
        applyDto.setIdNo(detail.getIdNumber());
        applyDto.setSex(detail.getSex());
        applyDto.setNationality("中国");
        applyDto.setNation(detail.getNation());
        applyDto.setBirthday(DateUtil.format(new Date(detail.getBirth()),"yyyy-MM-dd"));
        applyDto.setProvince(detail.getCustAddressProvice());
        applyDto.setCity(detail.getCustAddressCity());
        applyDto.setArea(detail.getCustAddressCounty());
        applyDto.setAddress(detail.getCustAddress());
        applyDto.setSignOrganization(detail.getAuthority());
        String[] split = detail.getValidDate().split("-");
        applyDto.setIdValidDateBegin(timeZ(split[0]));
        if (split[1].equals("长期")){
            applyDto.setIdValidDateEnd("2099-12-31");
            applyDto.setIdLongTerm("1");
        }else {
            applyDto.setIdValidDateEnd(timeZ(split[1]));
            applyDto.setIdLongTerm("0");
        }
        applyDto.setPhoneNo(userData.getMobile());
        applyDto.setEducation(detail.getEducationLevel());
        if (detail.getEducationLevel().equals("1")){
            applyDto.setEducation("40");
        }else if (detail.getEducationLevel().equals("2")){
            applyDto.setEducation("20");
        }else {
            applyDto.setEducation("10");
        }
        if (detail.getMaritalStatus().equals("1")){
            applyDto.setMarryType("10");
        }else if (detail.getMaritalStatus().equals("2")){
            applyDto.setMarryType("20");
        }else if (detail.getMaritalStatus().equals("3")){
            applyDto.setMarryType("40");
        }else if (detail.getMaritalStatus().equals("4")){
            applyDto.setMarryType("30");
        }else {
            applyDto.setMarryType("91");
        }
        applyDto.setIncome(Double.valueOf(detail.getIncomeMonth()));
        applyDto.setDeviceBrand(detail.getDeviceBrand());
        applyDto.setNetworkType(detail.getNetworkType());
        applyDto.setDevAlias(detail.getDevAlias());
        applyDto.setDeviceId(detail.getDeviceId());
        applyDto.setClientIp(detail.getClientIp());
        applyDto.setCoordinateType(detail.getCoordinateType());
        applyDto.setLng(detail.getLongitude());
        applyDto.setLat(detail.getLatitude());
        applyDto.setGpsCity(detail.getGpsCity());
        applyDto.setLbsAddress(detail.getLbsAddress());
        applyDto.setGpsAddress(detail.getGpsAddress());
        applyDto.setOs(detail.getOs());
        applyDto.setOsVersion(detail.getOsVersion());
        applyDto.setFaceScore(detail.getFaceScore());
        applyDto.setFaceConfidence(detail.getFaceConfidence());
        applyDto.setFaceSource(detail.getFaceSource());
        applyDto.setFaceTime(detail.getFaceTime());
        applyDto.setContactRelation1(relationship(detail.getRelationshipOne()));
        applyDto.setContactName1(detail.getEmergencyNameOne());
        applyDto.setContactPhoneNo1(detail.getEmergencyMobileOne());
        applyDto.setContactRelation2(relationship(detail.getRelationshipTwo()));
        applyDto.setContactName2(detail.getEmergencyNameTwo());
        applyDto.setContactPhoneNo2(detail.getEmergencyMobileTwo());
        applyDto.setIdCardFrontUrl(detail.getIdCardFrondUrl());
        applyDto.setIdCardBackUrl(detail.getIdCardReverseUrl());
        applyDto.setLiveUrl(detail.getFaceUrl());
        applyDto.setCompanyName(detail.getCompanyName());
        applyDto.setProfessional(detail.getProfessional());
        applyDto.setOrderNo(IdUtil.fastSimpleUUID());
        applyDto.setIdcardAddress(detail.getAddress());
        ResponseResult<CreditApplyVo> creditApply = capitalApi.creditApply(applyDto);
        if (!creditApply.getData().getResponseCode().equals("0000")){
            return ResponseResult.error(ErrorCodeEnum.FAIL);
        }
        if (ZiFangBeanConstant.FENZHUAN.equals(capitalData.getBeanName())) {
            UserRegisterResult userRegisterResult = new UserRegisterResult();
            userRegisterResult.setUserId(userId);
            userRegisterResult.setOrderNo(applyDto.getOrderNo());
            userRegisterResult.setCapitalId(capitalData.getId());
            userRegisterResultMapper.insert(userRegisterResult);
        }
        return ResponseResult.success();
    }

    public String relationship(String ship){
        switch (ship) {
            case "1":
                return "02";
            case "2":
                return "01";
            case "3":
                return "03";
            case "10":
                return "08";
            case "20":
                return "06";
            case "30":
                return "07";
            default:
                return "05";
        }
    }

    @Override
    public ResponseResult<Void> loanApply(Long userId) {
        LoanApplyDto dto = new LoanApplyDto();
        UserData userData = userDataMapper.selectById(userId);
        UserDetail detail = userDetailMapper.queryByUserId(userId);
        DisburseData data = disburseDataMapper.selectByUserId(userId);
        BackVo backVo = userBankCardMapper.queryBackByUserId(userId);
        dto.setCreditNo(data.getCreditNo());
        dto.setLoanNo(data.getLoanNo());
        dto.setIdNo(detail.getIdNumber());
        dto.setSerialNo(CreditLoanNoUtils.getSequence("s"));
        dto.setApplyAmount(data.getCreditAmount().toString());
        dto.setApplyTerm(data.getPeriods().toString());
        dto.setTermType("02");
        if (data.getPurposeLoan().equals("个人日常消费")){
            dto.setApplyUse("TRA");
        }else if (data.getPurposeLoan().equals("装修")){
            dto.setApplyUse("DEC");
        }else if (data.getPurposeLoan().equals("教育")){
            dto.setApplyUse("EDU");
        }else if (data.getPurposeLoan().equals("手机数码")){
            dto.setApplyUse("MOD");
        }else if (data.getPurposeLoan().equals("电器")){
            dto.setApplyUse("HEA");
        }else if (data.getPurposeLoan().equals("家具家居")){
            dto.setApplyUse("DEC");
        }else if (data.getPurposeLoan().equals("医疗")){
            dto.setApplyUse("MED");
        }else if (data.getPurposeLoan().equals("租房")){
            dto.setApplyUse("REN");
        }else if (data.getPurposeLoan().equals("旅游")){
            dto.setApplyUse("TRA");
        }else if (data.getPurposeLoan().equals("婚庆")){
            dto.setApplyUse("MAR");
        }
        dto.setUserId(userId.toString());
        dto.setPayWay("R9926");
        dto.setBankCardNum(backVo.getBankAccount());
        dto.setBankPhone(backVo.getMobile());
        dto.setBankName(backVo.getBankName());
        dto.setApplyTime(DateUtil.now());
        CapitalData capitalData = capitalDataMapper.selectById(data.getCapitalId());
        if (ObjectUtil.isEmpty(capitalData)) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "资方未匹配");
        }
        LoanApi loanApi = zifangFactory.getApi(capitalData.getBeanName(), LoanApi.class);
        ResponseResult<CreditApplyVo> result = loanApi.loanApply(dto);
        if (result.getData().getResponseCode().equals("0000")){
            return ResponseResult.success();
        }
        return ResponseResult.error(ErrorCodeEnum.FAIL , result.getData().getResponseMsg());
    }

    private static String timeZ(String time){

        // 定义输入日期格式
        String inputFormat = "yyyy.MM.dd";
        // 定义输出日期格式
        String outputFormat = "yyyy-MM-dd";

        // 解析原始日期字符串
        java.util.Date date = DateUtil.parse(time, inputFormat);
        // 格式化日期为新的格式
        String formattedDate = DateUtil.format(date, outputFormat);
        return formattedDate;
    }


}
