package com.rongchen.byh.app.v2.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.rongchen.byh.app.constant.LoanType;
import com.rongchen.byh.app.dao.UserBankCardMapper;
import com.rongchen.byh.app.dao.UserCreditDataMapper;
import com.rongchen.byh.app.dao.UserDataMapper;
import com.rongchen.byh.app.dao.UserLoanApplyMapper;
import com.rongchen.byh.app.dto.app.BackVo;
import com.rongchen.byh.app.dto.h5.UserCheckLoanApplyDto;
import com.rongchen.byh.app.entity.UserCreditData;
import com.rongchen.byh.app.entity.UserData;
import com.rongchen.byh.app.entity.UserLoanApply;
import com.rongchen.byh.app.utils.UserTokenUtil;
import com.rongchen.byh.app.v2.dao.ProductDataMapper;
import com.rongchen.byh.app.v2.entity.ProductData;
import com.rongchen.byh.app.v2.handle.product.ProductHandleCenter;
import com.rongchen.byh.app.v2.handle.product.ProductHandleFactory;
import com.rongchen.byh.app.v2.service.IUserCheckLoanV2Service;
import com.rongchen.byh.app.vo.h5.UserCheckLoanApplyVo;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 用户初筛相关业务
 * @date 2024/12/11 10:26:05
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UserCheckLoanV2ServiceImpl implements IUserCheckLoanV2Service {
    private final UserLoanApplyMapper userLoanApplyMapper;
    private final UserDataMapper userDataMapper;
    private final UserCreditDataMapper userCreditDataMapper;
    private final UserBankCardMapper userBankCardMapper;
    private final ProductDataMapper productDataMapper;

    @Override
    public ResponseResult<UserCheckLoanApplyVo> apply(UserCheckLoanApplyDto userCheckLoanApplyDto) {
        Long userId = UserTokenUtil.getUserId();
        UserData userData = userDataMapper.selectById(userId);
        if (ObjectUtil.isEmpty(userData)) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "用户不存在");
        }

        ProductData productData = productDataMapper.selectById(userData.getSourceMode());
        if (ObjectUtil.isEmpty(productData)) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "产品信息异常");
        }
        ProductHandleFactory productHandle = ProductHandleCenter.getProductHandle(productData.getBeanName());
        return productHandle.apply(userCheckLoanApplyDto, userData);
    }



    @Override
    public ResponseResult<UserCheckLoanApplyVo> queryResult() {
        Long userId = UserTokenUtil.getUserId();
        UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userId, LoanType.CHECK,  0);
        UserCheckLoanApplyVo userCheckLoanApplyVo = new UserCheckLoanApplyVo();
        userCheckLoanApplyVo.setUserId(userId);
        userCheckLoanApplyVo.setBindCardStatus(0);
        if (ObjectUtil.isEmpty(userLoanApply)) {
            userCheckLoanApplyVo.setAuditStatus(0);
            return ResponseResult.success(userCheckLoanApplyVo);
        }
        UserCreditData userCreditData = userCreditDataMapper.queryByUserId(userId);
        userCheckLoanApplyVo.setAuditStatus(userLoanApply.getAuditsStatus());
        if (ObjectUtil.isNotEmpty(userCreditData)){
            userCheckLoanApplyVo.setCreditAmount(userCreditData.getCreditAmount());
        }
        BackVo backVo = userBankCardMapper.queryBackByUserId(userId);
        if (ObjectUtil.isNotEmpty(backVo)){
            userCheckLoanApplyVo.setBindCardStatus(1);
        }
        return ResponseResult.success(userCheckLoanApplyVo);
    }


    @Override
    public ResponseResult<UserCheckLoanApplyVo> flyQueryResult() {
        Long userId = UserTokenUtil.getUserId();
        UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userId, LoanType.CHECK,  2);
        UserCheckLoanApplyVo userCheckLoanApplyVo = new UserCheckLoanApplyVo();
        userCheckLoanApplyVo.setUserId(userId);
        userCheckLoanApplyVo.setBindCardStatus(0);
        if (ObjectUtil.isEmpty(userLoanApply)) {
            userCheckLoanApplyVo.setAuditStatus(0);
            return ResponseResult.success(userCheckLoanApplyVo);
        }
        UserCreditData userCreditData = userCreditDataMapper.queryByUserId(userId);
        userCheckLoanApplyVo.setAuditStatus(userLoanApply.getAuditsStatus());
        if (ObjectUtil.isNotEmpty(userCreditData)){
            userCheckLoanApplyVo.setCreditAmount(userCreditData.getCreditAmount());
        }
        BackVo backVo = userBankCardMapper.queryBackByUserId(userId);
        if (ObjectUtil.isNotEmpty(backVo)){
            userCheckLoanApplyVo.setBindCardStatus(1);
        }
        return ResponseResult.success(userCheckLoanApplyVo);
    }

}
