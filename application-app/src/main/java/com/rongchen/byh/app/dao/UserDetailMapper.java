package com.rongchen.byh.app.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rongchen.byh.app.entity.UserDetail;
import com.rongchen.byh.app.v2.dto.UserDetailDto;
import com.rongchen.byh.app.vo.app.UserFormVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【user_detail】的数据库操作Mapper
* @createDate 2024-12-11 11:56:33
* @Entity generator.domain.UserDetail
*/
@Mapper
public interface UserDetailMapper extends BaseMapper<UserDetail> {

    UserDetail queryByUserId(Long userId);

    UserFormVo queryUserFormByUserId(Long userId);

    UserDetail selectByIdCard(@Param("userId") Long userId, @Param("idNum")String idNum);

    UserDetailDto selectInfoByUserId(@Param("userId")Long userId);
}
