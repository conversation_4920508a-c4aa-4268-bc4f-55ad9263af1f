package com.rongchen.byh.app.controller.api;


import cn.dev33.satoken.annotation.SaIgnore;
import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.app.dto.api.*;
import com.rongchen.byh.app.service.FenZhuanZiFangService;
import com.rongchen.byh.app.service.ZifangApiService;
import com.rongchen.byh.common.api.beiyihua.config.BeiYiHuaProperties;
import com.rongchen.byh.common.api.zifang.service.mayi.config.ZiFangProperties;
import com.rongchen.byh.common.api.zifang.utils.ZiFangUtil;
import com.rongchen.byh.common.api.zifang.vo.BaseVo;
import com.rongchen.byh.common.core.annotation.DisableDataFilter;
import com.rongchen.byh.common.core.util.HttpUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Tag(name = "分转资方回调api接口")
@DisableDataFilter
@Slf4j
@RestController
@RequestMapping("/fenzhuan")
@SaIgnore
public class FenZhuanZifangApiController {

    @Resource
    ZiFangProperties ziFangProperties;
    @Resource
    FenZhuanZiFangService fenZhuanZiFangService;
    @Resource
    BeiYiHuaProperties beiYiHuaProperties;

    /**
     * 进件审核结果回调接口
     */
    @PostMapping("/creditNotice")
    public JSONObject creditNotice(@RequestBody String request) {
        try {
            log.info("【分转资方】授信审批结果通知参数：{}",request);
            JSONObject bizMap = JSONObject.parseObject(request);
            JSONObject data = bizMap.getJSONObject("respData");
            FenZhuanCreditNoticeDto dto = data.toJavaObject(FenZhuanCreditNoticeDto.class);
            if (dto.getOrderNo().startsWith("jw-")) {
                try {
                    String res = HttpUtil.postJson(beiYiHuaProperties.getByhCreditNoticeUrl(), request);
                    return JSONObject.parseObject(res);
                } catch (Exception e) {
                    log.error("【资方】授信审批结果分发 异常",e);
                }
                JSONObject result = new JSONObject();
                result.put("rspCode", "0001");
                result.put("rspMsg", "失败");
            }
            BaseVo baseVo = fenZhuanZiFangService.creditNotice(dto);
            JSONObject result = new JSONObject();
            result.put("rspCode", "0000");
            result.put("rspMsg", "成功");
            return result;
        } catch (Exception e) {

        }
        return null;
    }


    /**
     * 4.2 订单状态回调接口
     * @param request
     * @return
     */
    @PostMapping("/loanNotice")
    public JSONObject loanNotice(@RequestBody String request) {
        try {
            log.info("【分转资方】用信结果通知参数：{}",request);
            JSONObject bizMap = JSONObject.parseObject(request);
            FenZhuanLoanNoticeDto noticeDto = bizMap.getJSONObject("respData").toJavaObject(FenZhuanLoanNoticeDto.class);
            if (noticeDto.getOrderNo().startsWith("jw-")) {
                try {
                    String res = HttpUtil.postJson(beiYiHuaProperties.getByhLoanNoticeUrl(), request);
                    return JSONObject.parseObject(res);
                } catch (Exception e) {
                    log.error("【分转资方】用信结果分发 异常",e);
                }
                JSONObject result = new JSONObject();
                result.put("rspCode", "0001");
                result.put("rspMsg", "失败");
            }
            BaseVo baseVo = fenZhuanZiFangService.loanNotice(noticeDto);
            JSONObject result = new JSONObject();
            result.put("apiCode", bizMap.getString("apiCode"));
            result.put("rspCode", "0000");
            result.put("rspMsg", "成功");
            return result;
        } catch (Exception e) {
            log.error("【资方】 用信结果通知 异常",e);
        }

        return null;
    }

    /**
     * 还款计划变动回调接口
     * @param request
     * @return
     */
    @PostMapping("/loanNotice/billChange")
    public JSONObject repayNoticeBillChange(@RequestBody String request) {
        try {
            log.info("【分转资方】还款计划变动回调接口：{}",request);
            JSONObject bizMap = JSONObject.parseObject(request);
            FenZhuanRepayNotice repayNoticeDto = bizMap.getJSONObject("respData").toJavaObject(FenZhuanRepayNotice.class);
            if (repayNoticeDto.getThirdRepayNo().startsWith("jw-")) {
                try {
                    String res = HttpUtil.postJson(beiYiHuaProperties.getByhBillChangeUrl(), request);
                    return JSONObject.parseObject(res);
                } catch (Exception e) {
                    log.error("【分转资方】用信结果分发 异常",e);
                }
                JSONObject result = new JSONObject();
                result.put("rspCode", "0001");
                result.put("rspMsg", "失败");
            }
            JSONObject result = new JSONObject();
            result.put("apiCode", bizMap.getString("apiCode"));
            result.put("rspCode", "0000");
            result.put("rspMsg", "成功");
            return result;
        } catch (Exception e) {
            log.error("【资方】还款计划变动回调接口 异常",e);
        }
        return null;
    }

    /**
     * 4.4 还款结果回调接口
     * @return
     */
    @PostMapping("/repayNotice")
    public JSONObject repayNotice(@RequestBody String request) {
        try {
            log.info("【分转资方】还款结果回调参数：{}",request);
            JSONObject bizMap = JSONObject.parseObject(request);
            FenZhuanRepayNotice repayNoticeDto = bizMap.getJSONObject("respData").toJavaObject(FenZhuanRepayNotice.class);
            if (repayNoticeDto.getThirdRepayNo().startsWith("jw-")) {
                try {
                    String res = HttpUtil.postJson(beiYiHuaProperties.getByhRepayNoticeUrl(), request);
                    return JSONObject.parseObject(res);
                } catch (Exception e) {
                    log.error("【分转资方】还款结果分发 异常",e);
                }
                JSONObject result = new JSONObject();
                result.put("rspCode", "0001");
                result.put("rspMsg", "失败");
            }
            BaseVo baseVo = fenZhuanZiFangService.repayNotice(repayNoticeDto);

            JSONObject result = new JSONObject();
            result.put("apiCode", bizMap.getString("apiCode"));
            result.put("rspCode", baseVo.getResponseCode());
            result.put("rspMsg", baseVo.getResponseMsg());
            return result;
        } catch (Exception e) {
            log.error("【资方】还款结果通知 异常",e);
        }
        return null;
    }

}
