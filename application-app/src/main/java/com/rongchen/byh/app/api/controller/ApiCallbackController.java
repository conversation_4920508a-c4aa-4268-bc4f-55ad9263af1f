package com.rongchen.byh.app.api.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.app.api.service.strategy.factory.OutApiFactory;
import com.rongchen.byh.app.api.service.strategy.process.OutApiAbstractProcess;
import com.rongchen.byh.common.core.annotation.DisableDataFilter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Tag(name = "资方相关api接口")
@DisableDataFilter
@Slf4j
@RestController
@RequestMapping("/outApi")
@SaIgnore
public class ApiCallbackController {

    @Resource
    private OutApiFactory outApiFactory;

    @Operation(summary = "2.3.2资方还款结果通知")
    @PostMapping("/{channel}/getRepayNotice")
    public JSONObject getRepayNotice(@PathVariable("channel") String channel, @RequestBody String request){
        try {
            log.info("api还款结果通知渠道：{}，请求参数：{}", channel, request);
            JSONObject bizMap = JSONObject.parseObject(request);
            OutApiAbstractProcess outApiAbstractProcess = outApiFactory.getOutApiAbstractProcess(channel);
            if (ObjectUtil.isEmpty(outApiAbstractProcess)) {
                log.error("未找到对应的渠道");
                JSONObject jsonObject = new JSONObject();
                return jsonObject;
            }
            return outApiAbstractProcess.getRepayNotice(bizMap);
        } catch (Exception e) {
            log.error("【api获取还款结果】 失败：{}", e);
        }
        return new JSONObject();
    }
    @Operation(summary = "2.4.2赊销还款结果通知")
    @PostMapping("/{channel}/getSaleNotice")
    public JSONObject getSaleNotice(@PathVariable("channel") String channel, @RequestBody String request){
        try {
            log.info("api赊销还款结果通知渠道：{}，请求参数：{}", channel, request);
            JSONObject bizMap = JSONObject.parseObject(request);
            OutApiAbstractProcess outApiAbstractProcess = outApiFactory.getOutApiAbstractProcess(channel);
            if (ObjectUtil.isEmpty(outApiAbstractProcess)) {
                log.error("未找到对应的渠道");
                JSONObject jsonObject = new JSONObject();
                return jsonObject;
            }
            return outApiAbstractProcess.getSaleNotice(bizMap);
        } catch (Exception e) {
            log.error("【api获取赊销还款结果】 失败：{}", e);
        }
        return new JSONObject();
    }
}
