package com.rongchen.byh.app.mqListener;

import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.rongchen.byh.app.dao.DisburseDataMapper;
import com.rongchen.byh.app.service.CallCrmService;
import com.rongchen.byh.app.service.CreditService;
import com.rongchen.byh.app.service.DisburseRecordService;
import com.rongchen.byh.app.service.LoanService;
import com.rongchen.byh.app.service.impl.SmsLoanService;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.rabbitmq.constants.QueueConstant;
import com.rongchen.byh.common.rabbitmq.dto.LoanSuccessDto;
import com.rongchen.byh.common.redis.util.CommonRedisUtil;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

/**
 * 下款成功后通知监听
 */
@Component
@Slf4j
public class LoanSuccessListener {

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private CommonRedisUtil commonRedisUtil;

    @Resource
    LoanService loanService;
    @Resource
    CallCrmService callCrmService;
    @Resource
    CreditService creditService;
    @Resource
    SmsLoanService smsLoanService;
    @Resource
    DisburseDataMapper disburseDataMapper;
    @Resource
    DisburseRecordService disburseRecordService;

    @RabbitListener(queues = QueueConstant.LOAN_SUCCESS_QUEUE, ackMode = "MANUAL")
    public void receiver(String msg, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        
        LoanSuccessDto loanSuccessDto = null;
        String loanNo = "未知";
        String idempotencyKey = "未知";
        String   traceId = UUID.randomUUID().toString();
        try {
            try {
                loanSuccessDto = JSONObject.parseObject(msg, LoanSuccessDto.class);
                loanNo = loanSuccessDto.getLoanNo();
                 traceId = loanSuccessDto.getTraceId() != null ? loanSuccessDto.getTraceId()
                : UUID.randomUUID().toString();
                Map<String, String> contextMap = new HashMap<>();
                contextMap.put("traceId", traceId);
                MDC.setContextMap(contextMap);
                log.info("【下款成功监听器】开始处理消息，RabbitMQ原始消息: {} tag: {}", msg, deliveryTag);
            } catch (Exception e) {
                log.error("【下款成功监听器】消息解析失败，将拒绝消息。原始消息: {}, 错误: {}", msg, e.getMessage(), e);
                channel.basicNack(deliveryTag, false, false);
                return;
            }

            if (loanNo == null || loanNo.trim().isEmpty()) {
                log.error("【下款成功监听器】接收到的 DTO 缺少关键字段 loanNo，将拒绝消息。消息: {}", msg);
                channel.basicNack(deliveryTag, false, false);
                return;
            }

           
            log.info("【下款成功监听器】开始处理消息，借款单号: {}", loanNo);

            idempotencyKey = commonRedisUtil.buildKey("mq", "listener", "loan_success", "loanNo", loanNo);
            String statusKey = commonRedisUtil.buildKey("mq", "processed", "status", idempotencyKey);
            RBucket<String> statusBucket = redissonClient.getBucket(statusKey);

            if ("COMPLETED".equals(statusBucket.get())) {
                log.warn("【下款成功监听器】检测到重复消息（已处理完成），幂等键: {}。将确认消息。", idempotencyKey);
                channel.basicAck(deliveryTag, false);
                return;
            }

            log.info("【下款成功监听器】幂等性检查通过，幂等键: {}, 开始执行业务逻辑。", idempotencyKey);

            try {
                ResponseResult<Void> repaymentResult = loanService.queryRepaymentSchedule(loanNo);
                if (!repaymentResult.isSuccess()) {
                    log.error("【下款成功监听器】生成还款计划失败，借款单号: {}, 原因: {}, 将拒绝消息。", loanNo, repaymentResult.getErrorMessage());
                    disburseRecordService.saveRecord("借款单号:" + loanNo, traceId,
                            "生成还款计划失败: " + repaymentResult.getErrorMessage());
                    channel.basicNack(deliveryTag, false, false);
                    return;
                }
                log.info("【下款成功监听器】生成还款计划成功，借款单号: {}", loanNo);

                ResponseResult<Void> saleApplyResult = loanService.createSaleApply(loanNo);
                if (!saleApplyResult.isSuccess()) {
                    log.error("【下款成功监听器】生成赊销计划失败，借款单号: {}, 原因: {}, 将拒绝消息。", loanNo, saleApplyResult.getErrorMessage());
                    disburseRecordService.saveRecord("借款单号:" + loanNo, traceId,
                            "生成赊销计划失败: " + saleApplyResult.getErrorMessage());
                    channel.basicNack(deliveryTag, false, false);
                    return;
                }
                log.info("【下款成功监听器】生成赊销计划成功，借款单号: {}", loanNo);

                ResponseResult<Void> crmResult = callCrmService.sendLoan(loanNo);
                if (!crmResult.isSuccess()) {
                    log.error("【下款成功监听器】CRM侧通知失败，借款单号: {}, 原因: {}, 将拒绝消息。", loanNo, crmResult.getErrorMessage());
                    disburseRecordService.saveRecord("借款单号:" + loanNo, traceId, "crm侧通知失败: " + crmResult.getErrorMessage());
                    channel.basicNack(deliveryTag, false, false);
                    return;
                }
                log.info("【下款成功监听器】CRM侧通知成功，借款单号: {}", loanNo);

                ResponseResult<Void> smsResult = smsLoanService.send(loanNo);
                if (!smsResult.isSuccess()) {
                    log.error("【下款成功监听器】发送通知短信失败，借款单号: {}, 原因: {}, 将拒绝消息。", loanNo, smsResult.getErrorMessage());
                    disburseRecordService.saveRecord("借款单号:" + loanNo, traceId, "通知短信发送失败: " + smsResult.getErrorMessage());
                    channel.basicNack(deliveryTag, false, false);
                    return;
                }
                log.info("【下款成功监听器】发送通知短信成功，借款单号: {}", loanNo);

                statusBucket.set("COMPLETED", 1, TimeUnit.HOURS);
                log.info("【下款成功监听器】消息处理成功，幂等键: {}，将确认消息。", idempotencyKey);
                channel.basicAck(deliveryTag, false);
                log.debug("【下款成功监听器】消息已确认 (ACK)，幂等键: {}, deliveryTag: {}", idempotencyKey, deliveryTag);

            } catch (Exception e) {
                log.error("【下款成功监听器】处理业务逻辑时发生异常，幂等键: {}, 借款单号: {}, 错误: {}，将拒绝消息。", idempotencyKey, loanNo,
                        e.getMessage(), e);
                channel.basicNack(deliveryTag, false, false);
                log.warn("【下款成功监听器】业务处理异常，消息已拒绝 (NACK)，幂等键: {}, deliveryTag: {}", idempotencyKey, deliveryTag);
            }

        } catch (IOException e) {
            log.error("【下款成功监听器】手动确认/拒绝消息时发生 IO 错误, 幂等键: {}, deliveryTag: {}. 错误: {}",
                    idempotencyKey, deliveryTag, e.getMessage(), e);
        } catch (Exception e) {
            log.error("【下款成功监听器】处理消息外层发生未知错误, 幂等键: {}, deliveryTag: {}. 错误: {}，将尝试拒绝消息。",
                    idempotencyKey, deliveryTag, e.getMessage(), e);
            try {
                channel.basicNack(deliveryTag, false, false);
                log.warn("【下款成功监听器】外层未知错误，消息已尝试拒绝 (NACK)，幂等键: {}, deliveryTag: {}", idempotencyKey, deliveryTag);
            } catch (IOException ioEx) {
                log.error("【下款成功监听器】外层未知错误后，尝试拒绝消息时再次发生 IO 错误, 幂等键: {}, deliveryTag: {}. 错误: {}",
                        idempotencyKey, deliveryTag, ioEx.getMessage(), ioEx);
            }
        } finally {
            MDC.clear();
            log.debug("【下款成功监听器】MDC已清理，幂等键: {}", idempotencyKey);
        }
    }

    /**
     * 用信通知api队列
     * 
     * @param msg
     * @param channel
     * @param deliveryTag
     */
    @RabbitListener(queues = QueueConstant.LOAN_SUCCESS_API_QUEUE, ackMode = "MANUAL")
    public void receiverLoanSuccessApi(String msg, Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
       
        LoanSuccessDto loanSuccessDto = null;
        String loanNo = "未知";
        String traceId = null;
        String idempotencyKey = "未知";

        try {
            try {
                loanSuccessDto = JSONObject.parseObject(msg, LoanSuccessDto.class);
                loanNo = loanSuccessDto.getLoanNo();
                traceId = loanSuccessDto.getTraceId() != null ? loanSuccessDto.getTraceId()
                        : UUID.randomUUID().toString();
            } catch (Exception e) {
                log.error("【用信通知API监听器】消息解析失败，将拒绝消息。原始消息: {}, 错误: {}", msg, e.getMessage(), e);
                channel.basicNack(deliveryTag, false, false);
                return;
            }

            if (loanNo == null || loanNo.trim().isEmpty()) {
                log.error("【用信通知API监听器】接收到的 DTO 缺少关键字段 loanNo，将拒绝消息。消息: {}", msg);
                channel.basicNack(deliveryTag, false, false);
                return;
            }

            Map<String, String> contextMap = new HashMap<>();
            contextMap.put("traceId", traceId);
            MDC.setContextMap(contextMap);
            log.info("【用信通知API监听器】开始处理消息，RabbitMQ原始消息: {} tag: {}", msg, deliveryTag);
            log.info("【用信通知API监听器】开始处理消息，借款单号: {}", loanNo);

            idempotencyKey = commonRedisUtil.buildKey("mq", "listener", "loan_success_api", "loanNo", loanNo);
            String statusKey = commonRedisUtil.buildKey("mq", "processed", "status", idempotencyKey);
            RBucket<String> statusBucket = redissonClient.getBucket(statusKey);

            if ("COMPLETED".equals(statusBucket.get())) {
                log.warn("【用信通知API监听器】检测到重复消息（已处理完成），幂等键: {}。将确认消息。", idempotencyKey);
                channel.basicAck(deliveryTag, false);
                return;
            }

            log.info("【用信通知API监听器】幂等性检查通过，幂等键: {}, 开始执行业务逻辑。", idempotencyKey);

            try {
                ResponseResult<Void> repaymentResult = loanService.queryApiRepaymentSchedule(loanNo);
                if (!repaymentResult.isSuccess()) {
                    log.error("【用信通知API监听器】生成API还款计划失败，借款单号: {}, 原因: {}, 将拒绝消息。", loanNo, repaymentResult.getErrorMessage());
                    disburseRecordService.saveRecord("借款单号:" + loanNo, traceId,
                            "生成API还款计划失败: " + repaymentResult.getErrorMessage());
                    channel.basicNack(deliveryTag, false, false);
                    return;
                }
                log.info("【用信通知API监听器】生成API还款计划成功，借款单号: {}", loanNo);

                ResponseResult<Void> saleApplyResult = loanService.createApiSaleApply(loanNo);
                if (!saleApplyResult.isSuccess()) {
                    log.error("【用信通知API监听器】生成API赊销计划失败，借款单号: {}, 原因: {}, 将拒绝消息。", loanNo, saleApplyResult.getErrorMessage());
                    disburseRecordService.saveRecord("借款单号:" + loanNo, traceId,
                            "生成API赊销计划失败: " + saleApplyResult.getErrorMessage());
                    channel.basicNack(deliveryTag, false, false);
                    return;
                }
                log.info("【用信通知API监听器】生成API赊销计划成功，借款单号: {}", loanNo);

                // ResponseResult<Void> crmResult = callCrmService.sendLoan(loanNo);
                // if (!crmResult.isSuccess()) {
                // disburseRecordService.saveRecord("借款单号:"+loanNo,traceId,"crm侧通知");
                // }

                statusBucket.set("COMPLETED", 1, TimeUnit.HOURS);
                log.info("【用信通知API监听器】消息处理成功，幂等键: {}，将确认消息。", idempotencyKey);
                channel.basicAck(deliveryTag, false);
                log.debug("【用信通知API监听器】消息已确认 (ACK)，幂等键: {}, deliveryTag: {}", idempotencyKey, deliveryTag);

            } catch (Exception e) {
                log.error("【用信通知API监听器】处理业务逻辑时发生异常，幂等键: {}, 借款单号: {}, 错误: {}，将拒绝消息。", idempotencyKey, loanNo,
                        e.getMessage(), e);
                channel.basicNack(deliveryTag, false, false);
                log.warn("【用信通知API监听器】业务处理异常，消息已拒绝 (NACK)，幂等键: {}, deliveryTag: {}", idempotencyKey, deliveryTag);
            }

        } catch (IOException e) {
            log.error("【用信通知API监听器】手动确认/拒绝消息时发生 IO 错误, 幂等键: {}, deliveryTag: {}. 错误: {}",
                    idempotencyKey, deliveryTag, e.getMessage(), e);
        } catch (Exception e) {
            log.error("【用信通知API监听器】处理消息外层发生未知错误, 幂等键: {}, deliveryTag: {}. 错误: {}，将尝试拒绝消息。",
                    idempotencyKey, deliveryTag, e.getMessage(), e);
            try {
                channel.basicNack(deliveryTag, false, false);
                log.warn("【用信通知API监听器】外层未知错误，消息已尝试拒绝 (NACK)，幂等键: {}, deliveryTag: {}", idempotencyKey, deliveryTag);
            } catch (IOException ioEx) {
                log.error("【用信通知API监听器】外层未知错误后，尝试拒绝消息时再次发生 IO 错误, 幂等键: {}, deliveryTag: {}. 错误: {}",
                        idempotencyKey, deliveryTag, ioEx.getMessage(), ioEx);
            }
        } finally {
            MDC.clear();
            log.debug("【用信通知API监听器】MDC已清理，幂等键: {}", idempotencyKey);
        }
    }

}
