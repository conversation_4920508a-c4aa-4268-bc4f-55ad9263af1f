package com.rongchen.byh.app.controller;

import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.rongchen.byh.app.dto.app.BackVo;
import com.rongchen.byh.app.dto.app.LoanApplyDto;
import com.rongchen.byh.app.dto.app.TrialPaymentDto;
import com.rongchen.byh.app.service.DisburseService;
import com.rongchen.byh.app.utils.UserTokenUtil;
import com.rongchen.byh.app.vo.app.DisburseAmountVo;
import com.rongchen.byh.app.vo.app.DisburseStatusVo;
import com.rongchen.byh.app.vo.app.WebSaleUrlVo;
import com.rongchen.byh.common.api.zifang.dto.BindBankSmsDto;
import com.rongchen.byh.common.api.zifang.dto.QueryBindBankResultDto;
import com.rongchen.byh.common.api.zifang.dto.VerifyBindBankSmsDto;
import com.rongchen.byh.common.api.zifang.vo.BindBankSmsVo;
import com.rongchen.byh.common.api.zifang.vo.LoanElementVo;
import com.rongchen.byh.common.api.zifang.vo.QueryBindBankResultVo;
import com.rongchen.byh.common.api.zifang.vo.RepayPlanCalcVo;
import com.rongchen.byh.common.api.zifang.vo.VerifyBindBankSmsVo;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName DisburseController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/12 16:07
 * @Version 1.0
 **/
@Tag(name = "支用相关接口")
@ApiSupport(order = 3)
@RestController
@RequestMapping("/userApi")
public class DisburseController {

    @Resource
    DisburseService disburseService;
    @Resource
    RedissonClient redissonClient;


    @Operation(summary = "获取银行卡列表")
    @PostMapping("/backList")
    public ResponseResult<List<BackVo>> backList(){
        return disburseService.backList();
    }


    @Operation(summary = "借款试算")
    @PostMapping("/trialPayment")
    public ResponseResult<RepayPlanCalcVo> trialPayment(@RequestBody TrialPaymentDto dto){
        return disburseService.trialPayment(dto);
    }


    @Operation(summary = "绑卡-支付通道")
    @PostMapping("/bindBackPay")
    public ResponseResult<LoanElementVo> bindBackPay(){
        return disburseService.bindBackPay();
    }


    @Operation(summary = "绑卡-获取验证码")
    @PostMapping("/bindSendCode")
    public ResponseResult<BindBankSmsVo> bindSendCode(@RequestBody BindBankSmsDto dto){
        return disburseService.bindSendCode(dto);
    }


    @Operation(summary = "绑卡-提交验证码")
    @PostMapping("/verifyBindSend")
    public ResponseResult<VerifyBindBankSmsVo> verifyBindSend(@RequestBody VerifyBindBankSmsDto dto){
        return disburseService.verifyBindSend(dto);
    }


    @Operation(summary = "绑卡-结果查询 ")
    @PostMapping("/bindBankResult")
    public ResponseResult<QueryBindBankResultVo> bindBankResult(@RequestBody QueryBindBankResultDto dto){
        return disburseService.bindBankResult(dto);
    }

    @Operation(summary = "借款申请")
    @PostMapping("/loanApply")
    public ResponseResult<Void> loanApply(@RequestBody LoanApplyDto dto){
        Long userId = UserTokenUtil.getUserId();
        RLock lock = redissonClient.getLock("loanApply" + userId);
        boolean isLocked = false;
        try {
            isLocked = lock.tryLock(5, TimeUnit.SECONDS);
            if (!isLocked) {
                return ResponseResult.error(ErrorCodeEnum.FAIL, "系统繁忙，请稍后再试！");
            }
            return disburseService.loanApply(dto);
        } catch (Exception e) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "系统异常");
        } finally {
            if (isLocked) {
                lock.unlock();
            }
        }

    }



    @Operation(summary = "授信结果查询")
    @PostMapping("/queryCreditStatus")
    public ResponseResult<DisburseStatusVo> queryCreditStatus(){
        return disburseService.queryCreditStatus();
    }



    @Operation(summary = "支用首页 额度查询")
    @PostMapping("/disburseAmount")
    public ResponseResult<DisburseAmountVo> disburseAmount(){
        return disburseService.disburseAmount();
    }


    @Operation(summary = "获取赊销h5地址")
    @PostMapping("/webSaleUrl")
    public ResponseResult<WebSaleUrlVo> webSaleUrl(){
        return disburseService.webSaleUrl();
    }



}
