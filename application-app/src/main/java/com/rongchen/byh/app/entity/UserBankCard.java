package com.rongchen.byh.app.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**

 * 项目名称：byh_java
 * 文件名称: UserBankCard
 * 创建时间: 2025-04-03 11:50
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.app.entity
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */

/**
 * 用户绑定银行卡
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "`user_bank_card`")
public class UserBankCard implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    @TableField(value = "`user_id`")
    private Long userId;

    /**
     * 客户身份证号
     */
    @TableField(value = "`id_card`")
    private String idCard;

    /**
     * 开户行
     */
    @TableField(value = "`bank_name`")
    private String bankName;

    /**
     * 银行卡号
     */
    @TableField(value = "`bank_account`")
    private String bankAccount;

    /**
     * 手机号
     */
    @TableField(value = "`mobile`")
    private String mobile;

    /**
     * 服务合同号
     */
    @TableField(value = "`contract_num`")
    private String contractNum;

    /**
     * 宝付合同号
     */
    @TableField(value = "`bao_fu_contract_num`")
    private String baoFuContractNum;

    /**
     * 客户姓名
     */
    @TableField(value = "`customer_name`")
    private String customerName;

    /**
     * 创建时间
     */
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 合同录入时服务费收取方式0：百分比收取；1：固定金额收取
     */
    @TableField(value = "`consulting_mode`")
    private String consultingMode;

    /**
     * 合同录入时的服务费收取比例/金额
     */
    @TableField(value = "`consulting_rate`")
    private String consultingRate;

    private static final long serialVersionUID = 1L;
}