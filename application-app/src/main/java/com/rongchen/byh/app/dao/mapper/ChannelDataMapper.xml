<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.dao.ChannelDataMapper">

    <resultMap id="BaseResultMap" type="com.rongchen.byh.app.entity.ChannelData">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="secret" column="secret" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="createUserId" column="create_user_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateUserId" column="update_user_id" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,`name`,secret,status,create_user_id,
        create_time,update_user_id,update_time
    </sql>
    <select id="selectBySecret" resultType="com.rongchen.byh.app.entity.ChannelData">
        select * from channel_data where secret = #{secret}
    </select>
</mapper>
