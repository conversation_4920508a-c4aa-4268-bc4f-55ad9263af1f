package com.rongchen.byh.app.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.rongchen.byh.app.dao.ApiCreditRecordMapper;
import com.rongchen.byh.app.entity.ApiCreditRecord;
import com.rongchen.byh.common.core.util.MDCUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 蚂蚁全流程进行中数据处理（超过一小时还在进行中的数据，自动置为失败）
 * @date 2025/5/7 09:38:48
 */
@Component
@Slf4j
public class MaYiApiInProcessJob {
    @Resource
    private ApiCreditRecordMapper apiCreditRecordMapper;
    @XxlJob("maYiApiInProcessJob")
    public void maYiApiInProcessJob() {
        // 设置全局TraceId
        try {
            String jobTraceId = MDCUtil.generateTraceId();
            MDCUtil.setTraceId(jobTraceId);
            log.info("=========蚂蚁全流程进行中数据处理定时任务开始======");
            // 查询进行中的数据
            String param = XxlJobHelper.getJobParam();
            Integer day = 1;
            if (StrUtil.isNotEmpty(param)) {
                day = Integer.valueOf(param);
            }
            List<ApiCreditRecord> list = apiCreditRecordMapper.selectInProcessList(day);
            if (CollUtil.isEmpty(list)) {
                log.info("=========没有进行中的数据=========");
                return;
            }
            int batchSize = 10;
            int totalRequests = list.size();
            for (int i = 0; i < totalRequests; i += batchSize) {
                int end = Math.min(i + batchSize, totalRequests);
                List<ApiCreditRecord> batch = list.subList(i, end);
                int count = apiCreditRecordMapper.batchFail(batch);
                log.info("处理第 {} 批数据，成功更新 {} 条数据", i, count);
            }
            log.info("===========蚂蚁全流程进行中数据处理定时任务结束==========");
        } catch (Exception e) {
            log.error("蚂蚁全流程进行中数据处理定时任务异常", e);
        } finally {
            MDCUtil.clear();
        }
    }
}
