<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.dao.UserLoanApplyMapper">

    <resultMap id="BaseResultMap" type="com.rongchen.byh.app.entity.UserLoanApply">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="creditId" column="credit_id" jdbcType="VARCHAR"/>
            <result property="apiCreditNo" column="api_credit_no" jdbcType="VARCHAR"/>
            <result property="applyType" column="apply_type" jdbcType="INTEGER"/>
            <result property="auditsStatus" column="audits_status" jdbcType="INTEGER"/>
            <result property="onlineType" column="online_type" jdbcType="INTEGER"/>
            <result property="userApplyInfo" column="user_apply_info" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>

    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,apply_type,credit_id,api_credit_no,
        audits_status,online_type,user_apply_info,create_time,
        update_time
    </sql>
    <select id="selectByUserIdAndTypeStatus" resultType="com.rongchen.byh.app.entity.UserLoanApply">
        select
        <include refid="Base_Column_List"/>
        from user_loan_apply
        where user_id = #{userId} and apply_type = #{applyType} and  online_type in (${onlineType}) order by id desc limit 1
    </select>

    <select id="selectByUserIdAndType" resultType="com.rongchen.byh.app.entity.UserLoanApply">
        select
        <include refid="Base_Column_List"/>
        from user_loan_apply
        where user_id = #{userId} and apply_type = #{applyType} and online_type = #{onlineType} order by id desc limit 1
    </select>
    <select id="selectByCreditId" resultType="com.rongchen.byh.app.entity.UserLoanApply">
        select
        <include refid="Base_Column_List"/>
        from user_loan_apply
        where credit_id = #{creditId} order by id desc limit 1
    </select>
    <select id="selectInProcessList" resultType="com.rongchen.byh.app.entity.UserLoanApply">
        select <include refid="Base_Column_List"/>
        from user_loan_apply
        where audits_status = 0 and create_time > DATE_SUB(CURDATE(), INTERVAL ${day} DAY) and credit_id is not null
    </select>
</mapper>
