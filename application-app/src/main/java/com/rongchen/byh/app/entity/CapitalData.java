package com.rongchen.byh.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 资方信息表
 * @TableName capital_data
 */
@TableName(value ="capital_data")
@Data
public class CapitalData implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 资方名称
     */
    private String name;

    /**
     * 在线状态 0-下线 1-在线
     */
    private Integer status;

    /**
     * 排序（越小越靠前）
     */
    private Integer sort;

    /**
     * 配置bean名称
     */
    private String beanName;

    /**
     * 参数配置
     */
    private String paramConfig;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}