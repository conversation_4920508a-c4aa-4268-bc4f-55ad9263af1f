package com.rongchen.byh.app.mqListener;

import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.rongchen.byh.app.service.impl.SmsLoanService;
import com.rongchen.byh.common.rabbitmq.constants.QueueConstant;
import com.rongchen.byh.common.rabbitmq.dto.RepaySmsDto;
import com.rongchen.byh.common.redis.util.CommonRedisUtil;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

/**
 * 次日还款短信通知
 */
@Component
@Slf4j
public class RepaySmsListener {

    @Resource
    SmsLoanService smsLoanService;
    @Resource
    RedissonClient redissonClient;
    @Resource
    CommonRedisUtil commonRedisUtil;

    @RabbitListener(queues = QueueConstant.REPAY_SMS_QUEUE, ackMode = "MANUAL")
    public void receiver(String msg, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        
        RepaySmsDto dto = null;
        String repayIdForLog = "N/A";

        try {
            try {
                dto = JSONObject.parseObject(msg, RepaySmsDto.class);
            } catch (Exception e) {
                log.error("解析还款短信通知消息失败: {}. 错误: {}", msg, e.getMessage(), e);
                channel.basicNack(deliveryTag, false, false);
                return;
            }

            if (dto == null || dto.getRepayId() == null) {
                log.error("收到无效的还款短信通知消息 (dto 为 null 或 repayId 为 null): {}", msg);
                channel.basicNack(deliveryTag, false, false);
                return;
            }
            repayIdForLog = String.valueOf(dto.getRepayId());

            String idempotencyKey = commonRedisUtil.buildKey("mq", "processed", "repaySms", repayIdForLog);
            RBucket<String> statusBucket = redissonClient.getBucket(idempotencyKey);
            if ("COMPLETED".equals(statusBucket.get())) {
                log.warn("还款短信通知消息 (RepayId: {}) 已处理过，跳过重复处理。", repayIdForLog);
                channel.basicAck(deliveryTag, false);
                return;
            }

            String traceId = dto.getTraceId();
            Map<String, String> contextMap = new HashMap<>();
            contextMap.put("traceId", traceId);
            MDC.setContextMap(contextMap);
            log.info("【还款短信通知监听器】开始处理消息，RabbitMQ原始消息: {} tag: {}", msg, deliveryTag);
            
            try {
                log.info("开始处理还款短信通知 (RepayId: {})", repayIdForLog);
                smsLoanService.sendNeedRepaySms(dto.getDisburseId(), dto.getRepayId(), dto.getUserId());

                statusBucket.set("COMPLETED", 1, TimeUnit.HOURS);
                log.info("还款短信通知处理成功 (RepayId: {}). 设置幂等状态完成。", repayIdForLog);

                channel.basicAck(deliveryTag, false);
                log.debug("还款短信通知消息已确认 (ACK), RepayId: {}, deliveryTag: {}", repayIdForLog, deliveryTag);

            } catch (Exception e) {
                log.error("处理还款短信通知时发生业务异常 (RepayId: {}): {}", repayIdForLog, e.getMessage(), e);
                channel.basicNack(deliveryTag, false, false);
                log.warn("还款短信通知消息已拒绝 (NACK), RepayId: {}, deliveryTag: {}", repayIdForLog, deliveryTag);
            } finally {
                MDC.clear();
            }

        } catch (IOException e) {
            log.error("确认/拒绝还款短信通知消息时发生 IO 错误 (RepayId: {}), deliveryTag: {}. 错误: {}",
                    repayIdForLog, deliveryTag, e.getMessage(), e);
        } catch (Exception e) {
            log.error("处理还款短信通知消息时发生未知顶级异常 (RepayId: {}), deliveryTag: {}. 消息: {}. 错误: {}",
                    repayIdForLog, deliveryTag, msg, e.getMessage(), e);
            try {
                channel.basicNack(deliveryTag, false, false);
                log.warn("未知顶级异常，还款短信通知消息已尝试拒绝 (NACK), RepayId: {}, deliveryTag: {}", repayIdForLog, deliveryTag);
            } catch (IOException ioEx) {
                log.error("未知顶级异常，尝试拒绝消息时再次发生 IO 错误 (RepayId: {}), deliveryTag: {}. 错误: {}",
                        repayIdForLog, deliveryTag, ioEx.getMessage(), ioEx);
            }
        }
    }

    @RabbitListener(queues = QueueConstant.TODAY_REPAY_SMS_QUEUE, ackMode = "MANUAL")
    public void receiver2(String msg, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        log.info("【当日还款短信通知监听器】开始处理消息，RabbitMQ原始消息: {}", msg);
        RepaySmsDto dto = null;
        String repayIdForLog = "N/A";

        try {
            try {
                dto = JSONObject.parseObject(msg, RepaySmsDto.class);
            } catch (Exception e) {
                log.error("解析当日还款短信通知消息失败: {}. 错误: {}", msg, e.getMessage(), e);
                channel.basicNack(deliveryTag, false, false);
                return;
            }

            if (dto == null || dto.getRepayId() == null) {
                log.error("收到无效的当日还款短信通知消息 (dto 为 null 或 repayId 为 null): {}", msg);
                channel.basicNack(deliveryTag, false, false);
                return;
            }
            repayIdForLog = String.valueOf(dto.getRepayId());

            String idempotencyKey = commonRedisUtil.buildKey("mq", "processed", "todayRepaySms", repayIdForLog);
            RBucket<String> statusBucket = redissonClient.getBucket(idempotencyKey);
            if ("COMPLETED".equals(statusBucket.get())) {
                log.warn("当日还款短信通知消息 (RepayId: {}) 已处理过，跳过重复处理。", repayIdForLog);
                channel.basicAck(deliveryTag, false);
                return;
            }

            String traceId = dto.getTraceId();
            Map<String, String> contextMap = new HashMap<>();
            contextMap.put("traceId", traceId);
            MDC.setContextMap(contextMap);

            try {
                log.info("开始处理当日还款短信通知 (RepayId: {})", repayIdForLog);
                smsLoanService.sendTodayNeedRepaySms(dto.getDisburseId(), dto.getRepayId(), dto.getUserId());

                statusBucket.set("COMPLETED", 1, TimeUnit.HOURS);
                log.info("当日还款短信通知处理成功 (RepayId: {}). 设置幂等状态完成。", repayIdForLog);

                channel.basicAck(deliveryTag, false);
                log.debug("当日还款短信通知消息已确认 (ACK), RepayId: {}, deliveryTag: {}", repayIdForLog, deliveryTag);

            } catch (Exception e) {
                log.error("处理当日还款短信通知时发生业务异常 (RepayId: {}): {}", repayIdForLog, e.getMessage(), e);
                channel.basicNack(deliveryTag, false, false);
                log.warn("当日还款短信通知消息已拒绝 (NACK), RepayId: {}, deliveryTag: {}", repayIdForLog, deliveryTag);
            } finally {
                MDC.clear();
            }

        } catch (IOException e) {
            log.error("确认/拒绝当日还款短信通知消息时发生 IO 错误 (RepayId: {}), deliveryTag: {}. 错误: {}",
                    repayIdForLog, deliveryTag, e.getMessage(), e);
        } catch (Exception e) {
            log.error("处理当日还款短信通知消息时发生未知顶级异常 (RepayId: {}), deliveryTag: {}. 消息: {}. 错误: {}",
                    repayIdForLog, deliveryTag, msg, e.getMessage(), e);
            try {
                channel.basicNack(deliveryTag, false, false);
                log.warn("未知顶级异常，当日还款短信通知消息已尝试拒绝 (NACK), RepayId: {}, deliveryTag: {}", repayIdForLog, deliveryTag);
            } catch (IOException ioEx) {
                log.error("未知顶级异常，尝试拒绝消息时再次发生 IO 错误 (RepayId: {}), deliveryTag: {}. 错误: {}",
                        repayIdForLog, deliveryTag, ioEx.getMessage(), ioEx);
            }
        }
    }
}
