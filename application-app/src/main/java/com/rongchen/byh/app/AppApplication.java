package com.rongchen.byh.app;

import ch.qos.logback.classic.LoggerContext;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.Enumeration;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 应用服务启动类。
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@EnableAsync
@SpringBootApplication
@ComponentScan("com.rongchen.byh")
@MapperScan({ "com.rongchen.byh.app.dao", "com.rongchen.byh.common.log.dao","com.rongchen.byh.app.v2.dao"})
public class AppApplication {

	public static void main(String[] args) {
		initializeNetworkProperties(); // 初始化网络属性，包括 IP
		SpringApplication.run(AppApplication.class, args);
	}

	private static void initializeNetworkProperties() {
		String detectedIp = "127.0.0.1"; // Default to localhost if detection fails
		String detectedHostname = "qyc-unknown-host"; // Default for logging
		String eth0Ip = null; // To store eth0 IP if found
		String fallbackIp = null; // To store the first valid fallback IP

		try {
			// --- 设置 HOSTNAME 用于 Logback (保留原有逻辑) ---
			try {
				detectedHostname = InetAddress.getLocalHost().getHostName();
			} catch (Exception e) {
				System.err.println("Could not determine hostname for logging: " + e.getMessage());
			}
			String hostname = InetAddress.getLocalHost().getHostName();
			// 或获取 IP： String hostname = InetAddress.getLocalHost（）.getHostAddress（）;
			// 或实现自定义逻辑以获取所需的名称

			// --- 设置 HOSTNAME 用于 Logback (保留原有逻辑) ---
			try {
				detectedHostname = InetAddress.getLocalHost().getHostName();
			} catch (Exception e) {
				System.err.println("Could not determine hostname for logging: " + e.getMessage());
			}
			LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
			loggerContext.putProperty("HOSTNAME", detectedHostname); // 设置属性

			// --- 检测合适的局域网 IP (优先 eth0) ---
			Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
			outerLoop: // Label for breaking outer loop
			while (networkInterfaces.hasMoreElements()) {
				NetworkInterface ni = networkInterfaces.nextElement();
				// 过滤掉不活跃、环回、虚拟接口
				if (!ni.isUp() || ni.isLoopback() || ni.isVirtual()) {
					continue;
				}

				String interfaceName = ni.getName();
				Enumeration<InetAddress> inetAddresses = ni.getInetAddresses();
				while (inetAddresses.hasMoreElements()) {
					InetAddress address = inetAddresses.nextElement();
					// 选择 IPv4 的站点本地地址 (如 10.x.x.x, 172.16.x.x-172.31.x.x, 192.168.x.x)
					if (address instanceof Inet4Address && address.isSiteLocalAddress()) {
						String currentIp = address.getHostAddress();
						if ("eth0".equals(interfaceName)) {
							eth0Ip = currentIp;
							System.out.println("[Network Init] Found prioritized IP on eth0: " + eth0Ip);
							break outerLoop; // Found eth0, priority met, exit both loops
						} else if (fallbackIp == null) {
							// Store the first valid site-local IP from non-eth0 interfaces as fallback
							fallbackIp = currentIp;
							System.out.println("[Network Init] Found potential fallback IP: " + fallbackIp
									+ " on interface " + interfaceName);
							// Don't break outer loop, continue searching for eth0
						}
						// Found an address for this interface, move to the next interface
						break; // Break inner loop (address loop)
					}
				}
			}

			// Determine final IP based on priority
			if (eth0Ip != null) {
				detectedIp = eth0Ip;
				System.out.println("[Network Init] Using prioritized IP from eth0: " + detectedIp);
			} else if (fallbackIp != null) {
				detectedIp = fallbackIp;
				System.out.println("[Network Init] Using fallback IP: " + detectedIp);
			} else {
				System.err.println(
						"[Network Init] Could not detect a suitable site-local IPv4 address on eth0 or other interfaces. Using default: "
								+ detectedIp);
			}

		} catch (Exception e) {
			System.err.println("[Network Init] Error detecting hostname or IP: " + e.getMessage());
			// 保留默认值
		} finally {
			// 确保设置系统属性，即使检测失败也使用默认值
			System.setProperty("instance.ip", detectedIp);
			System.out.println("[Network Init] System property 'instance.ip' set to: " + detectedIp);
		}
	}
}
