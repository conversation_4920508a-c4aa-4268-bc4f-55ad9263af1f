package com.rongchen.byh.app.job;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.app.dao.RepayScheduleMapper;
import com.rongchen.byh.app.entity.RepaySchedule;
import com.rongchen.byh.common.core.util.MDCUtil;
import com.rongchen.byh.common.rabbitmq.constants.QueueConstant;
import com.rongchen.byh.common.rabbitmq.dto.RepaySmsDto;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;


/**
 * 每日查询次日需要还款的账单，发送短信通知
 */
@Component
@Slf4j
public class RepaySmsJob {

    @Resource
    RepayScheduleMapper repayScheduleMapper;
    @Resource
    RabbitTemplate rabbitTemplate;

    @XxlJob("repaySmsHandler")
    public void repaySmsHandler() {

        String param = XxlJobHelper.getJobParam();
        int offset = Integer.parseInt(param);

        String traceId = UUID.fastUUID().toString();
        MDCUtil.setTraceId(traceId);

        DateTime date = DateUtil.date();
        String dateStr = DateUtil.offsetDay(date, offset).toDateStr();

        List<RepaySchedule> list = repayScheduleMapper.selectListRepayEq(dateStr);
        if (CollectionUtil.isEmpty(list)) {
            log.info("还款短信 提前 {} 天需还款账单为空",param);
            return;
        }
        list.forEach(li -> {
            String traceId1 = MDCUtil.generateTraceId();
            log.info("还款短信 提前 {} 天需还款账单 id:{} traceId:{}",param,li.getId(),traceId1);

            RepaySmsDto repayDto = new RepaySmsDto();
            repayDto.setDisburseId(li.getDisburseId());
            repayDto.setRepayId(li.getId());
            repayDto.setUserId(li.getUserId());
            repayDto.setTraceId(traceId1);

            rabbitTemplate.convertAndSend(QueueConstant.REPAY_SMS_QUEUE, JSONObject.toJSONString(repayDto));
        });

    }


    @XxlJob("todayRepaySmsHandler")
    public void todayRepaySmsHandler() {
        String traceId = UUID.fastUUID().toString();
        Map<String, String> contextMap = new HashMap<>();
        contextMap.put("traceId",traceId);
        MDC.setContextMap(contextMap);

        DateTime date = DateUtil.date();
        String dateStr = date.toDateStr();

        List<RepaySchedule> list = repayScheduleMapper.selectListRepayEq(dateStr);
        if (CollectionUtil.isEmpty(list)) {
            log.info("当日需要还款的账单为空");
            return;
        }
        list.forEach(li -> {

            log.info("当日需要还款的账单 id:{}",li.getId());

            RepaySmsDto repayDto = new RepaySmsDto();
            repayDto.setDisburseId(li.getDisburseId());
            repayDto.setRepayId(li.getId());
            repayDto.setUserId(li.getUserId());
            repayDto.setTraceId(traceId);

            rabbitTemplate.convertAndSend(QueueConstant.TODAY_REPAY_SMS_QUEUE, JSONObject.toJSONString(repayDto));
        });
    }


}
