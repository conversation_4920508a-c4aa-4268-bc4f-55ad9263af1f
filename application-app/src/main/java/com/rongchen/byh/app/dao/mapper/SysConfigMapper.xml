<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.dao.SysConfigMapper">

    <resultMap id="BaseResultMap" type="com.rongchen.byh.app.entity.SysConfig">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="configKey" column="config_key" jdbcType="VARCHAR"/>
            <result property="configValue" column="config_value" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,config_key,config_value,
        description,create_time,update_time
    </sql>
</mapper>
