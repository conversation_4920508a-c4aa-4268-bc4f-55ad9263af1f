package com.rongchen.byh.app.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.rongchen.byh.app.dao.DisburseDataMapper;
import com.rongchen.byh.app.dao.RepayScheduleMapper;
import com.rongchen.byh.app.dao.SaleScheduleMapper;
import com.rongchen.byh.app.dao.UserBankCardMapper;
import com.rongchen.byh.app.dao.UserDataMapper;
import com.rongchen.byh.app.dao.UserDetailMapper;
import com.rongchen.byh.app.dto.app.BackVo;
import com.rongchen.byh.app.entity.DisburseData;
import com.rongchen.byh.app.entity.RepaySchedule;
import com.rongchen.byh.app.entity.SaleSchedule;
import com.rongchen.byh.app.entity.UserData;
import com.rongchen.byh.app.entity.UserDetail;
import com.rongchen.byh.common.api.notice.dto.CurrentRepaymentDto;
import com.rongchen.byh.common.api.notice.dto.DeductionFailedDto;
import com.rongchen.byh.common.api.notice.dto.LoanNoticeDto;
import com.rongchen.byh.common.api.notice.dto.RepaymentSuccessDto;
import com.rongchen.byh.common.api.notice.dto.ThreeDayRepaymentDto;
import com.rongchen.byh.common.api.notice.service.NoticeSmsService;
import com.rongchen.byh.common.api.notice.service.impl.CurrentRepaymentService;
import com.rongchen.byh.common.api.notice.service.impl.DeductionFailedNoticeService;
import com.rongchen.byh.common.api.notice.service.impl.RepaymentSuccessService;
import com.rongchen.byh.common.api.notice.service.impl.ThreeDayRepaymentService;
import com.rongchen.byh.common.api.notice.vo.NoticeSmsVo;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class SmsLoanService {

    @Resource(name = "loanNoticeService")
    NoticeSmsService noticeSmsService;
    @Resource
    UserDetailMapper userDetailMapper;
    @Resource
    UserBankCardMapper userBankCardMapper;
    @Resource
    UserDataMapper userDataMapper;
    @Resource
    DisburseDataMapper disburseDataMapper;

    @Resource(name = "repaymentSuccessService")
    RepaymentSuccessService repaymentSuccessService;
    @Resource
    RepayScheduleMapper repayScheduleMapper;
    @Resource
    SaleScheduleMapper saleScheduleMapper;
    @Resource(name = "deductionFailedNoticeService")
    DeductionFailedNoticeService deductionFailedNoticeService;
    @Resource(name = "threeDayRepaymentService")
    ThreeDayRepaymentService threeDayRepaymentService;
    @Resource(name = "currentRepaymentService")
    CurrentRepaymentService currentRepaymentService;

    @Resource
    private RedissonClient redissonClient;

    @Value("${qyc.sms.daily-limit-per-phone:3}")
    private int dailySmsLimitPerPhone;

    /**
     * 检查指定手机号的短信发送次数是否已达每日上限，并递增计数器。
     * 
     * @param phone 手机号码
     * @return true 如果已超限，false 如果未超限
     */
    private boolean isSmsLimitExceeded(String phone) {
        if (StrUtil.isBlank(phone)) {
            log.error("短信发送限制检查：手机号为空，跳过检查。phone:{}", phone);
            return false;
        }
        if (dailySmsLimitPerPhone <= 0) {
            log.error("短信每日发送限制配置为 {}，限制未启用。", dailySmsLimitPerPhone);
            //不能影响业务，返回true，继续发送短信
            return false;
        }

        String key = "sms:limit:daily:" + phone;
        RAtomicLong atomicLong = redissonClient.getAtomicLong(key);

        long count = 0;
        try {
            count = atomicLong.incrementAndGet();

            if (count == 1) {
                DateTime endOfDay = DateUtil.endOfDay(DateUtil.date());
                long expireSeconds = DateUtil.between(DateUtil.date(), endOfDay, cn.hutool.core.date.DateUnit.SECOND);
                if (expireSeconds > 0) {
                    atomicLong.expire(expireSeconds, TimeUnit.SECONDS);
                    log.info("首次设置手机号 {} 的短信日限流 key: {}, 过期时间: {} 秒", phone, key, expireSeconds);
                } else {
                    log.warn("计算手机号 {} 的短信日限流 key: {} 过期时间异常， expireSeconds: {}。设置为360秒过期。", phone, key, expireSeconds);
                    atomicLong.expire(360, TimeUnit.SECONDS);
                }
            }
        } finally {
        }

        boolean exceeded = count > dailySmsLimitPerPhone;
        if (exceeded) {
            log.warn("手机号 {} 今日短信发送次数已达上限 {} (尝试发送第 {} 次)", phone, dailySmsLimitPerPhone, count);
        } else {
            log.info("手机号 {} 今日已发送短信 {} 次，上限 {}", phone, count, dailySmsLimitPerPhone);
        }

        return exceeded;
    }

    /**
     * 发送下款成功通知短信
     * 
     * @param loanNo
     */
    public ResponseResult<Void> send(String loanNo) {
        DisburseData disburseData = disburseDataMapper.selectByLoanNo(loanNo);

        Long userId = disburseData.getUserId();
        UserData userData = userDataMapper.selectById(userId);
        String phone = userData.getMobile();

        if (isSmsLimitExceeded(phone)) {
            log.warn("短信发送限制检查：手机号 {} 今日短信发送次数已达上限 {} ", phone, dailySmsLimitPerPhone);
            return ResponseResult.error(ErrorCodeEnum.FAIL, "您今日的短信接收次数已达上限");
        }

        UserDetail userDetail = userDetailMapper.queryByUserId(userId);
        LoanNoticeDto noticeDto = new LoanNoticeDto();
        noticeDto.setUserName(userDetail.getAppName());

        String contractNo = StrUtil.sub(loanNo, loanNo.length(), -4);
        noticeDto.setContractNum(contractNo);
        BackVo backVo = userBankCardMapper.queryBackByUserId(userId);
        String bankAccount = backVo.getBankAccount();
        String bankAccountNo = StrUtil.sub(bankAccount, bankAccount.length(), -4);
        noticeDto.setBankAccount(bankAccountNo);
        noticeDto.setLoanAmount(disburseData.getCreditAmount().toString());
        noticeDto.setLoanTerm(disburseData.getPeriods() + "");
        DateTime date = DateUtil.date();
        int day = DateUtil.dayOfMonth(date);
        noticeDto.setRepaymentDate(day + "");
        noticeDto.setPhone(phone);

        NoticeSmsVo noticeSmsVo = noticeSmsService.sendSms(noticeDto);
        if (noticeSmsVo.getCode().equals(0)) {
            return ResponseResult.success();
        }
        return ResponseResult.error(ErrorCodeEnum.FAIL, noticeSmsVo.getMsg());
    }

    /**
     * 发送还款扣款短信
     * 
     * @param disburseId
     * @param term
     * @return
     */
    public ResponseResult<Void> sendRepaySms(Long disburseId, String term) {
        DisburseData disburseData = disburseDataMapper.selectById(disburseId);
        UserData userData = userDataMapper.selectById(disburseData.getUserId());
        String phone = userData.getMobile();

        if (isSmsLimitExceeded(phone)) {
            log.warn("短信发送限制检查：手机号 {} 今日短信发送次数已达上限 {} ", phone, dailySmsLimitPerPhone);
            return ResponseResult.error(ErrorCodeEnum.FAIL, "您今日的短信接收次数已达上限");
        }

        // 查询还款账单
        RepaySchedule repaySchedule = repayScheduleMapper.selectByDisburseIdAndTerm(disburseId, term);
        RepaymentSuccessDto dto = new RepaymentSuccessDto();
        String loanNo = disburseData.getLoanNo();
        String contractNo = StrUtil.sub(loanNo, loanNo.length(), -4);
        dto.setContractNum(contractNo);
        // 2024年12月12日
        String datePay = repaySchedule.getDatePay();
        String[] split = datePay.split("-");
        dto.setRepaymentDate(split[0] + "年" + split[1] + "月" + split[2] + "日");
        BigDecimal amt = repaySchedule.getTotalAmt();
        if (Integer.parseInt(term) <= 3) {
            SaleSchedule saleSchedule = saleScheduleMapper.selectByDisburseIdAndTerm(disburseId, term);
            amt = amt.add(saleSchedule.getTotalAmt());
        }
        dto.setRepaymentAmount(amt.toString());

        dto.setPhone(phone);

        NoticeSmsVo noticeSmsVo = repaymentSuccessService.sendSms(dto);
        if (noticeSmsVo.getCode().equals(0)) {
            return ResponseResult.success();
        }
        return ResponseResult.error(ErrorCodeEnum.FAIL, noticeSmsVo.getMsg());
    }

    /**
     * 还款失败发送短信
     * 
     * @param disburseId
     * @param id
     * @param userId
     * @param type       1-本金还款失败 2-赊销还款失败
     * @return
     */
    public ResponseResult<Void> sendRepayFailSms(Long disburseId, Long id, Long userId, int type) {
        UserData userData = userDataMapper.selectById(userId);
        String phone = userData.getMobile();

        if (isSmsLimitExceeded(phone)) {
            log.warn("短信发送限制检查：手机号 {} 今日短信发送次数已达上限 {} ", phone, dailySmsLimitPerPhone);
            return ResponseResult.error(ErrorCodeEnum.FAIL, "您今日的短信接收次数已达上限");
        }

        UserDetail userDetail = userDetailMapper.queryByUserId(userId);
        DisburseData disburseData = disburseDataMapper.selectById(disburseId);
        RepaySchedule repaySchedule = repayScheduleMapper.selectById(id);

        DeductionFailedDto dto = new DeductionFailedDto();
        dto.setUserName(userDetail.getWebName());
        String loanNo = disburseData.getLoanNo();
        String contractNo = StrUtil.sub(loanNo, loanNo.length(), -4);
        dto.setContractNum(contractNo);
        String repayOwneDate = repaySchedule.getRepayOwneDate();
        DateTime parse = DateUtil.parse(repayOwneDate);
        dto.setDeductionDate(DateUtil.format(parse, "yyyy年MM月dd日"));
        String term = repaySchedule.getRepayTerm();
        BigDecimal amt = repaySchedule.getTotalAmt();
        SaleSchedule saleSchedule = null;
        if (Integer.parseInt(term) <= 3) {
            saleSchedule = saleScheduleMapper.selectByDisburseIdAndTerm(disburseId, term);
            amt = amt.add(saleSchedule.getTotalAmt());
        }
        dto.setPeriodAmount(amt.toString());
        if (type == 1) {
            dto.setRemainAmount(repaySchedule.getTotalAmt().toString());
        } else if (type == 2) {
            if (saleSchedule != null) {
                dto.setRemainAmount(saleSchedule.getTotalAmt().toString());
            } else {
                return ResponseResult.error(ErrorCodeEnum.FAIL, "赊销账单不存在");
            }
        }

        dto.setPhone(phone);
        deductionFailedNoticeService.sendSms(dto);
        return ResponseResult.success();
    }

    /**
     * 到期还款提醒发送短信
     * 
     * @param disburseId
     * @param userId
     * @return
     */
    public ResponseResult<Void> sendNeedRepaySms(Long disburseId, Long id, Long userId) {
        UserData userData = userDataMapper.selectById(userId);
        String phone = userData.getMobile();

        if (isSmsLimitExceeded(phone)) {
            log.warn("短信发送限制检查：手机号 {} 今日短信发送次数已达上限 {} ", phone, dailySmsLimitPerPhone);
            return ResponseResult.error(ErrorCodeEnum.FAIL, "您今日的短信接收次数已达上限");
        }

        UserDetail userDetail = userDetailMapper.queryByUserId(userId);
        // 客户名称
        String webName = userDetail.getWebName();

        RepaySchedule repaySchedule = repayScheduleMapper.selectById(id);
        String repayOwneDate = repaySchedule.getRepayOwneDate();
        DateTime parse = DateUtil.parse(repayOwneDate);
        // 还款日
        String format = DateUtil.format(parse, "yyyy年MM月dd日");

        BigDecimal amt = repaySchedule.getTotalAmt();
        String term = repaySchedule.getRepayTerm();
        SaleSchedule saleSchedule = null;
        if (Integer.parseInt(term) <= 3) {
            saleSchedule = saleScheduleMapper.selectByDisburseIdAndTerm(disburseId, term);
            amt = amt.add(saleSchedule.getTotalAmt());
        }

        BackVo backVo = userBankCardMapper.queryBackByUserId(userId);
        String bankAccount = backVo.getBankAccount();
        String bankAccountNo = StrUtil.sub(bankAccount, bankAccount.length(), -4);
        ThreeDayRepaymentDto dto = new ThreeDayRepaymentDto();
        dto.setUserName(webName);
        dto.setPeriodAmount(amt.toString());
        dto.setDeductionDate(format);
        dto.setBankAccount(bankAccountNo);
        dto.setPhone(phone);

        threeDayRepaymentService.sendSms(dto);

        return ResponseResult.success();
    }

    /**
     * 到期还款提醒发送短信
     * 
     * @param disburseId
     * @param userId
     * @return
     */
    public ResponseResult<Void> sendTodayNeedRepaySms(Long disburseId, Long id, Long userId) {
        UserData userData = userDataMapper.selectById(userId);
        String phone = userData.getMobile();

        if (isSmsLimitExceeded(phone)) {
            log.warn("短信发送限制检查：手机号 {} 今日短信发送次数已达上限 {} ", phone, dailySmsLimitPerPhone);
            return ResponseResult.error(ErrorCodeEnum.FAIL, "您今日的短信接收次数已达上限");
        }

        UserDetail userDetail = userDetailMapper.queryByUserId(userId);
        // 客户名称
        String webName = userDetail.getWebName();

        RepaySchedule repaySchedule = repayScheduleMapper.selectById(id);
        String repayOwneDate = repaySchedule.getRepayOwneDate();
        DateTime parse = DateUtil.parse(repayOwneDate);
        // 还款日
        String format = DateUtil.format(parse, "yyyy年MM月dd日");

        BigDecimal amt = repaySchedule.getTotalAmt();
        String term = repaySchedule.getRepayTerm();
        SaleSchedule saleSchedule = null;
        if (Integer.parseInt(term) <= 3) {
            saleSchedule = saleScheduleMapper.selectByDisburseIdAndTerm(disburseId, term);
            amt = amt.add(saleSchedule.getTotalAmt());
        }

        BackVo backVo = userBankCardMapper.queryBackByUserId(userId);
        String bankAccount = backVo.getBankAccount();
        String bankAccountNo = StrUtil.sub(bankAccount, bankAccount.length(), -4);
        CurrentRepaymentDto dto = new CurrentRepaymentDto();
        dto.setUserName(webName);
        dto.setPeriodAmount(amt.toString());
        dto.setDeductionDate(format);
        dto.setBankAccount(bankAccountNo);
        dto.setPhone(phone);

        currentRepaymentService.sendSms(dto);

        return ResponseResult.success();
    }
}
