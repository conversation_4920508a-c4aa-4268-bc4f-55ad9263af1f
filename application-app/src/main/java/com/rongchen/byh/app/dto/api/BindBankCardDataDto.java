package com.rongchen.byh.app.dto.api;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/12/11 19:50:54
 */
@Data
public class BindBankCardDataDto {
    @Schema(description = "服务合同号")
    private String ContractNum;

    @Schema(description = "客户姓名")
    private String CustomerName;

    @Schema(description = "客户身份证号码")
    private String IDCard;

    @Schema(description = "银行卡列表")
    private List<BindBankCardInfoDto> BankList;

    @Schema(description = "合同录入时服务费收取方式/比例、固定")
    private String ConsultingMode;

    @Schema(description = "合同录入时的服务费收取比例/金额")
    private String ConsultingRate;
}
