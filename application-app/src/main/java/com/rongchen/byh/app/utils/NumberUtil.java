package com.rongchen.byh.app.utils;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

@Slf4j
public class NumberUtil {

    public static BigDecimal safeParseBigDecimal(String value) {
        if (StrUtil.isBlank(value)) {
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(value);
        } catch (NumberFormatException e) {
            log.warn("解析 BigDecimal 失败: '{}', 返回 ZERO。", value, e);
            return BigDecimal.ZERO;
        }
    }
}
