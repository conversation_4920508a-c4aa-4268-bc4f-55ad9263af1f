package com.rongchen.byh.app.api.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.app.api.service.strategy.process.OutApiAbstractProcess;
import com.rongchen.byh.app.api.service.strategy.factory.OutApiFactory;
import com.rongchen.byh.common.core.annotation.DisableDataFilter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description api授信结果相关接口
 * @date 2025/3/20 11:15:33
 */
@Tag(name = "api授信结果相关接口")
@DisableDataFilter
@Slf4j
@RestController
@RequestMapping("/outApi")
@SaIgnore
public class ApiCreditController {
    @Resource
    private OutApiFactory outApiFactory;
    /**
     * 进件接收接口
     */
    @PostMapping("/{channel}/credit")
    public JSONObject creditNotice(@PathVariable("channel") String channel, @RequestBody String request) {
        try {
            log.info("api授信通知渠道：{}，请求参数：{}", channel, request);
            JSONObject bizMap = JSONObject.parseObject(request);
            OutApiAbstractProcess outApiAbstractProcess = outApiFactory.getOutApiAbstractProcess(channel);
            if (ObjectUtil.isEmpty(outApiAbstractProcess)) {
                log.error("未找到对应的渠道");
                JSONObject jsonObject = new JSONObject();
                return jsonObject;
            }
            return outApiAbstractProcess.credit(bizMap);
        } catch (Exception e) {
            log.error("【api授信解耦】 失败：{}", e);
        }
        return new JSONObject();
    }


    /**
     * 授信查询接口
     * @param channel
     * @param request
     * @return
     */
    @PostMapping("/{channel}/creditQuery")
    public JSONObject creditQuery(@PathVariable("channel") String channel, @RequestBody String request) {
        try {
            log.info("api授信查询 渠道：{}，请求参数：{}", channel, request);
            JSONObject bizMap = JSONObject.parseObject(request);
            OutApiAbstractProcess outApiAbstractProcess = outApiFactory.getOutApiAbstractProcess(channel);
            if (ObjectUtil.isEmpty(outApiAbstractProcess)) {
                log.info("api授信查询 未找到对应的渠道 渠道：{}",channel);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("responseCode","9999");
                jsonObject.put("responseMsg","未找到对应的渠道");
                return jsonObject;
            }
            return outApiAbstractProcess.creditQueryResult(bizMap);
        } catch (Exception e) {
            log.error("【授信查询接口】 异常 渠道：{}",channel, e);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("responseCode","9999");
            jsonObject.put("responseMsg","接口异常");
            return jsonObject;
        }
    }
}
