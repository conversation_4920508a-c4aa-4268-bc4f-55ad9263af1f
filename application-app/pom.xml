<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.rongchen.byh</groupId>
        <artifactId>byh</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>application-app</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <itextpdf.version>7.0.3</itextpdf.version>
        <logback.version>1.3.14</logback.version>
    </properties>

    <dependencies>
        <!-- 业务组件依赖 -->
        <dependency>
            <groupId>com.rongchen.byh</groupId>
            <artifactId>common-api</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.rongchen.byh</groupId>
            <artifactId>common-satoken</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.rongchen.byh</groupId>
            <artifactId>common-ext</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.rongchen.byh</groupId>
            <artifactId>common-redis</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.rongchen.byh</groupId>
            <artifactId>common-log</artifactId>
            <version>1.0.0</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.rongchen.byh</groupId>-->
        <!--            <artifactId>common-minio</artifactId>-->
        <!--            <version>1.0.0</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.rongchen.byh</groupId>
            <artifactId>common-qcloud</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.rongchen.byh</groupId>
            <artifactId>common-sequence</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.rongchen.byh</groupId>
            <artifactId>common-swagger</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.rongchen.byh</groupId>
            <artifactId>common-dict</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.rongchen.byh</groupId>
            <artifactId>common-pojo</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.rongchen.byh</groupId>
            <artifactId>common-rabbitmq</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.rongchen.byh</groupId>
            <artifactId>common-xxljob</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.ancun.netsign</groupId>
            <artifactId>netsign-sdk</artifactId>
            <version>2.5.83.mix</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/libs/netsign-sdk-2.5.83.mix.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.13</version>
        </dependency>

        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>kernel</artifactId>
            <version>${itextpdf.version}</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>io</artifactId>
            <version>${itextpdf.version}</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>layout</artifactId>
            <version>${itextpdf.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.github.loki4j/loki-logback-appender-jdk8 -->
        <dependency>
            <groupId>com.github.loki4j</groupId>
            <artifactId>loki-logback-appender-jdk8</artifactId>
            <version>1.5.2</version>
        </dependency>

        <!-- Add loki-protobuf for Protobuf encoding support -->
        <dependency>
            <groupId>com.github.loki4j</groupId>
            <artifactId>loki-protobuf</artifactId>
            <version>0.0.1_pb3.24.0</version>
        </dependency>

        <!-- Required for LokiApacheHttpAppender (now managed transitively) -->
        <!--     <dependency>
            <artifactId>httpclient</artifactId>
            <groupId>org.apache.httpcomponents</groupId>
            <version>4.5.13</version>
        </dependency>-->

        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-config-spring-boot-starter</artifactId>
            <version>0.2.11</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>