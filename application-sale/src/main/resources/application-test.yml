spring:
  redis:
    host: 127.0.0.1
    port: 6379
  rabbitmq:
    host: 127.0.0.1
    port: 5672
    virtual-host: /
    username: guest
    password: guest
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      # 数据库链接 [主数据源]
      main:
        url: *******************************************************************************************************************************
        username: qyc12_user
        password: "@Qyc_168168"
      driverClassName: com.mysql.cj.jdbc.Driver
      name: application-app
      initialSize: 10
      minIdle: 10
      maxActive: 50
      maxWait: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
      maxOpenPreparedStatements: 20
      validationQuery: SELECT 'x'
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      filters: stat,wall
      useGlobalDataSourceStat: true
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*,/actuator/*"
      stat-view-servlet:
        enabled: true
        urlPattern: /druid/*
        resetEnable: true

application:
  # 初始化密码。
  defaultUserPassword: 123456
  # 缺省的文件上传根目录。
  uploadFileBaseDir: ./zz-resource/upload-files/app
  # 跨域的IP(http://*************:8086)白名单列表，多个IP之间逗号分隔(* 表示全部信任，空白表示禁用跨域信任)。
  credentialIpList: "*"
  # Session的用户和数据权限在Redis中的过期时间(秒)。一定要和sa-token.timeout
  sessionExpiredSeconds: 86400
  # 是否排他登录。
  excludeLogin: false

# 这里仅仅是一个第三方配置的示例，如果没有接入斯三方系统，
# 这里的配置项也不会影响到系统的行为，如果觉得多余，也可以手动删除。
common-ext:
  urlPrefix: /admin/commonext
  # 这里可以配置多个第三方应用，这里的应用数量，通常会和上面third-party.auth的配置数量一致。
  apps:
    # 应用唯一编码，尽量不要使用中文。
  - appCode: orange-forms-default
    # 业务组件的数据源配置。
    bizWidgetDatasources:
    # 组件的类型，多个类型之间可以逗号分隔。
    - types: upms_user,upms_dept
      # 组件获取列表数据的接口地址。
      listUrl: http://localhost:8083/orangePlugin/listBizWidgetData
      # 组件获取详情数据的接口地址。
      viewUrl: http://localhost:8083/orangePlugin/viewBizWidgetData

common-sequence:
  # Snowflake 分布式Id生成算法所需的WorkNode参数值。
  snowflakeWorkNode: 1

# 存储session数据的Redis，所有服务均需要，因此放到公共配置中。
# 根据实际情况，该Redis也可以用于存储其他数据。
common-redis:
  # redisson的配置。每个服务可以自己的配置文件中覆盖此选项。
  redisson:
    # 如果该值为false，系统将不会创建RedissionClient的bean。
    enabled: true
    # mode的可用值为，single/cluster/sentinel/master-slave
    mode: single
    # single: 单机模式
    #   address: redis://localhost:6379
    # cluster: 集群模式
    #   每个节点逗号分隔，同时每个节点前必须以redis://开头。
    #   address: redis://localhost:6379,redis://localhost:6378,...
    # sentinel:
    #   每个节点逗号分隔，同时每个节点前必须以redis://开头。
    #   address: redis://localhost:6379,redis://localhost:6378,...
    # master-slave:
    #   每个节点逗号分隔，第一个为主节点，其余为从节点。同时每个节点前必须以redis://开头。
    #   address: redis://localhost:6379,redis://localhost:6378,...
    address: redis://127.0.0.1:6379
    # 链接超时，单位毫秒。
    timeout: 6000
    # 单位毫秒。分布式锁的超时检测时长。
    # 如果一次锁内操作超该毫秒数，或在释放锁之前异常退出，Redis会在该时长之后主动删除该锁使用的key。
    lockWatchdogTimeout: 60000
    # redis 密码，空可以不填。
    password:
    pool:
      # 连接池数量。
      poolSize: 20
      # 连接池中最小空闲数量。
      minIdle: 5

minio:
  enabled: true
  endpoint: http://127.0.0.1:19000
  accessKey: admin
  secretKey: admin123456
  bucketName: application

sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: Authorization
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: ${application.sessionExpiredSeconds}
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: false
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: random-64
  # 是否输出操作日志
  is-log: true
  # 配置 Sa-Token 单独使用的 Redis 连接
  # 修改这里Sa-Token的Redis配置，同时也要修改当前配置文件顶部spring.data.redis的相关配置。
  alone-redis:
    # Redis数据库索引（默认为0）
    database: 8
    # Redis服务器地址
    host: 127.0.0.1
    # Redis服务器连接端口
    port: 6379
    # Redis服务器连接密码（默认为空）
    password:
    # 连接超时时间
    timeout: 10s
  is-read-header: true
  is-read-cookie: false

# 极光验证码短信参数配置
jiguang:
  appKey: bd0d8e9548fb9a380c636bb9
  masterSecret: e3f106f1204fcdb8a6600981
  tempId: 1
  signId: 28411

# 腾讯
tencent:
  secretId: AKIDizGbuOm2FRYebRVPiFybJBJPTUJvQp9y
  secretKey: oXAd1PvLrHaoy0xqfLqHdpu03ISSFrIY
  map:
    ipCityUrl: https://apis.map.qq.com/ws/location/v1/ip
    key: RPTBZ-ZP4E7-W2BX7-PXXB2-4QT3V-D6F5E


qcloud:
  cos:
    enabled: true
    expireSeconds: 1000
    # 下面几项均需在申请腾讯云COS后，根据自己的实际情况进行配置。
    accessKey: AKIDizGbuOm2FRYebRVPiFybJBJPTUJvQp9y
    secretKey: oXAd1PvLrHaoy0xqfLqHdpu03ISSFrIY
    bucketName: 12qyc-1328155466
    region: ap-guangzhou



# 资方api相关参数
zifang:
  # 请求域名
  host: https://qktest.lvxtech.com
  # 请求方
  reqSysCode: MYZH
  # 渠道编码
  channelCode: myzh
  # 产品编码
  productCode: MYZH
  # 资金方编码
  fundCode: MYZH
  # 我方私钥
  privateKey: MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAM2QqFhe7Fx6hsJOdUEoNpRx0PSrNzCugaU9D+gREnmhMOxN5GIUy7krOmI4IrYk3obj9u3NimWgQVFFVXlLMgUQF8rxlo+0bRem6sD7d08+FehSz1WBc3W7JzKgfwBpTgzozE9jcpeWebCwsaxdt7RDL8S2g4jhfFmwtfZzMCXvAgMBAAECgYA1P1NcELqI+fQOQOR+jDwXF6RYvED/izRp6PdF1aZDe02A6GxQO7LJtDjbWE8lf0dlCYfc8XD4AhrpQlxxntjFoAs4ozgv9WQprzNgwFPO9OASBYv6jkJfqPgvsD/s9ILW2pZ7+0qv1wdq6TWZ0tDn5iEXd0GvR+jZshlXxlSHgQJBAO1w9CixEhJMK01g+wkaQvPJhjYZMJFlJEB6G4uoFep4u2Nq3hdA16V1Q6SPb+fg59NdDJEqHDzUeF5x5bJCyxsCQQDdoeHoQCovsumlGosEodL5uZR0kXfIxfcRmag0scz9/ex2dhf/H/fkmjuTPmuf5s2JgTPMLadfC0OZZe/xuMm9AkABBY1nZ2umfIFrMdGitmQ3XFShgvY8iezgAiaAMVb2zVIxGVKfUv4ajiieWqemncchl7LoJQA+GsPx1Bei7rqlAkEA29tesnoUX0ToKfRuu6WcGEZUUl6LqQtY0IjRJ5TJeZyPGeSEYX1g99FYD/T35qOaeTFGJOGVCt+tq1pH8E4TMQJBAJNUEKceFOilqR6PBqNqpZe29Rxr6qSiAYv1dUWn0En/rTXBFn4f9ZGxl5V1q53m3Fjf5Q5eD0rsMUlOPUtg05M=
  # 我方公钥
  publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDNkKhYXuxceobCTnVBKDaUcdD0qzcwroGlPQ/oERJ5oTDsTeRiFMu5KzpiOCK2JN6G4/btzYploEFRRVV5SzIFEBfK8ZaPtG0XpurA+3dPPhXoUs9VgXN1uycyoH8AaU4M6MxPY3KXlnmwsLGsXbe0Qy/EtoOI4XxZsLX2czAl7wIDAQAB
  # 资方公钥
  outPublicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCGhh2/9k4hrXuAGrgdkBsAk3T9aylHwR7rrwo4hCmzoxpylq0Wl+mZTnLsuyV4v6Cq5ym5vVdBjM6XSEhh+7rcEVoQLSjOrZ0436ub/2ekcl+rjY6d1d2CNyFYQ2J9Dct0p1J8eh2OwwKE/86ppfO9uKqG2g6WE/8311CGwF1UAQIDAQAB

risk:
  control:
    # 预授信请求域名
    preLoanAuditUrl: http://**************:1112/x_engine/credit
    # 预授信aesKey
    aesKey: EtYaUhSYnltLMw9G
    # 预授信rsa私钥
    rsaPrivateKey: MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAL2kBdHvba6+ZEMd6CeJfxerjgD5MWGR4xwaSpTDNmhcwE6nYCKNHddZpu51xjQjaacpr1ixkItwpQbXyB+kTathiPR6H14JFWP9HUmmLmR8eLfCEFFo1W+RSHXR6QT3C391dHK5G5LzuznP9c2phBdLx0PnOP6+WthAFt/DNIelAgMBAAECgYEAs4WKXOojEsD5eO/ezU7EGUw16YX80TihngDliV4jKzhidBLOVubv0OT1udeUAddkNPKpI3U0OEwybP/oWsvXGTlIcKRO1YUoRjoEb+6d0ZuJLI9mojyYttS8N1KyfUJx7I/hw18n6Xbla6NwO0kJqFlBAUDElkfWjBJ0bKwdngECQQD7GGOVps5DK1jwoxA5/G1gXpwb0BZ6/EN03QwnIR2FmdjyasiUvBHGtkx5OGv6kS/oKAqkk/ps5bi0B4VB1FTVAkEAwVhT97fvY67GRwcj2pfWD2oPZmT/ZaIHpPktl3ZEpU66m6u8KYrP+loeMQZ5Ha4i5Vl2DygKkm+pCJu4QRgPkQJBAJV+2NOhw45UQZjLzP5pHwnQamtYwfmpNdRfQzwMyFHh3ju+ffun2YGQygkmIYvGY1p6dJO2EtRBFW4CSEGAVJ0CQQCfQkGQGBuBMbKzSQdYhJ9XfRaIUoMpVUkUtAfA7jNnMy11EwXf9i6QWnDqExnqv8iQwJsFqwbiTkWfCJ3CSK5xAkBdqSIYELl9/lzHzbBDnclwnDYoo/Wxy4UzI+oHPTyqeFPaVsnFOaZstveY61TY3COnXt9KP2tgHDX70zfjDbAU
    # 预授信渠道来源id
    sourceId: 12qzf_grxfxyd_0001
    # 预授信渠道来源
    channel: 3
    # 预授信产品编码
    productCode: 3
    # crm推送地址
    crmPushUrl: http://gp.jimwitech.cn/Admin/UserIncomeApi/addUserAes
    # crm推送渠道
    crmPushSource: ds2
    # crm推送密钥
    crmPushSecret: jwkjcy12345678dt
    # 预授信aesKey
    appAesKey: ipXXXJPDL7Jk89wv
    # 预授信rsa私钥
    appRsaPrivateKey: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBALYgBQ7bEg81mIWGwgF/AfjnBfkl1uXliNxBAyJVMKvDp+AitWoR+49Vn8qWFBAzuW3a5yE7uP8GW0GMHrOu/tLbpg0ipJh0FTtEjENn+mFsfEoQHKsFP1xf9TNLd3m7OecCUoTinkZAnTFQIjavlbF/KR6+T88sZKvEBMiwwoXlAgMBAAECgYBNJk8+Ncr2qmuL1MQGQjkhqSu8mSzpgkxxkRC00IWnnWBV8B4NasS3uTvRY7XoDyEzyhEy4MvDLHwnziWLVEwZaqL9XET+cOtHeHLemeACzmKA9CkiasFWmgK9ABofMY308yFA6gcGQ+JwIfhdl8dcFAn1kKbZtDd6ijVzW+gvWQJBAO5D+SZJlot11R77pgboQOl0vKN6VhKaNcRI5Kum5tUc5oLB419muS7k0UqBC4kBrC7nZbxssOwCJoN/povnFY8CQQDDrlG87bX2n4GOBRsv15pNYv8Pjdr4ZsQOuSuvvRrljKXmGm7D+BZ5yNzMzb+AKbg6wKEGyf8JeH85Zr6BVPtLAkEAjGJ0bECzeNwmhCjVfABgLq1fcBrml/NQdqRUR2cBXtO2ZZiDtXQ67AxZ3EIIX+MiZkhkww5vd78UnioaIRg4tQJAUSFhOlfEbfBMIrnzP67Ahv1YivZhp0PUXdZgSoi5MhtSXUjwS8f8aZniEffsQhMKEzHykYIGf8K53O7AZZb6aQJAIex4xqMipFWjsGxWdpbvWDf+o3enui/MoqB4XQMnnbI6MyCexHn5oVR182iSCNBMK9UwMV6dklq6SflCmTo4vA==
    # 预授信渠道来源id
    appSourceId: 12qzf_grxfxyd_0002
    # 预授信渠道来源
    appChannel: 4
    # 预授信产品编码
    appProductCode: 4



love-sign:
  baseUrl: https://prev.asign.cn/
  appId: 950964741
  rsaPubKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyzGgsXhA4ZjH3DuTl/mSBsHKQEnm/ukAo77Xd9MFzdQUKpxIWFw4MpyDUagxFBoZDZEsKzrMUAZL6IpQLOxaFT6rxC+MCzR0aE8nQJwgkC+Ad3iaHsgSMoIJo9Thpd4r9g8fonCipveA5ejgjmq49gUk0cQvpnjJpWh+UuaP8CiIdGU/wwvmMihgKUAhvrLIpHi096JLGDc3v5TUrqC6E40rjR9mRLDxXI8YIUz5RKGiCrQOAB/htzVhRHs+b9uWaibDuRd5/hyL9JzTmlN5joMdzfspGwBiEcu9JmNYlP6YTL3U2fgrScvPY3x9NIijal7ocIv5CuG812zQEzhf3QIDAQAB
  rsaPriKey: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDLMaCxeEDhmMfcO5OX+ZIGwcpASeb+6QCjvtd30wXN1BQqnEhYXDgynINRqDEUGhkNkSwrOsxQBkvoilAs7FoVPqvEL4wLNHRoTydAnCCQL4B3eJoeyBIyggmj1OGl3iv2Dx+icKKm94Dl6OCOarj2BSTRxC+meMmlaH5S5o/wKIh0ZT/DC+YyKGApQCG+ssikeLT3oksYNze/lNSuoLoTjSuNH2ZEsPFcjxghTPlEoaIKtA4AH+G3NWFEez5v25ZqJsO5F3n+HIv0nNOaU3mOgx3N+ykbAGIRy70mY1iU/phMvdTZ+CtJy89jfH00iKNqXuhwi/kK4bzXbNATOF/dAgMBAAECggEAIcUhu90Mi3Hk5YlRQlTMHCtg/cZKidsXrB8vmE6ODGNO5rYKoATTojW47X1SitC7kTGrcZPYGQSy4Pb71DuDzTwZOAXe9I3HOv7Mu0Q3dT8vzqtQkgUWYSaTi5L53aNLEx34V/r4TV+dIJjPmVvLWbgJIoPEl5pDiSYQuJ5xFayamT+kKGZpWyBvg4PUmoXq710AqRtrC4FcZvlZuiXZEZddnAE0l5YGRpyTvPFUnx6E2F95QRsl0yphf531VA+uUEN9ct1Ej/XXxRrzL6/emwlO8MOp2sbHL66Q2lqFgAzYBIvkOYqVcnyQZmUUWmgAhCis9BZX8lhjHK/A5o0GgQKBgQD+6VVcXQj37VNumkV+XnKzDvS5SMWLcvE/xRw2V1miqjBWz1T2LaXyg4L3TnnLmGJAmYxCW/PHvLYkVUUX6eCHhd/lvTVfHG6LRJoCsqFRKipJw4iAhcQJQugCihn88KyxuVaJLBnto+EygXk4qbcuDzIUTCE3QatoTFRPixKwhQKBgQDMD8HS2d7JwVuykgC/DgXa+7XPWleG+wEnEk88Y+Kz9T7D7XvUAhxAFB13L8eT8w9vMbBD9y1ZSh878oPIpmRMSLxAFENUM4Ji9PQMir0Cb6F0YUFtfB5/k/7i6744g6RZbZyta4W0FJ4clApZJLXwCmOc2BgeL33XMo+sqgp9eQKBgEHK0YhUTuNGstkCIluaXvpOKuXDnZD/arrt0flIlE8CpBVTz/b2EzXDscLI03Cz9O/9cZETEJmF4HECPSIN8DCqYf521lalDPcPDr0Rg8diHaYKPKoHXHCTfzRglpKNP9VjI62l309Rk+coObRhQXE25NMOTyhm36m8waoCGCHNAoGBAKUqV9JG//lDJOo7HJmHSxv8wiKL1+5r+XnbawDRRCwuY1gIP0LRVnZTEapnpN1OTX7NtAK+7RipUvCdkSb17paEBMLE494iNvXLmEEShnuTSRY+pcttO+49JoAbGYL6oiqz+q2vKrAgQ/2ToXCXsx8P+aJ4kcTU4cEtrnatxwHZAoGAH6sGcC91u/C7diPqWH8eCbye2DAfOetB/mlQrlY4I7y/Ud/ifhEvSnt2ygd+FNfN9yZdLfHq4yhrLvMfk9oo2qp8Jjh7kiGfdQQTmMLJqJ7pFZRuegiKhQgskZscFijTHkfNef5W1tt7TV6w+9ccRq0b7qKYwzdNR33MlLBjoCg=
  # 个人信息共享授权书
  person: TNEB76AF365D2C47A99595BABAE3DD2021
  # 人脸识别授权书
  face: TNBA90311CBA954E5D8433602B867EAD03
  # 4-电子签授权书
  sign: TN7B95D5DF5BB646AFBCE823A18D8A84E3
  companyAccount: ***********

notice:
  sms:
    noticeSmsUrl: http://api.1cloudsp.com/api/v2/send
    accesskey: v7okX7lilzgbCGrB
    secret: YxkL45sGTiEEJbEoTyjKkvCjikFdfGlp
    sign: 【七叶草】

daikou:
  ua: **********
  uaKey: 910000$BAA42DA9-15E6-4FE7-ACA8-7CD66ED8A1DF$**********
  url: https://testexopenapi.hefu-tech.cn/loan/sendloan
  creditOrgId: 9B1189D5-A03A-40A9-9264-87097832EAE8
  productId: AC5B4ABE-CDBB-4840-875C-46BBB7541F22


xxljob:
  ### 调度中心部署根地址 [选填]：如调度中心集群部署存在多个地址则用逗号分隔。执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"；为空则关闭自动注册；
  adminAddresses: http://127.0.0.1:8089/xxl-job-admin
  ### 执行器通讯TOKEN [选填]：非空时启用；
  accessToken: default_token
  ### 执行器AppName [选填]：执行器心跳注册分组依据；为空则关闭自动注册
  appname: application-app
  ### 执行器注册 [选填]：优先使用该配置作为注册地址，为空时使用内嵌服务 ”IP:PORT“ 作为注册地址。从而更灵活的支持容器类型执行器动态IP和动态映射端口问题。
  address: ''
  ### 执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用；地址信息用于 "执行器注册" 和 "调度中心请求并触发任务"；
  ip: ''
  ### 执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口；
  port: 9999
  ### 执行器运行日志文件存储磁盘路径 [选填] ：需要对该路径拥有读写权限；为空则使用默认路径；
  logpath:
  ### 执行器日志文件保存天数 [选填] ： 过期日志自动清理, 限制值大于等于3时生效; 否则, 如-1, 关闭自动清理功能；
  logRetentionDays: 4