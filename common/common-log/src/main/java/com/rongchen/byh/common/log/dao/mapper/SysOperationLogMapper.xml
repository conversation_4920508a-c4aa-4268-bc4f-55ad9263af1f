<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.common.log.dao.SysOperationLogMapper">
    <resultMap id="BaseResultMap" type="com.rongchen.byh.common.log.model.SysOperationLog">
        <id column="log_id" jdbcType="BIGINT" property="logId"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="operation_type" jdbcType="INTEGER" property="operationType"/>
        <result column="service_name" jdbcType="VARCHAR" property="serviceName"/>
        <result column="api_class" jdbcType="VARCHAR" property="apiClass"/>
        <result column="api_method" jdbcType="VARCHAR" property="apiMethod"/>
        <result column="session_id" jdbcType="VARCHAR" property="sessionId"/>
        <result column="trace_id" jdbcType="VARCHAR" property="traceId"/>
        <result column="elapse" jdbcType="BIGINT" property="elapse"/>
        <result column="request_method" jdbcType="VARCHAR" property="requestMethod"/>
        <result column="request_url" jdbcType="VARCHAR" property="requestUrl"/>
        <result column="request_arguments" jdbcType="LONGVARCHAR" property="requestArguments"/>
        <result column="response_result" jdbcType="VARCHAR" property="responseResult"/>
        <result column="request_ip" jdbcType="VARCHAR" property="requestIp"/>
        <result column="success" jdbcType="BOOLEAN" property="success"/>
        <result column="error_msg" jdbcType="VARCHAR" property="errorMsg"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="operator_name" jdbcType="VARCHAR" property="operatorName"/>
        <result column="operation_time" jdbcType="TIMESTAMP" property="operationTime"/>
    </resultMap>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="filterRef">
        <if test="sysOperationLogFilter != null">
            <if test="sysOperationLogFilter.operationType != null">
                AND zz_sys_operation_log.operation_type = #{sysOperationLogFilter.operationType}
            </if>
            <if test="sysOperationLogFilter.requestUrl != null and sysOperationLogFilter.requestUrl != ''">
                <bind name = "safeRequestUrl" value = "'%' + sysOperationLogFilter.requestUrl + '%'" />
                AND zz_sys_operation_log.request_url LIKE #{safeRequestUrl}
            </if>
            <if test="sysOperationLogFilter.traceId != null and sysOperationLogFilter.traceId != ''">
                AND zz_sys_operation_log.trace_id = #{sysOperationLogFilter.traceId}
            </if>
            <if test="sysOperationLogFilter.success != null">
                AND zz_sys_operation_log.success = #{sysOperationLogFilter.success}
            </if>
            <if test="sysOperationLogFilter.operatorName != null and sysOperationLogFilter.operatorName != ''">
                <bind name = "safeOperatorName" value = "'%' + sysOperationLogFilter.operatorName + '%'" />
                AND zz_sys_operation_log.operator_name LIKE #{safeOperatorName}
            </if>
            <if test="sysOperationLogFilter.elapseMin != null and sysOperationLogFilter.elapseMin != ''">
                AND zz_sys_operation_log.elapse &gt;= #{sysOperationLogFilter.elapseMin}
            </if>
            <if test="sysOperationLogFilter.elapseMax != null and sysOperationLogFilter.elapseMax != ''">
                AND zz_sys_operation_log.elapse &lt;= #{sysOperationLogFilter.elapseMax}
            </if>
            <if test="sysOperationLogFilter.operationTimeStart != null and sysOperationLogFilter.operationTimeStart != ''">
                AND zz_sys_operation_log.operation_time &gt;= #{sysOperationLogFilter.operationTimeStart}
            </if>
            <if test="sysOperationLogFilter.operationTimeEnd != null and sysOperationLogFilter.operationTimeEnd != ''">
                AND zz_sys_operation_log.operation_time &lt;= #{sysOperationLogFilter.operationTimeEnd}
            </if>
        </if>
    </sql>

    <insert id="insertList">
        INSERT INTO zz_sys_operation_log VALUES
        <foreach collection="list" index="index" item="item" separator=",">
            (#{item.logId},
            #{item.description},
            #{item.operationType},
            #{item.serviceName},
            #{item.apiClass},
            #{item.apiMethod},
            #{item.sessionId},
            #{item.traceId},
            #{item.elapse},
            #{item.requestMethod},
            #{item.requestUrl},
            #{item.requestArguments},
            #{item.responseResult},
            #{item.requestIp},
            #{item.success},
            #{item.errorMsg},
            #{item.tenantId},
            #{item.operatorId},
            #{item.operatorName},
            #{item.operationTime})
        </foreach>
    </insert>

    <select id="getSysOperationLogList" resultMap="BaseResultMap" parameterType="com.rongchen.byh.common.log.model.SysOperationLog">
        SELECT * FROM zz_sys_operation_log
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>