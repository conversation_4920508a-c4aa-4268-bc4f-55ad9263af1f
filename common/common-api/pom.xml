<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.rongchen.byh</groupId>
        <artifactId>common</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>common-api</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.rongchen.byh</groupId>
            <artifactId>common-core</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.rongchen.byh</groupId>
            <artifactId>common-swagger</artifactId>
            <version>1.0.0</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.weiyan.api</groupId>-->
<!--            <artifactId>hrfq</artifactId>-->
<!--            <version>1.0</version>-->
<!--            <scope>system</scope>-->
<!--            <systemPath>${project.basedir}/lib/hrzx-1.0-SNAPSHOT.jar</systemPath>-->
<!--        </dependency>-->
    </dependencies>
</project>