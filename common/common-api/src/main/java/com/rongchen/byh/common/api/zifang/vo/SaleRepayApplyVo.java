package com.rongchen.byh.common.api.zifang.vo;

import lombok.Data;

/**
 * @ClassName SaleRepayApplyVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/9 19:29
 * @Version 1.0
 **/
@Data
public class SaleRepayApplyVo {

    /**
     * 必填，还款申请流水，备注：作为业务幂等字段，用于唯一标识每一次还款申请操作，避免重复处理相同的申请。
     */
    private String repayApplyNo;
    /**
     * 必填，响应码，备注：用于表示系统对还款申请等相关操作做出响应的代码，不同代码对应不同的处理结果情况。
     */
    private String responseCode;
    /**
     * 非必填，响应消息，备注：用于对响应码对应的情况给出更详细的文字解释说明内容。
     */
    private String responseMsg;
    /**
     * 非必填，短信是否发送成功，备注：取值为01表示发送成功，取值为02表示发送失败，用于反馈短信发送操作的结果情况。
     */
    private String success;
}
