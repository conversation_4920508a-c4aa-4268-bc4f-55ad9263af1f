package com.rongchen.byh.common.api.zifang.dto;


import lombok.Data;

@Data
public class CreditApplyDto {

    /**
     * 授信流水号
     */
    private String creditNo;

    /**
     * 用户编号
     */
    private String userId;

    /**
     * 借款人姓名
     */
    private String custName;

    /**
     * 身份证
     */
    private String idNo;

    /**
     * 授信金额，单位元，精确到小数点后两位
     */
    private String creditAmount;

    /**
     * 性别，“男” “女”
     */
    private String sex;

    /**
     * 国籍，中国
     */
    private String nationality;

    /**
     * 民族，例如：汉或汉族
     */
    private String nation;

    /**
     * 出生日期，yyyy-mm-dd
     */
    private String birthday;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String area;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 签发机关
     */
    private String signOrganization;

    /**
     * 有效期起始时间，yyyy-mm-dd
     */
    private String idValidDateBegin;

    /**
     * 有效期结束时间，yyyy-mm-dd，长期2999-12-31
     */
    private String idValidDateEnd;

    /**
     * 长期身份证，1：长期身份证 0：非长期
     */
    private String idLongTerm;

    /**
     * 手机号码
     */
    private String phoneNo;

    /**
     * 学历
     */
    private String education;

    /**
     * 婚姻状况
     */
    private String marryType;

    /**
     * 个人月收入
     */
    private Double income;


    /**
     * 设备品牌
     */
    private String deviceBrand;

    /**
     * 设备网络类型，4G、offline
     */
    private String networkType;

    /**
     * 手机型号
     */
    private String devAlias;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * IP地址
     */
    private String clientIp;

    /**
     * 经纬度坐标系类型，wgs84/gcj02/bd09II
     */
    private String coordinateType;

    /**
     * 经度，至少精确到小数点后5位.
     */
    private String lng;

    /**
     * 维度，至少精确到小数点后5位.
     */
    private String lat;

    /**
     * 设备GPS定位城市，传城市名称，如无，传默认值：0
     */
    private String gpsCity;

    /**
     * LBS定位地址
     */
    private String lbsAddress;

    /**
     * GPS定位地址
     */
    private String gpsAddress;

    /**
     * 操作系统，Android\IOS
     */
    private String os;

    /**
     * 手机系统版本号
     */
    private String osVersion;

    /**
     * 人脸识别分数，可从腾讯云人脸识别获取
     */
    private String faceScore;

    /**
     * 人脸校验置信区间，例：70 可从腾讯云人脸识别获取
     */
    private String faceConfidence;

    /**
     * 人脸识别渠道，人脸识别机构，可以默认为腾讯云
     */
    private String faceSource;

    /**
     * 人脸完成时间，yyyy-MM-dd HH:mm:ss
     */
    private String faceTime;


    /***********************************************/

    /**
     * 联系人关系1 如已婚，家庭联系人必填配偶
     */
    private String contactRelation1;

    /**
     * 联系人姓名1
     */
    private String contactName1;

    /**
     * 联系人手机号1
     */
    private String contactPhoneNo1;

    /**
     * 联系人关系2
     */
    private String contactRelation2;

    /**
     * 联系人姓名2
     */
    private String contactName2;

    /**
     * 联系人手机号2
     */
    private String contactPhoneNo2;

    /**
     * 身份证头像照地址
     */
    private String idCardFrontUrl;

    /**
     * 身份证国徽照地址
     */
    private String idCardBackUrl;

    /**
     * 活检照片地址
     */
    private String liveUrl;

    /**
     * 进件单号
     */
    private String orderNo;

    /**
     * 公司名
     */
    private String companyName;

    /**
     * 职业
     */
    private Integer professional;

    /**
     * 身份证地址
     */
    private String idcardAddress;

}
