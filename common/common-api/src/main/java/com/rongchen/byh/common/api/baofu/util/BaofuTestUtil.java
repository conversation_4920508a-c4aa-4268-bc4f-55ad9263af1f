package com.rongchen.byh.common.api.baofu.util;

import com.rongchen.byh.common.api.baofu.vo.req.BankCardBindReqVo;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;

/**
 * 宝付支付测试工具类
 */
@Slf4j
public class BaofuTestUtil {

    private static final String USER_ID = "test123456";

    // 测试卡号 - 使用宝付官方示例中的测试卡号
    private static final String DEFAULT_TEST_CARD = "6222021001015343576";

    // 移除DEFAULT_CARD_PREFIX，改为使用官方测试卡号
    // private static final String DEFAULT_CARD_PREFIX = "622622";
    private static final Random RANDOM = new Random();

    // 各银行借记卡长度 (不包括Luhn校验位)
    private static final Map<BankType, Integer> CARD_LENGTHS = new ConcurrentHashMap<>();

    static {
        // 初始化各银行卡号长度 (不含校验位)
        CARD_LENGTHS.put(BankType.ICBC, 18); // 工商银行19位
        CARD_LENGTHS.put(BankType.ABC, 18); // 农业银行19位
        CARD_LENGTHS.put(BankType.BOC, 18); // 中国银行19位
        CARD_LENGTHS.put(BankType.CCB, 18); // 建设银行19位
        CARD_LENGTHS.put(BankType.CMB, 15); // 招商银行16位
        CARD_LENGTHS.put(BankType.PINGAN, 15); // 平安银行16位
    }

    // 常见省份地区代码
    private static final String[] AREA_CODES = {
            "110101", // 北京市东城区
            "310101", // 上海市黄浦区
            "440103", // 广州市荔湾区
            "440304", // 深圳市福田区
            "330102", // 杭州市上城区
            "320102" // 南京市玄武区
    };

    // 身份证加权因子
    private static final int[] WEIGHT = { 7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2 };

    // 身份证校验码
    private static final char[] VALIDATE_CODE = { '1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2' };

    // 常见手机号前缀
    private static final String[] MOBILE_PREFIXES = {
            "130", "131", "132", "133", "134", "135", "136", "137", "138", "139",
            "150", "151", "152", "153", "155", "156", "157", "158", "159",
            "170", "176", "177", "178",
            "180", "181", "182", "183", "184", "185", "186", "187", "188", "189",
            "199"
    };

    /**
     * 测试卡类型枚举
     */
    public enum TestCardType {
        /**
         * 交易成功
         */
        SUCCESS(0),

        /**
         * 余额不足
         */
        INSUFFICIENT_BALANCE(1),

        /**
         * 卡状态失败
         */
        CARD_STATUS_FAIL(3),

        /**
         * 交易失败
         */
        TRANSACTION_FAIL(5),

        /**
         * 处理中 2分钟后失败
         */
        PROCESSING_FAIL(7),

        /**
         * 处理中 2分钟后成功
         */
        PROCESSING_SUCCESS(9);

        private final int lastDigit;

        TestCardType(int lastDigit) {
            this.lastDigit = lastDigit;
        }

        public int getLastDigit() {
            return lastDigit;
        }
    }

    /**
     * 银行类型枚举
     * 测试环境只支持工农中建招平安银行
     */
    public enum BankType {
        /**
         * 工商银行
         */
        ICBC("工商银行", "621226"),

        /**
         * 农业银行
         */
        ABC("农业银行", "622848"),

        /**
         * 中国银行
         */
        BOC("中国银行", "621660"),

        /**
         * 建设银行
         */
        CCB("建设银行", "621700"),

        /**
         * 招商银行
         */
        CMB("招商银行", "621483"),

        /**
         * 平安银行
         */
        PINGAN("平安银行", "622156");

        private final String bankName;
        private final String cardPrefix;

        BankType(String bankName, String cardPrefix) {
            this.bankName = bankName;
            this.cardPrefix = cardPrefix;
        }

        public String getBankName() {
            return bankName;
        }

        public String getCardPrefix() {
            return cardPrefix;
        }
    }

    /**
     * 生成有效的身份证号码
     * 
     * @return 18位有效身份证号
     */
    public static String generateValidIdCardNo() {
        // 1. 随机选择地区码
        String areaCode = AREA_CODES[RANDOM.nextInt(AREA_CODES.length)];

        // 2. 生成出生日期 (1980-2005年间)
        int year = 1980 + RANDOM.nextInt(25); // 1980-2004
        int month = 1 + RANDOM.nextInt(12); // 1-12
        int dayOfMonth = 1 + RANDOM.nextInt(28); // 避免月份天数问题，限制在28天内

        LocalDate birthDate = LocalDate.of(year, month, dayOfMonth);
        String birthDateStr = birthDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        // 3. 生成顺序码（3位数字）
        String sequenceCode = String.format("%03d", RANDOM.nextInt(999));

        // 4. 组合前17位
        String idCardBase = areaCode + birthDateStr + sequenceCode;

        // 5. 计算校验码
        char validateCode = calculateValidateCode(idCardBase);

        // 6. 返回完整18位身份证号
        return idCardBase + validateCode;
    }

    /**
     * 计算身份证校验码
     * 
     * @param idCardBase 身份证前17位
     * @return 校验码（数字或X）
     */
    private static char calculateValidateCode(String idCardBase) {
        if (idCardBase == null || idCardBase.length() != 17) {
            throw new IllegalArgumentException("身份证号前17位长度必须为17位");
        }

        int sum = 0;
        for (int i = 0; i < 17; i++) {
            sum += Character.getNumericValue(idCardBase.charAt(i)) * WEIGHT[i];
        }

        int mod = sum % 11;
        return VALIDATE_CODE[mod];
    }

    /**
     * 验证身份证号是否有效
     * 
     * @param idCardNo 完整的18位身份证号
     * @return 是否有效
     */
    public static boolean validateIdCardNo(String idCardNo) {
        if (idCardNo == null || idCardNo.length() != 18) {
            return false;
        }

        // 验证最后一位校验码
        String idCardBase = idCardNo.substring(0, 17);
        char validateCode = calculateValidateCode(idCardBase);

        char lastChar = Character.toUpperCase(idCardNo.charAt(17));
        return lastChar == validateCode;
    }

    /**
     * 使用Luhn算法生成银行卡校验位
     * 
     * @param number 不含校验位的卡号
     * @return 校验位
     */
    private static int generateLuhnCheckDigit(String number) {
        int sum = 0;
        boolean alternate = false;

        // 从右到左遍历（但我们会反向处理，因为后面会加校验位，所以相当于校验位左边的位置）
        for (int i = number.length() - 1; i >= 0; i--) {
            int digit = Character.getNumericValue(number.charAt(i));

            if (alternate) {
                digit *= 2;
                if (digit > 9) {
                    digit = digit - 9;
                }
            }

            sum += digit;
            alternate = !alternate;
        }

        // 反转最后的alternate状态，因为校验位会被加在最右边
        alternate = !alternate;

        // 如果校验位被处理（偶数位），我们需要计算什么数字*2后能凑成10的倍数
        if (alternate) {
            // 找出(sum+x*2)%10==0的x值
            int check = 0;
            while ((sum + (check * 2)) % 10 != 0) {
                check++;
                if (check > 4) {
                    // 如果check*2 > 9，则需要调整
                    check = check - 9;
                }
            }
            return check;
        } else {
            // 如果校验位不被处理（奇数位），直接计算凑成10的倍数的值
            return (10 - (sum % 10)) % 10;
        }
    }

    /**
     * 使用Luhn算法校验银行卡是否有效
     * 
     * @param cardNo 完整的银行卡号（包含校验位）
     * @return 是否有效
     */
    public static boolean validateCardNumber(String cardNo) {
        if (cardNo == null || cardNo.length() < 13 || !cardNo.matches("\\d+")) {
            return false;
        }

        int sum = 0;
        boolean alternate = false;

        // 从右往左遍历
        for (int i = cardNo.length() - 1; i >= 0; i--) {
            int digit = Character.getNumericValue(cardNo.charAt(i));

            if (alternate) {
                digit *= 2;
                if (digit > 9) {
                    digit = digit - 9;
                }
            }

            sum += digit;
            alternate = !alternate;
        }

        return (sum % 10 == 0);
    }

    /**
     * 生成指定银行和测试类型的银行卡号
     * 
     * @param bankType 银行类型
     * @param testType 测试卡类型
     * @return 银行卡号
     */
    public static String generateCardNumber(BankType bankType, TestCardType testType) {
        // 如果是成功场景，对任何银行都直接返回官方测试卡号
        if (testType == TestCardType.SUCCESS) {
            return DEFAULT_TEST_CARD;
        }

        // 获取银行卡前缀
        String prefix = bankType.getCardPrefix();

        // 获取卡号长度(不含校验位)
        // 默认使用18位（与工行一致）
        int length = CARD_LENGTHS.getOrDefault(bankType, 18);

        // 构建卡号：前缀 + 随机数 + 测试类型数字 + 校验位
        StringBuilder cardBuilder = new StringBuilder(prefix);

        // 计算需要生成的随机数位数
        int randomDigits = length - prefix.length() - 1; // 减1是因为要预留一位放测试类型数字

        // 生成随机数部分
        for (int i = 0; i < randomDigits; i++) {
            cardBuilder.append(RANDOM.nextInt(10));
        }

        // 添加测试类型标识数字
        cardBuilder.append(testType.getLastDigit());

        // 计算并添加校验位
        String cardWithoutCheckDigit = cardBuilder.toString();

        // 直接使用验证方法反向调整，找到合适的校验位
        for (int checkDigit = 0; checkDigit < 10; checkDigit++) {
            String fullCard = cardWithoutCheckDigit + checkDigit;
            if (validateCardNumber(fullCard)) {
                return fullCard;
            }
        }

        // 如果无法找到合适的校验位（理论上不会发生），使用原算法
        int checkDigit = generateLuhnCheckDigit(cardWithoutCheckDigit);
        return cardWithoutCheckDigit + checkDigit;
    }

    /**
     * 生成指定测试类型的银行卡号
     * 使用工商银行作为默认银行
     *
     * @param type 测试卡类型
     * @return 银行卡号
     */
    public static String generateCardNumber(TestCardType type) {
        // 如果是成功场景，返回宝付官方测试卡号
        if (type == TestCardType.SUCCESS) {
            return DEFAULT_TEST_CARD;
        }
        return generateCardNumber(BankType.ICBC, type);
    }

    /**
     * 生成成功场景的银行卡号
     * 使用宝付官方文档中的测试卡号
     *
     * @return 银行卡号
     */
    public static String generateSuccessCardNumber() {
        return DEFAULT_TEST_CARD;
    }

    /**
     * 生成随机手机号
     * 
     * @return 11位手机号
     */
    public static String generateMobileNumber() {
        // 随机选择一个手机号前缀
        String prefix = MOBILE_PREFIXES[RANDOM.nextInt(MOBILE_PREFIXES.length)];

        // 生成8位随机数作为后缀
        StringBuilder suffix = new StringBuilder();
        for (int i = 0; i < 8; i++) {
            suffix.append(RANDOM.nextInt(10));
        }

        return prefix + suffix.toString();
    }

    /**
     * 混淆手机号中间部分，用于显示
     * 
     * @param mobile 完整手机号
     * @return 混淆后的手机号（如：138****1234）
     */
    public static String maskMobileNumber(String mobile) {
        if (mobile == null || mobile.length() != 11) {
            return mobile;
        }
        return mobile.substring(0, 3) + "****" + mobile.substring(7);
    }

    /**
     * 构建测试绑卡VO
     *
     * @param type 测试卡类型
     * @return 绑卡VO
     */
    public static BankCardBindReqVo buildTestBindVO(TestCardType type) {
        return buildTestBindVO(BankType.ICBC, type);
    }

    /**
     * 构建指定银行和测试类型的绑卡VO
     *
     * @param bankType 银行类型
     * @param testType 测试卡类型
     * @return 绑卡VO
     */
    public static BankCardBindReqVo buildTestBindVO(BankType bankType, TestCardType testType) {
        BankCardBindReqVo bindVO = new BankCardBindReqVo();
        bindVO.setUserId(USER_ID);
        bindVO.setBankCardNo(generateCardNumber(bankType, testType));
        bindVO.setCardholderName("徐长卿");
        bindVO.setIdCardType("01"); // 身份证

        // 生成有效的身份证号码
        String idCardNo = generateValidIdCardNo();
        log.debug("生成有效的身份证号码: {}", idCardNo);
        bindVO.setIdCardNo(idCardNo);

        // 生成随机手机号
        String mobile = generateMobileNumber();
        log.debug("生成随机手机号: {}", mobile);
        bindVO.setMobile(mobile);

        bindVO.setCardType("101"); // 借记卡
        return bindVO;
    }

    /**
     * 构建成功场景测试绑卡VO
     * 使用宝付官方文档中的测试数据
     *
     * @return 绑卡VO
     */
    public static BankCardBindReqVo buildSuccessBindVO() {
        BankCardBindReqVo bindVO = new BankCardBindReqVo();
        bindVO.setUserId(USER_ID);
        bindVO.setBankCardNo(DEFAULT_TEST_CARD);
        bindVO.setCardholderName("王勇");
        bindVO.setIdCardType("01"); // 身份证
        bindVO.setIdCardNo("310115200501018559");
        bindVO.setMobile("***********");
        bindVO.setCardType("101"); // 借记卡
        return bindVO;
    }

    /**
     * 构建指定银行的成功场景测试绑卡VO
     *
     * @param bankType 银行类型
     * @return 绑卡VO
     */
    public static BankCardBindReqVo buildSuccessBindVO(BankType bankType) {
        // 对于成功场景，我们始终使用官方指定的测试卡
        return buildSuccessBindVO();
    }

    /**
     * 根据银行类型和卡号尾数构建测试绑卡VO
     * 卡号尾数规则：
     * - 0、2、4、6、8：交易成功
     * - 1：余额不足
     * - 3：卡状态失败
     * - 5：交易失败
     * - 7：处理中，2分钟后失败
     * - 9：处理中，2分钟后成功
     *
     * @param bankType  银行类型
     * @param lastDigit 卡号尾数(0-9)
     * @return 绑卡VO
     */
    public static BankCardBindReqVo buildBindVoByLastDigit(BankType bankType, int lastDigit) {
        TestCardType testType;
        switch (lastDigit) {
            case 1:
                testType = TestCardType.INSUFFICIENT_BALANCE;
                break;
            case 3:
                testType = TestCardType.CARD_STATUS_FAIL;
                break;
            case 5:
                testType = TestCardType.TRANSACTION_FAIL;
                break;
            case 7:
                testType = TestCardType.PROCESSING_FAIL;
                break;
            case 9:
                testType = TestCardType.PROCESSING_SUCCESS;
                break;
            default:
                testType = TestCardType.SUCCESS;
                break;
        }
        return buildTestBindVO(bankType, testType);
    }

    /**
     * 根据卡号尾数构建测试绑卡VO（使用随机银行）
     * 卡号尾数规则：
     * - 0、2、4、6、8：交易成功
     * - 1：余额不足
     * - 3：卡状态失败
     * - 5：交易失败
     * - 7：处理中，2分钟后失败
     * - 9：处理中，2分钟后成功
     *
     * @param lastDigit 卡号尾数(0-9)
     * @return 绑卡VO
     */
    public static BankCardBindReqVo buildBindVoByLastDigit(int lastDigit) {
        // 随机选择一个银行类型
        BankType[] bankTypes = BankType.values();
        BankType bankType = bankTypes[RANDOM.nextInt(bankTypes.length)];
        return buildBindVoByLastDigit(bankType, lastDigit);
    }

    /**
     * 根据卡号尾数构建测试绑卡VO（简化版，直接修改官方卡号尾数）
     * 卡号尾数规则：
     * - 0、2、4、6、8：交易成功
     * - 1：余额不足
     * - 3：卡状态失败
     * - 5：交易失败
     * - 7：处理中，2分钟后失败
     * - 9：处理中，2分钟后成功
     *
     * @param lastDigit 卡号尾数(0-9)
     * @return 绑卡VO
     */
    public static BankCardBindReqVo buildSimpleTestCardByLastDigit(int lastDigit) {
        BankCardBindReqVo bindVO = buildSuccessBindVO();

        // 获取原始卡号并修改最后一位
        String originalCard = bindVO.getBankCardNo();
        String newCard = originalCard.substring(0, originalCard.length() - 1) + lastDigit;
        bindVO.setBankCardNo(newCard);
        log.info("当前卡号: {} 状态：{}", bindVO.getBankCardNo(), lastDigit);

        return bindVO;
    }

    /**
     * 输出测试卡使用说明
     */
    public static void printTestCardUsage() {
        log.info("===== 宝付测试卡使用说明 =====");

        // 生成并验证各类银行卡
        for (BankType bankType : BankType.values()) {
            String cardNumber = generateCardNumber(bankType, TestCardType.SUCCESS);
            boolean isValid = validateCardNumber(cardNumber);
            log.info("银行: {}, 成功场景卡号示例: {}, 验证结果: {}",
                    bankType.getBankName(), cardNumber, isValid);
        }

        // 生成并验证各种测试场景
        log.info("===== 测试场景卡号示例 =====");
        for (TestCardType type : TestCardType.values()) {
            String cardNumber = generateCardNumber(type);
            boolean isValid = validateCardNumber(cardNumber);
            log.info("场景: {}, 卡号示例: {}, 验证结果: {}",
                    type.name(), cardNumber, isValid);
        }

        // 验证身份证
        String idCard = generateValidIdCardNo();
        log.info("身份证号示例: {}, 验证结果: {}", idCard, validateIdCardNo(idCard));
    }

    /**
     * 生成有效的测试数据并输出验证结果
     */
    public static void generateAndValidateTestData() {
        log.info("===== 生成测试数据 =====");

        // 1. 银行卡号
        for (BankType bankType : BankType.values()) {
            String cardNumber = generateCardNumber(bankType, TestCardType.SUCCESS);
            boolean isValid = validateCardNumber(cardNumber);
            log.info("银行: {}, 卡号: {}, 位数: {}, 验证结果: {}",
                    bankType.getBankName(), cardNumber, cardNumber.length(), isValid);

            // 如果验证失败，输出详细验证过程
            if (!isValid) {
                // log.error("卡号验证失败，详细分析: {}", analyzeCardNumber(cardNumber));
            }
        }

        // 2. 身份证号
        for (int i = 0; i < 3; i++) {
            String idCardNo = generateValidIdCardNo();
            boolean isValid = validateIdCardNo(idCardNo);
            log.info("身份证号 #{}: {}, 验证结果: {}", (i + 1), idCardNo, isValid);
        }
    }

    /**
     * 分析银行卡号验证失败的原因
     * 
     * @param cardNumber 银行卡号
     * @return 详细分析
     */
    private static String analyzeCardNumber(String cardNumber) {
        StringBuilder analysis = new StringBuilder();
        analysis.append("卡号: ").append(cardNumber).append("\n");

        int sum = 0;
        boolean alternate = false;

        analysis.append("位置\t数字\t处理后\t累计和\n");

        // 从右往左遍历
        for (int i = cardNumber.length() - 1; i >= 0; i--) {
            int digit = Character.getNumericValue(cardNumber.charAt(i));
            int processedDigit = digit;

            if (alternate) {
                processedDigit = digit * 2;
                if (processedDigit > 9) {
                    processedDigit = processedDigit - 9;
                }
            }

            sum += processedDigit;

            analysis.append(cardNumber.length() - i)
                    .append("\t").append(digit)
                    .append("\t").append(processedDigit)
                    .append("\t").append(sum)
                    .append("\n");

            alternate = !alternate;
        }

        analysis.append("最终和: ").append(sum)
                .append(", 除以10余数: ").append(sum % 10)
                .append(", 验证结果: ").append(sum % 10 == 0 ? "通过" : "失败");

        return analysis.toString();
    }

    /**
     * 混淆身份证号，只显示前6位和后4位
     * 
     * @param idCardNo 完整身份证号
     * @return 混淆后的身份证号（如：110101********0000）
     */
    public static String maskIdCardNo(String idCardNo) {
        if (idCardNo == null || idCardNo.length() != 18) {
            return idCardNo;
        }
        return idCardNo.substring(0, 6) + "********" + idCardNo.substring(14);
    }
}