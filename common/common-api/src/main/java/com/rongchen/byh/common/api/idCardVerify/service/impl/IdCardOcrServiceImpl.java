package com.rongchen.byh.common.api.idCardVerify.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Charsets;
import com.google.common.hash.Hashing;
import com.rongchen.byh.common.api.idCardVerify.config.OcrConstant;
import com.rongchen.byh.common.api.idCardVerify.config.TencentProperties;
import com.rongchen.byh.common.api.idCardVerify.dto.OcrDto;
import com.rongchen.byh.common.api.idCardVerify.service.IdCardOcrService;
import com.rongchen.byh.common.api.idCardVerify.utils.TencentUtil;
import com.rongchen.byh.common.api.idCardVerify.vo.IdCardResult;
import com.rongchen.byh.common.api.idCardVerify.vo.OcrNewVo;
import com.rongchen.byh.common.api.idCardVerify.vo.OcrVo;
import com.rongchen.byh.common.core.util.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;


@Service
@Slf4j
public class IdCardOcrServiceImpl extends QcloudBase implements IdCardOcrService {

    @Resource
    private TencentProperties tencentProperties;

    @Override
    public OcrVo idCardOcr(OcrDto ocrDto) {
        JSONObject param = new JSONObject(3);
        if (!StrUtil.isEmpty(ocrDto.getImageUrl())) {
            param.put("ImageUrl",ocrDto.getImageUrl());
        }
        if (!StrUtil.isEmpty(ocrDto.getImageBase64())) {
            param.put("ImageBase64",ocrDto.getImageBase64());
        }
        if (!StrUtil.isEmpty(ocrDto.getCardSide())) {
            param.put("CardSide",ocrDto.getCardSide());
        }
        String body = param.toJSONString();
        String endpoint = "https://" + OcrConstant.host;
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String auth = TencentUtil.getAuth(tencentProperties.getSecretId(), tencentProperties.getSecretKey(), OcrConstant.host, OcrConstant.contentType, timestamp, body);
        Map<String,String> header = new HashMap<>(10);
        header.put("Host", OcrConstant.host);
        header.put("X-TC-Timestamp", timestamp);
        header.put("X-TC-Version", OcrConstant.Version);
        header.put("X-TC-Action", OcrConstant.Action);
        header.put("X-TC-Region", "");
        header.put("Authorization", auth);
        String res = HttpUtil.postJson(endpoint, param, header);
        JSONObject object = JSONObject.parseObject(res);
        JSONObject response = object.getJSONObject("Response");
        OcrVo vo = new OcrVo();
        if (response.containsKey("Error")) {
            vo.setCode(-1);
            vo.setMsg(response.getJSONObject("Error").getString("Message"));
            return vo;
        }
        vo.setCode(0);
        vo.setMsg("成功");
        IdCardResult idCardResult = new IdCardResult();
        idCardResult.setName(response.getString("Name"));
        idCardResult.setSex(response.getString("Sex"));
        idCardResult.setNation(response.getString("Nation"));
        idCardResult.setBirth(response.getString("Birth"));
        idCardResult.setAddress(response.getString("Address"));
        idCardResult.setIdNum(response.getString("IdNum"));
        idCardResult.setAuthority(response.getString("Authority"));
        idCardResult.setValidDate(response.getString("ValidDate"));
        vo.setIdCardResult(idCardResult);
        return vo;
    }

    @Override
    public OcrNewVo idCardOcrNew(String orderNo) {
        JSONObject jsonObject = JSONObject.parseObject(getToken());
        String token = jsonObject.getString("access_token");
        JSONObject jsonObject1 = JSONObject.parseObject(getTicket(token));
        Object tickets = jsonObject1.getJSONArray("tickets").get(0);
        JSONObject jsonObject2 = JSONObject.parseObject(tickets.toString());
        String ticket = jsonObject2.getString("value");
        String nonce = UUID.randomUUID().toString().replace("-", "");
        String sign = getSign(ticket, nonce, orderNo);
        JSONObject param = new JSONObject();
        param.put("app_id", APPID);
        param.put("version", VERSION);
        param.put("nonce", nonce);
        param.put("order_no", orderNo);
        param.put("sign", sign);
        param.put("get_file", "1");
        String ret = HttpUtil.get("https://kyc1.qcloud.com/api/server/getOcrResult", param);
        JSONObject json = JSONObject.parseObject(ret);
        String result = json.getString("result");
        return JSONObject.parseObject(result,OcrNewVo.class);
    }



}
