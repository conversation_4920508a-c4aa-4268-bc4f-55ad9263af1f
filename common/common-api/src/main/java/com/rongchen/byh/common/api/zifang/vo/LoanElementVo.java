package com.rongchen.byh.common.api.zifang.vo;


import com.rongchen.byh.common.api.zifang.vo.loanElement.Fund;
import com.rongchen.byh.common.api.zifang.vo.loanElement.PayChannel;
import com.rongchen.byh.common.api.zifang.vo.loanElement.PayWay;
import com.rongchen.byh.common.api.zifang.vo.loanElement.Term;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class LoanElementVo extends BaseVo {

    /**
     * 最小可借金额
     */
    @Schema(description = "最小可借金额")
    private String minLoanAmt;

    /**
     * 最大可借金额
     */
    @Schema(description = "最大可借金额")
    private String maxLoanAmt;

    /**
     * 递增单位
     */
    @Schema(description = "递增单位")
    private String incrementUnit;

    /**
     * 年化利率
     */
    @Schema(description = "年化利率")
    private String yearRate;

    /**
     * 绑卡通道列表
     */
    @Schema(description = "绑卡通道列表")
    private List<PayChannel> payChannelList;

    /**
     * 支持的期数列表
     */
    @Schema(description = "支持的期数列表")
    private List<Term> termList;

    /**
     * 支持的还款方式列表    还款方式编码
     */
    @Schema(description = "支持的还款方式列表")
    private List<PayWay> payWayList;

    /**
     * 资方列表
     */
    @Schema(description = "资金方列表")
    private List<Fund> fundList;

    /**
     * 授信流水号
     */
    @Schema(description = "授信流水号")
    private String creditNo;
}
