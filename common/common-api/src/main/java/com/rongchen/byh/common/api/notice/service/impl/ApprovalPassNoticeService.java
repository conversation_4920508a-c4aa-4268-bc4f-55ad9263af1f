package com.rongchen.byh.common.api.notice.service.impl;

import com.rongchen.byh.common.api.notice.config.NoticeSmsContent;
import com.rongchen.byh.common.api.notice.dto.ApprovalPassDto;
import com.rongchen.byh.common.api.notice.dto.NoticeSmsDto;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 还款成功短信
 * @date 2024/12/12 16:47:11
 */
@Service
public class ApprovalPassNoticeService extends AbstractNoticeSmsService {
    @Override
    protected String getContent(NoticeSmsDto noticeSmsDto) {
        ApprovalPassDto smsDto = (ApprovalPassDto) noticeSmsDto;
        return smsDto.getUserName() + "##" + smsDto.getBorrowingAmount();
    }

    @Override
    protected Integer getTemplateId() {
        return NoticeSmsContent.APPROVALPASSED;
    }
}
