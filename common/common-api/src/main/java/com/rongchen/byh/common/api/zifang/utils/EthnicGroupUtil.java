package com.rongchen.byh.common.api.zifang.utils;


import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 民族工具类
 * @date 2025/3/20 16:41:36
 */
public class EthnicGroupUtil {

    // 编号到民族的映射
    private static final Map<String, String> CODE_TO_ETHNIC_GROUP = new HashMap<>();

    // 民族到编号的映射
    private static final Map<String, String> ETHNIC_GROUP_TO_CODE = new HashMap<>();

    static {
        // 初始化编号到民族的映射
        CODE_TO_ETHNIC_GROUP.put("1", "汉族");
        CODE_TO_ETHNIC_GROUP.put("2", "壮族");
        CODE_TO_ETHNIC_GROUP.put("3", "满族");
        CODE_TO_ETHNIC_GROUP.put("4", "回族");
        CODE_TO_ETHNIC_GROUP.put("5", "苗族");
        CODE_TO_ETHNIC_GROUP.put("6", "维吾尔族");
        CODE_TO_ETHNIC_GROUP.put("7", "土家族");
        CODE_TO_ETHNIC_GROUP.put("8", "彝族");
        CODE_TO_ETHNIC_GROUP.put("9", "蒙古族");
        CODE_TO_ETHNIC_GROUP.put("10", "藏族");
        CODE_TO_ETHNIC_GROUP.put("11", "布依族");
        CODE_TO_ETHNIC_GROUP.put("12", "侗族");
        CODE_TO_ETHNIC_GROUP.put("13", "瑶族");
        CODE_TO_ETHNIC_GROUP.put("14", "朝鲜族");
        CODE_TO_ETHNIC_GROUP.put("15", "白族");
        CODE_TO_ETHNIC_GROUP.put("16", "哈尼族");
        CODE_TO_ETHNIC_GROUP.put("17", "哈萨克族");
        CODE_TO_ETHNIC_GROUP.put("18", "黎族");
        CODE_TO_ETHNIC_GROUP.put("19", "傣族");
        CODE_TO_ETHNIC_GROUP.put("20", "畲族");
        CODE_TO_ETHNIC_GROUP.put("21", "傈僳族");
        CODE_TO_ETHNIC_GROUP.put("22", "仡佬族");
        CODE_TO_ETHNIC_GROUP.put("23", "东乡族");
        CODE_TO_ETHNIC_GROUP.put("24", "高山族");
        CODE_TO_ETHNIC_GROUP.put("25", "拉祜族");
        CODE_TO_ETHNIC_GROUP.put("26", "水族");
        CODE_TO_ETHNIC_GROUP.put("27", "佤族");
        CODE_TO_ETHNIC_GROUP.put("28", "纳西族");
        CODE_TO_ETHNIC_GROUP.put("29", "羌族");
        CODE_TO_ETHNIC_GROUP.put("30", "土族");
        CODE_TO_ETHNIC_GROUP.put("31", "仫佬族");
        CODE_TO_ETHNIC_GROUP.put("32", "锡伯族");
        CODE_TO_ETHNIC_GROUP.put("33", "柯尔克孜族");
        CODE_TO_ETHNIC_GROUP.put("34", "达斡尔族");
        CODE_TO_ETHNIC_GROUP.put("35", "景颇族");
        CODE_TO_ETHNIC_GROUP.put("36", "毛南族");
        CODE_TO_ETHNIC_GROUP.put("37", "撒拉族");
        CODE_TO_ETHNIC_GROUP.put("38", "布朗族");
        CODE_TO_ETHNIC_GROUP.put("39", "塔吉克族");
        CODE_TO_ETHNIC_GROUP.put("40", "阿昌族");
        CODE_TO_ETHNIC_GROUP.put("41", "普米族");
        CODE_TO_ETHNIC_GROUP.put("42", "鄂温克族");
        CODE_TO_ETHNIC_GROUP.put("43", "怒族");
        CODE_TO_ETHNIC_GROUP.put("44", "京族");
        CODE_TO_ETHNIC_GROUP.put("45", "基诺族");
        CODE_TO_ETHNIC_GROUP.put("46", "德昂族");
        CODE_TO_ETHNIC_GROUP.put("47", "保安族");
        CODE_TO_ETHNIC_GROUP.put("48", "俄罗斯族");
        CODE_TO_ETHNIC_GROUP.put("49", "裕固族");
        CODE_TO_ETHNIC_GROUP.put("50", "乌孜别克族");
        CODE_TO_ETHNIC_GROUP.put("51", "门巴族");
        CODE_TO_ETHNIC_GROUP.put("52", "鄂伦春族");
        CODE_TO_ETHNIC_GROUP.put("53", "独龙族");
        CODE_TO_ETHNIC_GROUP.put("54", "塔塔尔族");
        CODE_TO_ETHNIC_GROUP.put("55", "赫哲族");
        CODE_TO_ETHNIC_GROUP.put("56", "珞巴族");
        CODE_TO_ETHNIC_GROUP.put("57", "穿青族");
        CODE_TO_ETHNIC_GROUP.put("58", "革家人");
        CODE_TO_ETHNIC_GROUP.put("99", "其他");

        // 初始化民族到编号的映射
        for (Map.Entry<String, String> entry : CODE_TO_ETHNIC_GROUP.entrySet()) {
            ETHNIC_GROUP_TO_CODE.put(entry.getValue(), entry.getKey());
        }
    }

    /**
     * 根据编号获取民族描述
     *
     * @param code 民族编号
     * @return 民族描述，如果编号不存在则返回 null
     */
    public static String getEthnicGroupByCode(String code) {
        return CODE_TO_ETHNIC_GROUP.get(code);
    }

    /**
     * 根据民族描述获取编号
     *
     * @param ethnicGroup 民族描述
     * @return 民族编号，如果民族描述不存在则返回 null
     */
    public static String getCodeByEthnicGroup(String ethnicGroup) {
        return ETHNIC_GROUP_TO_CODE.get(ethnicGroup);
    }

    public static void main(String[] args) {
        // 测试工具类
        System.out.println("编号 1 对应的民族是: " + getEthnicGroupByCode("1")); // 输出: 汉族
        System.out.println("民族 '壮族' 对应的编号是: " + getCodeByEthnicGroup("壮族")); // 输出: 2
        System.out.println("编号 99 对应的民族是: " + getEthnicGroupByCode("99")); // 输出: 其他
        System.out.println("民族 '未知民族' 对应的编号是: " + getCodeByEthnicGroup("未知民族")); // 输出: null
    }
}
