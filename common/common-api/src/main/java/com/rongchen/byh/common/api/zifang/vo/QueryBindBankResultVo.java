package com.rongchen.byh.common.api.zifang.vo;

import com.rongchen.byh.common.api.baofu.vo.rsp.BaoFUBankCardProtocolRspVo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * @ClassName QueryBindBankResultVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/9 11:59
 * @Version 1.0
 **/
@Data
public class QueryBindBankResultVo {

    /**
     * 响应码
     */
    @Schema(description = "响应码  0000成功  P00001 处理中，间隔3秒再查询")
    private String responseCode;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String responseMsg;

    /**
     * 支付渠道
     */
    @Schema(description = "支付渠道")
    private String channel;

    /**
     * 银行卡信息
     */
    @Schema(description = "银行卡信息")
    private List<BaoFUBankCardProtocolRspVo> baofuBankCardInfoList;
}
