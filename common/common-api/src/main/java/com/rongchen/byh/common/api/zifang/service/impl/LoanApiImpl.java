//package com.rongchen.byh.common.api.zifang.service.impl;
//
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.rongchen.byh.common.api.zifang.config.ZiFangConstant;
//import com.rongchen.byh.common.api.zifang.dto.LoanApplyDto;
//import com.rongchen.byh.common.api.zifang.dto.RepayPlanCalcDto;
//import com.rongchen.byh.common.api.zifang.dto.UseLoanQueryDto;
//import com.rongchen.byh.common.api.zifang.service.LoanApi;
//import com.rongchen.byh.common.api.zifang.vo.BaseVo;
//import com.rongchen.byh.common.api.zifang.vo.RepayPlanCalcVo;
//import com.rongchen.byh.common.api.zifang.vo.UseLoanQueryVo;
//import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
//import com.rongchen.byh.common.core.object.ResponseResult;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//
//
//@Slf4j
//@Service
//public class LoanApiImpl extends AbstractApi implements LoanApi {
//    @Override
//    public ResponseResult<BaseVo> loanApply(LoanApplyDto loanApplyDto) {
//        JSONObject params = new JSONObject();
//        params.put("reqSysCode", ziFangProperties.getReqSysCode());
//        params.put("productCode", ziFangProperties.getProductCode());
//        params.put("loanInfo", loanApplyDto);
//        try {
//            JSONObject map = this.sendRequest(params, ZiFangConstant.loanApplyUrl);
//            BaseVo vo = map.toJavaObject(BaseVo.class);
//            return ResponseResult.success(vo);
//        } catch (Exception e) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL,e.getMessage());
//        }
//    }
//
//
//    @Override
//    public ResponseResult<UseLoanQueryVo> useLoanQuery(UseLoanQueryDto useLoanQueryDto) {
//        JSONObject params = new JSONObject();
//        params.put("reqSysCode", ziFangProperties.getReqSysCode());
//        params.put("productCode", ziFangProperties.getProductCode());
//        params.put("loanNo",useLoanQueryDto.getLoanNo());
//        try {
//            JSONObject map = this.sendRequest(params, ZiFangConstant.useLoanQueryUrl);
//            UseLoanQueryVo vo = map.toJavaObject(UseLoanQueryVo.class);
//            return ResponseResult.success(vo);
//        } catch (Exception e) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL,e.getMessage());
//        }
//    }
//
//    @Override
//    public ResponseResult<RepayPlanCalcVo> repayPlanCalc(RepayPlanCalcDto repayPlanCalcDto) {
//        JSONObject params = new JSONObject();
//        params.put("amount",repayPlanCalcDto.getAmount());
//        params.put("period",repayPlanCalcDto.getPeriod());
//        params.put("reqSysCode", ziFangProperties.getReqSysCode());
//        params.put("productCode", ziFangProperties.getProductCode());
//        try {
//            JSONObject map = this.sendRequest(params, ZiFangConstant.repayPlanCalcUrl);
//            RepayPlanCalcVo vo = map.toJavaObject(RepayPlanCalcVo.class);
//            return ResponseResult.success(vo);
//        } catch (Exception e) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL,e.getMessage());
//        }
//    }
//
//
//}
