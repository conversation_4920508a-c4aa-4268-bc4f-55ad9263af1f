package com.rongchen.byh.common.api.zifang.vo;

import lombok.Data;

/**
 * @ClassName RepaymentPlanQueryPkgVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/9 15:07
 * @Version 1.0
 **/
@Data
public class RepaymentPlanQueryPkgVo {

    /**
     * 还款期数，类型为String，长度为3，必填（Y表示必填）
     */
    private String repayTerm;
    /**
     * 还款起日，类型为String，长度为10，必填（Y表示必填），代表自动扣款开始日期，格式为yyyy-MM-dd格式
     */
    private String repayOwnbDate;
    /**
     * 还款止日，类型为String，长度为10，必填（Y表示必填），代表自动扣款结束日期，格式为yyyy-MM-dd格式
     */
    private String repayOwneDate;
    /**
     * 本期起日，类型为String，长度为10，必填（Y表示必填），代表结息周期开始日期，格式为yyyy-MM-dd格式，还款计划列表按本期起日正序排列
     */
    private String repayIntbDate;
    /**
     * 本期止日，类型为String，长度为10，必填（Y表示必填），代表结息周期截止日期，格式为yyyy-MM-dd格式
     */
    private String repayInteDate;
    /**
     * 本期应还总金额，类型为String，格式为17位长度且保留2位小数，必填（Y表示必填），单位为元
     */
    private String totalAmt;
    /**
     * 本期应还本金，类型为String，格式为17位长度且保留2位小数，必填（Y表示必填），单位为元
     */
    private String termRetPrin;
    /**
     * 本期已还本金，类型为String，格式为17位长度且保留2位小数，必填（Y表示必填），单位为元
     */
    private String prinAmt;
    /**
     * 本期未还本金，类型为String，格式为17位长度且保留2位小数，必填（Y表示必填），单位为元
     */
    private String noRetAmt;
    /**
     * 本期应还利息，类型为String，格式为17位长度且保留2位小数，必填（Y表示必填），单位为元
     */
    private String termRetInt;
    /**
     * 本期已还利息，类型为String，格式为17位长度且保留2位小数，必填（Y表示必填），单位为元
     */
    private String intAmt;
    /**
     * 本期未还利息，类型为String，格式为17位长度且保留2位小数，必填（Y表示必填），单位为元
     */
    private String noRetInt;
    /**
     * 本期应还罚息，类型为String，格式为17位长度且保留2位小数，必填（Y表示必填），单位为元
     */
    private String termRetFint;
    /**
     * 本期已还罚息，类型为String，格式为17位长度且保留2位小数，必填（Y表示必填），单位为元
     */
    private String termFintFinish;
    /**
     * 本期未还罚息，类型为String，格式为17位长度且保留2位小数，必填（Y表示必填），单位为元
     */
    private String noRetFin;
    /**
     * 本期应还保费，类型为String，格式为17位长度且保留2位小数，非必填（N表示非必填），含保险时必填，单位为元
     */
    private String termRetFee;
    /**
     * 本期已还保费，类型为String，格式为17位长度且保留2位小数，非必填（N表示非必填），含保险时必填，单位为元
     */
    private String feeAmt;
    /**
     * 本期未还保费，类型为String，格式为17位长度且保留2位小数，非必填（N表示非必填），含保险时必填，单位为元
     */
    private String noRetFee;
    /**
     * 本期应还融担费，类型为String，格式为17位长度且保留2位小数，非必填（N表示非必填），单位为元
     */
    private String termGuarantorFee;
    /**
     * 本期已还融担费，类型为String，格式为17位长度且保留2位小数，非必填（N表示非必填），单位为元
     */
    private String guarantorFee;
    /**
     * 本期未还融担费，类型为String，格式为17位长度且保留2位小数，非必填（N表示非必填），单位为元
     */
    private String noGuarantorFee;
    /**
     * 本期应还服务费，类型为String，格式为17位长度且保留2位小数，非必填（N表示非必填），单位为元
     */
    private String termServiceFee;
    /**
     * 本期已还服务费，类型为String，格式为17位长度且保留2位小数，非必填（N表示非必填），单位为元
     */
    private String serviceFee;
    /**
     * 本期未还服务费，类型为String，格式为17位长度且保留2位小数，非必填（N表示非必填），单位为元
     */
    private String noServiceFee;

    /**
     * 本期应还融担罚费  单位元
     */
    private String termOverdueGuarantorFee;
    /**
     * 本期已还融担罚费   单位元
     */
    private String overdueGuarantorFee;
    /**
     * 本期未还融担罚费  单位元
     */
    private String noOverdueGuarantorFee;
    /**
     * 计划状态，类型为String，长度为2，必填（Y表示必填），可选值：N - 正常，G - 宽限期，O - 逾期，L - 呆滞（逾期90天以上），B - 呆账（逾期180天以上）。若无呆滞呆账状态则作逾期状态返回。
     */
    private String termStatus;
    /**
     * 结清标志，类型为String，长度为10，必填（Y表示必填），可选值：RUNNING - 未结 ，CLOSE - 已结。
     */
    private String settleFlag;
    /**
     * 实际还款日，类型为String，长度为10，必填（Y表示必填），格式为yyyy-MM-dd格式
     */
    private String datePay;
    /**
     * 本期应还超次提现手续费，类型为String，格式为17位长度且保留2位小数，非必填（N表示非必填），单位为元
     */
    private String termRetOverFee;
    /**
     * 本期已还超次提现手续费，类型为String，格式为17位长度且保留2位小数，非必填（N表示非必填），单位为元
     */
    private String overFee;
    /**
     * 本期未还超次提现手续费，类型为String，格式为17位长度且保留2位小数，非必填（N表示非必填），单位为元
     */
    private String noRetOverFee;
    /**
     * 自定义费用名称，类型为String，长度为10，非必填（N表示非必填），用于前端页面展示费用名称，如“本期应还XXX”
     */
    private String otherFeeName;
    /**
     * 本期应还自定义费用，类型为String，格式为17位长度且保留2位小数，非必填（N表示非必填），单位为元
     */
    private String termOtherFee;
    /**
     * 本期已还自定义费用，类型为String，格式为17位长度且保留2位小数，非必填（N表示非必填），单位为元
     */
    private String retOtherFee;
    /**
     * 本期未还自定义费用，类型为String，格式为17位长度且保留2位小数，非必填（N表示非必填），单位为元
     */
    private String noRetOtherFee;
    /**
     * 机构账单号
     */
    private String billId;
    /**
     * 还款计划编号
     */
    private String planId;
}
