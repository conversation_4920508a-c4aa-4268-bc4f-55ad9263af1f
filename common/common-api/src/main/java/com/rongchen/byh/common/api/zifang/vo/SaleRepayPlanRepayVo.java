package com.rongchen.byh.common.api.zifang.vo;

import lombok.Data;

/**
 * @ClassName SaleRepayPlanRepay
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/10 10:29
 * @Version 1.0
 **/
@Data
public class SaleRepayPlanRepayVo {

    /**
     * 必填，还款期数，备注：用于表示还款对应的期数情况。
     */
    private String repayTerm;
    /**
     * 必填，本期应还总金额（不包含VIP减免），备注：格式为17位整数2位小数，单位为元，代表本期正常情况下应还的总金额。
     */
    private String totalAmt;
    /**
     * 必填，本期应还本金，备注：格式为17位整数2位小数，单位为元，代表本期按计划应还的本金金额。
     */
    private String termRetPrin;
    /**
     * 必填，本期已还本金，备注：格式为17位整数2位小数，单位为元，代表本期实际已经偿还的本金金额。
     */
    private String prinAmt;
    /**
     * 必填，VIP减免本金，备注：格式为17位整数2位小数，单位为元，代表因VIP身份等因素减免的本金金额。
     */
    private String vipDeratePrin;
    /**
     * 必填，减免本金，备注：格式为17位整数2位小数，单位为元，代表其他原因减免的本金金额。
     */
    private String deratePrin;
    /**
     * 必填，本期起日，备注：格式为yyyy-MM-dd，用于明确本期还款计算等相关业务对应的起始日期。
     */
    private String repayIntbDate;
    /**
     * 必填，本期止日，备注：格式为yyyy-MM-dd，用于明确本期还款计算等相关业务对应的截止日期。
     */
    private String repayInteDate;
    /**
     * 必填，还款起日，备注：格式为yyyy-MM-dd，用于确定还款周期开始的日期。
     */
    private String repayOwnbDate;
    /**
     * 必填，还款止日，备注：格式为yyyy-MM-dd，用于确定还款周期结束的日期。
     */
    private String repayOwneDate;
    /**
     * 非必填，还款成功时间，备注：当status等于1、2时必填，格式为yyyy-MM-dd HH:mm:ss，用于记录还款成功的具体时间点。
     */
    private String repaySuccTime;
    /**
     * 非必填，还款入账时间，备注：当status等于1、2或repayMode等于4时必填，格式为yyyy-MM-dd HH:mm:ss，用于记录还款入账的具体时间点。
     */
    private String repayFinishTime;
    /**
     * 非必填，还款状态，备注：取值有0（未还）、1（已还）、2（部分还款），用于体现当前还款的实际状态情况。
     */
    private String status;
    /**
     * 非必填，还款模式，备注：取值有0（线上还款）、1（线下还款）、4（退货，代表不再发起扣款），用于说明还款采用的具体模式。
     */
    private String repayMode;
    /**
     * 非必填，还款方式，备注：取值有CLEAN（全部结清）、OVERDUE（归还逾期）、CURRENT（归还当期到期）、OVER（归还到期，逾期 + 当期到期）、PREPAYMENT（提前还当期），用于明确还款的具体方式。
     */
    private String repayType;
    /**
     * 还款流水号
     */
    private String repayApplyNo;


}
