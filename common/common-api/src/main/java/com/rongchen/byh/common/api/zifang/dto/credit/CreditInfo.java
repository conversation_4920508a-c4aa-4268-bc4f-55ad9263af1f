package com.rongchen.byh.common.api.zifang.dto.credit;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CreditInfo {

    /**
     * 授信流水号
     */
    private String creditNo;

    /**
     * 用户编号
     */
    private String userId;

    /**
     * 借款人姓名
     */
    private String custName;

    /**
     * 身份证
     */
    private String idNo;

    /**
     * 授信金额，单位元，精确到小数点后两位
     */
    private String creditAmount;

    /**
     * 性别，“男” “女”
     */
    private String sex;

    /**
     * 国籍，中国
     */
    private String nationality;

    /**
     * 民族，例如：汉或汉族
     */
    private String nation;

    /**
     * 出生日期，yyyy-mm-dd
     */
    private String birthday;

    /**
     * 户籍省区域编码，行政区划编码
     */
    private String idProvinceCode;

    /**
     * 户籍市区域编码，行政区划编码
     */
    private String idCityCode;

    /**
     * 户籍区区域编码，行政区划编码
     */
    private String idAreaCode;

    /**
     * 户籍省
     */
    private String idProvince;

    /**
     * 户籍市
     */
    private String idCity;

    /**
     * 户籍区
     */
    private String idArea;

    /**
     * 户籍地址
     */
    private String idAddress;

    /**
     * 签发机关
     */
    private String signOrganization;

    /**
     * 有效期起始时间，yyyy-mm-dd
     */
    private String idValidDateBegin;

    /**
     * 有效期结束时间，yyyy-mm-dd，长期2999-12-31
     */
    private String idValidDateEnd;

    /**
     * 长期身份证，1：长期身份证 0：非长期
     */
    private String idLongTerm;

    /**
     * 手机号
     */
    private String phoneNo;

    /**
     * 居住省区域编码
     */
    private String liveProvinceCode;
    /**
     * 居住市区域编码
     */
    private String liveCityCode;
    /**
     * 居住区区域编码
     */
    private String liveAreaCode;
    /**
     * 居住省
     */
    private String liveProvince;
    /**
     * 居住市
     */
    private String liveCity;
    /**
     * 居住区
     */
    private String liveArea;
    /**
     * 详细居住地址
     */
    private String liveAddress;


    /**
     * 客户学历，取值我们的教育程度
     */
    private String custEducation;

    /**
     * 学位
     */
    private String degree;

    /**
     * 婚姻状况
     */
    private String marryType;

    /**
     * 子女状况
     */
    private String childrenStatus;

    /**
     * 职业，我们没有收集，建议填写为固定值为：企业白领
     */
    private String occupation;

    /**
     * 个人年收入，我们没有收集，建议填写为固定值为：？？？
     */
    private String salary;

    /**
     * 个人月收入，我们没有收集，建议填写为固定值为：？？？
     */
    private String income;

    /**
     * 单位名称，我们没有收集，建议填写为固定值为：？？？
     */
    private String companyName;

    /**
     * 单位所在省区域编码，行政区划编码，我们没有收集，建议填写为固定值为：？？？
     */
    private String companyProvinceCode;

    /**
     * 单位所在市区域编码，行政区划编码，我们没有收集，建议填写为固定值为：？？？
     */
    private String companyCityCode;

    /**
     * 单位所在区区域编码，行政区划编码，我们没有收集，建议填写为固定值为：？？？
     */
    private String companyAreaCode;

    /**
     * 单位所在省，我们没有收集，建议填写为固定值为：？？？
     */
    private String companyProvince;

    /**
     * 单位所在市，我们没有收集，建议填写为固定值为：？？？
     */
    private String companyCity;

    /**
     * 单位所在区，我们没有收集，建议填写为固定值为：？？？
     */
    private String companyArea;

    /**
     * 单位详细地址，我们没有收集，建议填写为固定值为：？？？
     */
    private String companyAddress;

    /**
     * 设备品牌
     */
    private String deviceBrand;

    /**
     * 设备网络类型，4G、offline
     */
    private String networkType;

    /**
     * 手机型号
     */
    private String devAlias;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * IP地址
     */
    private String clientIp;

    /**
     * 经纬度坐标系类型，wgs84/gcj02/bd09II
     */
    private String coordinateType;

    /**
     * 经度，至少精确到小数点后5位.
     */
    private String lng;

    /**
     * 维度，至少精确到小数点后5位.
     */
    private String lat;

    /**
     * 设备GPS定位城市，传城市名称，如无，传默认值：0
     */
    private String gpsCity;

    /**
     * LBS定位地址
     */
    private String lbsAddress;

    /**
     * GPS定位地址
     */
    private String gpsAddress;

    /**
     * 操作系统，Android\IOS
     */
    private String os;

    /**
     * 手机系统版本号
     */
    private String osVersion;

    /**
     * 人脸识别分数，可从腾讯云人脸识别获取
     */
    private String faceScore;

    /**
     * 人脸校验置信区间，例：70 可从腾讯云人脸识别获取
     */
    private String faceConfidence;

    /**
     * 人脸识别渠道，人脸识别机构，可以默认为腾讯云
     */
    private String faceSource;

    /**
     * 人脸完成时间，yyyy-MM-dd HH:mm:ss
     */
    private String faceTime;

    private List<ContactRelation> contactRelationLists;

    private List<PictureInfo> pictureList;

}
