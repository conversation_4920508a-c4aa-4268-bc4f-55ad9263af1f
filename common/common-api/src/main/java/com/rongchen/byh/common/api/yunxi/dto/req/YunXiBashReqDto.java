package com.rongchen.byh.common.api.yunxi.dto.req;

import lombok.Data;

/**
 * 项目名称：byh_java
 * 文件名称: YunXiBashReqDto
 * 创建时间: 2025-03-04 18:59
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.common.api.yunxi.dto.req
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
@Data
public class YunXiBashReqDto {
    /**
     * 应用ID
     */
    private String appId;


    /**
     * API编码
     */
    private String apiCode;

    /**
     * 版本号
     */
    private String version = "2.0";

    /**
     * 标签类型(是否关闭二级嵌套)
     */
    private String flagType = "Y";

    /**
     * 签名类型
     */
    private String signType = "MD5";
    /**
     * 时间戳
     */
    private String timestamp;
    /**
     * 签名
     */
    private String sign;

  

}
