package com.rongchen.byh.common.api.zifang.dto;

import lombok.Data;

/**
 * @ClassName RepaymentResultDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/9 16:55
 * @Version 1.0
 **/
@Data
public class RepaymentResultDto {

    /**
     * 用户编号，长度为32，此字段必填（Y表示必填），用于唯一标识具体的用户。
     */
    private String userId;
    /**
     * 还款申请流水，长度为32，此字段必填（Y表示必填），用于代表用户还款请求或线下还款接口里的还款申请流水，方便对还款申请操作进行追踪及后续处理。
     */
    private String repayApplyNo;


}
