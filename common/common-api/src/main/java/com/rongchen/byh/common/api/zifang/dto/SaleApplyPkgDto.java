package com.rongchen.byh.common.api.zifang.dto;

import lombok.Data;

/**
 * @ClassName SaleApplyPkgDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/9 18:12
 * @Version 1.0
 **/
@Data
public class SaleApplyPkgDto {
    /**
     * 必填，代表还款期数。
     */
    private String repayTerm;
    /**
     * 必填，自动扣款开始日期，备注：格式为yyyy-MM-dd。
     */
    private String repayOwnbDate;
    /**
     * 必填，自动扣款结束日期，备注：格式为yyyy-MM-dd。
     */
    private String repayOwneDate;
    /**
     * 必填，结息周期开始日期，备注：格式为yyyy-MM-dd，还款计划列表按此日期正序排列。
     */
    private String repayIntbDate;
    /**
     * 必填，结息周期截止日期，备注：格式为yyyy-MM-dd。
     */
    private String repayInteDate;
    /**
     * 必填，本期应还总金额，两位小数，备注：单位为元。
     */
    private String totalAmt;
    /**
     * 必填，本期应还本金，两位小数，备注：单位为元。
     */
    private String termRetPrin;
    /**
     * 必填，本期应还利息，两位小数，备注：单位为元。
     */
    private String termRetInt;
    /**
     * 必填，本期应还罚息，两位小数，备注：单位为元。
     */
    private String termRetFint;
}
