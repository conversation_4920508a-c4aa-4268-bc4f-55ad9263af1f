package com.rongchen.byh.common.api.zifang.vo;

import lombok.Data;

/**
 * @ClassName RepaymentPlanQueryPrivilegeVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/9 15:07
 * @Version 1.0
 **/
@Data
public class RepaymentPlanQueryPrivilegeVo {

    /**
     * 还款期数，数据类型为String，长度为3，此字段必填（Y表示必填）
     */
    private String repayTerm;
    /**
     * 到期时间，数据类型为String，长度为10，格式要求为yyyy-MM-dd，此字段必填（Y表示必填）
     */
    private String dueTime;
    /**
     * 本期应还总金额，数据类型为String，格式为17位整数2位小数，单位为元，此字段必填（Y表示必填）
     */
    private String totalAmt;
    /**
     * 本期应还本金，数据类型为String，格式为17位整数2位小数，单位为元，此字段必填（Y表示必填）
     */
    private String termRetPrin;
    /**
     * 本期已还本金，数据类型为String，格式为17位整数2位小数，单位为元，此字段必填（Y表示必填）
     */
    private String prinAmt;
    /**
     * 当前是否逾期，数据类型为String，长度为3，取值为0表示否，1表示是，此字段必填（Y表示必填）
     */
    private String isOverdue;
    /**
     * 本期还款状态，数据类型为String，长度为3，取值0表示未还款，1表示已还，此字段必填（Y表示必填）
     */
    private String repayStatus;
    /**
     * 实际还款时间，数据类型为String，长度为20，格式要求为yyyy-MM-dd HH:mm:ss，已还情况有效，此字段必填（Y表示必填）
     */
    private String repayTime;

}
