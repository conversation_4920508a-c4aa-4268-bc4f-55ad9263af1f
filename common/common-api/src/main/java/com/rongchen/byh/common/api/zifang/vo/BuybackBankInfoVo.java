package com.rongchen.byh.common.api.zifang.vo;

import lombok.Data;

/**
 * @ClassName BuybackBankInfoVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/9 17:11
 * @Version 1.0
 **/
@Data
public class BuybackBankInfoVo {

    /**
     * 响应码，必填，不同取值代表不同的响应情况，例如0000表示成功，1000表示未回购，2000表示未绑卡等。
     */
    private String responseCode;
    /**
     * 响应描述，非必填，用于对响应码对应的具体情况做进一步文字描述。
     */
    private String responseMsg;
    /**
     * 银行卡号，非必填，用于记录相关的银行卡信息（若有）。
     */
    private String bankNo;
    /**
     * 代扣通道编码，非必填，用于标识代扣通道（若有相关代扣操作时使用）。
     */
    private String payChannel;
    /**
     * 代扣协议号，非必填，但响应码为成功时必填，用于标识代扣相关的协议编号（在成功响应场景下需要提供）。
     */
    private String protocolCode;


}
