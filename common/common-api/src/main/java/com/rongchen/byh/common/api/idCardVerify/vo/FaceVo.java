package com.rongchen.byh.common.api.idCardVerify.vo;


import lombok.Data;

@Data
public class FaceVo {

    /**
     * 相似度。
     * - 取值范围 [0.00, 100.00]。
     * - 推荐相似度大于等于70时可判断为同一人，可根据具体场景自行调整阈值（阈值70的误通过率为千分之一，阈值80的误通过率是万分之一）。
     * 示例值：88.33
     */
    private Float sim;

    /**
     * 验证通过后的视频最佳截图照片。
     * - 照片为BASE64编码后的值，jpg格式。
     * 注意：此字段可能返回 null，表示取不到有效值。
     * 示例值：/9j/4AAQSkZJRg…..s97n//2Q==
     */
    private String bestFrameBase64;

    /**
     * 验证结果。
     */
    private String msg;
}
