package com.rongchen.byh.common.api.beiyihua.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 授信额度dto
 * @date 2025/3/26 18:18:26
 */
@Data
public class CreditPushDto {
    @Schema(description = "客户姓名")
    private String username;

    @Schema(description = "客户身份证号码")
    private String idCard;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "授信金额")
    private BigDecimal creditAmount;
}
