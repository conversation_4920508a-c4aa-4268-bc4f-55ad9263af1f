package com.rongchen.byh.common.api.yunxi.dto.req;

import java.io.Serializable;

import lombok.Data;

/**
 * 获取权益行权地址请求
 * API Code: AP002
 */
@Data
public class BenefitGatewayReqDto extends YunXiBashReqDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * API编码
     */
    public static final String API_CODE = "AP002";

    /**
     * 合作方用户编号
     */
    private String openId;

    /**
     * 合作方用户手机号
     */
    private String userMobile;

    /**
     * 权益平台单号
     */
    private String orderNum;

}