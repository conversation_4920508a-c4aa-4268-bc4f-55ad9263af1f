package com.rongchen.byh.common.api.riskControl.service.impl;

import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.common.api.riskControl.config.RiskControlProperties;
import com.rongchen.byh.common.api.riskControl.dto.CrmPushDto;
import com.rongchen.byh.common.api.riskControl.service.CrmPushService;
import com.rongchen.byh.common.api.riskControl.vo.CrmPushVo;
import com.rongchen.byh.common.api.tencentMap.dto.IpCityDto;
import com.rongchen.byh.common.api.tencentMap.service.IpCityService;
import com.rongchen.byh.common.api.tencentMap.vo.IpCityVo;
import com.rongchen.byh.common.core.util.ContextUtil;
import com.rongchen.byh.common.core.util.HttpUtil;
import com.rongchen.byh.common.core.util.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

/**
 * <AUTHOR>
 * @version 1.0
 * @description crm同步
 * @date 2024/12/11 15:06:26
 */
@Service
@Slf4j
public class CrmPushServiceImpl implements CrmPushService {
    @Resource
    private RiskControlProperties riskControlProperties;
    @Resource
    private IpCityService ipCityService;
    @Override
    public CrmPushVo push(CrmPushDto crmPushDto) {
        String dataStr = buildParam(crmPushDto);
        String res = HttpUtil.postJson(riskControlProperties.getCrmPushUrl(), dataStr);
        log.info("crm推送接口请求参数：{}，响应结果：{}", dataStr, res);
        return buildResult(res);
    }


    private String buildParam(CrmPushDto crmPushDto) {
        JSONObject reg = new JSONObject();
        reg.put("qdName",riskControlProperties.getCrmPushSource());
        reg.put("id_number", crmPushDto.getIdNumber());
        reg.put("name", crmPushDto.getUserName());
        reg.put("phone",crmPushDto.getMobile());
        reg.put("price",50000);
        reg.put("limit_amount", crmPushDto.getLimitAmount());
        reg.put("house",0);
        reg.put("car",0);
        reg.put("insurance",0);
        reg.put("fund",0);
        reg.put("socital",1);
        reg.put("xyk",0);
        reg.put("df",0);
        reg.put("business_license",0);
        if (StrUtil.isNotEmpty(crmPushDto.getIp())) {
            reg.put("ip", crmPushDto.getIp());
        }
//        handCity(reg);
        reg.put("city", "深圳市");
        reg.put("source", riskControlProperties.getCrmPushSource());
        reg.put("age", IdcardUtil.getAgeByIdCard(crmPushDto.getIdNumber()));
        reg.put("credit_rating", crmPushDto.getCreditRating());
        return encrypt(reg.toJSONString(), riskControlProperties.getCrmPushSecret(), riskControlProperties.getCrmPushSecret());
    }

    /**
     * 处理城市信息
     * @param reg
     */
    private void handCity(JSONObject reg) {
        IpCityDto ipCityDto = new IpCityDto();
        if (StrUtil.isNotEmpty(reg.getString("ip"))) {
            ipCityDto.setIp(reg.getString("ip"));
        } else {
            ipCityDto.setIp( "**************");
        }
        reg.put("city", "未知");
        IpCityVo ipCity = ipCityService.getIpCity(ipCityDto);
        if (ipCity.getCode() == 0) {
            reg.put("city", ipCity.getCity());
        }
    }

    private CrmPushVo buildResult(String res) {
        JSONObject result = JSONObject.parseObject(res);
        CrmPushVo crmPushVo = new CrmPushVo();
        crmPushVo.setMessage(result.getString("mes"));
        if (!"0".equals(result.getString("status"))) {
            crmPushVo.setResult(-1);
            return crmPushVo;
        }
        crmPushVo.setResult(0);
        return crmPushVo;
    }

    public static String encrypt(String content,String key , String iv) {
        try {
            byte[] raw = key.getBytes("utf-8");
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");//"算法/模式/补码方式"
            //使用CBC模式，需要一个向量iv，可增加加密算法的强度
            IvParameterSpec ips = new IvParameterSpec(iv.getBytes());
            cipher.init(Cipher.ENCRYPT_MODE, skeySpec, ips);
            byte[] encrypted = cipher.doFinal(content.getBytes());
            return Base64.getEncoder().encodeToString(encrypted);
        } catch (Exception e) {
            log.error("aes_加密失败",e);
        }
        return null;
    }

    public static void main(String[] args) {
        JSONObject reg = new JSONObject();
        reg.put("qdName","ds2");
        reg.put("name", "测试");
        reg.put("phone","***********");
        reg.put("price",50000);
        reg.put("house",1);
        reg.put("car",1);
        reg.put("insurance",1);
        reg.put("fund",1);
        reg.put("socital",1);
        reg.put("xyk",1);
        reg.put("df",1);
        reg.put("business_license",1);
        reg.put("city", "北京市");
        reg.put("source", "ds2");
        reg.put("age", 35);
        String encrypt = encrypt(reg.toJSONString(), "jwkjcy12345678dt", "jwkjcy12345678dt");
        String res = HttpUtil.postJson("http://gp.jimwitech.cn/Admin/UserIncomeApi/addUserAes", encrypt);
        log.info("crm推送接口请求参数：{}，响应结果：{}", encrypt, res);
    }
}
