package com.rongchen.byh.common.api.idCardVerify.service;

import com.rongchen.byh.common.api.idCardVerify.dto.FaceDto;
import com.rongchen.byh.common.api.idCardVerify.dto.FaceImgDto;
import com.rongchen.byh.common.api.idCardVerify.dto.FaceNewDto;
import com.rongchen.byh.common.api.idCardVerify.vo.FaceNewVo;
import com.rongchen.byh.common.api.idCardVerify.vo.FaceVo;

public interface FaceVerifyService {

    FaceVo faceVerify(FaceDto faceDto);

    /**
     * 照片人脸核身
     * @param faceImgDto
     * @return
     */
    FaceVo faceImgVerify(FaceImgDto faceImgDto);

    /**
     * 照片人脸核身
     * @param faceNewDto
     * @return
     */
    FaceNewVo faceImgVerifyNew(FaceNewDto faceNewDto);
}
