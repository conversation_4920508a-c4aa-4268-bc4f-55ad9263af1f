package com.rongchen.byh.common.api.zifang.service;


import com.rongchen.byh.common.api.zifang.dto.*;
import com.rongchen.byh.common.api.zifang.vo.*;
import com.rongchen.byh.common.api.zifang.vo.loan.RespSaleRepayPlanVo;
import com.rongchen.byh.common.core.object.ResponseResult;

/**
 * <AUTHOR>
 */
public interface OtherApi {


    /**
     * 2.8.4.赊销订单申请接口
     * @date 2024/12/9 14:34
     *
     * @param applyDto
     * @return java.lang.String
     */
    ResponseResult<SaleApplyVo> getSaleApply(SaleApplyDto applyDto);

    /**
     * 2.4.3.api赊销订单获取接口
     */
    ResponseResult<RespSaleRepayPlanVo> getApiSaleApply(SaleApplyDto applyDto);


    /**
     * 2.8.5.赊销订单结果查询接口
     * @date 2024/12/9 14:34
     *
     * @param resultDto
     * @return java.lang.String
     */
    ResponseResult<SaleResultVo> getSaleResult(SaleResultDto resultDto);


    /**
     * 2.8.6.赊销合同查询
     * @date 2024/12/9 14:34
     *
     * @param queryDto
     * @return java.lang.String
     */
    ResponseResult<SaleSignedQueryVo> getSaleSignedQuery(SaleSignedQueryDto queryDto);


    /**
     * api模式 赊销合同查询
     * @param queryDto
     * @return
     */
    ResponseResult<SaleSignedQueryVo> getApiSaleSignedQuery(SaleSignedQueryDto queryDto);


    /**
     * 赊销订单还款请求接口
     * @date 2024/12/9 14:34
     *
     * @param applyDto
     * @return java.lang.String
     */
    ResponseResult<SaleRepayApplyVo> getSaleRepayApply(SaleRepayApplyDto applyDto);


    /**
     * 2.8.8.赊销还款结果查询
     * @date 2024/12/9 14:34
     *
     * @param applyDto
     * @return java.lang.String
     */
    ResponseResult<SaleRepayResultVo> getSaleRepayResult(SaleRepayResultDto applyDto);


    /**
     * 2.8.10.赊销还款计划查询接口-合作方查询资金方赊销还款计划
     * @date 2024/12/10 14:34
     *
     * @param planDto
     * @return java.lang.String
     */
    ResponseResult<SaleRepayPlanVo> getSaleRepayPlan(SaleRepayPlanDto planDto);


    /**
     * 赊销还款退费接口
     * @date 2024/12/10 14:34
     *
     * @param feeDto
     * @return java.lang.String
     */
    ResponseResult<SaleRepayReturnFeeVo> getSaleRepayReturnFee(SaleRepayReturnFeeDto feeDto);


    /**
     * 赊销还款退费结果查询
     * @date 2024/12/10 14:34
     *
     * @param dto
     * @return java.lang.String
     */
    ResponseResult<SaleRefundResultVo> getSaleRefundResult(SaleRefundResultDto dto);


    /**
     * 2.8.19.H5页面获取接口
     * @date 2024/12/10 14:34
     *
     * @param dto
     * @return java.lang.String
     */
    ResponseResult<HfUrlVo> getH5Url(HfUrlDto dto);
}
