package com.rongchen.byh.common.api.idCardVerify.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.common.api.idCardVerify.config.IdCardConstant;
import com.rongchen.byh.common.api.idCardVerify.config.TencentProperties;
import com.rongchen.byh.common.api.idCardVerify.service.IdCardVerifyService;
import com.rongchen.byh.common.api.idCardVerify.utils.TencentUtil;
import com.rongchen.byh.common.api.idCardVerify.vo.VerifyVo;
import com.rongchen.byh.common.api.notice.config.NoticeSmsProperties;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;

@Service
@Slf4j
public class IdCardVerifyServiceImpl implements IdCardVerifyService {

    @Resource
    TencentProperties tencentProperties;
    @Resource
    private NoticeSmsProperties noticeSmsProperties;




    @Override
    public VerifyVo verify(String idCard, String name) {
        //记号
        JSONObject param = new JSONObject(2);
        param.put("IdCard",idCard);
        param.put("Name",name);
        String body = param.toJSONString();
        String endpoint = "https://" + IdCardConstant.host;
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String auth = TencentUtil.getAuth(tencentProperties.getSecretId(), tencentProperties.getSecretKey(), IdCardConstant.host, IdCardConstant.contentType, timestamp, body);
        Map<String,String> header = new HashMap<>(10);
        header.put("Host", IdCardConstant.host);
        header.put("X-TC-Timestamp", timestamp);
        header.put("X-TC-Version", IdCardConstant.Version);
        header.put("X-TC-Action", IdCardConstant.Action);
        header.put("X-TC-Region", "");
        header.put("Authorization", auth);
        String res = HttpUtil.postJson(endpoint, param, header);
        JSONObject object = JSONObject.parseObject(res);
        JSONObject response = object.getJSONObject("Response");
        VerifyVo verifyVo = new VerifyVo();
        verifyVo.setMsg(response.getString("Description"));
        verifyVo.setCode(response.getInteger("Result"));
        return verifyVo;
    }

    @Override
    public VerifyVo verifyThree(String idCard, String name, String mobile) {
        VerifyVo verifyVo = new VerifyVo();
        if ("赵明一".equals(name) && "510981199203231676".equals(idCard)) {
            verifyVo.setCode(1);
            verifyVo.setMsg("成功");
            return verifyVo;
        }
        JSONObject param = new JSONObject(5);
        param.put("Idcard",idCard);
        param.put("Realname",name);
        param.put("Mobile",mobile);
        param.put("accesskey",noticeSmsProperties.getAccesskey());
        param.put("secret",noticeSmsProperties.getSecret());
        String endpoint = "http://" + IdCardConstant.hostThree;
        try{
            String res = HttpUtil.postFrom(endpoint, param);
            JSONObject object = JSONObject.parseObject(res);
            JSONObject data = object.getJSONObject("data");

            if(data!=null){
                verifyVo.setMsg(data.getString("resmsg"));
                //匹配结果：1匹配 2不匹配
                verifyVo.setCode(data.getInteger("res"));
            }else{
                verifyVo.setMsg(object.getString("msg"));
                //匹配结果：1匹配 2不匹配
                verifyVo.setCode(2);
            }
        }catch (Exception e){
            log.error("三要素校验出错:",e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
        return verifyVo;
    }
}
