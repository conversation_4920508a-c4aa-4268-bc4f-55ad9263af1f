package com.rongchen.byh.common.api.baofu.adapter;

import com.alibaba.fastjson.JSON;
import com.rongchen.byh.common.api.baofu.constant.BaofuConstant;
import com.rongchen.byh.common.api.baofu.dto.rsp.ConfirmBindCardRspDto;
import com.rongchen.byh.common.api.baofu.dto.rsp.PreBindCardRspDto;
import com.rongchen.byh.common.api.baofu.dto.rsp.QueryBindCardRspDto;
import com.rongchen.byh.common.api.baofu.service.BaofuService;
import com.rongchen.byh.common.api.baofu.util.BaofuUtil;
import com.rongchen.byh.common.api.baofu.vo.req.BankCardBindReqVo;
import com.rongchen.byh.common.api.baofu.vo.req.BankCardConfirmReqVo;
import com.rongchen.byh.common.api.baofu.vo.req.DirectPayReqVo;
import com.rongchen.byh.common.api.baofu.vo.rsp.BaoFUBankCardProtocolRspVo;
import com.rongchen.byh.common.api.baofu.vo.rsp.DirectPayRspVo;
import com.rongchen.byh.common.api.zifang.dto.BindBankSmsDto;
import com.rongchen.byh.common.api.zifang.dto.QueryBindBankResultDto;
import com.rongchen.byh.common.api.zifang.dto.VerifyBindBankSmsDto;
import com.rongchen.byh.common.api.zifang.vo.BindBankSmsVo;
import com.rongchen.byh.common.api.zifang.vo.LoanElementVo;
import com.rongchen.byh.common.api.zifang.vo.QueryBindBankResultVo;
import com.rongchen.byh.common.api.zifang.vo.VerifyBindBankSmsVo;
import com.rongchen.byh.common.api.zifang.vo.loanElement.PayChannel;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 宝付接口适配器
 * 负责将宝付的接口转换为系统现有的接口格式
 */
@Slf4j
@Service
public class BaofuAdapterService {

    @Resource
    private BaofuService baofuService;

    /**
     * 获取支付通道信息
     *
     * @return 支付通道响应
     */
    public ResponseResult<LoanElementVo> getPayChannel() {
        log.info("开始获取支付通道信息");
        LoanElementVo vo = new LoanElementVo();
        // 设置宝付通道
        List<PayChannel> payChannelList = new ArrayList<>();
        PayChannel channel = new PayChannel();
        channel.setCode("BAOFU");
        channel.setName("宝付支付");
        payChannelList.add(channel);
        vo.setPayChannelList(payChannelList);

        log.info("获取支付通道信息完成, 返回数据: {}", JSON.toJSONString(vo));
        return ResponseResult.success(vo);
    }

    /**
     * 获取绑卡验证码
     *
     * @param dto 绑卡验证码请求
     * @return 绑卡验证码响应
     */
    public ResponseResult<BindBankSmsVo> getBindCardSms(BindBankSmsDto dto) {
        log.info("开始获取绑卡验证码, 请求参数: {}", JSON.toJSONString(dto));
        try {
            // 1. 将BindBankSmsDto转换为BankCardBindReqVo
            BankCardBindReqVo bindReqVo = new BankCardBindReqVo();
            bindReqVo.setUserId(dto.getUserId());
            bindReqVo.setBankCardNo(dto.getBankCardNum());
            bindReqVo.setMobile(dto.getPhoneNo());
            bindReqVo.setCardholderName(dto.getCustName());
            bindReqVo.setIdCardNo(dto.getIdNo());
            bindReqVo.setIdCardType("01"); // 默认身份证
            bindReqVo.setCardType("101"); // 默认借记卡
            log.info("转换后的宝付请求参数: {}", JSON.toJSONString(bindReqVo));

            // 2. 调用宝付预绑卡接口
            log.info("开始调用宝付预绑卡接口");
            PreBindCardRspDto preBindResult = baofuService.preBindCard(bindReqVo);
            log.info("宝付预绑卡接口调用完成, 返回结果: {}", JSON.toJSONString(preBindResult));

            // 4. 转换为系统响应格式
            BindBankSmsVo response = new BindBankSmsVo();
            response.setMessageNo(preBindResult.getUnique_code());

            // 根据宝付返回码设置响应码
            if (BaofuConstant.BIZ_CODE_SUCCESS.equals(preBindResult.getBiz_resp_code())) {
                response.setResponseCode("0000"); // 成功
                response.setResponseMsg("获取验证码成功");
                log.info("获取绑卡验证码成功, 返回数据: {}", JSON.toJSONString(response));
            } else {
                response.setResponseCode("9999"); // 失败
                response.setResponseMsg(preBindResult.getBiz_resp_msg());
                log.warn("获取绑卡验证码失败, 宝付返回错误信息: {}", preBindResult.getBiz_resp_msg());
            }

            return ResponseResult.success(response);
        } catch (Exception e) {
            log.error("获取绑卡验证码异常, 请求参数: {}, 异常信息: ", JSON.toJSONString(dto), e);
            return ResponseResult.error(ErrorCodeEnum.FAILED_TO_INVOKE_THIRDPARTY_URL, "获取绑卡验证码失败");
        }
    }

    /**
     * 验证绑卡验证码
     *
     * @param dto 验证绑卡请求
     * @return 验证绑卡响应
     */
    public ResponseResult<VerifyBindBankSmsVo> verifyBindCardSms(VerifyBindBankSmsDto dto) {
        log.info("开始验证绑卡验证码, 请求参数: {}", JSON.toJSONString(dto));
        try {
            // 1. 将VerifyBindBankSmsDto转换为BankCardConfirmReqVo
            BankCardConfirmReqVo confirmReqVo = new BankCardConfirmReqVo();
            confirmReqVo.setUniqueCode(dto.getMessageNo());
            confirmReqVo.setSmsCode(dto.getPhoneCode());
            log.info("转换后的宝付确认绑卡请求参数: {}", JSON.toJSONString(confirmReqVo));

            // 2. 调用宝付确认绑卡接口
            log.info("开始调用宝付确认绑卡接口");
            ConfirmBindCardRspDto confirmResult = baofuService.confirmBindCard(confirmReqVo);
            log.info("宝付确认绑卡接口调用完成, 返回结果: {}", JSON.toJSONString(confirmResult));

            // 3. 转换为系统响应格式
            VerifyBindBankSmsVo response = new VerifyBindBankSmsVo();
            response.setMessageNo(dto.getMessageNo());
            response.setChannel("BAOFU");

            // 根据宝付返回码设置响应码
            if (BaofuConstant.BIZ_CODE_SUCCESS.equals(confirmResult.getBiz_resp_code())) {
                response.setResponseCode("0000"); // 成功
                response.setResponseMsg("验证成功");
                response.setAgreementNumber(confirmResult.getProtocol_no());
                response.setNeedSmsCode("N"); // 不需要再次发送验证码
                response.setBindStatus("VALID");
                log.info("验证绑卡验证码成功, 返回数据: {}", JSON.toJSONString(response));
            } else {
                response.setResponseCode("9999"); // 失败
                response.setResponseMsg(confirmResult.getBiz_resp_msg());
                response.setBindStatus("FREEZE");
                log.warn("验证绑卡验证码失败, 宝付返回错误信息: {}", confirmResult.getBiz_resp_msg());
            }

            return ResponseResult.success(response);
        } catch (Exception e) {
            log.error("验证绑卡验证码异常, 请求参数: {}, 异常信息: ", JSON.toJSONString(dto), e);
            return ResponseResult.error(ErrorCodeEnum.FAILED_TO_INVOKE_THIRDPARTY_URL, "验证绑卡验证码失败");
        }
    }

    /**
     * 查询绑卡结果
     *
     * @param dto 查询绑卡结果请求
     * @return 查询绑卡结果响应
     */
    public ResponseResult<QueryBindBankResultVo> queryBindCardResult(QueryBindBankResultDto dto) {
        log.info("开始查询绑卡结果, 请求参数: {}", JSON.toJSONString(dto));
        try {
            // 获取用户ID和银行卡号
            String uniqueCode = dto.getMessageNo();
            log.info("获取到的uniqueCode: {}", uniqueCode);

            // 获取用户ID和银行卡号
            String userId = dto.getUserId();
            String bankCardNo = dto.getBankCardNo();
            log.info("查询绑卡使用的用户ID: {}, 银行卡号: {}", userId, bankCardNo);

            // 调用宝付查询绑卡接口
            log.info("开始调用宝付查询绑卡接口");
            QueryBindCardRspDto queryResult = baofuService.queryBindCard(userId, bankCardNo);
            log.info("宝付查询绑卡接口调用完成, 返回结果: {}", JSON.toJSONString(queryResult));

            // 转换为系统响应格式
            QueryBindBankResultVo response = new QueryBindBankResultVo();
            response.setChannel("BAOFU");

            // 根据宝付返回码设置响应码
            if (BaofuConstant.BIZ_CODE_SUCCESS.equals(queryResult.getBiz_resp_code())) {
                response.setResponseCode("0000"); // 成功
                response.setResponseMsg("查询成功");
                log.info("查询绑卡结果成功, 返回数据: {}", JSON.toJSONString(response));
                // 解析协议列表
                List<BaoFUBankCardProtocolRspVo> protocolList = BaofuUtil.parseProtocols(queryResult.getProtocols());
                response.setBaofuBankCardInfoList(protocolList);
            } else if (BaofuConstant.BIZ_CODE_PROCESSING.equals(queryResult.getBiz_resp_code())) {
                response.setResponseCode("P00001"); // 处理中
                response.setResponseMsg("处理中，请稍后再试");
                log.info("查询绑卡结果处理中, 返回数据: {}", JSON.toJSONString(response));
            } else {
                response.setResponseCode("9999"); // 失败
                response.setResponseMsg(queryResult.getBiz_resp_msg());
                log.warn("查询绑卡结果失败, 宝付返回错误信息: {}", queryResult.getBiz_resp_msg());
            }

            return ResponseResult.success(response);
        } catch (Exception e) {
            log.error("查询绑卡结果异常, 请求参数: {}, 异常信息: ", JSON.toJSONString(dto), e);
            return ResponseResult.error(ErrorCodeEnum.FAILED_TO_INVOKE_THIRDPARTY_URL, "查询绑卡结果失败");
        }
    }

    public ResponseResult<DirectPayRspVo> directPay(DirectPayReqVo reqVo) {
        log.info("开始执行宝付代扣 请求参数: {}", JSON.toJSONString(reqVo));
        try {
            DirectPayRspVo rspVo = baofuService.directPay(reqVo);
            log.info("宝付代扣完成 返回结果: {}", JSON.toJSONString(rspVo));
            return ResponseResult.success(rspVo);
        } catch (Exception e) {
            log.error("宝付代扣异常, 请求参数: {}, 异常信息: ", JSON.toJSONString(reqVo), e);
            return ResponseResult.error(ErrorCodeEnum.FAILED_TO_INVOKE_THIRDPARTY_URL, "宝付代扣异常:"+e);
        }
    }
}
