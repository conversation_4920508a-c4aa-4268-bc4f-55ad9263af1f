package com.rongchen.byh.common.api.zifang.vo;

import lombok.Data;

/**
 * @ClassName RepaymentApplyVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/9 16:13
 * @Version 1.0
 **/
@Data
public class RepaymentApplyVo {

    /**
     * 还款申请流水，数据类型为String，长度为32，此字段必填（Y表示必填）。
     * 作为业务幂等字段。
     */
    private String repayApplyNo;
    /**
     * 响应码，数据类型为String，长度为4，此字段必填（Y表示必填）。
     */
    private String responseCode;
    /**
     * 响应消息，数据类型为String，长度最大可为128，此字段非必填（N表示非必填）。
     */
    private String responseMsg;
    /**
     * 短信是否发送成功，数据类型为String，长度为4，此字段非必填（N表示非必填）。
     * 取值为01表示发送成功，取值为02表示发送失败。
     */
    private String success;

}
