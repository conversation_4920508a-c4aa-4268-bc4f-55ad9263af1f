package com.rongchen.byh.common.api.zifang.dto;

import lombok.Data;

/**
 * @ClassName SaleRepayApplyDetailDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/9 19:10
 * @Version 1.0
 **/
@Data
public class SaleRepayApplyDetailDto {

    /**
     * 必填，还款期数，备注：用于表示单期的相关情况，用于明确还款对应的期数。
     */
    private String repayterm;
    /**
     * 必填，当期还款本金，备注：格式为17位整数2位小数，单位为元，代表当期需要偿还的本金金额数目。
     */
    private String printAmt;
    /**
     * 必填，当期还款利息，备注：格式为17位整数2位小数，单位为元，代表当期需要偿还的利息金额数目。
     */
    private String intAmt;
    /**
     * 非必填，当期还款罚息，备注：格式为17位整数2位小数，单位为元，代表当期需要偿还的罚息金额数目（若有）。
     */
    private String forfeitAmt;
    /**
     * 非必填，当期还款融担费，备注：格式为17位整数2位小数，单位为元，代表当期需要偿还的融担费金额数目（若有）。
     */
    private String guarantorFee;
    /**
     * 非必填，当期还款服务费，备注：格式为17位整数2位小数，单位为元，代表当期需要偿还的服务费金额数目（若有）。
     */
    private String serviceAmt;
    /**
     * 必填，减免本金，备注：格式为17位整数2位小数，单位为元，代表当期减免本金的具体金额数目。
     */
    private String deratePrin;
    /**
     * 必填，减免利息，备注：格式为17位整数2位小数，单位为元，代表当期减免利息的具体金额数目。
     */
    private String derateInt;
    /**
     * 非必填，减免罚息，备注：格式为17位整数2位小数，单位为元，代表当期减免罚息的金额数目（若有）。
     */
    private String derateForfeit;
    /**
     * 非必填，减免融担费，备注：格式为17位整数2位小数，单位为元，代表当期减免融担费的金额数目（若有）。
     */
    private String derateGuarantor;
    /**
     * 非必填，减免服务费，备注：格式为17位整数2位小数，单位为元，代表当期减免服务费的金额数目（若有）。
     */
    private String derateService;
    /**
     * 非必填，违约金，备注：格式为17位整数2位小数，单位为元，代表当期需要支付的违约金金额数目（若有）。
     */
    private String breachFee;

}
