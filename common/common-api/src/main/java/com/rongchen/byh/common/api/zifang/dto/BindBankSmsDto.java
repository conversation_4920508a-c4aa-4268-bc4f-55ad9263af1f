package com.rongchen.byh.common.api.zifang.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName BindBankSMSDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/6 19:19
 * @Version 1.0
 **/
@Data
public class BindBankSmsDto {

    /**
     *进件单号
     */
    @Schema(description = "进件单号" , hidden = true)
    private String creditNo;

    /**
     *交易流水号
     */
    @Schema(description = "交易流水号" , hidden = true)
    private String serialNo;

    /**
     *场景
     *
     * 03 还款绑卡
     * 04 贷前绑
     */
    @Schema(description = "场景 03 还款绑卡   04 贷前绑卡" , hidden = true)
    private String scene;

    /**
     *贷款编号
     *
     * 精确到小数点后两位
     * 当scene=03 该字段必填
     */
//    private String loanNo;

    /**
     *身份证
     */
    @Schema(description = "身份证")
    private String idNo;

    /**
     *借款人姓名
     */
    @Schema(description = "借款人姓名")
    private String custName;

    /**
     *手机号
     */
    @Schema(description = "手机号")
    private String phoneNo;

    /**
     *用户编号
     */
    @Schema(description = "用户编号" , hidden = true)
    private String userId;

    /**
     * 代扣通道
     */
    @Schema(description = "代扣通道")
    private String payChannel;

    /**
     *银行卡号
     */
    @Schema(description = "银行卡号")
    private String bankCardNum;

    /**
     *银行编码
     */
    @Schema(description = "开户行")
    private String bankName;

    /**
     *货款编号
     */
    @Schema(description = "货款编号")
    private String loanNo;

}
