package com.rongchen.byh.common.api.zifang.vo;

import lombok.Data;

/**
 * @ClassName PreRepayApplyVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/9 14:44
 * @Version 1.0
 **/
@Data
public class PreRepayApplyVo {

    /**
     * 交易流水号
     */
    private String merserno;

    /**
     * 响应码
     */
    private String responseCode;

    /**
     * 响应消息
     */
    private String responseMsg;

    /**
     * 还款状态
     */
    private String status;

    /**
     * 本次还款本金
     */
    private String payNormAmt;

    /**
     * 本次还款利息
     */
    private String payInteAmt;

    /**
     * 本次还款罚息
     */
    private String payEnteAmt;

    /**
     * 手续费/违约金
     */
    private String fee;

    /**
     * 还款总额
     */
    private String payTotalAmt;

    /**
     * 还款日期
     */
    private String payDate;

    /**
     * 融担费
     */
    private String guarantorAmt;

    /**
     * 服务费
     */
    private String serviceAmt;

    /**
     * 融担罚费用
     */
    private String overdueGuarantorFee;

}
