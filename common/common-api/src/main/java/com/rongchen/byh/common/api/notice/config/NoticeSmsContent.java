package com.rongchen.byh.common.api.notice.config;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 短信通知内容
 * @date 2024/12/12 12:07:55
 */
public class NoticeSmsContent {
    /**
     * 扣款失败。
     */
    public static final int DEDUCATIONFAIL = 308276;
    /**
     * 还款通知。
     */
    public static final int REPAYMENTNOTICE = 308275;
    /**
     * 放款通知。
     */
    public static final int LOANNOTICE = 308274;
    /**
     * 放款通过
     */
    public static final int APPROVALPASSED = 308273;
    /**
     * 到期前3天还款提醒
     */
    public static final int THREEDAYREPAYNOTICE = 100000;
    /**
     * 当日到期还款提醒
     */
    public static final int CURRENTREPAYNOTICE = 100001;
    /**
     * 放款通过
     */
    public static final int OVERDUENOTICE = 100002;
    /**
     * 复审通过
     */
    public static final int REVIEWPASS = 100003;

    /**
     * 复审通过 短信平台2
     */
    public static final int REVIEWPASSJSON = 100003;

    /**
     * 发送空中放款模式支用h5短信
     */
    public static final int SENDAIRH5 = 100004;

    public static final Map<Object, String> DICT_MAP = new HashMap<>(9);

    static {
        DICT_MAP.put(DEDUCATIONFAIL, "尊敬的客户{1}，很抱歉，您申请的借款编号为{2}于{3}扣款失败，本期应还金额为人民币{4}元，剩余人民币{5}元未还，请确保还款账户余额充足且账户状态正常，以便系统扣款成功，逾期将会对您的征信记录产生不良影响。");
        DICT_MAP.put(REPAYMENTNOTICE, "尊敬的客户，您申请的借款单号为{1}已于{2}还款人民币{3}元。");
        DICT_MAP.put(LOANNOTICE, "尊敬的客户{1}，您申请的借款单号后四位为{2}的贷款，已成功放款到您尾号{3}的银行卡上，放款金额为人民币{4}元，贷款期限为{5}个月, 每月还款日为{6}号。请您按时足额还款，以免对您的征信记录产生不良影响。");
        DICT_MAP.put(APPROVALPASSED, "尊敬的客户{1}，恭喜您，已成功获得申请的人民币{2}元的借款额度。请登录App提现到您的银行卡。");
        DICT_MAP.put(THREEDAYREPAYNOTICE, "尊敬的客户{userName}，您的贷款本期应还{periodAmount}元，扣款日为{deductionDate}，请至少提前一天将上述款项存入尾数为{bankAccount}的还款账户或登录APP自助还款。扣款不足额产生逾期本息，须报送金融信用信息基础数据库。本金额仅供参考，如已存足，请忽略本信息。");
        DICT_MAP.put(CURRENTREPAYNOTICE, "尊敬的客户{userName}，今天是您的还款日，您的贷款本期应还{periodAmount}元，扣款日为{deductionDate}，请至少提前一天将上述款项存入尾数为{bankAccount}的还款账户或登录APP自助还款。扣款不足额产生逾期本息，须报送金融信用信息基础数据库。本金额仅供参考，如已存足，请忽略本信息。");
        DICT_MAP.put(OVERDUENOTICE, "尊敬的客户{userName}，您的借款单号后四位为{contractNum}的借款截至目前已逾期{overdueDays}天，已产生新增滞纳金及不良信用记录，当前应还款金额为人民币{periodAmount}元，请尽快还款以恢复您的信用记录。");
        DICT_MAP.put(REVIEWPASS, "尊敬的{userName}，恭喜您，已成功获得申请的人民币{loanAmount}元的借款额度。请下载App提现。下载地址：http://d.qyc8.xyz");
        DICT_MAP.put(REVIEWPASSJSON, "【七叶草】尊敬的{userName}，恭喜您获得人民币{loanAmount}元的借款额度。请下载App提现。下载地址:http://d.qyc8.xyz。拒收请回复R");
        DICT_MAP.put(SENDAIRH5, "【七叶草】尊敬的客户{userName}，恭喜您，已成功申请{loanAmount}元的借款额度。请点击链接提现http://dx26.cn/N3JgrN。拒收请回复R");
    }

    /**
     * 判断参数是否为当前常量字典的合法值。
     *
     * @param value 待验证的参数值。
     * @return 合法返回true，否则false。
     */
    public static boolean isValid(Integer value) {
        return value != null && DICT_MAP.containsKey(value);
    }
}
