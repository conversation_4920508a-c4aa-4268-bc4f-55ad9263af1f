package com.rongchen.byh.common.api.zifang.dto;

import lombok.Data;

/**
 * @ClassName HFUrlDto
 * @Description H5页面获取接口
 * <AUTHOR>
 * @Date 2024/12/10 12:15
 * @Version 1.0
 **/
@Data
public class HfUrlDto {

    /**
     * 必填，用户编号，备注：用于唯一标识具体的用户，在多个业务场景中关联到对应的用户主体。
     */
    private String userId;
    /**
     * 非必填，贷款编号，备注：在还款、权益兑换场景下必填，用于明确具体的贷款业务相关信息，方便业务操作关联对应的贷款记录。
     */
    private String loanNo;
    /**
     * 非必填，授信编号，备注：在借款场景下必填，用于标识对应的授信情况，与借款操作紧密相关，便于进行相关业务逻辑处理。
     */
    private String creditNo;
    /**
     * 必填，类型，备注：取值有LOAN（借款）、REPAY（还款）、RIGHTS（权益兑换），用于明确此次操作所属的具体业务类型。
     */
    private String type;
    /**
     * 非必填，回调地址，备注：用于在相关业务操作完成后进行回调的网络地址信息（若有需要）。
     */
    private String fromH5;
    /**
     * 非必填，用户手机号，备注：用于在相关业务操作完成后进行回调的网络地址信息（若有需要）。
     */
    private String userMobile;
    /**
     * 赊销订单号
     */
    private String saleNo;
    /**
     * 外部赊销订单号
     */
    private String outSaleNo;
}
