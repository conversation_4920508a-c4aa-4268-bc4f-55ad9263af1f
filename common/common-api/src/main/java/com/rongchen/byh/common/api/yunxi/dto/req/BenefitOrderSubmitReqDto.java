package com.rongchen.byh.common.api.yunxi.dto.req;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 权益订单购买提交请求
 * API Code: AP101
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BenefitOrderSubmitReqDto extends YunXiBashReqDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * API编码
     */
    public static final String API_CODE = "AP101";

    /**
     * 权益券包号
     */
    private String couponPackageId;

    /**
     * 合作方用户编号，须保证唯一性
     */
    private String openId;

    /**
     * 合作方用户手机号（已AES加密）
     */
    private String userMobile;

    /**
     * 支付方式
     * BAOFU_PROXY_PAY-宝付
     * SUNING_PROXY_PAY-苏宁
     */
    private String payWay;

    /**
     * 支付流水号
     */
    private String paymentNo;

    /**
     * 合作方订单号
     */
    private String externalOrderNum;

    /**
     * 订单金额，单位元
     */
    private String orderAmount;

    /**
     * 订单期数
     * 为空或1时，权益方认为该订单为全额支付
     * 大于1时，权益方认为该订单分多期收款，需要调取权益订单还款通知
     */
    private Integer orderPeriods;
}