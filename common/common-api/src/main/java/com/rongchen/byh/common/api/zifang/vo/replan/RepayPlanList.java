package com.rongchen.byh.common.api.zifang.vo.replan;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class RepayPlanList {

    /**
     * 总金额
     */
    @Schema(description = "总金额")
    private String amt;

    /**
     * 当前期数
     */
    @Schema(description = "当前期数")
    private String period;

    /**
     * 应还款日 yyyy-MM-dd
     */
    @Schema(description = "应还款日 yyyy-MM-dd")
    private String dateDue;

    /**
     * 本金
     */
    @Schema(description = "本金")
    private String prcpAmt;

    /**
     * 利息
     */
    @Schema(description = "利息")
    private String intAmt;

    /**
     * 融担费
     */
    @Schema(description = "融担费")
    private String guarAmt;

    /**
     * 服务费
     */
    @Schema(description = "服务费")
    private String serviceAmt;

    /**
     * 服务费
     */
    @Schema(description = "赊销费")
    private String saleAmt;
}
