package com.rongchen.byh.common.api.zifang.vo;

import com.rongchen.byh.common.api.yunxi.vo.YunXiOrderDetailVo;
import lombok.Data;

/**
 * @ClassName SaleResultVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/9 18:35
 * @Version 1.0
 **/
@Data
public class SaleResultVo {

    /**
     * 必填，响应码，用于标识请求处理后的状态，取值为0000表示成功。
     */
    private String responseCode;
    /**
     * 必填，备注信息，用于对业务相关情况进行说明。
     */
    private String responseMsg;
    /**
     * 必填，订单结果，体现订单最终的处理结果情况。
     */
    private String orderResult;
    /**
     * 非必填，结果描述，可对订单结果做更详细文字解释。
     */
    private String resultDesc;
    /**
     * 非必填，放款成功时间，当loanResult为06时必须填写，格式为yyyy-MM-dd HH:mm:ss，用于记录放款成功的具体时间。
     */
    private String loanSuccTime;
    /**
     * 非必填，到账时间，当loanResult为10时必须填写，格式为yyyy-MM-dd HH:mm:ss，用于记录资金到账的具体时间。
     */
    private String loanArrivalTime;
    /**
     * 非必填，资方订单号，放款成功时必须有，代表绿信订单号，用于标识对应的资方订单。
     */
    private String contractId;
    /**
     * 非必填，商品名称，用于记录对应的商品名称（若有）。
     */
    private String goodsName;
    /**
     * 非必填，商品编码，用于标识对应的商品编码（若有）。
     */
    private String goodsCode;
    /**
     * 非必填，商品失效时间，格式为yyyy-MM-dd HH:mm:ss，用于记录商品失效的具体时间（若有）。
     */
    private String goodsOutTime;
    /**
     * 非必填，承保方，可选值有LXBL（绿信保理）、ZKBBL（中凯博保理）、DYRD（鼎一融担），用于表示对应的承保主体（若有）。
     */
    private String insurer;

    /**
     * 外部赊销订单号
     */
    private String saleNo;

    /**
     * 赊销订单状态
     */
    private String orderStatus;

    private YunXiOrderDetailVo yunXiOrderDetailVo;
}
