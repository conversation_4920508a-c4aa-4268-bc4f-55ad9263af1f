package com.rongchen.byh.common.api.zifang.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.util.ArrayList;
import java.util.Collections;

/**
 * @ClassName FenZhuanAesUtils
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/1 12:29
 * @Version 1.0
 **/
public class FenZhuanAesUtils {

   //* 加密算法 *//*
    private static final String ALGORITHM = "AES/CBC/PKCS5Padding";
    static {
        //* AES 加密默认 128 位的 key，这里改成 256 位的 *//*
//        UnlimitedKeyStrengthJurisdictionPolicy.ensure();
    }
    /**
     *
     * @param srcData 要加密的数组（String 需要 base64 编码）
     * @param key 公钥，32 位 byte 数组
     * @param iv 私钥，16 位 byte 数组
     * @return 加密后的 byte 数组
     * @throws Exception 找不到加密算法等
     */
    public static byte[] encrypt(byte[] srcData, byte[] key, byte[] iv) throws Exception {
        Cipher.getMaxAllowedKeyLength(ALGORITHM);
        SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, keySpec, new IvParameterSpec(iv));
        return cipher.doFinal(srcData);
    }
    public static String encryptStr(String srcDataStr, String keyStr, String ivStr) throws Exception {
        String key = keyStr.length() < 16 ? fillZero(keyStr) : keyStr;
        byte[] iv = new byte[16];
        System.arraycopy(ivStr.getBytes("UTF-8"), 0, iv, 0, iv.length);
        return removeRNT(Base64.encodeBase64String(encrypt(srcDataStr.getBytes("UTF-8"), key.getBytes("UTF-8"), iv)));
    }

    public static String fillZero(String keyStr) {
        int length = keyStr.length();
        length = 16 - length;
        for (int i = 0; i < length; i++) {
            keyStr = "0" + keyStr;
        }
        return keyStr;
    }
    /**
     *
     * @param encData 要解密的数组
     * @param key 公钥
     * @param iv 私钥
     * @return 解密后的 byte 数组
     * @throws Exception 找不到解密算法等
     */
    public static byte[] decrypt(byte[] encData, byte[] key, byte[] iv) throws Exception {
        Cipher.getMaxAllowedKeyLength(ALGORITHM);
        SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, keySpec, new IvParameterSpec(iv));
        return cipher.doFinal(encData);
    }
    public static String decryptStr(String srcDataStr, String keyStr, String ivStr) throws Exception {
        String key = keyStr.length() < 16 ? fillZero(keyStr) : keyStr;
        byte[] iv = new byte[16];
        System.arraycopy(ivStr.getBytes("UTF-8"), 0, iv, 0, iv.length);
        return new String(decrypt(Base64.decodeBase64(srcDataStr), key.getBytes("UTF-8"), iv), "UTF-8");
    }

    private static String removeRNT(String originalStr) {
        if (originalStr == null || originalStr.isEmpty()) {
            return originalStr;
        }
        return originalStr.replaceAll("[\t\n\r]","");
    }
    public static void main(String[] args) throws Exception {
        String appId = "";
//        String md5Key = "";
//        String encryptStr = AESWrapCipher.AES256.encryptStr("手机号码", appId, md5Key);
//        System.out.println(encryptStr);
//        String phone = AESWrapCipher.AES256.decryptStr(encryptStr, appId, md5Key);
//        System.out.println(phone);
        String s = "{\"timestamp\":1656501448659,\"apiCode\":\"UC001\",\"appId\":\"220610116055\",\"version\":\"1.0\",\"phone\":\"A73E60708CFE9C9DF1F20D4DBCEB37A4\",\"signType\":\"MD5\"}";
        JSONObject jsonObject = JSONObject.parseObject(s);
//        JSONObject sort = sort(jsonObject);
//        System.out.println(sort.keySet());
    }




}
