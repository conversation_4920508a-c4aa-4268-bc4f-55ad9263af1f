package com.rongchen.byh.common.api.zifang.vo.loan;

import lombok.Data;

/**
 * 赊销还款计划
 */
@Data
public class RepayPlanListVo {
    private String repayTerm; // 还款期数
    private String totalAmt; // 本期应还总金额（不包含VIP减免）
    private String termRetPrin; // 本期应还本金
    private String prinAmt; // 本期已还本金
    private String vipDeratePrin; // VIP减免本金
    private String deratePrin; // 减免本金
    private String repayIntbDate; // 本期起日
    private String repayInteDate; // 本期止日
    private String repayOwnbDate; // 还款起日
    private String repayOwneDate; // 还款止日
    private String repaySuccTime; // 还款成功时间
    private String repayFinishTime; // 还款入账时间
    private String status; // 还款状态
    private String repayApplyNo;//还款申请流水
}
