package com.rongchen.byh.common.api.zifang.vo;

import lombok.Data;

import java.util.List;

/**
 * @ClassName SaleSignedQueryVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/9 18:45
 * @Version 1.0
 **/
@Data
public class SaleSignedQueryVo {

    /**
     * 必填，响应码，用于标识请求处理后的状态，取值为0000表示成功。
     */
    private String responseCode;
    /**
     * 必填，备注信息，用于对业务相关情况进行说明。
     */
    private String responseMsg;

    List<SaleSignedQueryContractVo> contractVoList;

    List<SaleSignedQueryContractVo> contractLists;

}
