package com.rongchen.byh.common.api.zifang.service.fenzhuan;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.common.api.yunxi.config.YunXiApiConfig;
import com.rongchen.byh.common.api.yunxi.config.YunXiPayWay;
import com.rongchen.byh.common.api.yunxi.dto.YunXiApiRspDto;
import com.rongchen.byh.common.api.yunxi.dto.req.BenefitGatewayReqDto;
import com.rongchen.byh.common.api.yunxi.dto.req.BenefitOrderQueryReqDto;
import com.rongchen.byh.common.api.yunxi.dto.req.BenefitOrderRepaymentReqDto;
import com.rongchen.byh.common.api.yunxi.dto.req.BenefitOrderSubmitQkReqDto;
import com.rongchen.byh.common.api.yunxi.service.YunXiApiService;
import com.rongchen.byh.common.api.yunxi.vo.YunXiOrderDetailVo;
import com.rongchen.byh.common.api.zifang.constant.RepayStatusConstant;
import com.rongchen.byh.common.api.zifang.dto.*;
import com.rongchen.byh.common.api.zifang.service.*;
import com.rongchen.byh.common.api.zifang.service.mayi.config.FenZhuanConstant;
import com.rongchen.byh.common.api.zifang.utils.AreaCodeUtil;
import com.rongchen.byh.common.api.zifang.utils.CityCodeUtil;
import com.rongchen.byh.common.api.zifang.utils.FenZhuanAesUtils;
import com.rongchen.byh.common.api.zifang.utils.ProvinceCodeUtil;
import com.rongchen.byh.common.api.zifang.vo.*;
import com.rongchen.byh.common.api.zifang.vo.contraact.ContractList;
import com.rongchen.byh.common.api.zifang.vo.loan.RespRepayPlanVo;
import com.rongchen.byh.common.api.zifang.vo.loan.RespSaleRepayPlanVo;
import com.rongchen.byh.common.api.zifang.vo.replan.RepayPlanList;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025/3/2 15:25:39
 */
@Slf4j
@Service
public class FenZhuanService extends AbstractFenZhuanService
        implements BindCardApi, CapitalApi, ContractApi, LoanApi, OtherApi, RepaymentApi {

    @Resource
    private YunXiApiService yunXiApiService;

    @Resource
    private YunXiApiConfig yunXiApiConfig;

    @Override
    public ResponseResult<UsableBankListVo> getUsableBankList(UsableBankListDto bankListDto) {
        JSONObject params = new JSONObject();
        params.put("orderNo", bankListDto.getOrderNo());
        UsableBankListVo usableBankListVo = new UsableBankListVo();
        try {
            JSONObject jsonObject = this.fenZhuanSendRequest(params, FenZhuanConstant.getUsableBankList);
            List<BankCardListsVo> list = jsonObject.getJSONArray("bankList").toJavaList(BankCardListsVo.class);
            usableBankListVo.setBankCardListsVos(list);
            usableBankListVo.setResponseCode(jsonObject.getString("rspCode"));
            usableBankListVo.setResponseMsg(jsonObject.getString("rspMsg"));
            return ResponseResult.success(usableBankListVo);
        } catch (Exception e) {
            log.error("分转查询支持银行列表查询接口,异常：{}", e.getMessage());
            return ResponseResult.error(ErrorCodeEnum.FAIL);
        }
    }

    @Override
    public ResponseResult<BindBankSmsVo> getBindBankSMS(BindBankSmsDto bindBankSmsDto) {
        JSONObject params = new JSONObject();
        params.put("orderNo", bindBankSmsDto.getCreditNo());
        params.put("bindRequestNo", bindBankSmsDto.getSerialNo());
        params.put("name", bindBankSmsDto.getCustName());
        params.put("idCardNo", bindBankSmsDto.getIdNo());
        params.put("phone", bindBankSmsDto.getPhoneNo());
        params.put("bankCardNo", bindBankSmsDto.getBankCardNum());
        BindBankSmsVo bindBankSmsVo = new BindBankSmsVo();
        try {
            JSONObject jsonObject = this.fenZhuanSendRequest(params, FenZhuanConstant.getBindBankSMS);
            bindBankSmsVo.setResponseCode(jsonObject.getString("rspCode"));
            bindBankSmsVo.setResponseMsg(jsonObject.getString("rspMsg"));
            if (bindBankSmsVo.getResponseCode().equals("0000")) {
                JSONObject data = jsonObject.getJSONObject("data");
                bindBankSmsVo.setMessageNo(data.getString("bindRequestNo"));
                bindBankSmsVo.setBindStatus(data.getString("bindStatus"));
                bindBankSmsVo.setBindId(data.getString("bankCardBindId"));
            }
            return ResponseResult.success(bindBankSmsVo);
        } catch (Exception e) {
            log.error("分转 绑定银行卡接口 接口,异常：{}", e.getMessage());
            return ResponseResult.error(ErrorCodeEnum.FAIL);
        }
    }

    @Override
    public ResponseResult<VerifyBindBankSmsVo> getVerifyBindBankSMSUrl(VerifyBindBankSmsDto bindBankSmsDto) {
        JSONObject params = new JSONObject();
        params.put("orderNo", bindBankSmsDto.getCreditNo());
        params.put("bindRequestNo", bindBankSmsDto.getSerialNo());
        params.put("verifyCode", bindBankSmsDto.getPhoneCode());
        VerifyBindBankSmsVo verifyBindBankSmsVo = new VerifyBindBankSmsVo();
        try {
            JSONObject jsonObject = this.fenZhuanSendRequest(params, FenZhuanConstant.getVerifyBindBankSMSUrl);
            // {"apiCode":"AP811","appId":"****************","rspCode":"9998","rspMsg":"0009-签约银行卡不存在","sign":"3845B98A19D79E4BA1EC9BCDB384EDD3","signType":"MD5","timestamp":*************,"version":"1.0"}
            verifyBindBankSmsVo.setResponseCode(jsonObject.getString("rspCode"));
            verifyBindBankSmsVo.setResponseMsg(jsonObject.getString("rspMsg"));
            JSONObject data = jsonObject.getJSONObject("data");
            if (data != null) {
                verifyBindBankSmsVo.setMessageNo(data.getString("bindRequestNo"));
                verifyBindBankSmsVo.setBindStatus(data.getString("bindStatus"));
                verifyBindBankSmsVo.setBankCardBindId(data.getString("bankCardBindId"));
            }
            return ResponseResult.success(verifyBindBankSmsVo);
        } catch (Exception e) {
            log.error("分转 提交验证码接口 接口,异常：{}", e.getMessage());
            return ResponseResult.error(ErrorCodeEnum.FAIL);
        }
        // todo 绑卡接口
    }

    @Override
    public ResponseResult<QueryBindBankResultVo> getQueryBindBankResult(QueryBindBankResultDto bindBankSmsDto) {
        JSONObject params = new JSONObject();
        params.put("orderNo", bindBankSmsDto.getOrderNo());
        params.put("bankCardNo", bindBankSmsDto.getBankCardNo());
        params.put("phone", bindBankSmsDto.getPhone());
        params.put("idCardNo", bindBankSmsDto.getIdCardNo());
        params.put("name", bindBankSmsDto.getName());
        params.put("bindRequestNos", Arrays.asList(bindBankSmsDto.getMessageNo()));
        QueryBindBankResultVo queryBindBankResultVo = new QueryBindBankResultVo();
        try {
            JSONObject jsonObject = this.fenZhuanSendRequest(params, FenZhuanConstant.getQueryBindBankResult);
            if ("VALID".equals(jsonObject.getJSONObject("data").getString("bindStatus"))) {
                queryBindBankResultVo.setResponseCode("0000");
                return ResponseResult.success(queryBindBankResultVo);
            }
            queryBindBankResultVo.setResponseCode("P00001");
            queryBindBankResultVo.setResponseMsg(jsonObject.getString("rspMsg"));
            return ResponseResult.success(queryBindBankResultVo);
        } catch (Exception e) {
            log.error("分转 绑卡信息查询接口 接口,异常：{}", e.getMessage());
            return ResponseResult.error(ErrorCodeEnum.FAIL);
        }
    }

    @Override
    public ResponseResult<ApplyCheckVo> applyCheck(ApplyCheckDto applyCheckDto) {
        JSONObject params = new JSONObject();
        params.put("collidParameter",
                DigestUtil.md5Hex(applyCheckDto.getPhoneNo(), StandardCharsets.UTF_8.name()).toUpperCase());
        params.put("collidType", 5);
        ApplyCheckVo applyCheckVo = new ApplyCheckVo();
        try {
            JSONObject jsonObject = this.fenZhuanSendRequest(params, FenZhuanConstant.getApplyCheck);
            if (jsonObject != null && "0000".equals(jsonObject.getString("rspCode"))
                    && jsonObject.getJSONObject("data").getInteger("isCanLoan") == 1) {
                applyCheckVo.setResponseCode("0000");
                applyCheckVo.setResponseMsg("rejectReasons");
                return ResponseResult.success(applyCheckVo);
            }
            applyCheckVo.setResponseCode("9999");
            applyCheckVo.setResponseMsg("rejectReasons");
            return ResponseResult.success(applyCheckVo);
        } catch (Exception e) {
            log.error("分转 撞库接口 接口,异常：{}", e.getMessage());
            return ResponseResult.error(ErrorCodeEnum.FAIL);
        }
    }

    @Override
    public ResponseResult<CreditApplyVo> creditApply(CreditApplyDto creditApplyDto) {
        JSONObject userBaseInfo = new JSONObject();
        JSONObject liveInfo = new JSONObject();
        JSONObject positionInfo = new JSONObject();
        JSONObject identityInfo = new JSONObject();
        JSONObject applyInfo = new JSONObject();
        JSONArray contactsInfo = new JSONArray();
        JSONObject companyInfo = new JSONObject();
        JSONObject deviceInfo = new JSONObject();
        try {
            userBaseInfo.put("phone", FenZhuanAesUtils.encryptStr(creditApplyDto.getPhoneNo(),
                    fenZhuanProperties.getFenZhuanAesKey(), fenZhuanProperties.getFenZhuanIv()));
            userBaseInfo.put("name", FenZhuanAesUtils.encryptStr(creditApplyDto.getCustName(),
                    fenZhuanProperties.getFenZhuanAesKey(), fenZhuanProperties.getFenZhuanIv()));
            userBaseInfo.put("documentType", 0);
            userBaseInfo.put("idcard", FenZhuanAesUtils.encryptStr(creditApplyDto.getIdNo(),
                    fenZhuanProperties.getFenZhuanAesKey(), fenZhuanProperties.getFenZhuanIv()));
            userBaseInfo.put("marriage", creditApplyDto.getMarryType());
            userBaseInfo.put("education", creditApplyDto.getEducation());
            userBaseInfo.put("yearIncome", creditApplyDto.getIncome() * 12);
            userBaseInfo.put("liveProvinceCode", ProvinceCodeUtil.getCodeByName(creditApplyDto.getProvince()));
            userBaseInfo.put("liveProvince", creditApplyDto.getProvince());
            userBaseInfo.put("liveCityCode", CityCodeUtil.getCodeByName(creditApplyDto.getCity()));
            userBaseInfo.put("liveCity", creditApplyDto.getCity());
            userBaseInfo.put("liveDistrictCode",
                    AreaCodeUtil.getCodeByName(creditApplyDto.getCity() + "_" + creditApplyDto.getArea()));
            userBaseInfo.put("liveDistrict", creditApplyDto.getArea());
            userBaseInfo.put("liveAddr", creditApplyDto.getAddress());
            liveInfo.put("faceImg", creditApplyDto.getLiveUrl());
            liveInfo.put("faceScore", creditApplyDto.getFaceScore());
            liveInfo.put("faceStatus", "PASS");
            liveInfo.put("faceTime", creditApplyDto.getFaceTime());

            identityInfo.put("name", FenZhuanAesUtils.encryptStr(creditApplyDto.getCustName(),
                    fenZhuanProperties.getFenZhuanAesKey(), fenZhuanProperties.getFenZhuanIv()));
            identityInfo.put("idcard", FenZhuanAesUtils.encryptStr(creditApplyDto.getIdNo(),
                    fenZhuanProperties.getFenZhuanAesKey(), fenZhuanProperties.getFenZhuanIv()));
            identityInfo.put("gender", "男".equals(creditApplyDto.getSex()) ? 1 : 2);
            identityInfo.put("nation", creditApplyDto.getNation());
            identityInfo.put("idcardAddr", creditApplyDto.getIdcardAddress());
            identityInfo.put("office", creditApplyDto.getSignOrganization());
            identityInfo.put("startTime", creditApplyDto.getIdValidDateBegin());
            identityInfo.put("endTime", creditApplyDto.getIdValidDateEnd());
            identityInfo.put("idcardFrontImg", creditApplyDto.getIdCardFrontUrl());
            identityInfo.put("idcardBackImg", creditApplyDto.getIdCardBackUrl());
            applyInfo.put("amount", creditApplyDto.getCreditAmount());
            applyInfo.put("periods", 12);
            applyInfo.put("periodsType", 2);
            // TODO 借款用途
            applyInfo.put("loanPurpose", "消费");
            // TODO 产品编号 产品码：联系 ?
            applyInfo.put("loanProduct", "BP008");

            JSONObject con1 = new JSONObject();
            con1.put("linkmanType", 1);
            con1.put("name", creditApplyDto.getContactName1());
            con1.put("phone", creditApplyDto.getContactPhoneNo1());
            con1.put("relationship", creditApplyDto.getContactRelation1());
            contactsInfo.add(con1);
            // TODO 关系映射
            JSONObject con2 = new JSONObject();
            con2.put("linkmanType", 2);
            con2.put("name", creditApplyDto.getContactName2());
            con2.put("phone", creditApplyDto.getContactPhoneNo2());
            con2.put("relationship", creditApplyDto.getContactRelation2());
            contactsInfo.add(con2);

            // TODO 公司信息暂无
            companyInfo.put("companyName" , "广州市泰富信通科技有限公司");
            if (creditApplyDto.getCompanyName() != null) {
                companyInfo.put("companyName" , creditApplyDto.getCompanyName());
            }
            companyInfo.put("companyTel" , "");
            companyInfo.put("companyProvince" , "");
            companyInfo.put("companyCity" , "");
            companyInfo.put("companyDistrict" , "");
            companyInfo.put("companyAddress" , "");
            companyInfo.put("companyIndustry" , "Z");
            companyInfo.put("companyNatureType" , "300");
            companyInfo.put("professional" , 1);
            if (creditApplyDto.getProfessional() != null) {
                companyInfo.put("professional" , creditApplyDto.getProfessional());
            }
            deviceInfo.put("osType", creditApplyDto.getOs().toLowerCase());
            deviceInfo.put("deviceBrand", creditApplyDto.getDeviceBrand());
            deviceInfo.put("deviceModel", creditApplyDto.getDevAlias());
            positionInfo.put("applyY", creditApplyDto.getLng());
            positionInfo.put("applyX", creditApplyDto.getLat());
            positionInfo.put("applyLoc", creditApplyDto.getGpsAddress());

        } catch (Exception e) {
            log.error("分转空指针异常：", e);
            throw new RuntimeException(e);
        }
        JSONObject params = new JSONObject();
        params.put("orderNo", creditApplyDto.getOrderNo());
        params.put("userBaseInfo", JSONObject.toJSONString(userBaseInfo));
        params.put("liveInfo", JSONObject.toJSONString(liveInfo));
        params.put("identityInfo", JSONObject.toJSONString(identityInfo));
        params.put("applyInfo", JSONObject.toJSONString(applyInfo));
        params.put("contactsInfo", JSONObject.toJSONString(contactsInfo));
        params.put("companyInfo", JSONObject.toJSONString(companyInfo));
        params.put("deviceInfo", JSONObject.toJSONString(deviceInfo));
        params.put("positionInfo", JSONObject.toJSONString(positionInfo));
        // TODO 通知地址
        params.put("notifyUrl", FenZhuanConstant.CREDIT_NOTICE_URL);

        try {
            JSONObject jsonObject = this.fenZhuanSendRequest(params, FenZhuanConstant.getApplyRegister);
            CreditApplyVo applyVo = new CreditApplyVo();
            applyVo.setResponseCode(jsonObject.getString("rspCode"));
            log.info("分转 进件接口 接口,返回：{}", jsonObject);
            return ResponseResult.success(applyVo);
        } catch (Exception e) {
            log.error("分转 进件接口 接口,异常：{}", e.getMessage());
            return ResponseResult.error(ErrorCodeEnum.FAIL);
        }
    }

    @Override
    public ResponseResult<CreditAuditVo> getCreditAudit(CreditAuditDto creditAuditDto) {
        return null;
    }

    @Override
    public ResponseResult<ApiGateWayListVo> apiGateWay(ApiGateWayDto apiGateWayDto) {
        return null;
    }

    @Override
    public ResponseResult<ContractListVo> contractListQuery(ContractListDto contractListDto) {
        JSONObject params = new JSONObject();
        params.put("orderNo", contractListDto.getOrderNo());
        String scene = contractListDto.getScene();
        switch (scene) {
            case "01":
                params.put("scene", "CREDIT");
                break;
            case "02":
                params.put("scene", "MAKELOAN");
                break;
            case "03":
                params.put("scene", "BINDCARD");
                break;
            case "04":
                params.put("scene", "EQUITY");
                break;
            default:
                params.put("scene", "CREDIT");
        }
        try {
            JSONObject result = this.fenZhuanSendRequest(params, FenZhuanConstant.fenZhuanProtocolCode);
            ContractListVo applyVo = new ContractListVo();
            applyVo.setResponseCode(result.getString("rspCode"));
            if ("0000".equals(applyVo.getResponseCode())) {
                JSONObject data = result.getJSONObject("data");
                List<JSONObject> list = JSONObject.parseArray(data.getString("agreementList"), JSONObject.class);
                List<ContractList> contractListList = list.stream().map(item -> {
                    ContractList contractList = new ContractList();
                    contractList.setContractName(item.getString("agreementName"));
                    contractList.setContractUrl(item.getString("agreementUrl"));
                    return contractList;
                }).collect(Collectors.toList());
                applyVo.setContractLists(contractListList);
            }
            return ResponseResult.success(applyVo);
        } catch (Exception e) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<ContractSignedQueryVo> contractSignedQuery(ContractSignedQueryDto contractSignedQueryDto) {
        return null;
    }

    @Override
    public ResponseResult<LoanElementVo> loanElement(LoanElementDto loanElementDto) {
        return null;
    }

    @Override
    public ResponseResult<CreditApplyVo> loanApply(LoanApplyDto loanApplyDto) {
        JSONObject params = new JSONObject();
        params.put("orderNo", loanApplyDto.getCreditNo());
        params.put("loanRequestNo", loanApplyDto.getLoanNo());
        params.put("loanAmount", loanApplyDto.getApplyAmount());
        params.put("loanPeriod", loanApplyDto.getApplyTerm());
        // params.put("bindRequestNos" ,
        // JSONObject.toJSONString(Arrays.asList(loanApplyDto.getSerialNo())));
        params.put("bankCardBindIds", JSONObject.toJSONString(Arrays.asList(loanApplyDto.getSerialNo())));
        params.put("notifyUrl", FenZhuanConstant.LOAN_NOTICE_URL);
        try {
            CreditApplyVo applyVo = new CreditApplyVo();
            JSONObject result = this.fenZhuanSendRequest(params, FenZhuanConstant.fenZhuanCreditApplyCode);
            applyVo.setResponseCode(result.getString("rspCode"));
            applyVo.setResponseMsg(result.getString("rspMsg"));
            if (result.getString("rspCode").equals("0000")) {
                applyVo.setLoanOrderNo(result.getJSONObject("data").getString("loanOrderNo"));
            }
            return ResponseResult.success(applyVo);
        } catch (Exception e) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<UseLoanQueryVo> useLoanQuery(UseLoanQueryDto useLoanQueryDto) {
        return null;
    }

    @Override
    public ResponseResult<RepayPlanCalcVo> repayPlanCalc(RepayPlanCalcDto repayPlanCalcDto) {
        JSONObject params = new JSONObject();
        params.put("orderNo", repayPlanCalcDto.getOrderNo());
        params.put("trialAmount", repayPlanCalcDto.getAmount());
        try {
            JSONObject result = this.fenZhuanSendRequest(params, FenZhuanConstant.fenZhuanRepayPlanCalcCode);
            RepayPlanCalcVo planCalcVo = new RepayPlanCalcVo();
            planCalcVo.setResponseCode(result.getString("rspCode"));
            if ("0000".equals(planCalcVo.getResponseCode())) {
                BigDecimal totalInt = BigDecimal.ZERO;
                BigDecimal totalPrcp = BigDecimal.ZERO;
                BigDecimal totalGuar = BigDecimal.ZERO;
                BigDecimal totalService = BigDecimal.ZERO;
                BigDecimal totalSale = BigDecimal.ZERO;
                JSONObject data = result.getJSONObject("data");

                JSONArray productTrialResponses = data.getJSONArray("productTrialResponses");
                // todo 确认是哪个产品
                JSONObject responsesJSONObject = productTrialResponses.getJSONObject(0);
                JSONObject trialResponse = responsesJSONObject.getJSONObject("trialResponse");
                JSONArray repayPlan = trialResponse.getJSONArray("repayPlan");
                List<RepayPlanList> repayPlanList = new ArrayList<>();
                for (int i = 0; i < repayPlan.size(); i++) {
                    JSONObject plan = repayPlan.getJSONObject(i);
                    totalInt = totalInt.add(plan.getBigDecimal("repayInterest"));
                    totalService = totalService.add(plan.getBigDecimal("repaySvcFee"));
                    totalGuar = totalGuar.add(plan.getBigDecimal("repayGuaranteeFee"));
                    totalPrcp = totalPrcp.add(plan.getBigDecimal("repayInterest"));
                    RepayPlanList planList = new RepayPlanList();
                    planList.setAmt(plan.getString("repayAmount"));
                    planList.setPeriod(plan.getString("periodNo"));
                    planList.setDateDue(plan.getString("repayTime"));
                    planList.setPrcpAmt(plan.getString("repayPrincipal"));
                    planList.setIntAmt(plan.getString("repayInterest"));
                    planList.setGuarAmt(plan.getString("repayGuaranteeFee"));
                    planList.setServiceAmt(plan.getString("repaySvcFee"));
                    // todo 赊销费用
                    planList.setSaleAmt("0");
                    repayPlanList.add(planList);
                }
                planCalcVo.setTotalAmt(trialResponse.getString("repayAmount"));
                planCalcVo.setTotalPrcp(totalPrcp.toPlainString());
                planCalcVo.setTotalInt(totalInt.toPlainString());
                planCalcVo.setTotalGuar(totalGuar.toPlainString());
                planCalcVo.setTotalService(totalService.toPlainString());
                planCalcVo.setTotalSale(totalSale.toPlainString());
                planCalcVo.setRepayPlanList(repayPlanList);
            }

            return ResponseResult.success(planCalcVo);
        } catch (Exception e) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<SaleApplyVo> getSaleApply(SaleApplyDto applyDto) {
        log.info("【云樨-权益赊销】订单申请接口开始 - 借款单号：{}", applyDto.getLoanNo());
        try {
            SaleApplyVo saleApplyVo = new SaleApplyVo();
            // 默认返回成功
            saleApplyVo.setResponseCode("0000");
            if (StrUtil.isBlank(applyDto.getPaymentNo())) {
                log.info("【云樨-权益赊销】订单申请接口 - 借款单号：{}，未找到支付订单号，无法申请赊销或者当前不允许申请赊销，不是先享后付", applyDto.getLoanNo());
                saleApplyVo.setResponseMsg("无法申请赊销或者当前不允许申请赊销");
                return ResponseResult.success(saleApplyVo);
            }
            // 创建权益订单购买请求
            BenefitOrderSubmitQkReqDto orderRequest = new BenefitOrderSubmitQkReqDto();
            // 设置基本信息
            orderRequest.setExternalOrderNum(applyDto.getSaleNo());
            orderRequest.setPaymentNo(applyDto.getPaymentNo());
            // 转换期数信息
            int periods = 3; // 默认3期
            if (applyDto.getApplyTerm() != null) {
                try {
                    periods = Integer.parseInt(applyDto.getApplyTerm());
                } catch (NumberFormatException e) {
                    log.warn("【云樨-权益赊销】期数转换异常，使用默认期数", e);
                }
            }
            orderRequest.setOrderPeriods(periods);

            // 设置订单金额 - 从赊销申请总金额中获取
            BigDecimal totalAmount = BigDecimal.ZERO;
            if (applyDto.getApplyAmt() != null) {
                try {
                    totalAmount = new BigDecimal(applyDto.getApplyAmt());
                } catch (NumberFormatException e) {
                    log.warn("【云樨-权益赊销】金额转换异常，使用默认金额", e);
                }
            }

            orderRequest.setOrderAmount(totalAmount);
            orderRequest.setUserMobile(applyDto.getMobile());
            orderRequest.setOpenId(applyDto.getUserId());



            // 设置支付方式 - 默认使用分期付款
            orderRequest.setPayWay(YunXiPayWay.BAOFU_PROXY_PAY);

            // 调用云樨API创建权益订单
            YunXiApiRspDto<YunXiOrderDetailVo> yunXiResult = yunXiApiService.submitBenefitOrderQk(orderRequest);

            // 转换响应结果
            SaleApplyVo result = saleApplyVo;
            if (yunXiResult.isSuccess()) {
                YunXiOrderDetailVo orderDetail = yunXiResult.getData();
                result.setYunXiOrderDetail(orderDetail);
                result.setResponseCode("0000");
                result.setResponseMsg("成功");
                result.setSaleNo(orderDetail.getOrderNum());
                return ResponseResult.success(result);
            } else {
                log.error("【云樨-权益赊销】订单申请失败 - 借款单号：{}, 错误码：{}, 错误信息：{}",
                        applyDto.getLoanNo(), yunXiResult.getRspCode(), yunXiResult.getRspMsg());
                result.setResponseCode(yunXiResult.getRspCode());
                result.setResponseMsg(yunXiResult.getRspMsg());
                return ResponseResult.success(result);
            }
        } catch (Exception e) {
            log.error("【云樨-权益赊销】订单申请异常：", e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, "权益赊销订单申请异常");
        }
    }


    @Override
    public ResponseResult<SaleResultVo> getSaleResult(SaleResultDto resultDto) {
        log.info("【云樨-权益赊销】订单结果查询接口开始 - 订单号：{}", resultDto.getSaleNo());
        try {
            // 创建权益订单查询请求
            BenefitOrderQueryReqDto queryRequest = new BenefitOrderQueryReqDto();
            queryRequest.setOrderNum(resultDto.getOutSaleNo());

            // 调用云樨API查询订单
            YunXiApiRspDto<YunXiOrderDetailVo> yunXiResult = yunXiApiService.queryBenefitOrder(queryRequest);

            // 转换响应结果
            SaleResultVo result = new SaleResultVo();
            if (yunXiResult.isSuccess()) {
                YunXiOrderDetailVo orderDetail = yunXiResult.getData();
                result.setResponseCode("0000");
                result.setResponseMsg("成功");

                result.setYunXiOrderDetailVo(orderDetail);

                // 转换订单状态
                String orderStatus = orderDetail.getOrderStatus();
                if ("PAY_SUCCESS".equals(orderStatus)) {
                    result.setOrderStatus("SUCCESS");
                } else if ("PAY_FAIL".equals(orderStatus)) {
                    result.setOrderStatus("FAIL");
                } else if ("PAY_ING".equals(orderStatus)) {
                    result.setOrderStatus("DOING");
                } else if ("REFUND_ING".equals(orderStatus)) {
                    result.setOrderStatus("REFUND_ING");
                } else if ("REFUND_SUCCESS".equals(orderStatus)) {
                    result.setOrderStatus("REFUND_SUCCESS");
                } else if ("REFUND_FAIL".equals(orderStatus)) {
                    result.setOrderStatus("REFUND_FAIL");
                } else {
                    result.setOrderStatus("UNKNOWN");
                }

                result.setSaleNo(orderDetail.getOrderNum());
                return ResponseResult.success(result);
            } else {
                log.error("【云樨-权益赊销】订单结果查询失败 - 订单号：{}, 错误码：{}, 错误信息：{}",
                        resultDto.getSaleNo(), yunXiResult.getRspCode(), yunXiResult.getRspMsg());
                result.setResponseCode(yunXiResult.getRspCode());
                result.setResponseMsg(yunXiResult.getRspMsg());
                return ResponseResult.success(result);
            }
        } catch (Exception e) {
            log.error("【云樨-权益赊销】订单结果查询异常：", e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, "权益赊销订单结果查询异常");
        }
    }

    @Override
    public ResponseResult<SaleSignedQueryVo> getSaleSignedQuery(SaleSignedQueryDto queryDto) {
        log.info("【云樨-权益赊销】合同查询接口开始 - 订单号：{}", queryDto.getSaleNo());
        return null;
    }

    @Override
    public ResponseResult<SaleRepayApplyVo> getSaleRepayApply(SaleRepayApplyDto applyDto) {
        log.info("【云樨-权益赊销】还款请求接口开始 - 订单号：{}, 还款金额：{}",
                applyDto.getSaleNo(), applyDto.getAmount());

        try {
            // 创建权益订单还款通知请求
            BenefitOrderRepaymentReqDto repayRequest = new BenefitOrderRepaymentReqDto();
            repayRequest.setExternalOrderNum(applyDto.getSaleNo());
            repayRequest.setRepayAmt(applyDto.getRepayList().get(0).getRepayAmt());
            repayRequest.setPaymentNo(applyDto.getRepayApplyNo());
            repayRequest.setPayWay(YunXiPayWay.BAOFU_PROXY_PAY);

            // 调用云樨API通知还款
            YunXiApiRspDto<Void> yunXiResult = yunXiApiService.notifyBenefitOrderRepayment(repayRequest);

            // 转换响应结果
            SaleRepayApplyVo result = new SaleRepayApplyVo();
            if (yunXiResult.isSuccess()) {
                result.setResponseCode("0000");
                result.setResponseMsg("成功");
                result.setRepayApplyNo(applyDto.getRepayApplyNo());
            } else {
                log.error("【云樨-权益赊销】还款请求失败 - 订单号：{}, 错误码：{}, 错误信息：{}",
                        applyDto.getSaleNo(), yunXiResult.getRspCode(), yunXiResult.getRspMsg());
                result.setResponseCode(yunXiResult.getRspCode());
                result.setResponseMsg(yunXiResult.getRspMsg());
            }
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("【云樨-权益赊销】还款请求异常：", e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, "权益赊销还款请求异常");
        }
    }

    @Override
    public ResponseResult<SaleRepayResultVo> getSaleRepayResult(SaleRepayResultDto applyDto) {
        return null;
    }

    @Override
    public ResponseResult<SaleRepayPlanVo> getSaleRepayPlan(SaleRepayPlanDto planDto) {
        log.info("【权益赊销】还款计划查询接口开始 - 订单号：{}", planDto.getSaleNo());
        return null;
    }

    @Override
    public ResponseResult<SaleRepayReturnFeeVo> getSaleRepayReturnFee(SaleRepayReturnFeeDto feeDto) {
        return null;
    }

    @Override
    public ResponseResult<SaleRefundResultVo> getSaleRefundResult(SaleRefundResultDto dto) {
        return null;
    }

    @Override
    public ResponseResult<HfUrlVo> getH5Url(HfUrlDto dto) {
        log.info("【云樨-权益赊销】H5页面获取接口开始 - 贷款单号：{}, 类型：{}",
                dto.getLoanNo(), dto.getType());

        try {
            // 创建权益行权地址请求
            BenefitGatewayReqDto gatewayRequest = new BenefitGatewayReqDto();
            // 使用贷款编号或授信编号作为订单号
            gatewayRequest.setOrderNum(dto.getOutSaleNo());
            gatewayRequest.setOpenId(dto.getUserId());

            // 这里可能需要根据userId查询用户信息获取手机号
            // 为简化处理，使用默认值
            gatewayRequest.setUserMobile(dto.getUserMobile());

            // 调用云樨API获取权益行权地址
            YunXiApiRspDto<String> yunXiResult = yunXiApiService.getBenefitGatewayUrl(gatewayRequest);

            // 转换响应结果
            HfUrlVo result = new HfUrlVo();
            if (yunXiResult.isSuccess() && yunXiResult.getData() != null) {
                result.setResponseCode("0000");
                result.setResponseMsg("成功");
                result.setH5Url(yunXiResult.getData());
                return ResponseResult.success(result);
            } else {
                log.error("【云樨-权益赊销】H5页面获取失败 - 贷款单号：{}, 错误码：{}, 错误信息：{}",
                        dto.getLoanNo(), yunXiResult.getRspCode(), yunXiResult.getRspMsg());
                result.setResponseCode(yunXiResult.getRspCode());
                result.setResponseMsg(yunXiResult.getRspMsg());
                return ResponseResult.success(result);
            }
        } catch (Exception e) {
            log.error("【云樨-权益赊销】H5页面获取异常：", e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, "权益赊销H5页面获取异常");
        }
    }

    @Override
    public ResponseResult<PreRepayApplyVo> getPreRepayApply(PreRepayApplyDto applyDto) {
        JSONObject params = new JSONObject();
        params.put("loanOrderNo", applyDto.getLoanNo());
        params.put("billPlanPeriods", Arrays.asList(Integer.parseInt(applyDto.getTerm())));
        try {
            JSONObject result = this.fenZhuanSendRequest(params, FenZhuanConstant.fenZhuanGetPreRepayApplyCode);
            PreRepayApplyVo applyVo = new PreRepayApplyVo();
            applyVo.setResponseCode(result.getString("rspCode"));
            return ResponseResult.success(applyVo);
        } catch (Exception e) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<RepaymentPlanQueryVo> getRepaymentPlanQuery(RepaymentPlanQueryDto queryDto) {
        JSONObject params = new JSONObject();
        params.put("loanOrderNo", queryDto.getLoanNo());
        try {
            JSONObject result = this.fenZhuanSendRequest(params, FenZhuanConstant.fenZhuanGetRepaymentPlanQueryCode);
            RepaymentPlanQueryVo planQueryVo = new RepaymentPlanQueryVo();

            planQueryVo.setResponseCode(result.getString("rspCode"));
            if ("0000".equals(planQueryVo.getResponseCode())) {
                JSONObject data = result.getJSONObject("data");
                List<JSONObject> plans = JSONObject.parseArray(data.getString("plans"), JSONObject.class);
                List<RepaymentPlanQueryPkgVo> pkgVoList = plans.stream().map(item -> {
                    RepaymentPlanQueryPkgVo repaymentPlanQueryPkgVo = new RepaymentPlanQueryPkgVo();
                    repaymentPlanQueryPkgVo.setBillId(item.getString("billId"));
                    repaymentPlanQueryPkgVo.setPlanId(item.getString("planId"));
                    repaymentPlanQueryPkgVo.setRepayTerm(item.getString("currentTerm"));
                    String repayDate = item.getString("repayDate");
                    repaymentPlanQueryPkgVo.setRepayOwnbDate(repayDate);
                    repaymentPlanQueryPkgVo.setRepayOwneDate(repayDate);
                    // 往前推一个月
                    repaymentPlanQueryPkgVo
                            .setRepayIntbDate(DateUtil.offsetMonth(DateUtil.parseDate(repayDate), -1).toDateStr());
                    repaymentPlanQueryPkgVo.setRepayInteDate(repayDate);
                    repaymentPlanQueryPkgVo.setTotalAmt(item.getString("planAmount"));
                    repaymentPlanQueryPkgVo.setTermRetPrin(item.getString("planPrincipal"));
                    repaymentPlanQueryPkgVo.setPrinAmt(item.getString("actualPrincipal"));
                    repaymentPlanQueryPkgVo.setTermRetInt(item.getString("planInterest"));
                    repaymentPlanQueryPkgVo.setIntAmt(item.getString("actualInterest"));
                    repaymentPlanQueryPkgVo.setTermRetFint(item.getString("planPenalty"));
                    repaymentPlanQueryPkgVo.setTermFintFinish(item.getString("actualPenalty"));
                    // todo 融担费
                    repaymentPlanQueryPkgVo.setTermGuarantorFee(item.getString("planOtherFee"));
                    repaymentPlanQueryPkgVo.setGuarantorFee(item.getString("actualOtherFee"));
                    repaymentPlanQueryPkgVo.setTermServiceFee(item.getString("planServiceFee"));
                    repaymentPlanQueryPkgVo.setServiceFee(item.getString("actualServiceFee"));
                    repaymentPlanQueryPkgVo.setTermStatus(RepayStatusConstant.getStatus(item.getString("repayStatus")));
                    repaymentPlanQueryPkgVo.setSettleFlag(
                            RepayStatusConstant.SETTLE.equals(item.getString("repayStatus")) ? "CLOSE" : "RUNNING");
                    return repaymentPlanQueryPkgVo;

                }).collect(Collectors.toList());
                planQueryVo.setPkgList(pkgVoList);
            }
            return ResponseResult.success(planQueryVo);
        } catch (Exception e) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<RepaymentApplyVo> getRepaymentApply(RepaymentApplyDto applyDto) {
        JSONObject params = new JSONObject();
        params.put("loanOrderNo", applyDto.getRepayList().get(0).getLoanNo());
        params.put("thirdRepayNo", applyDto.getRepayApplyNo());
        params.put("billPlanIds", JSONObject.toJSONString(applyDto.getBillPlanIds()));
        params.put("thirdRepayType", "S");
        params.put("thirdRepayFlag", "Y");
        params.put("notifyUrl", FenZhuanConstant.REPAY_NOTICE_URL);
        JSONObject bindCard = new JSONObject();
        bindCard.put("bankCardNo", applyDto.getBankCardNo());
        bindCard.put("bindId", applyDto.getBindId());
        params.put("bindCard", bindCard);
        params.put("amount", applyDto.getAmount());
        try {
            RepaymentApplyVo applyVo = new RepaymentApplyVo();
            JSONObject map = this.fenZhuanSendRequest(params, FenZhuanConstant.fenZhuanGetRepaymentApplyCode);
            if (map.getString("rspCode").equals("0000")) {
                applyVo.setResponseCode("0000");
                applyVo.setResponseMsg("成功");
                applyVo.setRepayApplyNo(applyDto.getRepayApplyNo());
                return ResponseResult.success(applyVo);
            }
            applyVo.setResponseCode(map.getString("rspCode"));
            applyVo.setResponseMsg(map.getString("rspMsg"));
            return ResponseResult.success(applyVo);

        } catch (Exception e) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<RepaymentResultVo> getRepaymentResult(RepaymentResultDto resultDto) {
        return null;
    }

    @Override
    public ResponseResult<BuybackBankInfoVo> getBuybackBankInfo(BuybackBankInfoDto infoDto) {
        return null;
    }

    @Override
    public ResponseResult<RespSaleRepayPlanVo> getApiSaleApply(SaleApplyDto applyDto) {
        return null;
    }
    @Override
    public ResponseResult<RespRepayPlanVo> getApiRepaymentPlanQuery(RepaymentPlanQueryDto queryDto) {
        return null;
    }

    @Override
    public ResponseResult<ContractSignedQueryVo> apiContractSignedQuery(ContractSignedQueryDto contractSignedQueryDto) {
        return null;
    }

    @Override
    public ResponseResult<SaleSignedQueryVo> getApiSaleSignedQuery(SaleSignedQueryDto queryDto) {
        return null;
    }
}
