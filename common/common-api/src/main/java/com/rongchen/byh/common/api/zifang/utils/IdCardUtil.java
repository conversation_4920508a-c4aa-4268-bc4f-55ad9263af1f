package com.rongchen.byh.common.api.zifang.utils;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 身份证信息处理工具类
 * @date 2025/2/27 15:53:35
 */
public class IdCardUtil {
    public static Integer nationOcr(String nation){
        if (StrUtil.isEmpty(nation)) {
            return 99;
        }
        if (!nation.endsWith("族")) {
            nation = nation + "族";
        }
        switch (nation) {
            case "汉族":
                return 1;
            case "蒙古族":
                return 2;
            case "回族":
                return 3;
            case "藏族":
                return 4;
            case "维吾尔族":
                return 5;
            case "苗族":
                return 6;
            case "彝族":
                return 7;
            case "壮族":
                return 8;
            case "布依族":
                return 9;
            case "朝鲜族":
                return 10;
            case "满族":
                return 11;
            case "侗族":
                return 12;
            case "瑶族":
                return 13;
            case "白族":
                return 14;
            case "土家族":
                return 15;
            case "哈尼族":
                return 16;
            case "哈萨克族":
                return 17;
            case "傣族":
                return 18;
            case "黎族":
                return 19;
            case "僳僳族":
                return 20;
            case "佤族":
                return 21;
            case "畲族":
                return 22;
            case "高山族":
                return 23;
            case "拉祜族":
                return 24;
            case "水族":
                return 25;
            case "东乡族":
                return 26;
            case "纳西族":
                return 27;
            case "景颇族":
                return 28;
            case "柯尔克孜族":
                return 29;
            case "土族":
                return 30;
            case "达斡尔族":
                return 31;
            case "仫佬族":
                return 32;
            case "羌族":
                return 33;
            case "布朗族":
                return 34;
            case "撒拉族":
                return 35;
            case "毛南族":
                return 36;
            case "仡佬族":
                return 37;
            case "锡伯族":
                return 38;
            case "阿昌族":
                return 39;
            case "普米族":
                return 40;
            case "塔吉克族":
                return 41;
            case "怒族":
                return 42;
            case "乌孜别克族":
                return 43;
            case "俄罗斯族":
                return 44;
            case "鄂温克族":
                return 45;
            case "德昂族":
                return 46;
            case "保安族":
                return 47;
            case "裕固族":
                return 48;
            case "京族":
                return 49;
            case "塔塔尔族":
                return 50;
            case "独龙族":
                return 51;
            case "鄂伦春族":
                return 52;
            case "赫哲族":
                return 53;
            case "门巴族":
                return 54;
            case "珞巴族":
                return 55;
            case "基诺族":
                return 56;
            default:
                return 99;
        }
    }

    public static Integer educationSw(String education){
        switch (education) {
            case "1":
                return 3;
            case "2":
                return 4;
            case "3":
                return 5;
            case "4":
            case "5":
                return 6;
            default:
                return 99;
        }
    }

    public static Integer marriageStateSw(String maritalStatus){
        switch (maritalStatus) {
            case "1":
                return 0;
            case "2":
                return 1;
            case "3":
                return 2;
            case "4":
                return 3;
            default:
                return 99;
        }
    }

    public static Integer houseSw(String houseStatus){
        switch (houseStatus) {
            case "1":
            case "2":
                return 3;
            case "3":
                return 5;
            case "4":
                return 4;
            default:
                return 99;
        }
    }

    public static String addressResolution(String address){
        if (address.contains("北京市")){
            address = "北京市"+address;
        }else if (address.contains("天津市")){
            address = "天津市"+address;
        }else if (address.contains("上海市")){
            address = "上海市"+address;
        }else if (address.contains("重庆市")){
            address = "重庆市"+address;
        }
        String regex="(?<province>[^省]+自治区|.*?省|.*?行政区|.*?市)(?<city>[^市]+自治州|.*?地区|.*?行政单位|.+盟|市辖区|.*?市|.*?县)(?<county>[^县]+县|.+区|.+市|.+旗|.+海域|.+岛)?(?<town>[^区]+区|.+镇)?(?<village>.*)";
        Matcher m= Pattern.compile(regex).matcher(address);
        String province=null,city=null,county=null,town=null,village=null;
        JSONObject jso = new JSONObject();
        while(m.find()){
            province=m.group("province");
            jso.put("province", province==null?"":province.trim());
            city=m.group("city");
            jso.put("city", city==null?"":city.trim());
            county=m.group("county");
            jso.put("county", county==null?"":county.trim());
            town=m.group("town");
            jso.put("town", town==null?"":town.trim());
            village=m.group("village");
            jso.put("village", village==null?"":village.trim());
        }
        String codeByName = AreaCodeUtil.getCodeByName(city + "_" + county);
        if (codeByName != null){
            return codeByName;
        }else {
            String codeByName1 = CityCodeUtil.getCodeByName(city);
            if (codeByName1 != null){
                return codeByName1;
            }else {
                return ProvinceCodeUtil.getCodeByName(province);
            }
        }
    }
}
