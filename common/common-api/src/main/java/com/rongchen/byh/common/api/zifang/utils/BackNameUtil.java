package com.rongchen.byh.common.api.zifang.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class BackNameUtil {

    private BackNameUtil(){}


    private static final Map<String,String> map = new HashMap<>();

    static {
        map.put("中国工商银行","ICBC");
        map.put("中国建设银行","CCB");
        map.put("中国银行","BOC");
        map.put("中国农业银行","ABC");
        map.put("中国邮政储蓄银行","PSBC");
        map.put("招商银行","CMB");
        map.put("中国光大银行","CEB");
        map.put("广发银行","CGB");
        map.put("中信银行","CITIC");
        map.put("兴业银行","CIB");
        map.put("平安银行","PAB");
        map.put("浦发银行","SPDB");
        map.put("交通银行","COMM");
        map.put("上海银行","BOSH");
        map.put("民生银行","CMBC");
        map.put("浙商银行","CZB");
        map.put("恒丰银行","EGB");
        map.put("渤海银行","CBHB");
        map.put("北京银行","BJB");
        map.put("华夏银行","HXB");
        map.put("杭州银行","HZCB");
        map.put("广西农村信用社","GXNX");
        map.put("云南省农村信用社","YNRCC");
        map.put("四川农村信用社","SCNX");
    }

    public static String getCodeByName(String name){
        return map.get(name);
    }
}
