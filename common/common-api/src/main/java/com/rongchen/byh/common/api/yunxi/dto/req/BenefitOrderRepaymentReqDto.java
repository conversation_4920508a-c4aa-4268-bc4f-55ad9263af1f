package com.rongchen.byh.common.api.yunxi.dto.req;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

/**
 * 权益订单还款通知请求
 * API Code: AP104
 */
@Data
public class BenefitOrderRepaymentReqDto extends YunXiBashReqDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * API编码
     */
    public static final String API_CODE = "AP104";

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 签名
     */
    private String sign;

    /**
     * 合作方订单号
     */
    private String externalOrderNum;

    /**
     * 还款金额，单位元
     */
    private String repayAmt;

    /**
     * 渠道交易流水号
     */
    private String paymentNo;

    /**
     * 支付方式
     * BAOFU_PROXY_PAY-宝付
     * SUNING_PROXY_PAY-苏宁
     */
    private String payWay;
}