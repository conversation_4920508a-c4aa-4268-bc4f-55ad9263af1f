package com.rongchen.byh.common.api.riskControl.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 央行征信风控
 * @date 2025/2/27 15:22:50
 */
@Data
public class CreditPreLoanAuditDto {
    // 申请人身份证
    private String idcard_no;

    // 申请人手机号
    private String mobile;

    // 申请人姓名
    private String name;

    // 申请ID
    private String credit_id;

    // 授信申请时间
    private String credit_time;

    // 渠道ID
    private String channel;

    // 产品代码
    private int product_code;

    // 申请人民族(OCR)
    private int nation_ocr;

    // 申请人户籍地址(OCR)
    private String idcard_address_ocr;

    // 申请人身份证号开始时间
    private String cert_start;

    // 申请人身份证号结束时间
    private String cert_end;

    // 婚姻状况
    private Integer marriage_state;

    // 社会统一代码
    private JSONObject company_info;

}
