package com.rongchen.byh.common.api.zifang.service;


import com.rongchen.byh.common.api.zifang.dto.ContractListDto;
import com.rongchen.byh.common.api.zifang.dto.ContractSignedQueryDto;
import com.rongchen.byh.common.api.zifang.dto.LoanElementDto;
import com.rongchen.byh.common.api.zifang.vo.LoanElementVo;
import com.rongchen.byh.common.api.zifang.vo.ContractListVo;
import com.rongchen.byh.common.api.zifang.vo.ContractSignedQueryVo;
import com.rongchen.byh.common.core.object.ResponseResult;

/**
 * 合同相关接口
 */
public interface ContractApi {

    /**
     * 2.5.1.合同列表查询接口
     * @param contractListDto
     * @return
     */
    ResponseResult<ContractListVo> contractListQuery(ContractListDto contractListDto);

    /**
     * 2.5.3.已签合同查询
     */
    ResponseResult<ContractSignedQueryVo> contractSignedQuery(ContractSignedQueryDto contractSignedQueryDto);


    /**
     * 2.5.4.结清证明申请
     */
//    ResponseResult<>

    /**
     * 2.5.5.结清证明下载
     */

    /**
     * 2.5.6.借款要素查询
     */
    ResponseResult<LoanElementVo> loanElement(LoanElementDto loanElementDto);

    /**
     * 2.5.3.api已签合同查询
     */
    ResponseResult<ContractSignedQueryVo> apiContractSignedQuery(ContractSignedQueryDto contractSignedQueryDto);

}
