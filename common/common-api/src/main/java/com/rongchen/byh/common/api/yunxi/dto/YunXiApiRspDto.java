package com.rongchen.byh.common.api.yunxi.dto;

import java.io.Serializable;
import lombok.Data;

/**
 * 云溪API通用响应
 */
@Data
public class YunXiApiRspDto<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 成功响应码
     */
    public static final String SUCCESS_CODE = "0000";
    


    /**
     * 请求码
     */
    private String apiCode;

    private String respCode;
    /**
     * 应答码
     */
    private String rspCode;

    /**
     * 应答描述
     */
    private String rspMsg;

    private String respExplain;
    
    /**
     * 时间戳
     */
    private String timestamp;

    /**
     * 返回数据
     */
    private T data;

    /**
     * 是否成功
     *
     * @return 是否成功
     */
    public boolean isSuccess() {
        return SUCCESS_CODE.equals(rspCode);
    }

}