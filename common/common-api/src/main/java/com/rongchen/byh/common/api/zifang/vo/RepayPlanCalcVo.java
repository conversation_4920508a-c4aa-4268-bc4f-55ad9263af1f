package com.rongchen.byh.common.api.zifang.vo;

import com.rongchen.byh.common.api.zifang.vo.replan.RepayPlanList;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class RepayPlanCalcVo extends BaseVo{

    /**
     * 还款总金额
     */
    @Schema(description = "还款总金额")
    private String totalAmt;

    /**
     * 总本金金额
     */
    @Schema(description = "总本金金额")
    private String totalPrcp;

    /**
     * 总利息金额
     */
    @Schema(description = "总利息金额")
    private String totalInt;

    /**
     * 总融担费金额
     */
    @Schema(description = "总融担费金额")
    private String totalGuar;

    /**
     * 总服务费金额
     */
    @Schema(description = "总服务费金额")
    private String totalService;
    /**

    /**
     * 总服务费金额
     */
    @Schema(description = "总赊销金额")
    private String totalSale;
    /**
     * 还款计划列表
     */
    @Schema(description = "还款计划列表")
    private List<RepayPlanList> repayPlanList;
}
