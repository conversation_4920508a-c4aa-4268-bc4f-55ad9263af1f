package com.rongchen.byh.common.api.baofu.constant;

/**
 * 宝付支付常量类
 */
public class BaofuConstant {

    /**
     * 交易URL
     */
    public static final String TEST_URL = "https://vgw.baofoo.com/cutpayment/protocol/backTransRequest";
    public static final String PROD_URL = "https://public.baofoo.com/cutpayment/protocol/backTransRequest";

    /**
     * 交易类型
     */
    public static final String TXN_TYPE_PRE_BIND_CARD = "11"; // 预绑卡
    public static final String TXN_TYPE_CONFIRM_BIND_CARD = "12"; // 确认绑卡
    public static final String TXN_TYPE_QUERY_BIND_CARD = "03"; // 绑定结果查询
    public static final String TXN_TYPE_DIRECT_PAY = "18"; // 直接支付
    public static final String TXN_TYPE_QUERY_PAYMENT = "07"; // 支付结果查询

    /**
     * 版本号
     */
    public static final String VERSION = "4.0.0.0";

    /**
     * 数字信封算法标识
     */
    public static final String ALGORITHM_AES = "01";

    /**
     * 响应码
     * S	成功
     * F	失败
     * I	处理中
     * FF	失败（支付结果查询类交易才会返回，表示订单查询参数错误或其他原因导致的订单查询失败，而非订单交易失败）
     */
    public static final String RESP_CODE_SUCCESS = "S";
    /**
     * @deprecated 请使用 {@link #BIZ_CODE_SUCCESS} 替代
     */
    @Deprecated
    public static final String BIZ_RESP_CODE_SUCCESS = "0000";

    /**
     * 签名算法
     */
    public static final String SIGNATURE_ALGORITHM = "SHA1withRSA";

    /**
     * 字符编码
     */
    public static final String CHARSET = "UTF-8";

    /**
     * 证书相关
     */
    public static final String TEST_MERCHANT_PRIVATE_KEY_PATH = "baofu/test/BAOFU20240612_pri.pfx";
    public static final String TEST_BAOFU_PUBLIC_KEY_PATH = "baofu/test/BAOFUP20240612_pub.cer";
    public static final String TEST_CERT_PASSWORD = "123456";

    public static final String PROD_MERCHANT_PRIVATE_KEY_PATH = "baofu/prod/bfkey_91719@@1284597.pfx";
    /*这个给钥匙2048的证书会导致接口异常*/
    //public static final String PROD_BAOFU_PUBLIC_KEY_PATH = "baofu/prod/bfkey_91719@@1284597.cer";
    /*1024的证书*/
    public static final String PROD_BAOFU_PUBLIC_KEY_PATH = "baofu/prod/baofu.cer";

    public static final String PROD_CERT_PASSWORD = "917190";

    /**
     * 业务响应码
     */
    // 成功码
    public static final String BIZ_CODE_SUCCESS = "0000"; // 交易成功

    // 处理中
    public static final String BIZ_CODE_PROCESSING_QUERY = "01"; // 查询绑卡处理中

    // 系统错误
    public static final String BIZ_CODE_SYSTEM_ERROR = "BF00100"; // 系统异常，请联系宝付
    public static final String BIZ_CODE_BUSY = "BF00112"; // 系统繁忙，请稍后再试
    public static final String BIZ_CODE_PROCESSING = "BF00113"; // 交易处理中，请稍后查询
    public static final String BIZ_CODE_PROCESSING_LATER = "BF00115"; // 交易处理中，请稍后查询

    // 持卡人信息错误
    public static final String BIZ_CODE_CARD_INFO_ERROR = "BF00101"; // 持卡人信息有误
    public static final String BIZ_CODE_CARD_EXPIRED = "BF00102"; // 银行卡已过有效期，请联系发卡行
    public static final String BIZ_CODE_CARD_INVALID = "BF00236"; // 卡号无效，请确认后输入
    public static final String BIZ_CODE_CARD_FROZEN = "BF00237"; // 该卡已冻结，请联系发卡行
    public static final String BIZ_CODE_CARD_CANCELLED = "BF00140"; // 该卡已被注销
    public static final String BIZ_CODE_CARD_LOST = "BF00141"; // 该卡已挂失
    public static final String BIZ_CODE_CARD_NUMBER_ERROR = "BF00182"; // 您输入的银行卡号有误，请重新输入
    public static final String BIZ_CODE_CARD_TYPE_MISMATCH = "BF00322"; // 卡类型和卡号不匹配
    public static final String BIZ_CODE_ID_CARD_INVALID = "BF00321"; // 身份证号不合法
    public static final String BIZ_CODE_ID_CARD_EXPIRED = "BF00424"; // 持卡人身份证已过期

    // 交易限制
    public static final String BIZ_CODE_INSUFFICIENT_BALANCE = "BF00103"; // 账户余额不足
    public static final String BIZ_CODE_AMOUNT_EXCEED_LIMIT = "BF00104"; // 交易金额超限
    public static final String BIZ_CODE_AMOUNT_BELOW_LIMIT = "BF00109"; // 交易金额低于限额
    public static final String BIZ_CODE_SINGLE_AMOUNT_LIMIT = "BF00235"; // 单笔交易金额超限
    public static final String BIZ_CODE_DAILY_AMOUNT_LIMIT = "BF00234"; // 单日交易金额超限
    public static final String BIZ_CODE_ORDER_AMOUNT_LIMIT = "BF00146"; // 订单金额超过单笔限额
    public static final String BIZ_CODE_AMOUNT_INCORRECT = "BF00135"; // 交易金额不正确

    // 短信验证码相关
    public static final String BIZ_CODE_SMS_ERROR = "BF00105"; // 短信验证码错误
    public static final String BIZ_CODE_SMS_EXPIRED = "BF00106"; // 短信验证码失效
    public static final String BIZ_CODE_SMS_SEND_FAILED = "BF00255"; // 发送短信验证码失败
    public static final String BIZ_CODE_SMS_RESEND = "BF00256"; // 请重新获取验证码
    public static final String BIZ_CODE_SMS_EXPIRED_RESEND = "BF00260"; // 短信验证码已过期，请重新发送
    public static final String BIZ_CODE_SMS_ERROR_LIMIT = "BF00261"; // 短信验证码错误次数超限，请重新获取
    public static final String BIZ_CODE_SMS_INVALID = "BF00317"; // 短信验证码已失效，请重新获取
    public static final String BIZ_CODE_SMS_VERIFY_LIMIT = "BF10024"; // 验证码错误次数超过发卡行限制

    // 银行卡支持问题
    public static final String BIZ_CODE_CARD_NOT_SUPPORT = "BF00107"; // 当前银行卡不支持该业务，请联系发卡行
    public static final String BIZ_CODE_CARD_NOT_SUPPORT_TRANS = "BF00110"; // 该卡暂不支持此交易
    public static final String BIZ_CODE_CARD_NOT_SUPPORT_PAYMENT = "BF00147"; // 该银行卡不支持此交易
    public static final String BIZ_CODE_CREDIT_CARD_NOT_SUPPORT = "BF00187"; // 暂不支持信用卡的绑定
    public static final String BIZ_CODE_CREDIT_CARD_NOT_ALLOWED = "BF00372"; // 暂不支持信用卡交易
    public static final String BIZ_CODE_BANK_MAINTENANCE = "BF00325"; // 目前该银行正在维护中，请更换其他银行卡支付
    public static final String BIZ_CODE_BANK_NOT_SUPPORT = "BF00324"; // 暂不支持此银行卡支付，请更换其他银行卡或咨询商户客服

    // 交易失败
    public static final String BIZ_CODE_TRANS_FAILED = "BF00111"; // 交易失败
    public static final String BIZ_CODE_CONTACT_BANK = "BF00108"; // 交易失败，请联系发卡行
    public static final String BIZ_CODE_RETRY_PAYMENT = "BF00332"; // 交易失败，请重新支付
    public static final String BIZ_CODE_RISK_CONTROL = "BF00333"; // 该卡有风险，发卡行限制交易
    public static final String BIZ_CODE_RISK_CONTROL_UNIONPAY = "BF00341"; // 该卡有风险，请持卡人联系银联客服[95516]
    public static final String BIZ_CODE_RISK_AUDIT_FAILED = "BF00407"; // 风控审核不通过

    // 订单相关
    public static final String BIZ_CODE_ORDER_PAID = "BF00114"; // 订单已支付成功，请勿重复支付
    public static final String BIZ_CODE_ORDER_EXISTS = "BF00126"; // 该笔订单已存在
    public static final String BIZ_CODE_ORDER_NOT_EXISTS = "BF00128"; // 该笔订单不存在
    public static final String BIZ_CODE_ORDER_CREATE_FAILED = "BF00136"; // 订单创建失败
    public static final String BIZ_CODE_ORDER_EXPIRED = "BF00249"; // 订单已过期，请使用新的订单号发起交易
    public static final String BIZ_CODE_ORDER_UNPAID = "BF00251"; // 订单未支付
    public static final String BIZ_CODE_DUPLICATE_SUBMIT = "BF00373"; // 请勿重复提交

    // 绑卡相关
    public static final String BIZ_CODE_BIND_FAILED = "BF00188"; // 绑卡失败
    public static final String BIZ_CODE_BIND_NOT_EXISTS = "BF00134"; // 绑定关系不存在
    public static final String BIZ_CODE_BIND_REBIND = "BF00422"; // 该用户信息发生变更，请重新绑卡
    public static final String BIZ_CODE_BIND_FAILED_REBIND = "BF00423"; // 绑卡失败，请重新签约
    public static final String BIZ_CODE_BIND_UNBOUND = "BF00421"; // 持卡人已与银行解绑，请重新绑卡后再进行支付

    // 频率限制
    public static final String BIZ_CODE_FREQUENT_TRANS = "BF00347"; // 交易次数频繁，请稍后重试
    public static final String BIZ_CODE_DAILY_FAIL_LIMIT = "BF00350"; // 该卡当日失败次数已超过3次，请次日再试！
    public static final String BIZ_CODE_DAILY_TRANS_LIMIT = "BF00351"; // 该卡当日交易笔数超过限制，请次日再试！
    public static final String BIZ_CODE_DAILY_FAIL_LIMIT_NEXT = "BF00415"; // 该卡当日失败次数超限，请次日再试
    public static final String BIZ_CODE_BALANCE_INSUFFICIENT_LIMIT = "BF00342"; // 单卡单日余额不足次数超限

    // 验证失败
    public static final String BIZ_CODE_VERIFY_PHONE_FAILED = "BF00343"; // 验证失败（手机号有误）
    public static final String BIZ_CODE_VERIFY_CARD_FAILED = "BF00344"; // 验证失败（卡号有误）
    public static final String BIZ_CODE_VERIFY_NAME_FAILED = "BF00345"; // 验证失败（姓名有误)
    public static final String BIZ_CODE_VERIFY_ID_FAILED = "BF00346"; // 验证失败（身份证号有误）
    public static final String BIZ_CODE_VERIFY_CARD_STATUS = "BF00355"; // 验证失败（卡状态异常）
    public static final String BIZ_CODE_VERIFY_DAILY_LIMIT = "BF00356"; // 验证失败（该卡当日失败次数超限，请次日再试）
    public static final String BIZ_CODE_PHONE_CHECK_FAILED = "BF00258"; // 手机号码校验失败
    public static final String BIZ_CODE_PHONE_EMPTY = "BF00315"; // 手机号码为空，请重新输入
    public static final String BIZ_CODE_PHONE_NOT_SET = "BF00353"; // 未设置手机号码，请联系发卡行确认

    // 超时相关
    public static final String BIZ_CODE_TRANS_TIMEOUT = "BF00202"; // 交易超时，请稍后查询
    public static final String BIZ_CODE_SMS_TIMEOUT = "BF00132"; // 短信验证超时，请稍后再试
    public static final String BIZ_CODE_SUBMIT_TIMEOUT = "BF00206"; // 交易超时，请重新提交

    // 分账相关
    public static final String BIZ_CODE_SHARE_FORMAT_ERROR = "BF00334"; // 分账信息格式不正确
    public static final String BIZ_CODE_SHARE_PARAM_ERROR = "BF00335"; // 分账参数验证失败
    public static final String BIZ_CODE_SHARE_INCONSISTENT = "BF00336"; // 分账信息和之前不一致
    public static final String BIZ_CODE_SHARE_ACCEPTED = "BF00337"; // 分账指令已经受理
    public static final String BIZ_CODE_SHARE_SUCCESS = "BF00338"; // 分账成功
    public static final String BIZ_CODE_SHARED = "BF00339"; // 已分账
    public static final String BIZ_CODE_PAYMENT_WAITING_SHARE = "BF00376"; // 该笔交易，扣款已成功，待分账
    public static final String BIZ_CODE_NO_SHARE_RELATION = "BF00485"; // 无共享关系配置

    // 银行卡状态相关
    public static final String BIZ_CODE_BANK_INFO_INCOMPLETE = "BF10000"; // 银行预留信息不符或不完整，请联系发卡行
    public static final String BIZ_CODE_ACCOUNT_SUSPENDED = "BF10001"; // 账户已暂停非柜面业务，请到柜台办理
    public static final String BIZ_CODE_ACCOUNT_INACTIVE = "BF10002"; // 账户为长期不动户，请联系发卡行
    public static final String BIZ_CODE_CARD_NOT_ACTIVATED = "BF10003"; // 卡未激活或协议支付功能未激活
    public static final String BIZ_CODE_CARD_LOCKED = "BF10004"; // 该卡已锁定，请联系发卡行
    public static final String BIZ_CODE_ACCOUNT_STOPPED = "BF10005"; // 账户状态为止付，请联系发卡行
    public static final String BIZ_CODE_ACCOUNT_SUSPENDED_TRANS = "BF10006"; // 该账号已暂停交易，请到柜台办理
    public static final String BIZ_CODE_ACCOUNT_BLACKLISTED = "BF10007"; // 银行黑名单账户或因风控原因拒绝交易
    public static final String BIZ_CODE_UPDATE_ID_INFO = "BF10008"; // 需更新证件信息，请联系发卡行
    public static final String BIZ_CODE_CARD_RECEIVE_ONLY = "BF10009"; // 卡状态为只收不付，请联系发卡行
    public static final String BIZ_CODE_ACCOUNT_SECURITY_LOCKED = "BF10010"; // 账户已开启安全锁，请到柜台办理
    public static final String BIZ_CODE_ACCOUNT_CASE_INVOLVED = "BF10011"; // 该账号已涉案，中止使用
    public static final String BIZ_CODE_CARD_DORMANT = "BF10012"; // 休眠卡，请联系发卡行
    public static final String BIZ_CODE_CARD_FUNCTION_CLOSED = "BF10013"; // 银行卡已关闭交易功能，请联系发卡行
    public static final String BIZ_CODE_PASSWORD_LOCKED = "BF10014"; // 密码已锁定，请联系发卡行
    public static final String BIZ_CODE_CARD_RESTRICTED = "BF10015"; // 卡交易受限，请联系发卡行

    // 其他错误码
    public static final String BIZ_CODE_LOAN_NOT_ALLOWED = "BF10016"; // 贷款资金不得用于当前交易
    public static final String BIZ_CODE_CUMULATIVE_COUNT_LIMIT = "BF10017"; // 累计笔数超限，请联系发卡行
    public static final String BIZ_CODE_ACCOUNT_AMOUNT_LIMIT = "BF10018"; // 二三类账户金额超限
    public static final String BIZ_CODE_MONTHLY_LIMIT = "BF10019"; // 月累计金额或笔数超限，请联系发卡行
    public static final String BIZ_CODE_YEARLY_LIMIT = "BF10020"; // 年累计金额或笔数超限，请联系发卡行
    public static final String BIZ_CODE_CUSTOMER_INFO_VERIFY = "BF10021"; // 客户信息需要核实，请到柜台办理
    public static final String BIZ_CODE_INITIAL_PASSWORD = "BF10022"; // 当前密码为初始密码，请修改
    public static final String BIZ_CODE_AGE_REQUIREMENT = "BF10023"; // 年龄未达要求，发卡行拒绝交易
    public static final String BIZ_CODE_PHONE_STATUS_ABNORMAL = "BF10025"; // 预留手机状态异常，请到柜台核实
    public static final String BIZ_CODE_ID_TYPE_NOT_SUPPORTED = "BF10026"; // 预留的证件类型不支持验证，请到柜台更换为身份证
    public static final String BIZ_CODE_SECURITY_CHECK_FAILED = "BF10027"; // 大额支付安全检查未通过，请联系发卡行
    public static final String BIZ_CODE_QUICK_PAY_CLOSED = "BF10028"; // 用户已关闭快捷支付功能，请开启后重试
    public static final String BIZ_CODE_SMS_NOT_ENABLED = "BF10029"; // 持卡人未开通短信功能，请联系发卡行
    public static final String BIZ_CODE_TRANS_BLOCKED = "BF10030"; // 交易被阻断，请联系发卡行
    public static final String BIZ_CODE_PROTOCOL_FROZEN = "BF10031"; // 协议号对应支付协议已冻结，请解冻后重试
    public static final String BIZ_CODE_QUICK_AUTH_TERMINATED = "BF10035"; // 持卡人已中止快捷授权，请到开启后重试
}