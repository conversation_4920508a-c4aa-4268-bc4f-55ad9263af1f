package com.rongchen.byh.common.api.zifang.vo;

import lombok.Data;

/**
 * @ClassName RepaymentResultVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/9 16:57
 * @Version 1.0
 **/
@Data
public class RepaymentResultVo {

    /**
     * 响应码，必填，用于标识还款相关操作返回的响应代码。
     */
    private String responseCode;
    /**
     * 响应描述，非必填，对响应码对应的情况做进一步说明。
     */
    private String responseMsg;
    /**
     * 还款流水号，必填，用于唯一标识一次还款操作的流水记录。
     */
    private String repayApplyNo;
    /**
     * 贷款编号，必填，用于明确此次还款对应的贷款。
     */
    private String loanNo;
    /**
     * 还款金额，必填，代表此次还款操作涉及的金额数量，单位为元。
     */
    private String amt;
    /**
     * 还款状态，必填，有SUCCESS（成功）、REPAYING（还款中）、FAIL（失败）、WAITING（还款待确认）、COLSE（还款关闭）这些可选值，用于体现还款所处的状态情况。
     */
    private String status;
    /**
     * 还款结果描述，非必填，不过在还款失败时必填，用于返回失败原因。
     */
    private String result;
    /**
     * 还款时间，必填，按格式yyyy-MM-dd HH:mm:ss记录还款实际发生的时间。
     */
    private String repayTime;
}
