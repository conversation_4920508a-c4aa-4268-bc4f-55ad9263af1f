//package com.rongchen.byh.common.api.zifang.service.impl;
//
//import cn.hutool.core.util.StrUtil;
//import com.alibaba.fastjson.JSONObject;
//import com.rongchen.byh.common.api.zifang.config.ZiFangProperties;
//import com.rongchen.byh.common.api.zifang.utils.ZiFangUtil;
//import com.rongchen.byh.common.core.util.ApplicationContextHolder;
//import com.rongchen.byh.common.core.util.HttpUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.boot.CommandLineRunner;
//
//@Slf4j
//public abstract class AbstractApi implements CommandLineRunner {
//
//    protected ZiFangProperties ziFangProperties;
//
//
//    @Override
//    public void run(String... args) throws Exception {
//        ziFangProperties = ApplicationContextHolder.getBean(ZiFangProperties.class);
//    }
//
//    /**
//     * 请求接口
//     * @param params
//     * @param path
//     * @return
//     * @throws Exception
//     */
//    protected JSONObject sendRequest(JSONObject params,String path) throws Exception {
//        log.info("【资方接口】 地址：{}", path);
//        log.info("【资方接口】 加密前参数：{}", params);
//        String body = ZiFangUtil.buildRequest(params, ziFangProperties.getPrivateKey(), ziFangProperties.getOutPublicKey());
//        log.info("【资方接口】 加密后参数：{}", body);
//        String res = HttpUtil.postJson(ziFangProperties.getHost()+getUrl(ziFangProperties.getChannelCode(),path), body);
//        log.info("【资方接口】 解密前响应：{}", res);
//        JSONObject map = ZiFangUtil.parseRequest(res, ziFangProperties.getPrivateKey(), ziFangProperties.getOutPublicKey());
//        log.info("【资方接口】 解密后响应：{}", map);
//        return map;
//    }
//
//    /**
//     * 请求地址
//     * @param channel
//     * @param path
//     * @return
//     */
//    protected String getUrl(String channel,String path) {
//        return StrUtil.replace(path, "{channel}", channel);
//    }
//}
