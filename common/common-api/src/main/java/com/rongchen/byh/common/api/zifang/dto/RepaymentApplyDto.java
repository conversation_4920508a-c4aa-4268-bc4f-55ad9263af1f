package com.rongchen.byh.common.api.zifang.dto;

import lombok.Data;

import java.util.List;

/**
 * @ClassName RepaymentApplyDtp
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/9 15:50
 * @Version 1.0
 **/
@Data
public class RepaymentApplyDto {


    /**
     * 还款申请流水，数据类型为String，长度为32，此字段必填（Y表示必填），代表还款申请的流水号，用于追踪还款申请流程。
     */
    private String repayApplyNo;

    /**
     * 资金方编号
     */
    private String fundCode;

    /**
     * 用户编号，数据类型为String，长度为32，此字段必填（Y表示必填），用于唯一标识具体的用户。
     */
    private String userId;
    /**
     * 还款方式，数据类型为String，长度为32，此字段必填（Y表示必填）。
     * 取值为0表示线上还款，1表示线下还款，用于指定还款的途径。
     */
    private String repayMethod;
    /**
     * 还款类型，数据类型为String，长度为32，此字段必填（Y表示必填）。
     * 具体取值有PREPAYMENT（提前还当期）、CLEAN（全部结清，提前结清）、OVERDUE（归还逾期）、
     * CURRENT（归还当期到期，账期日还款）、OVER（归还到期，逾期 + 当期到期）、OVERCLEAN（逾期结清），用于明确还款的具体类型。
     */
    private String repayType;
    /**
     * 银行预留手机号，数据类型为String，长度为11，此字段必填（Y表示必填），用于在还款相关验证等操作时使用。
     */
    private String bankPhoneNo;
    /**
     * 还款总金额，数据类型为String，格式为17位整数2位小数，单位为元，此字段必填（Y表示必填），代表此次还款的总金额数目。
     */
    private String amount;
    /**
     * 银行卡号，数据类型为String，长度为32，此字段必填（Y表示必填），用于指定还款所使用的银行卡号。
     */
    private String bankCardNo;
    /**
     * 账户卡类型，数据类型为String，长度为2，此字段必填（Y表示必填）。
     * 取值为1表示借记，2表示贷记，用于表明客户账户借记贷记类型。
     */
    private String accountCardType;
    /**
     * 身份证号码，数据类型为String，长度为32，此字段必填（Y表示必填），用于对用户身份进行验证等操作。
     */
    private String idNo;
    /**
     * 用户姓名，数据类型为String，长度为32，此字段必填（Y表示必填），代表进行还款操作的用户的姓名。
     */
    private String customerName;
    /**
     * 银行卡支行名称，数据类型为String，长度为64，此字段必填（Y表示必填），用于明确银行卡所属的具体支行信息。
     */
    private String branchName;
    /**
     * 银行卡类型，数据类型为String，长度为2，此字段必填（Y表示必填）。
     * 取值为0表示对私，1表示对公，用于区分银行卡对应的账户类型。
     */
    private String bankAccountType;

    List<RepaymentApplyRepayDto> repayList;

    /**
     * 机构账单计划编号
     */
    private List<String> billPlanIds;

    /**
     * 机构绑卡 id
     */
    private String bindId;

    /**
     * 回调地址
     */
    private String notifyUrl;


}
