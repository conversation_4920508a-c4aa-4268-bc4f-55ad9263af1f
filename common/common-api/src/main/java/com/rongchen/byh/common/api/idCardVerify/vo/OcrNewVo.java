package com.rongchen.byh.common.api.idCardVerify.vo;


import lombok.Data;

@Data
public class OcrNewVo {

    /**
     * “0” 说明人像面识别成功
     */
    private String frontCode;

    /**
     * “0” 说明国徽面识别成功
     */
    private String backCode;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * frontCode为 0 返回：证件姓名
     */
    private String name;

    /**
     * frontCode为 0 返回：性别
     */
    private String sex;

    /**
     * frontCode为 0 返回：民族
     */
    private String nation;

    /**
     * frontCode为 0 返回：出生日期（例：19920320）
     */
    private String birth;

    /**
     * frontCode为 0 返回：地址
     */
    private String address;

    /**
     * frontCode为 0 返回：身份证号
     */
    private String idcard;

    /**
     * backCode为 0 返回：证件的有效期（例：20160725-20260725）
     */
    private String validDate;

    /**
     * backCode为 0 返回：发证机关
     */
    private String authority;

    /**
     * 人像面照片，转换后为 JPG 格式
     */
    private String frontPhoto;

    /**
     * 国徽面照片，转换后为 JPG 格式
     */
    private String backPhoto;

    /**
     * 人像面切边照片，切边图在识别原图少边或者存在遮挡的情况有小概率可能会导致切图失败，该字段会返回空；如切边图为空时建议可使用原图替代
     */
    private String frontCrop;

    /**
     * 国徽面切边照片，切边图在识别原图少边或者存在遮挡的情况有小概率可能会导致切图失败，该字段会返回空；如切边图为空时建议可使用原图替代
     */
    private String backCrop;

    /**
     * 身份证头像照片
     */
    private String headPhoto;

    /**
     * 人像面告警码，在身份证有遮挡、缺失、信息不全时会返回告警码；当 frontCode 为0时才会出现告警码，告警码的含义请参考 身份证 OCR 错误码
     */
    private String frontWarnCode;

    /**
     * 国徽面告警码，在身份证有遮挡、缺失、信息不全时会返回告警码；当 backCode 为0时才会出现告警码，告警码的含义请参考 身份证 OCR 错误码
     */
    private String backWarnCode;

    /**
     * 做 OCR 的操作时间（例：2020-02-27 17:08:03）
     */
    private String operateTime;

    /**
     * 正面多重告警码，含义请参考 身份证 OCR 错误码
     */
    private String frontMultiWarning;

    /**
     * 反面多重告警码，含义请参考 身份证 OCR 错误码
     */
    private String backMultiWarning;

    /**
     * 正面图片清晰度
     */
    private String frontClarity;

    /**
     * 反面图片清晰度
     */
    private String backClarity;

    /**
     * 籍贯信息
     */
    private String nativePlace;
}
