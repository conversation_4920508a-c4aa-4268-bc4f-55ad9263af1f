package com.rongchen.byh.common.api.zifang.dto;

import lombok.Data;

import java.util.List;

/**
 * @ClassName SaleRepayApplyRepayDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/9 19:09
 * @Version 1.0
 **/
@Data
public class SaleRepayApplyRepayDto {

    /**
     * 必填，赊销订单号，备注：用于唯一标识每一笔赊销订单，方便后续业务关联与查询等操作。
     */
    private String saleNo;
    /**
     * 必填，申请还款金额，备注：格式为17位整数2位小数，单位为元，代表此次申请还款的具体金额数目。
     */
    private String repayAmt;
    /**
     * 非必填，申请还款期数，备注：还多期时用逗号分隔，如：1,2，用于指定此次还款涉及的期数情况（若有）。
     */
    private String repayTerm;
    /**
     * 必填，分账标识，备注：取值有1表示不分帐，2表示按金额分账(单账户)，3表示按金额多账户分账，默认值为1，用于明确此次还款的分账方式。
     */
    private String splitType;
    /**
     * 非必填，资金方分账金额，备注：格式为17位整数2位小数，如果分账标识为2，则必填，用于记录资金方在分账情况下应分得的金额（若符合相应分账条件）。
     */
    private String settlementAmount;
    /**
     * 非必填，合作方分账金额，备注：格式为17位整数2位小数，如果分账标识为2，则必填，用于记录合作方在分账情况下应分得的金额（若符合相应分账条件）。
     */
    private String settlementCooperAmount;
    /**
     * 必填，还款本金，备注：格式为17位整数2位小数，单位为元，代表此次还款中本金的具体金额数目。
     */
    private String prinAmt;
    /**
     * 非必填，还款利息，备注：格式为17位整数2位小数，单位为元，代表此次还款中利息的金额数目（若有）。
     */
    private String intAmt;
    /**
     * 非必填，还款罚息，备注：格式为17位整数2位小数，单位为元，代表此次还款中罚息的金额数目（若有）。
     */
    private String forfeitAmt;
    /**
     * 非必填，还款融担费，备注：格式为17位整数2位小数，单位为元，代表此次还款中融担费的金额数目（若有）。
     */
    private String guarantorFee;
    /**
     * 非必填，还款服务费，备注：格式为17位整数2位小数，单位为元，代表此次还款中服务费的金额数目（若有）。
     */
    private String serviceAmt;
    /**
     * 必填，减免本金，备注：格式为17位整数2位小数，单位为元，代表此次还款中减免本金的具体金额数目。
     */
    private String deratePrin;
    /**
     * 必填，减免利息，备注：格式为17位整数2位小数，单位为元，代表此次还款中减免利息的具体金额数目。
     */
    private String derateInt;
    /**
     * 非必填，减免罚息，备注：格式为17位整数2位小数，单位为元，代表此次还款中减免罚息的金额数目（若有）。
     */
    private String derateForfeit;
    /**
     * 非必填，减免融担费，备注：格式为17位整数2位小数，单位为元，代表此次还款中减免融担费的金额数目（若有）。
     */
    private String derateGuarantor;
    /**
     * 非必填，减免服务费，备注：格式为17位整数2位小数，单位为元，代表此次还款中减免服务费的金额数目（若有）。
     */
    private String derateService;
    /**
     * 非必填，违约金，备注：格式为17位整数2位小数，单位为元，代表此次还款中违约金的金额数目（若有）。
     */
    private String breachFee;

    /**
     * 单笔还款明细信息
     */
    private List<SaleRepayApplyDetailDto> repayDetailList;

}
