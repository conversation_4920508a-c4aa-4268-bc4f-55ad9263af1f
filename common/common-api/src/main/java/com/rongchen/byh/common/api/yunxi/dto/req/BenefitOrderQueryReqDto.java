package com.rongchen.byh.common.api.yunxi.dto.req;

import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * 权益订单结果查询请求
 * API Code: AP102
 */
@Setter
@Getter
public class BenefitOrderQueryReqDto  extends YunXiBashReqDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * API编码
     */
    public static final String API_CODE = "AP102";

    /**
     * 权益平台订单号
     */
    private String orderNum;

}