package com.rongchen.byh.common.api.yunxi.config;

/**
 * 云樨订单状态常量
 */
public class YunXiOrderStatus {

    /**
     * 支付中
     */
    public static final String PAY_ING = "PAY_ING";

    /**
     * 支付成功
     */
    public static final String PAY_SUCCESS = "PAY_SUCCESS";

    /**
     * 退款中
     */
    public static final String REFUND_ING = "REFUND_ING";

    /**
     * 退款成功
     */
    public static final String REFUND_SUCCESS = "REFUND_SUCCESS";

    /**
     * 退款失败
     */
    public static final String REFUND_FAIL = "REFUND_FAIL";

    private YunXiOrderStatus() {
        // 禁止实例化
    }
}