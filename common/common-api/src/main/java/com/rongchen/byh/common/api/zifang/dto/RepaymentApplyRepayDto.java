package com.rongchen.byh.common.api.zifang.dto;

import lombok.Data;

import java.util.List;

/**
 * @ClassName RepaymentApplyRepayDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/9 15:58
 * @Version 1.0
 **/
@Data
public class RepaymentApplyRepayDto {

    /**
     * 贷款编号，数据类型为String，长度为32，此字段必填（Y表示必填），用于唯一标识一笔贷款。
     */
    private String loanNo;
    /**
     * 申请还款金额，数据类型为String，格式为17位整数2位小数，单位为元，此字段必填（Y表示必填），代表此次申请还款的金额数目。
     */
    private String repayAmt;
    /**
     * 申请还款期数，数据类型为String，长度为11，此字段非必填（N表示非必填）。
     * 在还多期时用逗号分隔，如：1,2，用于指定还款涉及的期数情况。
     */
    private String repayTerm;
    /**
     * 分账标识，数据类型为String，长度为2，此字段必填（Y表示必填）。
     * 分账标识含义如下：1 - 不分帐，2 - 按金额分账(单账户)，3 - 按金额多账户分账，默认值为1。
     */
    private String splitType;
    /**
     * 资金方分账金额，数据类型为String，格式为17位整数2位小数，此字段非必填（O表示非必填）。
     * 如果分账标识为2，则该字段必填，用于记录资金方在分账情况下应分得的金额。
     */
    private String settlementAmount;
    /**
     * 合作方分账金额，数据类型为String，格式为17位整数2位小数，此字段非必填（O表示非必填）。
     * 如果分账标识为2，则该字段必填，用于记录合作方在分账情况下应分得的金额。
     */
    private String settlementCooperAmount;
    /**
     * 还款本金，数据类型为String，格式为17位整数2位小数，单位为元，此字段必填（Y表示必填），代表此次还款中本金的金额数目。
     */
    private String prinAmt;
    /**
     * 还款利息，数据类型为String，格式为17位整数2位小数，单位为元，此字段必填（Y表示必填），代表此次还款中利息的金额数目。
     */
    private String intAmt;
    /**
     * 还款罚息，数据类型为String，格式为17位整数2位小数，单位为元，此字段非必填（N表示非必填），代表此次还款中罚息的金额数目（若有）。
     */
    private String forfeitAmt;
    /**
     * 还款融担费，数据类型为String，格式为17位整数2位小数，单位为元，此字段非必填（N表示非必填），代表此次还款中融担费的金额数目（若有）。
     */
    private String guarantorFee;
    /**
     * 当期还款服务费，数据类型为String，格式为17位整数2位小数，单位为元，此字段非必填（N表示非必填），代表此次还款中服务费的金额数目（若有）。
     */
    private String serviceAmt;
    /**
     * 违约金，数据类型为String，格式为17位整数2位小数，单位为元，此字段非必填（N表示非必填），代表此次还款中违约金的金额数目（若有）。
     */
    private String breachFee;

    List<RepaymentApplyDetailDto> repayDetailList;
}
