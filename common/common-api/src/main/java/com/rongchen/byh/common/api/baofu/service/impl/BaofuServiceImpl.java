package com.rongchen.byh.common.api.baofu.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.common.api.baofu.constant.BaofuConstant;
import com.rongchen.byh.common.api.baofu.dto.req.ConfirmBindCardReqDto;
import com.rongchen.byh.common.api.baofu.dto.req.DirectPayReqDto;
import com.rongchen.byh.common.api.baofu.dto.req.QueryBindCardReqDto;
import com.rongchen.byh.common.api.baofu.dto.req.QueryPaymentReqDto;
import com.rongchen.byh.common.api.baofu.dto.rsp.ConfirmBindCardRspDto;
import com.rongchen.byh.common.api.baofu.dto.rsp.DirectPayRspDto;
import com.rongchen.byh.common.api.baofu.dto.rsp.PreBindCardRspDto;
import com.rongchen.byh.common.api.baofu.dto.rsp.QueryBindCardRspDto;
import com.rongchen.byh.common.api.baofu.dto.rsp.QueryPaymentRspDto;
import com.rongchen.byh.common.api.baofu.service.BaofuService;
import com.rongchen.byh.common.api.baofu.util.AmountUtil;
import com.rongchen.byh.common.api.baofu.util.BaofuEncryptUtil;
import com.rongchen.byh.common.api.baofu.util.BaofuUtil;
import com.rongchen.byh.common.api.baofu.vo.req.BankCardBindReqVo;
import com.rongchen.byh.common.api.baofu.vo.req.BankCardConfirmReqVo;
import com.rongchen.byh.common.api.baofu.vo.req.DirectPayReqVo;
import com.rongchen.byh.common.api.baofu.vo.req.QueryPaymentReqVo;
import com.rongchen.byh.common.api.baofu.vo.rsp.DirectPayRspVo;
import com.rongchen.byh.common.api.baofu.vo.rsp.QueryPaymentRspVo;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

/**
 * 宝付支付服务实现
 */
@Slf4j
@Service
public class BaofuServiceImpl implements BaofuService {

    @Value("${baofu.member-id}")
    private String memberId;

    @Value("${baofu.terminal-id}")
    private String terminalId;

    @Value("${baofu.is-test}")
    private boolean isTest;

    @Value("${baofu.share-info.enabled:false}")
    private boolean shareInfoEnabled;

    @Value("${baofu.share-info.merchants:}")
    private List<String> shareInfoMerchants = new ArrayList<>();

    private final Environment environment;

    public BaofuServiceImpl(Environment environment) {
        this.environment = environment;
    }

    @PostConstruct
    public void init() {
        // 获取当前激活的profiles
        String[] activeProfiles = environment.getActiveProfiles();
        boolean isProdEnv = Arrays.asList(activeProfiles).contains("prod");
        log.info("【宝付-初始化】当前环境: {} shareInfoEnabled:{} shareInfoMerchants:{}",
            isProdEnv ? "生产环境" : "测试环境",
            shareInfoEnabled,
            shareInfoMerchants);

        // 判断是否需要使用默认配置
        if (shareInfoMerchants.isEmpty() && shareInfoEnabled) {
            if (isProdEnv) {
                if (StringUtils.isBlank(memberId)) {
                    log.error(
                        "【宝付-初始化】生产环境下必须配置分账商户信息，不允许使用默认值! 配置路径: baofu.member-id");
                    throw new RuntimeException("生产环境下必须配置分账商户信息，不允许使用默认值!");
                }
                isTest = false;
                // 默认第一个商户百分百
                shareInfoMerchants.add(memberId + ",100");

            } else {
                // 开发或测试环境可以使用默认配置
                log.info("【宝付-初始化】非生产环境使用默认分账商户信息");
                shareInfoMerchants.add("*********,30");
                shareInfoMerchants.add("*********,40");
                shareInfoMerchants.add("*********,30");
            }
        }

        log.info("【宝付-初始化】分账配置: enabled={}, merchants={}, activeProfiles={}",
            shareInfoEnabled, shareInfoMerchants, Arrays.toString(activeProfiles));
    }

    @Override
    public PreBindCardRspDto preBindCard(BankCardBindReqVo bindVO) throws Exception {
        log.info("【宝付-预绑卡】开始预绑卡: 用户={}, 银行卡号={}, 卡类型={}",
            bindVO.getUserId(),
            bindVO.getBankCardNo(),
            bindVO.getCardType());

        // 1. 设置基础请求参数
        String sendTime = BaofuUtil.getCurrentTimeString();
        String msgId = "QYC" + BaofuEncryptUtil.generateAESKey(28); // 使用自定义前缀和随机字符串

        // 2. 生成AES密钥并创建数字信封
        String aesKey = BaofuEncryptUtil.generateAESKey();
        log.info("【宝付-预绑卡】生成AES密钥成功，长度={}", aesKey.length());

        // 3. 使用宝付公钥加密数字信封
        String digitalEnvelope = BaofuEncryptUtil.createDigitalEnvelope(aesKey, isTest);
        log.info("【宝付-预绑卡】创建数字信封成功: 长度={}", digitalEnvelope.length());

        // 4. 加密账户信息
        // 银行卡号|持卡人姓名|证件号|手机号|银行卡安全码|银行卡有效期
        String accInfo = bindVO.getBankCardNo() + "|" + bindVO.getCardholderName() + "|"
            + bindVO.getIdCardNo() + "|" + bindVO.getMobile() + "||";
        log.info("【宝付-预绑卡】敏感信息原文: {}", accInfo);

        String encryptedAccInfo = BaofuEncryptUtil.encryptByAES(accInfo, aesKey);
        log.info("【宝付-预绑卡】敏感信息加密后: 长度={}", encryptedAccInfo.length());

        // 5. 构建请求参数
        TreeMap<String, String> requestParams = new TreeMap<>();
        requestParams.put("send_time", sendTime);
        requestParams.put("msg_id", msgId);
        requestParams.put("version", BaofuConstant.VERSION);
        requestParams.put("terminal_id", terminalId);
        requestParams.put("txn_type", BaofuConstant.TXN_TYPE_PRE_BIND_CARD);
        requestParams.put("member_id", memberId);
        requestParams.put("dgtl_envlp", digitalEnvelope);
        requestParams.put("user_id", bindVO.getUserId());
        requestParams.put("card_type", bindVO.getCardType());
        requestParams.put("id_card_type", bindVO.getIdCardType());
        requestParams.put("acc_info", encryptedAccInfo);
        log.info("【宝付-预绑卡】请求参数: {}", JSONObject.toJSONString(requestParams));

        // 6. 生成签名
        String signContent = BaofuUtil.generateSignContent(requestParams);
        log.info("【宝付-预绑卡】签名原文: {}", signContent);

        String sha1Digest = BaofuEncryptUtil.sha1Digest(signContent);
        log.info("【宝付-预绑卡】SHA-1摘要结果: {}", sha1Digest);

        String signature = BaofuEncryptUtil.sign(sha1Digest, isTest);
        log.info("【宝付-预绑卡】签名结果: {}", signature);

        requestParams.put("signature", signature);

        // 7. 发送请求
        String requestUrl = isTest ? BaofuConstant.TEST_URL : BaofuConstant.PROD_URL;
        log.info("【宝付-预绑卡】发送请求: URL={}", requestUrl);

        String responseStr = BaofuUtil.sendRequest(requestParams, isTest);
        log.info("【宝付-预绑卡】收到响应: 长度={}", responseStr.length());

        // 8. 解析响应
        Map<String, String> responseMap = BaofuUtil.parseResponseToMap(responseStr);
        log.info("【宝付-预绑卡】解析响应Map: 字段数={}, 响应码={}, 业务响应码={}",
            responseMap.size(),
            responseMap.get("resp_code"),
            responseMap.get("biz_resp_code"));

        // 9. 验证签名
        if (!responseMap.containsKey("signature")) {
            throw new Exception("响应缺少签名字段");
        }
        String respSignature = responseMap.get("signature");
        responseMap.remove("signature");

        String respSignContent = BaofuUtil.generateSignContent(responseMap);
        String respSha1Digest = BaofuEncryptUtil.sha1Digest(respSignContent);
        boolean verifyResult = BaofuEncryptUtil.verify(respSha1Digest, respSignature, isTest);

        // log.info("验签结果: {}", verifyResult);
        if (!verifyResult) {
            log.error("【宝付-预绑卡】响应签名验证失败");
            throw new Exception("响应签名验证失败");
        }

        // 10. 构建响应对象
        PreBindCardRspDto response = new PreBindCardRspDto();
        response.setResp_code(responseMap.get("resp_code"));
        response.setBiz_resp_code(responseMap.get("biz_resp_code"));
        response.setBiz_resp_msg(responseMap.get("biz_resp_msg"));
        response.setSend_time(responseMap.get("send_time"));
        response.setVersion(responseMap.get("version"));
        response.setTerminal_id(responseMap.get("terminal_id"));
        response.setMember_id(responseMap.get("member_id"));
        response.setDgtl_envlp(responseMap.get("dgtl_envlp"));
        response.setUnique_code(responseMap.get("unique_code"));

        // 11. 如果响应成功，解密唯一码
        if (BaofuConstant.RESP_CODE_SUCCESS.equals(response.getResp_code())
            && BaofuConstant.BIZ_CODE_SUCCESS.equals(response.getBiz_resp_code())) {
            log.info("【宝付-预绑卡】预绑卡响应成功，开始解密唯一码");

            // 解密数字信封获取AES密钥
            String responseEnvelope = response.getDgtl_envlp();
            String decryptedEnvelope = BaofuEncryptUtil.decryptDigitalEnvelope(responseEnvelope, isTest);
            String responseAesKey = BaofuEncryptUtil.getAesKeyFromEnvelope(decryptedEnvelope);
            log.info("【宝付-预绑卡】响应AES密钥: 长度={}", responseAesKey.length());

            // 解密唯一码
            String uniqueCode = response.getUnique_code();
            if (uniqueCode != null && !uniqueCode.isEmpty()) {
                String decryptedUniqueCode = BaofuEncryptUtil.decryptByAES(uniqueCode, responseAesKey);
                log.info("【宝付-预绑卡】解密唯一码结果: {}", decryptedUniqueCode);
                response.setUnique_code(decryptedUniqueCode);
            } else {
                log.warn("【宝付-预绑卡】响应中没有唯一码");
            }
        } else {
            log.error("【宝付-预绑卡】预绑卡响应失败: 响应码={}, 业务响应码={}, 业务响应消息={}",
                response.getResp_code(), response.getBiz_resp_code(), response.getBiz_resp_msg());
        }

        log.info("【宝付-预绑卡】预绑卡处理完成，返回结果");
        return response;
    }

    /**
     * 确认绑卡
     *
     * @param confirmVO
     * @return
     * @throws Exception
     */
    @Override
    public ConfirmBindCardRspDto confirmBindCard(BankCardConfirmReqVo confirmVO) throws Exception {
        // 1. 创建请求对象
        ConfirmBindCardReqDto request = new ConfirmBindCardReqDto();

        // 2. 设置基础请求参数
        request.setSend_time(BaofuUtil.getCurrentTimeString());
        String msg_id = "qyc_flow_" + RandomUtil.randomString(20);
        log.info("【宝付-确认绑卡】生成请求流水号msg_id: {}", msg_id);
        request.setMsg_id(msg_id);
        request.setVersion(BaofuConstant.VERSION);
        request.setTerminal_id(terminalId);
        request.setTxn_type(BaofuConstant.TXN_TYPE_CONFIRM_BIND_CARD);
        request.setMember_id(memberId);

        // 3. 生成AES密钥并创建数字信封
        String aesKey = BaofuEncryptUtil.generateAESKey();
        String digitalEnvelope = BaofuEncryptUtil.createDigitalEnvelope(aesKey, isTest);
        request.setDgtl_envlp(digitalEnvelope);

        // 4. 加密敏感信息
        // 预签约唯一码|短信验证码
        String uniqueInfo = confirmVO.getUniqueCode() + "|" + confirmVO.getSmsCode();
        String encryptedUniqueInfo = BaofuEncryptUtil.encryptByAES(uniqueInfo, aesKey);
        request.setUnique_code(encryptedUniqueInfo);

        // 5. 发送请求
        String responseStr = BaofuUtil.sendRequest(request, isTest);

        // 6. 解析响应
        Map<String, String> responseMap = BaofuUtil.parseResponseToMap(responseStr);

        // 7. 验证签名
        boolean verifyResult = BaofuUtil.verifySignature(responseMap, isTest);
        if (!verifyResult) {
            log.error("【宝付-确认绑卡】响应签名验证失败");
            throw new Exception("确认绑卡响应签名验证失败");
        }

        // 8. 解析响应对象
        ConfirmBindCardRspDto response = JSONObject.parseObject(JSONObject.toJSONString(responseMap),
            ConfirmBindCardRspDto.class);

        // 9. 如果响应成功，解密协议号
        if (BaofuConstant.RESP_CODE_SUCCESS.equals(response.getResp_code())
            && BaofuConstant.BIZ_CODE_SUCCESS.equals(response.getBiz_resp_code())) {
            // 解密数字信封获取AES密钥
            String responseEnvelope = response.getDgtl_envlp();
            String responseAesKey = BaofuEncryptUtil.decryptDigitalEnvelope(responseEnvelope, isTest).split("\\|")[1];

            // 解密协议号
            String protocolNo = response.getProtocol_no();
            if (protocolNo != null && !protocolNo.isEmpty()) {
                String decryptedProtocolNo = BaofuEncryptUtil.decryptByAES(protocolNo, responseAesKey);
                response.setProtocol_no(decryptedProtocolNo);
            }
        }

        return response;
    }

    /**
     * 查询绑卡结果
     */
    @Override
    public QueryBindCardRspDto queryBindCard(String userId, String bankCardNo) throws Exception {
        // 1. 创建请求对象
        QueryBindCardReqDto request = new QueryBindCardReqDto();

        // 2. 设置基础请求参数
        request.setSend_time(BaofuUtil.getCurrentTimeString());
        String msg_id = "qyc_flow_" + RandomUtil.randomString(20);
        log.info("【宝付-查询绑卡】生成请求流水号msg_id: {}", msg_id);
        request.setMsg_id(msg_id);
        request.setVersion(BaofuConstant.VERSION);
        request.setTerminal_id(terminalId);
        request.setTxn_type(BaofuConstant.TXN_TYPE_QUERY_BIND_CARD);
        request.setMember_id(memberId);

        // 3. 设置业务请求参数
        request.setUser_id(userId);

        // 4. 如果提供了银行卡号，则进行加密
        if (bankCardNo != null && !bankCardNo.isEmpty()) {
            // 生成AES密钥并创建数字信封
            String aesKey = BaofuEncryptUtil.generateAESKey();
            String digitalEnvelope = BaofuEncryptUtil.createDigitalEnvelope(aesKey, isTest);
            request.setDgtl_envlp(digitalEnvelope);

            // 加密银行卡号
            String encryptedAccNo = BaofuEncryptUtil.encryptByAES(bankCardNo, aesKey);
            request.setAcc_no(encryptedAccNo);
        }

        // 5. 发送请求
        String responseStr = BaofuUtil.sendRequest(request, isTest);

        // 6. 解析响应
        Map<String, String> responseMap = BaofuUtil.parseResponseToMap(responseStr);

        // 7. 验证签名
        boolean verifyResult = BaofuUtil.verifySignature(responseMap, isTest);
        if (!verifyResult) {
            log.error("【宝付-查询绑卡】查询绑卡结果响应签名验证失败");
            throw new Exception("查询绑卡结果响应签名验证失败");
        }

        // 8. 解析响应对象
        QueryBindCardRspDto response = JSONObject.parseObject(JSONObject.toJSONString(responseMap),
            QueryBindCardRspDto.class);

        // 9. 如果响应成功，解密协议列表
        if (BaofuConstant.RESP_CODE_SUCCESS.equals(response.getResp_code())
            && BaofuConstant.BIZ_CODE_SUCCESS.equals(response.getBiz_resp_code())) {
            // 解密数字信封获取AES密钥
            String responseEnvelope = response.getDgtl_envlp();
            String responseAesKey = BaofuEncryptUtil.decryptDigitalEnvelope(responseEnvelope, isTest).split("\\|")[1];

            // 解密协议列表
            String protocols = response.getProtocols();
            if (protocols != null && !protocols.isEmpty()) {
                String decryptedProtocols = BaofuEncryptUtil.decryptByAES(protocols, responseAesKey);
                response.setProtocols(decryptedProtocols);
            }
        }

        return response;
    }

    /**
     * 直接支付
     */
    @Override
    public DirectPayRspVo directPay(DirectPayReqVo payReqVo) throws Exception {
        // 1. 创建请求对象
        DirectPayReqDto request = new DirectPayReqDto();

        // 2. 设置基础请求参数
        request.setSend_time(BaofuUtil.getCurrentTimeString());
        String msg_id = "qyc_flow_" + RandomUtil.randomString(20);
        log.info("【宝付-直接支付】生成请求流水号msg_id: {}", msg_id);
        request.setMsg_id(msg_id);
        request.setVersion(BaofuConstant.VERSION);
        request.setTerminal_id(terminalId);
        request.setTxn_type(BaofuConstant.TXN_TYPE_DIRECT_PAY);
        request.setMember_id(memberId);

        // 3. 设置业务请求参数
        request.setTrans_id(payReqVo.getTransId());
        request.setUser_id(payReqVo.getUserId());
        request.setTxn_amt(payReqVo.getTxnAmt());
        request.setReturn_url(payReqVo.getReturnUrl());
        log.info("【宝付-直接支付】准备进行直接支付: 金额={}分 ({}元)", payReqVo.getTxnAmt(),
            AmountUtil.centToYuan(payReqVo.getTxnAmt()));
        // 处理分账信息
        merchantsHandler(payReqVo, request);

        request.setShare_notify_url(payReqVo.getShareNotifyUrl());
        request.setFee_member_id(payReqVo.getFeeMemberId());
        request.setCall_fee_member_id(payReqVo.getCallFeeMemberId());

        // 4. 风控参数处理
        if (payReqVo.getRiskItem() != null) {
            String riskItemJson = JSONObject.toJSONString(payReqVo.getRiskItem());
            request.setRisk_item(riskItemJson);
        }

        // 5. 敏感信息加密
        // 生成AES密钥并创建数字信封
        String aesKey = BaofuEncryptUtil.generateAESKey();
        String digitalEnvelope = BaofuEncryptUtil.createDigitalEnvelope(aesKey, isTest);
        request.setDgtl_envlp(digitalEnvelope);

        // 加密签约协议号
        String encryptedProtocolNo = BaofuEncryptUtil.encryptByAES(payReqVo.getProtocolNo(), aesKey);
        request.setProtocol_no(encryptedProtocolNo);

        // 如果有卡信息，加密处理
        if (payReqVo.getCardInfo() != null && !payReqVo.getCardInfo().isEmpty()) {
            String encryptedCardInfo = BaofuEncryptUtil.encryptByAES(payReqVo.getCardInfo(), aesKey);
            request.setCard_info(encryptedCardInfo);
        }

        // 6. 发送请求
        String responseStr = BaofuUtil.sendRequest(request, isTest);

        // 7. 解析响应
        Map<String, String> responseMap = BaofuUtil.parseResponseToMap(responseStr);

        // 8. 验证签名
        boolean verifyResult = BaofuUtil.verifySignature(responseMap, isTest);
        if (!verifyResult) {
            log.error("【宝付-直接支付】响应签名验证失败");
            throw new Exception("直接支付响应签名验证失败");
        }

        // 9. 解析响应对象
        DirectPayRspDto response = JSONObject.parseObject(JSONObject.toJSONString(responseMap), DirectPayRspDto.class);

        // 10. 转换为响应VO
        DirectPayRspVo rspVo = new DirectPayRspVo();
        rspVo.setTransId(response.getTrans_id());
        rspVo.setOrderId(response.getOrder_id());
        rspVo.setCode(response.getBiz_resp_code());
        rspVo.setMessage(response.getBiz_resp_msg());
        rspVo.setSuccAmt(response.getSucc_amt());
        rspVo.setSuccTime(response.getSucc_time());
        rspVo.setChannelOrderId(response.getChannel_order_id());

        // 设置交易状态
        if (BaofuConstant.RESP_CODE_SUCCESS.equals(response.getResp_code())) {
            if (BaofuConstant.BIZ_CODE_SUCCESS.equals(response.getBiz_resp_code())) {
                rspVo.setStatus("SUCCESS");
            } else {
                rspVo.setStatus("FAIL");
            }
        } else {
            rspVo.setStatus("ERROR");
        }

        return rspVo;
    }

    private void merchantsHandler(DirectPayReqVo payReqVo, DirectPayReqDto request) {
        if (payReqVo.getShareInfo() != null && !payReqVo.getShareInfo().isEmpty()) {
            // 如果请求中已经包含分账信息，优先使用请求中的
            request.setShare_info(payReqVo.getShareInfo());
            log.info("【宝付-直接支付】使用请求中的分账信息: {}", payReqVo.getShareInfo());
        } else if (shareInfoEnabled && !shareInfoMerchants.isEmpty()) {
            // 否则，如果配置了分账信息，则使用配置的分账信息
            // 计算交易金额，按照配置的比例分账
            int txnAmt = Integer.parseInt(payReqVo.getTxnAmt());
            BigDecimal yuanAmount = AmountUtil.centToYuan(txnAmt);
            // 构建分账信息字符串
            String shareInfo = calculatedShareInfo(txnAmt);
            request.setShare_info(shareInfo);
            log.info("【宝付-直接支付】使用配置的分账信息: {}", shareInfo);
        } else {
            // 判断当前环境
            String[] activeProfiles = environment.getActiveProfiles();
            boolean isProdEnv = Arrays.asList(activeProfiles).contains("prod");

            if (isProdEnv) {
                if (StringUtils.isBlank(memberId)) {
                    log.error(
                        "【宝付-初始化】生产环境下必须配置分账商户信息，不允许使用默认值! 配置路径: baofu.member-id memberId={}, shareInfoEnabled={}, shareInfoMerchants={}",
                        memberId, shareInfoEnabled, shareInfoMerchants);
                    throw new RuntimeException("生产环境下必须配置分账商户信息，不允许使用默认值!");
                }
                isTest = false;
                // 默认第一个商户百分百
                shareInfoMerchants.add(memberId + ",100");
            } else {
                // 非生产环境使用默认分账信息
                int txnAmt = Integer.parseInt(payReqVo.getTxnAmt());
                BigDecimal yuanAmount = AmountUtil.centToYuan(txnAmt);
                log.warn("【宝付-直接支付】未配置分账信息，非生产环境使用默认分账商户信息, 支付金额={}分 ({}元)",
                    txnAmt, AmountUtil.formatYuan(yuanAmount));
                String shareInfo = "*********,30;*********,40;*********,30";
                request.setShare_info(shareInfo);
                log.info("【宝付-直接支付】使用默认分账信息: {}", shareInfo);
            }
        }
    }

    /**
     * 根据交易金额计算分账信息
     *
     * @param txnAmt 交易金额(单位:分)
     * @return 分账信息字符串(格式 : 商户1, 金额1 ; 商户2, 金额2...)
     */
    private String calculatedShareInfo(int txnAmt) {
        if (shareInfoMerchants == null || shareInfoMerchants.isEmpty()) {
            return "";
        }

        // 将交易金额从分转为元，方便在日志中显示
        BigDecimal yuanAmount = AmountUtil.centToYuan(txnAmt);
        log.info("【宝付-分账】计算分账信息，交易总金额: {}元 ({}分)", yuanAmount, txnAmt);

        StringBuilder shareInfoBuilder = new StringBuilder();
        int totalAmount = 0;

        // 遍历分账商户，计算每个商户的分账金额
        for (int i = 0; i < shareInfoMerchants.size(); i++) {
            String merchantInfo = shareInfoMerchants.get(i);
            String[] parts = merchantInfo.split(",");
            if (parts.length != 2) {
                log.warn("【宝付-分账】分账商户配置格式错误: {}", merchantInfo);
                continue;
            }

            String merchantId = parts[0].trim();
            int percentage;
            try {
                percentage = Integer.parseInt(parts[1].trim());
            } catch (NumberFormatException e) {
                log.warn("【宝付-分账】分账比例格式错误: {}", parts[1]);
                continue;
            }

            // 计算该商户的分账金额
            int shareAmount;
            if (i == shareInfoMerchants.size() - 1) {
                // 最后一个商户获取剩余金额，避免因小数点舍入导致金额不一致
                shareAmount = txnAmt - totalAmount;
            } else {
                shareAmount = (txnAmt * percentage) / 100;
                totalAmount += shareAmount;
            }

            // 添加到分账信息字符串
            if (i > 0) {
                shareInfoBuilder.append(";");
            }
            shareInfoBuilder.append(merchantId).append(",").append(shareAmount);

            // 日志记录，同时显示分和元
            log.info("【宝付-分账】商户[{}]分账金额: {}分 ({}元), 比例: {}%",
                merchantId, shareAmount, AmountUtil.centToYuanStr(shareAmount), percentage);
        }

        String result = shareInfoBuilder.toString();
        log.info("【宝付-分账】分账信息计算完成: {}", result);
        return result;
    }

    /**
     * 查询支付结果
     *
     * @param queryPaymentReqVo 支付查询请求信息
     * @return 支付查询响应
     * @throws Exception 处理异常
     */
    @Override
    public QueryPaymentRspVo queryPayment(QueryPaymentReqVo queryPaymentReqVo) throws Exception {
        // 1. 创建请求对象
        QueryPaymentReqDto request = new QueryPaymentReqDto();

        // 2. 设置基础请求参数
        request.setSend_time(BaofuUtil.getCurrentTimeString());
        String msg_id = "qyc_flow_" + RandomUtil.randomString(20);
        log.info("【宝付-查询支付】生成请求流水号msg_id: {}", msg_id);
        request.setMsg_id(msg_id);
        request.setVersion(BaofuConstant.VERSION);
        request.setTerminal_id(terminalId);
        request.setTxn_type(BaofuConstant.TXN_TYPE_QUERY_PAYMENT);
        request.setMember_id(memberId);

        // 3. 设置业务参数
        request.setOrig_trans_id(queryPaymentReqVo.getOrigTransId());
        request.setOrig_trade_date(queryPaymentReqVo.getOrigTradeDate());

        // 4. 设置可选参数
        if (StringUtils.isNotBlank(queryPaymentReqVo.getReqReserved1())) {
            request.setReq_reserved1(queryPaymentReqVo.getReqReserved1());
        }
        if (StringUtils.isNotBlank(queryPaymentReqVo.getReqReserved2())) {
            request.setReq_reserved2(queryPaymentReqVo.getReqReserved2());
        }

        // 5. 发送请求
        String responseStr = BaofuUtil.sendRequest(request, isTest);

        // 6. 解析响应
        Map<String, String> responseMap = BaofuUtil.parseResponseToMap(responseStr);
        log.info("【宝付-查询支付】支付结果查询响应解析结果: {}", responseMap);

        // 7. 构建响应DTO
        QueryPaymentRspDto rspDto = new QueryPaymentRspDto();
        rspDto.setResp_code(responseMap.get("resp_code"));
        rspDto.setBiz_resp_code(responseMap.get("biz_resp_code"));
        rspDto.setBiz_resp_msg(responseMap.get("biz_resp_msg"));
        rspDto.setSucc_amt(responseMap.get("succ_amt"));
        rspDto.setSucc_time(responseMap.get("succ_time"));
        rspDto.setOrder_id(responseMap.get("order_id"));
        rspDto.setTrans_id(responseMap.get("trans_id"));
        rspDto.setChannel_order_id(responseMap.get("channel_order_id"));

        // 8. 验证签名
        boolean verifyResult = BaofuUtil.verifySignature(responseMap, isTest);
        if (!verifyResult) {
            log.error("【宝付-查询支付】支付结果查询响应签名验证失败");
            throw new Exception("支付结果查询响应签名验证失败");
        }

        // 9. 构建响应VO
        QueryPaymentRspVo rspVo = new QueryPaymentRspVo();

        // 设置基础响应信息
        rspVo.setRespCode(rspDto.getResp_code())
            .setBizRespCode(rspDto.getBiz_resp_code())
            .setBizRespMsg(rspDto.getBiz_resp_msg());

        // 判断是否成功
        boolean success = BaofuConstant.RESP_CODE_SUCCESS.equals(rspDto.getResp_code())
            && BaofuConstant.BIZ_CODE_SUCCESS.equals(rspDto.getBiz_resp_code());
        rspVo.setSuccess(success);

        // 设置业务数据
        if (success) {
            rspVo.setSuccAmt(rspDto.getSucc_amt())
                .setSuccTime(rspDto.getSucc_time())
                .setOrderId(rspDto.getOrder_id())
                .setTransId(rspDto.getTrans_id())
                .setChannelOrderId(rspDto.getChannel_order_id());

            log.info("【宝付-查询支付】支付结果查询成功: 商户订单号={}, 成功金额={}, 成功时间={}",
                rspDto.getTrans_id(), rspDto.getSucc_amt(), rspDto.getSucc_time());
        } else {
            log.error("【宝付-查询支付】支付结果查询失败: 响应码={}, 业务响应码={}, 业务响应消息={}",
                rspDto.getResp_code(), rspDto.getBiz_resp_code(), rspDto.getBiz_resp_msg());
        }

        log.info("【宝付-查询支付】支付结果查询处理完成，返回结果");
        return rspVo;
    }
}