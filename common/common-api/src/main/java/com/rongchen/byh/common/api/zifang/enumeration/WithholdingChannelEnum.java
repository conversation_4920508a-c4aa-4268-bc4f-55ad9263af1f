package com.rongchen.byh.common.api.zifang.enumeration;

/**
 * @ClassName WithholdingChannelEnum
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/9 11:28
 * @Version 1.0
 **/
public enum WithholdingChannelEnum {
    BF("BF","信合元宝付"),
    XHY_TL("XHY_TL","信合元通联"),
    TL("TL","绿信保理通联"),
    ZKB_TL("ZKB_TL","中凯博通联"),
    WP_BF("WP_BF","唯品宝付"),
    ZY_BF("ZY_BF","中原宝付"),
    CY_JD("CY_JD","长银京东"),
    JX_BF("JX_BF","捷信宝付"),
    JS_YB("JS_YB","晋商易宝"),
    ZY_HR_1("ZY_HR_1","中原通联/京东1"),
    ZY_HR_2("ZY_HR_2","中原通联/京东2"),
    ZY_FC_TL("ZY_FC_TL","中原富宸通联"),
    DX_BF("DX_BF","大兴安岭宝付");

    private String key;

    private String value;

    WithholdingChannelEnum(String key , String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return value;
    }
}
