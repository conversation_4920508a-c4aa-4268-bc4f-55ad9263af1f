package com.rongchen.byh.common.api.zifang.vo;

import lombok.Data;

/**
 * @ClassName SaleSignedQueryContractVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/9 18:46
 * @Version 1.0
 **/
@Data
public class SaleSignedQueryContractVo {

    /**
     * 必填，合同名称，用于标识具体合同的名称。
     */
    private String contractName;
    /**
     * 非必填，合同号，备注：借款合同一定要有，用于唯一标识对应的合同。
     */
    private String contractId;
    /**
     * 必填，合同地址，用于指定可访问该合同内容的具体网络地址等信息。
     */
    private String contractUrl;
    /**
     * 非必填，合同编号，备注：PDF合同编号，用于特定格式合同（如PDF格式）的编号标识（若有）。
     */
    private String contractNo;
    /**
     * 必填，url类型，取值有01表示pdf，02表示html，用于说明合同地址对应的文件类型。
     */
    private String urlType;

}
