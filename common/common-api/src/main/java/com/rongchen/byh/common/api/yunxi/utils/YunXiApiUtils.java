package com.rongchen.byh.common.api.yunxi.utils;

import com.alibaba.fastjson.JSON;
import com.rongchen.byh.common.api.yunxi.dto.req.YunXiBashReqDto;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 云樨API工具类
 */
public class YunXiApiUtils {
    private static final Logger log = LoggerFactory.getLogger(YunXiApiUtils.class);

    /**
     * AES加密算法
     */
    private static final String AES_ALGORITHM = "AES";

    /**
     * AES填充模式
     */
    private static final String AES_CIPHER = "AES/ECB/PKCS5Padding";

    /**
     * MD5签名生成
     *
     * @param params    请求参数
     * @param appSecret 应用密钥
     * @return 签名
     */
    public static String generateSign(Map<String, Object> params, String appSecret) {
        log.info("开始生成签名, 参数数量: {}", params.size());

        // 1. 移除sign参数和空值参数
        params.remove("sign");
        // 创建参数Key的列表
        List<String> keys = new ArrayList<>(params.keySet());
        // 移除空值参数
        keys.removeIf(key -> params.get(key) == null || "".equals(params.get(key)));
        log.info("过滤后的参数KEY列表: {}", keys);

        // 2. 按照ASCII码升序排序
        Collections.sort(keys);
        log.info("排序后的参数KEY列表: {}", keys);

        // 3. 拼接参数
        StringBuilder sb = new StringBuilder();
        for (String key : keys) {
            Object value = params.get(key);
            if (value != null && !"".equals(value)) {
                sb.append(key).append("=").append(value).append("&");
            }
        }
        // 添加密钥
        sb.append("key=").append(appSecret);
        log.info("拼接后的签名原文: {}", sb.toString());

        // 4. MD5签名
        String sign = md5(sb.toString());
        log.info("签名生成完成: {}", sign);
        return sign;
    }

    /**
     * MD5加密
     *
     * @param text 待加密字符串
     * @return 加密后的字符串
     */
    public static String md5(String text) {
        log.debug("开始MD5加密, 原文长度: {}", text.length());
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] bytes = md.digest(text.getBytes(StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            for (byte b : bytes) {
                sb.append(String.format("%02x", b));
            }
            log.debug("MD5加密完成, 结果: {}", sb.toString());
            return sb.toString();
        } catch (Exception e) {
            log.error("MD5加密失败, 原因: {}", e.getMessage(), e);
            throw new RuntimeException("MD5加密失败", e);
        }
    }

    /**
     * AES加密
     *
     * @param content 待加密内容
     * @param aesKey  AES密钥
     * @return 加密后的字符串
     */
    public static String aesEncrypt(String content, String aesKey) {
        log.info("开始AES加密, 内容长度: {}", content.length());
        try {
            log.debug("AES密钥长度: {}", aesKey.length());
            SecretKeySpec key = new SecretKeySpec(aesKey.getBytes(StandardCharsets.UTF_8), AES_ALGORITHM);
            Cipher cipher = Cipher.getInstance(AES_CIPHER);
            cipher.init(Cipher.ENCRYPT_MODE, key);
            byte[] encrypted = cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));
            String result = Base64.encodeBase64String(encrypted);
            log.debug("AES加密完成, 结果长度: {}", result.length());
            log.info("AES加密完成");
            return result;
        } catch (Exception e) {
            log.error("AES加密失败, 原因: {}", e.getMessage(), e);
            throw new RuntimeException("AES加密失败", e);
        }
    }

    /**
     * AES解密
     *
     * @param content 待解密内容
     * @param aesKey  AES密钥
     * @return 解密后的字符串
     */
    public static String aesDecrypt(String content, String aesKey) {
        log.info("开始AES解密, 内容长度: {}", content.length());
        if (content == null || content.isEmpty()) {
            log.error("AES解密失败, 输入内容为空");
            throw new RuntimeException("AES解密失败: 输入内容为空");
        }

        try {
            log.debug("AES密钥长度: {}", aesKey.length());
            // 确保密钥长度正确
            byte[] keyBytes = aesKey.getBytes(StandardCharsets.UTF_8);
            if (keyBytes.length != 16 && keyBytes.length != 24 && keyBytes.length != 32) {
                // AES要求密钥长度为16、24或32字节
                log.warn("密钥长度不标准: {}字节，尝试调整为16字节", keyBytes.length);
                byte[] adjustedKey = new byte[16];
                System.arraycopy(keyBytes, 0, adjustedKey, 0, Math.min(keyBytes.length, 16));
                keyBytes = adjustedKey;
            }
            
            SecretKeySpec key = new SecretKeySpec(keyBytes, AES_ALGORITHM);
            Cipher cipher = Cipher.getInstance(AES_CIPHER);
            cipher.init(Cipher.DECRYPT_MODE, key);

            // 多种解码尝试
            byte[] decoded = null;
            Exception lastException = null;
            
            // 方法1：标准Base64解码
            try {
                decoded = Base64.decodeBase64(content);
                log.debug("标准Base64解码后长度: {}", decoded.length);
                
                if (decoded.length % 16 == 0) {
                    // 尝试解密
                    byte[] decrypted = cipher.doFinal(decoded);
                    String result = new String(decrypted, StandardCharsets.UTF_8);
                    log.info("标准Base64解码解密成功, 结果长度: {}", result.length());
                    return result;
                }
            } catch (Exception e) {
                log.warn("标准Base64解码解密失败: {}", e.getMessage());
                lastException = e;
            }
            
            // 方法2：如果是十六进制字符串
            if (content.matches("[0-9a-fA-F]+")) {
                try {
                    decoded = hexStringToByteArray(content);
                    log.debug("十六进制解码后长度: {}", decoded.length);
                    
                    // 尝试解密
                    byte[] decrypted = cipher.doFinal(decoded);
                    String result = new String(decrypted, StandardCharsets.UTF_8);
                    log.info("十六进制解码解密成功, 结果长度: {}", result.length());
                    return result;
                } catch (Exception e) {
                    log.warn("十六进制解码解密失败: {}", e.getMessage());
                    lastException = e;
                }
            }
            
            // 方法3：尝试URL安全的Base64解码
            try {
                decoded = Base64.decodeBase64(content.replace('-', '+').replace('_', '/'));
                log.debug("URL安全Base64解码后长度: {}", decoded.length);
                
                // 尝试解密
                byte[] decrypted = cipher.doFinal(decoded);
                String result = new String(decrypted, StandardCharsets.UTF_8);
                log.info("URL安全Base64解码解密成功, 结果长度: {}", result.length());
                return result;
            } catch (Exception e) {
                log.warn("URL安全Base64解码解密失败: {}", e.getMessage());
                lastException = e;
            }
            
            // 方法4：尝试先去除可能的padding字符再解码
            try {
                String trimmed = content.replaceAll("[\\s=]", "");
                decoded = Base64.decodeBase64(trimmed);
                log.debug("去除padding后Base64解码长度: {}", decoded.length);
                
                // 尝试解密
                byte[] decrypted = cipher.doFinal(decoded);
                String result = new String(decrypted, StandardCharsets.UTF_8);
                log.info("去除padding后Base64解码解密成功, 结果长度: {}", result.length());
                return result;
            } catch (Exception e) {
                log.warn("去除padding后Base64解码解密失败: {}", e.getMessage());
                lastException = e;
            }
            
            // 所有方法都失败
            if (lastException != null) {
                throw lastException;
            } else {
                throw new RuntimeException("尝试所有解密方法均失败");
            }
        } catch (Exception e) {
            log.error("AES解密最终失败, 原因: {}", e.getMessage(), e);
            throw new RuntimeException("AES解密失败", e);
        }
    }

    /**
     * 将十六进制字符串转换为字节数组
     */
    private static byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                    + Character.digit(s.charAt(i + 1), 16));
        }
        return data;
    }

    /**
     * 构建通用请求参数并填充到YunXiBashReqDto对象中
     *
     * @param apiCode API代码
     * @param reqDto  请求对象
     * @return 填充好通用参数的请求对象
     */
    public static <T extends YunXiBashReqDto> T buildCommonParams(String apiCode, T reqDto, String appSecret) {
        log.info("开始构建通用请求参数, apiCode: {}, appId: {}", apiCode, reqDto.getAppId());

        // 设置时间戳
        long timestamp = System.currentTimeMillis();
        reqDto.setTimestamp(String.valueOf(timestamp));

        // 将整个对象转换为Map，包含所有业务参数
        Map<String, Object> allParams = new HashMap<>(JSON.parseObject(JSON.toJSONString(reqDto), Map.class));

        // 添加apiCode参数
        allParams.put("apiCode", apiCode);
        // 确保apiCode也被设置到reqDto对象中
        reqDto.setApiCode(apiCode);

        // 移除不参与签名的敏感参数
        allParams.remove("appSecret");
        allParams.remove("sign");

        // 移除空值参数（一般签名时不包含空值）
        allParams.entrySet().removeIf(entry -> entry.getValue() == null);

        log.debug("签名参数准备完毕，参数数量：{}", allParams.size());

        // 生成签名
        String sign = generateSign(allParams, appSecret);
        reqDto.setSign(sign);

        log.info("通用请求参数构建完成, 签名: {}", sign);
        log.debug("完整请求参数: {}", JSON.toJSONString(reqDto));

        return reqDto;
    }

    /**
     * 尝试自动解密，根据加密内容自动选择解密方式
     * 
     * @param encryptedData 加密数据
     * @param key 加密密钥
     * @return 解密后的内容
     */
    public static String autoDecrypt(String encryptedData, String key) {
        log.info("尝试自动解密数据，数据长度: {}", encryptedData.length());
        
        try {
            return aesDecrypt(encryptedData, key);
        } catch (Exception e) {
            log.warn("AES解密失败: {}", e.getMessage());
        }
        
        // 解密失败，检查是否是JSON格式
        if (encryptedData.startsWith("{") && encryptedData.endsWith("}")) {
            log.info("检测到数据可能是JSON格式，原样返回");
            return encryptedData;
        }
        
        // 尝试使用其他解密方式或返回原始数据
        log.warn("所有解密方式均失败，返回原始数据");
        return encryptedData;
    }

}