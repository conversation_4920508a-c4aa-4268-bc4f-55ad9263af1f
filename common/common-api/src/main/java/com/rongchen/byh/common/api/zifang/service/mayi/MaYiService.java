package com.rongchen.byh.common.api.zifang.service.mayi;

import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.common.api.zifang.dto.ApiGateWayDto;
import com.rongchen.byh.common.api.zifang.dto.ApplyCheckDto;
import com.rongchen.byh.common.api.zifang.dto.BindBankSmsDto;
import com.rongchen.byh.common.api.zifang.dto.BuybackBankInfoDto;
import com.rongchen.byh.common.api.zifang.dto.ContractListDto;
import com.rongchen.byh.common.api.zifang.dto.ContractSignedQueryDto;
import com.rongchen.byh.common.api.zifang.dto.CreditApplyDto;
import com.rongchen.byh.common.api.zifang.dto.CreditAuditDto;
import com.rongchen.byh.common.api.zifang.dto.HfUrlDto;
import com.rongchen.byh.common.api.zifang.dto.LoanApplyDto;
import com.rongchen.byh.common.api.zifang.dto.LoanElementDto;
import com.rongchen.byh.common.api.zifang.dto.PreRepayApplyDto;
import com.rongchen.byh.common.api.zifang.dto.QueryBindBankResultDto;
import com.rongchen.byh.common.api.zifang.dto.RepayPlanCalcDto;
import com.rongchen.byh.common.api.zifang.dto.RepaymentApplyDto;
import com.rongchen.byh.common.api.zifang.dto.RepaymentPlanQueryDto;
import com.rongchen.byh.common.api.zifang.dto.RepaymentResultDto;
import com.rongchen.byh.common.api.zifang.dto.SaleApplyDto;
import com.rongchen.byh.common.api.zifang.dto.SaleRefundResultDto;
import com.rongchen.byh.common.api.zifang.dto.SaleRepayApplyDto;
import com.rongchen.byh.common.api.zifang.dto.SaleRepayPlanDto;
import com.rongchen.byh.common.api.zifang.dto.SaleRepayResultDto;
import com.rongchen.byh.common.api.zifang.dto.SaleRepayReturnFeeDto;
import com.rongchen.byh.common.api.zifang.dto.SaleResultDto;
import com.rongchen.byh.common.api.zifang.dto.SaleSignedQueryDto;
import com.rongchen.byh.common.api.zifang.dto.UsableBankListDto;
import com.rongchen.byh.common.api.zifang.dto.UseLoanQueryDto;
import com.rongchen.byh.common.api.zifang.dto.VerifyBindBankSmsDto;
import com.rongchen.byh.common.api.zifang.dto.credit.ContactRelation;
import com.rongchen.byh.common.api.zifang.dto.credit.CreditInfo;
import com.rongchen.byh.common.api.zifang.dto.credit.DutyInfo;
import com.rongchen.byh.common.api.zifang.dto.credit.PictureInfo;
import com.rongchen.byh.common.api.zifang.service.BindCardApi;
import com.rongchen.byh.common.api.zifang.service.CapitalApi;
import com.rongchen.byh.common.api.zifang.service.ContractApi;
import com.rongchen.byh.common.api.zifang.service.LoanApi;
import com.rongchen.byh.common.api.zifang.service.OtherApi;
import com.rongchen.byh.common.api.zifang.service.RepaymentApi;
import com.rongchen.byh.common.api.zifang.service.mayi.config.ZiFangConstant;
import com.rongchen.byh.common.api.zifang.utils.AreaCodeUtil;
import com.rongchen.byh.common.api.zifang.utils.CityCodeUtil;
import com.rongchen.byh.common.api.zifang.utils.ProvinceCodeUtil;
import com.rongchen.byh.common.api.zifang.vo.ApiGateWayListVo;
import com.rongchen.byh.common.api.zifang.vo.ApplyCheckVo;
import com.rongchen.byh.common.api.zifang.vo.BankCardListsVo;
import com.rongchen.byh.common.api.zifang.vo.BindBankSmsVo;
import com.rongchen.byh.common.api.zifang.vo.BuybackBankInfoVo;
import com.rongchen.byh.common.api.zifang.vo.ContractListVo;
import com.rongchen.byh.common.api.zifang.vo.ContractSignedQueryVo;
import com.rongchen.byh.common.api.zifang.vo.CreditApplyVo;
import com.rongchen.byh.common.api.zifang.vo.CreditAuditVo;
import com.rongchen.byh.common.api.zifang.vo.HfUrlVo;
import com.rongchen.byh.common.api.zifang.vo.LoanElementVo;
import com.rongchen.byh.common.api.zifang.vo.PreRepayApplyVo;
import com.rongchen.byh.common.api.zifang.vo.QueryBindBankResultVo;
import com.rongchen.byh.common.api.zifang.vo.RepayPlanCalcVo;
import com.rongchen.byh.common.api.zifang.vo.RepaymentApplyVo;
import com.rongchen.byh.common.api.zifang.vo.RepaymentPlanQueryVo;
import com.rongchen.byh.common.api.zifang.vo.RepaymentResultVo;
import com.rongchen.byh.common.api.zifang.vo.SaleApplyVo;
import com.rongchen.byh.common.api.zifang.vo.SaleRefundResultVo;
import com.rongchen.byh.common.api.zifang.vo.SaleRepayApplyVo;
import com.rongchen.byh.common.api.zifang.vo.SaleRepayPlanVo;
import com.rongchen.byh.common.api.zifang.vo.SaleRepayResultVo;
import com.rongchen.byh.common.api.zifang.vo.SaleRepayReturnFeeVo;
import com.rongchen.byh.common.api.zifang.vo.SaleResultVo;
import com.rongchen.byh.common.api.zifang.vo.SaleSignedQueryVo;
import com.rongchen.byh.common.api.zifang.vo.UsableBankListVo;
import com.rongchen.byh.common.api.zifang.vo.UseLoanQueryVo;
import com.rongchen.byh.common.api.zifang.vo.VerifyBindBankSmsVo;
import com.rongchen.byh.common.api.zifang.vo.loan.RespRepayPlanVo;
import com.rongchen.byh.common.api.zifang.vo.loan.RespSaleRepayPlanVo;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@Service
@Primary
@Slf4j
public class MaYiService extends AbstractMaYiApi
        implements BindCardApi, CapitalApi, ContractApi, LoanApi, OtherApi, RepaymentApi {

    @Override
    public ResponseResult<UsableBankListVo> getUsableBankList(UsableBankListDto bankListDto) {
        JSONObject params = new JSONObject();
        params.put("reqSysCode", ziFangProperties.getReqSysCode());
        params.put("productCode", ziFangProperties.getProductCode());
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.getUsableBankListUrl);
            if (map == null) {
                log.error("【资方】获取可用银行列表失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "获取可用银行列表失败，远程接口调用异常");
            }
            UsableBankListVo ziFangVo = map.toJavaObject(UsableBankListVo.class);
            ziFangVo.setResponseCode(map.getString("responseCode"));
            ziFangVo.setResponseMsg(map.getString("responseMsg"));
            ziFangVo.setBankCardListsVos(map.getJSONArray("bankLists").toJavaList(BankCardListsVo.class));
            return ResponseResult.success(ziFangVo);
        } catch (Exception e) {
            log.error("【资方】获取可用银行列表异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<BindBankSmsVo> getBindBankSMS(BindBankSmsDto bindBankSmsDto) {
        JSONObject params = new JSONObject();
        params.put("serialNo", bindBankSmsDto.getSerialNo());
        params.put("reqSysCode", ziFangProperties.getReqSysCode());
        params.put("productCode", ziFangProperties.getProductCode());
        params.put("scene", bindBankSmsDto.getScene());
        params.put("payChannel", bindBankSmsDto.getPayChannel());
        if (StringUtils.isNotEmpty(bindBankSmsDto.getLoanNo())) {
            params.put("loanNo", bindBankSmsDto.getLoanNo());
        }
        params.put("idNo", bindBankSmsDto.getIdNo());
        params.put("custName", bindBankSmsDto.getCustName());
        params.put("phoneNo", bindBankSmsDto.getPhoneNo());
        params.put("userId", bindBankSmsDto.getUserId());
        params.put("bankCardNum", bindBankSmsDto.getBankCardNum());
        params.put("bankName", bindBankSmsDto.getBankName());
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.getBindBankSMSUrl);
            if (map == null) {
                log.error("【资方】获取绑卡短信失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "获取绑卡短信失败，远程接口调用异常");
            }
            BindBankSmsVo bindBankSmsVo = map.toJavaObject(BindBankSmsVo.class);
            return ResponseResult.success(bindBankSmsVo);
        } catch (Exception e) {
            log.error("【资方】获取绑卡短信异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<VerifyBindBankSmsVo> getVerifyBindBankSMSUrl(VerifyBindBankSmsDto bindBankSmsDto) {
        JSONObject params = new JSONObject();
        params.put("serialNo", bindBankSmsDto.getSerialNo());
        params.put("reqSysCode", ziFangProperties.getReqSysCode());
        params.put("productCode", ziFangProperties.getProductCode());
        params.put("scene", bindBankSmsDto.getScene());
        if (StringUtils.isNotEmpty(bindBankSmsDto.getLoanNo())) {
            params.put("loanNo", bindBankSmsDto.getLoanNo());
        }
        params.put("phoneCode", bindBankSmsDto.getPhoneCode());
        params.put("messageNo", bindBankSmsDto.getMessageNo());
        params.put("bankCardNum", bindBankSmsDto.getBankCardNum());
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.getVerifyBindBankSMSUrl);
            if (map == null) {
                log.error("【资方】验证绑卡短信失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "验证绑卡短信失败，远程接口调用异常");
            }
            VerifyBindBankSmsVo bindBankSmsVo = map.toJavaObject(VerifyBindBankSmsVo.class);
            return ResponseResult.success(bindBankSmsVo);
        } catch (Exception e) {
            log.error("【资方】验证绑卡短信异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<QueryBindBankResultVo> getQueryBindBankResult(QueryBindBankResultDto bindBankSmsDto) {
        JSONObject params = new JSONObject();
        params.put("reqSysCode", ziFangProperties.getReqSysCode());
        params.put("messageNo", bindBankSmsDto.getMessageNo());
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.getQueryBindBankResult);
            if (map == null) {
                log.error("【资方】查询绑卡结果失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "查询绑卡结果失败，远程接口调用异常");
            }
            QueryBindBankResultVo bindBankResultVo = map.toJavaObject(QueryBindBankResultVo.class);
            return ResponseResult.success(bindBankResultVo);
        } catch (Exception e) {
            log.error("【资方】查询绑卡结果异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<ApplyCheckVo> applyCheck(ApplyCheckDto applyCheckDto) {
        JSONObject params = new JSONObject();
        params.put("serialNo", applyCheckDto.getSerialNo());
        params.put("reqSysCode", ziFangProperties.getReqSysCode());
        params.put("productCode", ziFangProperties.getProductCode());
        params.put("idNo", applyCheckDto.getIdNo());
        params.put("custName", applyCheckDto.getCustName());
        params.put("phoneNo", applyCheckDto.getPhoneNo());
        params.put("type", 0);
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.applyCheckUrl);
            if (map == null) {
                log.error("【资方】进件前置校验失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "进件前置校验失败，远程接口调用异常");
            }
            ApplyCheckVo vo = map.toJavaObject(ApplyCheckVo.class);
            return ResponseResult.success(vo);
        } catch (Exception e) {
            log.error("【资方】进件前置校验异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<CreditApplyVo> creditApply(CreditApplyDto creditApplyDto) {
        JSONObject param = new JSONObject();

        // 生成CreditInfo的所有build参数构造
        CreditInfo creditInfo = CreditInfo.builder()
                .creditNo(creditApplyDto.getCreditNo())
                .custName(creditApplyDto.getCustName())
                .userId(creditApplyDto.getUserId())
                .idNo(creditApplyDto.getIdNo())
                .creditAmount(creditApplyDto.getCreditAmount())
                .sex(creditApplyDto.getSex())
                .birthday(creditApplyDto.getBirthday())
                .nation(creditApplyDto.getNation())
                .nationality(creditApplyDto.getNationality())
                .nation(creditApplyDto.getNation())

                .idProvince(creditApplyDto.getProvince())
                .idCity(creditApplyDto.getCity())
                .idArea(creditApplyDto.getArea())

                .idProvinceCode(ProvinceCodeUtil.getCodeByName(creditApplyDto.getProvince()))
                .idCityCode(CityCodeUtil.getCodeByName(creditApplyDto.getCity()))
                .idAreaCode(AreaCodeUtil.getCodeByName(creditApplyDto.getCity() + "_" + creditApplyDto.getArea()))
                .idAddress(creditApplyDto.getAddress())

                .signOrganization(creditApplyDto.getSignOrganization())
                .idValidDateBegin(creditApplyDto.getIdValidDateBegin())
                .idValidDateEnd(creditApplyDto.getIdValidDateEnd())
                .idLongTerm(creditApplyDto.getIdLongTerm())

                .phoneNo(creditApplyDto.getPhoneNo())
                .liveProvinceCode(ProvinceCodeUtil.getCodeByName(creditApplyDto.getProvince()))
                .liveCityCode(CityCodeUtil.getCodeByName(creditApplyDto.getCity()))
                .liveAreaCode(AreaCodeUtil.getCodeByName(creditApplyDto.getCity() + "_" + creditApplyDto.getArea()))
                .liveProvince(creditApplyDto.getProvince())
                .liveCity(creditApplyDto.getCity())
                .liveArea(creditApplyDto.getArea())
                .liveAddress(creditApplyDto.getArea())

                .custEducation(creditApplyDto.getEducation())
                .degree(formatDegree(creditApplyDto.getEducation()))

                .marryType(creditApplyDto.getMarryType())
                .childrenStatus("5")
                .occupation("Y")
                .income(creditApplyDto.getIncome() + "")
                .salary(String.valueOf(creditApplyDto.getIncome() * 12))
                .companyName("个体户")

                .companyProvince(creditApplyDto.getProvince())
                .companyCity(creditApplyDto.getCity())
                .companyArea(creditApplyDto.getArea())
                .companyProvinceCode(ProvinceCodeUtil.getCodeByName(creditApplyDto.getProvince()))
                .companyCityCode(CityCodeUtil.getCodeByName(creditApplyDto.getCity()))
                .companyAreaCode(AreaCodeUtil.getCodeByName(creditApplyDto.getCity() + "_" + creditApplyDto.getArea()))
                .companyAddress(creditApplyDto.getAddress())

                .deviceBrand(creditApplyDto.getDeviceBrand())
                .networkType(creditApplyDto.getNetworkType())
                .devAlias(creditApplyDto.getDevAlias())
                .deviceId(creditApplyDto.getDeviceId())
                .clientIp(creditApplyDto.getClientIp())
                .coordinateType(creditApplyDto.getCoordinateType())
                .lng(creditApplyDto.getLng())
                .lat(creditApplyDto.getLat())
                .gpsCity(creditApplyDto.getGpsCity())
                .lbsAddress(creditApplyDto.getLbsAddress())
                .gpsAddress(creditApplyDto.getGpsAddress())
                .os(creditApplyDto.getOs())
                .osVersion(creditApplyDto.getOsVersion())
                .faceScore(creditApplyDto.getFaceScore())
                .faceConfidence(creditApplyDto.getFaceConfidence())
                .faceSource(creditApplyDto.getFaceSource())
                .faceTime(creditApplyDto.getFaceTime())
                .build();

        List<ContactRelation> contactRelationList = new ArrayList<>(2);
        ContactRelation contact1 = new ContactRelation();
        contact1.setContactRelation(creditApplyDto.getContactRelation1());
        contact1.setContactName(creditApplyDto.getContactName1());
        contact1.setContactPhoneNo(creditApplyDto.getContactPhoneNo1());
        contactRelationList.add(contact1);

        ContactRelation contact2 = new ContactRelation();
        contact2.setContactRelation(creditApplyDto.getContactRelation2());
        contact2.setContactName(creditApplyDto.getContactName2());
        contact2.setContactPhoneNo(creditApplyDto.getContactPhoneNo2());
        contactRelationList.add(contact2);

        creditInfo.setContactRelationLists(contactRelationList);

        List<PictureInfo> pictureInfoList = new ArrayList<>(3);
        PictureInfo picture1 = new PictureInfo();
        picture1.setMethods("0");
        picture1.setPictureType("0");
        picture1.setPictureUrl(creditApplyDto.getIdCardFrontUrl());
        pictureInfoList.add(picture1);

        PictureInfo picture2 = new PictureInfo();
        picture2.setMethods("0");
        picture2.setPictureType("1");
        picture2.setPictureUrl(creditApplyDto.getIdCardBackUrl());
        pictureInfoList.add(picture2);

        PictureInfo picture3 = new PictureInfo();
        picture3.setMethods("0");
        picture3.setPictureType("2");
        picture3.setPictureUrl(creditApplyDto.getLiveUrl());
        pictureInfoList.add(picture3);

        creditInfo.setPictureList(pictureInfoList);

        param.put("creditInfo", creditInfo);

        DutyInfo dutyInfo = new DutyInfo();
        dutyInfo.setDuty("4");
        dutyInfo.setTechnical("0");
        dutyInfo.setCompanyTrade("A");
        dutyInfo.setCompanyNature("40");
        dutyInfo.setJobNature("10");
        dutyInfo.setEmployment("91");
        dutyInfo.setResidentialStatus("7");

        param.put("dutyInfo", dutyInfo);
        param.put("reqSysCode", ziFangProperties.getReqSysCode());
        param.put("productCode", ziFangProperties.getProductCode());
        System.out.println(param.toJSONString());
        try {
            JSONObject map = this.sendRequest(param, ZiFangConstant.creditApplyUrl);
            if (map == null) {
                log.error("【资方】授信申请失败：sendRequest 返回 null，请求参数：{}", param.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "授信申请失败，远程接口调用异常");
            }
            CreditApplyVo vo = map.toJavaObject(CreditApplyVo.class);
            return ResponseResult.success(vo);
        } catch (Exception e) {
            log.error("【资方】授信申请异常，请求参数：{}，异常：", param.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    private String formatDegree(String education) {
        String degree = "";
        if ("10".equals(education)) {
            degree = "2";
        } else if ("20".equals(education)) {
            degree = "4";
        } else if ("30".equals(education)) {
            degree = "0";
        } else if ("40".equals(education)) {
            degree = "5";
        } else if ("60".equals(education)) {
            degree = "0";
        } else if ("90".equals(education)) {
            degree = "0";
        } else if ("91".equals(education)) {
            degree = "0";
        } else if ("99".equals(education)) {
            degree = "0";
        }
        return degree;
    }

    @Override
    public ResponseResult<CreditAuditVo> getCreditAudit(CreditAuditDto creditAuditDto) {
        JSONObject params = new JSONObject();
        params.put("creditNo", creditAuditDto.getCreditNo());
        params.put("reqSysCode", ziFangProperties.getReqSysCode());
        params.put("productCode", ziFangProperties.getProductCode());
        params.put("userId", creditAuditDto.getUserId());
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.creditAuditUrl);
            if (map == null) {
                log.error("【资方】授信审核结果查询失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "授信审核结果查询失败，远程接口调用异常");
            }
            CreditAuditVo vo = map.toJavaObject(CreditAuditVo.class);
            return ResponseResult.success(vo);
        } catch (Exception e) {
            log.error("【资方】授信审核结果查询异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<ApiGateWayListVo> apiGateWay(ApiGateWayDto creditAuditDto) {
        return null;
    }

    @Override
    public ResponseResult<ContractListVo> contractListQuery(ContractListDto contractListDto) {
        String jsonString = JSONObject.toJSONString(contractListDto);
        JSONObject params = JSONObject.parseObject(jsonString);
        params.put("reqSysCode", ziFangProperties.getReqSysCode());
        params.put("productCode", ziFangProperties.getProductCode());
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.contractListQueryUrl);
            if (map == null) {
                log.error("【资方】合同列表查询失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "合同列表查询失败，远程接口调用异常");
            }
            ContractListVo vo = map.toJavaObject(ContractListVo.class);
            return ResponseResult.success(vo);
        } catch (Exception e) {
            log.error("【资方】合同列表查询异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<ContractSignedQueryVo> contractSignedQuery(ContractSignedQueryDto contractSignedQueryDto) {
        String jsonString = JSONObject.toJSONString(contractSignedQueryDto);
        JSONObject params = JSONObject.parseObject(jsonString);
        params.put("reqSysCode", ziFangProperties.getReqSysCode());
        params.put("productCode", ziFangProperties.getProductCode());
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.contractSignedQueryUrl);
            if (map == null) {
                log.error("【资方】合同签署查询失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "合同签署查询失败，远程接口调用异常");
            }
            ContractSignedQueryVo vo = map.toJavaObject(ContractSignedQueryVo.class);
            return ResponseResult.success(vo);
        } catch (Exception e) {
            log.error("【资方】合同签署查询异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<LoanElementVo> loanElement(LoanElementDto loanElementDto) {
        JSONObject params = new JSONObject();
        params.put("reqSysCode", ziFangProperties.getReqSysCode());
        params.put("productCode", ziFangProperties.getProductCode());
        params.put("creditNo", loanElementDto.getCreditNo());
        if (StringUtils.isNotEmpty(loanElementDto.getLoanNo())) {
            params.put("loanNo", loanElementDto.getLoanNo());
        }
        params.put("userId", loanElementDto.getUserId());
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.getLoanElementUrl);
            if (map == null) {
                log.error("【资方】获取支用要素失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "获取支用要素失败，远程接口调用异常");
            }
            LoanElementVo vo = map.toJavaObject(LoanElementVo.class);
            return ResponseResult.success(vo);
        } catch (Exception e) {
            log.error("【资方】获取支用要素异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<CreditApplyVo> loanApply(LoanApplyDto loanApplyDto) {
        JSONObject params = new JSONObject();
        params.put("reqSysCode", ziFangProperties.getReqSysCode());
        params.put("productCode", ziFangProperties.getProductCode());
        params.put("loanInfo", loanApplyDto);
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.loanApplyUrl);
            if (map == null) {
                log.error("【资方】支用申请失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "支用申请失败，远程接口调用异常");
            }
            CreditApplyVo vo = map.toJavaObject(CreditApplyVo.class);
            return ResponseResult.success(vo);
        } catch (Exception e) {
            log.error("【资方】支用申请异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<UseLoanQueryVo> useLoanQuery(UseLoanQueryDto useLoanQueryDto) {
        JSONObject params = new JSONObject();
        params.put("reqSysCode", ziFangProperties.getReqSysCode());
        params.put("productCode", ziFangProperties.getProductCode());
        params.put("loanNo", useLoanQueryDto.getLoanNo());
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.useLoanQueryUrl);
            if (map == null) {
                log.error("【资方】支用结果查询失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "支用结果查询失败，远程接口调用异常");
            }
            UseLoanQueryVo vo = map.toJavaObject(UseLoanQueryVo.class);
            return ResponseResult.success(vo);
        } catch (Exception e) {
            log.error("【资方】支用结果查询异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<RepayPlanCalcVo> repayPlanCalc(RepayPlanCalcDto repayPlanCalcDto) {
        JSONObject params = new JSONObject();
        params.put("amount", repayPlanCalcDto.getAmount());
        params.put("period", repayPlanCalcDto.getPeriod());
        params.put("reqSysCode", ziFangProperties.getReqSysCode());
        params.put("productCode", ziFangProperties.getProductCode());
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.repayPlanCalcUrl);
            if (map == null) {
                log.error("【资方】还款计划试算失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "还款计划试算失败，远程接口调用异常");
            }
            RepayPlanCalcVo vo = map.toJavaObject(RepayPlanCalcVo.class);
            return ResponseResult.success(vo);
        } catch (Exception e) {
            log.error("【资方】还款计划试算异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<SaleApplyVo> getSaleApply(SaleApplyDto applyDto) {
        String reg = JSONObject.toJSONString(applyDto);
        JSONObject params = JSONObject.parseObject(reg);
        params.put("reqSysCode", ziFangProperties.getReqSysCode());
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.getSaleApply);
            if (map == null) {
                log.error("【资方】寄售申请失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "寄售申请失败，远程接口调用异常");
            }
            SaleApplyVo ziFangVo = map.toJavaObject(SaleApplyVo.class);
            return ResponseResult.success(ziFangVo);
        } catch (Exception e) {
            log.error("【资方】寄售申请异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<RespSaleRepayPlanVo> getApiSaleApply(SaleApplyDto applyDto) {
        String reg = JSONObject.toJSONString(applyDto);
        JSONObject params = JSONObject.parseObject(reg);

        params.put("reqSysCode", ziFangProperties.getReqSysCode2());
        params.put("productCode", ziFangProperties.getProductCode2());
        params.put("saleNo", applyDto.getSaleNo());
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.getApiSaleApply,
                    ziFangProperties.getChannelCode2());
            if (map == null) {
                log.error("【资方】API寄售申请(含计划)失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "API寄售申请(含计划)失败，远程接口调用异常");
            }
            RespSaleRepayPlanVo ziFangVo = map.toJavaObject(RespSaleRepayPlanVo.class);
            return ResponseResult.success(ziFangVo);
        } catch (Exception e) {
            log.error("【资方】API寄售申请(含计划)异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<SaleResultVo> getSaleResult(SaleResultDto resultDto) {
        String reg = JSONObject.toJSONString(resultDto);
        JSONObject params = JSONObject.parseObject(reg);
        params.put("reqSysCode", ziFangProperties.getReqSysCode());
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.getSaleResult);
            if (map == null) {
                log.error("【资方】寄售结果查询失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "寄售结果查询失败，远程接口调用异常");
            }
            SaleResultVo ziFangVo = map.toJavaObject(SaleResultVo.class);
            return ResponseResult.success(ziFangVo);
        } catch (Exception e) {
            log.error("【资方】寄售结果查询异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<SaleSignedQueryVo> getSaleSignedQuery(SaleSignedQueryDto queryDto) {
        String reg = JSONObject.toJSONString(queryDto);
        JSONObject params = JSONObject.parseObject(reg);
        params.put("reqSysCode", ziFangProperties.getReqSysCode());
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.getSaleSignedQuery);
            if (map == null) {
                log.error("【资方】寄售协议签署查询失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "寄售协议签署查询失败，远程接口调用异常");
            }
            SaleSignedQueryVo ziFangVo = map.toJavaObject(SaleSignedQueryVo.class);
            return ResponseResult.success(ziFangVo);
        } catch (Exception e) {
            log.error("【资方】寄售协议签署查询异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<ContractSignedQueryVo> apiContractSignedQuery(ContractSignedQueryDto contractSignedQueryDto) {
        String jsonString = JSONObject.toJSONString(contractSignedQueryDto);
        JSONObject params = JSONObject.parseObject(jsonString);
        params.put("reqSysCode", ziFangProperties.getReqSysCode2());
        params.put("productCode", ziFangProperties.getProductCode2());
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.contractSignedQueryUrl,
                    ziFangProperties.getChannelCode2());
            if (map == null) {
                log.error("【资方】API合同签署查询失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "API合同签署查询失败，远程接口调用异常");
            }
            ContractSignedQueryVo vo = map.toJavaObject(ContractSignedQueryVo.class);
            return ResponseResult.success(vo);
        } catch (Exception e) {
            log.error("【资方】API合同签署查询异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<SaleSignedQueryVo> getApiSaleSignedQuery(SaleSignedQueryDto queryDto) {
        String reg = JSONObject.toJSONString(queryDto);
        JSONObject params = JSONObject.parseObject(reg);
        params.put("reqSysCode", ziFangProperties.getReqSysCode2());
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.getSaleSignedQuery,
                    ziFangProperties.getChannelCode2());
            if (map == null) {
                log.error("【资方】API寄售协议签署查询失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "API寄售协议签署查询失败，远程接口调用异常");
            }
            SaleSignedQueryVo ziFangVo = map.toJavaObject(SaleSignedQueryVo.class);
            return ResponseResult.success(ziFangVo);
        } catch (Exception e) {
            log.error("【资方】API寄售协议签署查询异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<SaleRepayApplyVo> getSaleRepayApply(SaleRepayApplyDto applyDto) {
        String reg = JSONObject.toJSONString(applyDto);
        JSONObject params = JSONObject.parseObject(reg);
        params.put("reqSysCode", ziFangProperties.getReqSysCode());
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.getSaleRepayApply);
            if (map == null) {
                log.error("【资方】获取销售还款申请失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "获取销售还款申请失败，远程接口调用异常");
            }
            SaleRepayApplyVo ziFangVo = map.toJavaObject(SaleRepayApplyVo.class);
            return ResponseResult.success(ziFangVo);
        } catch (Exception e) {
            log.error("【资方】获取销售还款申请异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<SaleRepayResultVo> getSaleRepayResult(SaleRepayResultDto applyDto) {
        String reg = JSONObject.toJSONString(applyDto);
        JSONObject params = JSONObject.parseObject(reg);
        params.put("reqSysCode", ziFangProperties.getReqSysCode());
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.getSaleRepayResult);
            if (map == null) {
                log.error("【资方】寄售还款结果查询失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "寄售还款结果查询失败，远程接口调用异常");
            }
            SaleRepayResultVo ziFangVo = map.toJavaObject(SaleRepayResultVo.class);
            return ResponseResult.success(ziFangVo);
        } catch (Exception e) {
            log.error("【资方】寄售还款结果查询异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<SaleRepayPlanVo> getSaleRepayPlan(SaleRepayPlanDto planDto) {
        String reg = JSONObject.toJSONString(planDto);
        JSONObject params = JSONObject.parseObject(reg);
        params.put("reqSysCode", ziFangProperties.getReqSysCode());
        params.put("productCode", ziFangProperties.getProductCode());
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.getSaleRepayPlan);
            if (map == null) {
                log.error("【资方】寄售还款计划查询失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "寄售还款计划查询失败，远程接口调用异常");
            }
            SaleRepayPlanVo ziFangVo = map.toJavaObject(SaleRepayPlanVo.class);
            return ResponseResult.success(ziFangVo);
        } catch (Exception e) {
            log.error("【资方】寄售还款计划查询异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<SaleRepayReturnFeeVo> getSaleRepayReturnFee(SaleRepayReturnFeeDto feeDto) {
        String reg = JSONObject.toJSONString(feeDto);
        JSONObject params = JSONObject.parseObject(reg);
        params.put("reqSysCode", ziFangProperties.getReqSysCode());
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.getSaleRepayReturnFee);
            if (map == null) {
                log.error("【资方】寄售退费试算失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "寄售退费试算失败，远程接口调用异常");
            }
            SaleRepayReturnFeeVo ziFangVo = map.toJavaObject(SaleRepayReturnFeeVo.class);
            return ResponseResult.success(ziFangVo);
        } catch (Exception e) {
            log.error("【资方】寄售退费试算异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<SaleRefundResultVo> getSaleRefundResult(SaleRefundResultDto dto) {
        String reg = JSONObject.toJSONString(dto);
        JSONObject params = JSONObject.parseObject(reg);
        params.put("reqSysCode", ziFangProperties.getReqSysCode());
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.getSaleRefundResult);
            if (map == null) {
                log.error("【资方】寄售退费结果查询失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "寄售退费结果查询失败，远程接口调用异常");
            }
            SaleRefundResultVo ziFangVo = map.toJavaObject(SaleRefundResultVo.class);
            return ResponseResult.success(ziFangVo);
        } catch (Exception e) {
            log.error("【资方】寄售退费结果查询异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<HfUrlVo> getH5Url(HfUrlDto dto) {
        String reg = JSONObject.toJSONString(dto);
        JSONObject params = JSONObject.parseObject(reg);
        params.put("reqSysCode", ziFangProperties.getReqSysCode());
        params.put("productCode", ziFangProperties.getProductCode());
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.getH5Url);
            if (map == null) {
                log.error("【资方】获取H5页面地址失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "获取H5页面地址失败，远程接口调用异常");
            }
            HfUrlVo ziFangVo = map.toJavaObject(HfUrlVo.class);
            return ResponseResult.success(ziFangVo);
        } catch (Exception e) {
            log.error("【资方】获取H5页面地址异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<PreRepayApplyVo> getPreRepayApply(PreRepayApplyDto applyDto) {
        if (applyDto == null) {
            log.error("【资方】 还款申请 还款申请为空");
            return ResponseResult.error(ErrorCodeEnum.FAIL, "还款申请为空");
        }
        JSONObject params = new JSONObject();
        params.put("reqSysCode", ziFangProperties.getReqSysCode());
        params.put("fundCode", applyDto.getFundCode());
        params.put("merserno", applyDto.getMerserno());
        params.put("userId", applyDto.getUserId());
        params.put("loanNo", applyDto.getLoanNo());
        params.put("prePayType", applyDto.getPrePayType());
        params.put("paytotalamt", applyDto.getPaytotalamt());
        params.put("term", applyDto.getTerm());
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.getPreRepayApply);
            if (map == null) {
                log.error("【资方】获取提前还款申请失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "获取提前还款申请失败，远程接口调用异常");
            }
            PreRepayApplyVo ziFangVo = map.toJavaObject(PreRepayApplyVo.class);
            return ResponseResult.success(ziFangVo);
        } catch (Exception e) {
            log.error("【资方】获取提前还款申请异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<RepaymentPlanQueryVo> getRepaymentPlanQuery(RepaymentPlanQueryDto queryDto) {
        JSONObject params = new JSONObject();
        params.put("reqSysCode", ziFangProperties.getReqSysCode());
        params.put("productCode", ziFangProperties.getProductCode());
        params.put("userId", queryDto.getUserId());
        params.put("loanNo", queryDto.getLoanNo());
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.getRepaymentPlanQuery);
            if (map == null) {
                log.error("【资方】还款计划查询失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "还款计划查询失败，远程接口调用异常");
            }
            RepaymentPlanQueryVo ziFangVo = map.toJavaObject(RepaymentPlanQueryVo.class);
            return ResponseResult.success(ziFangVo);
        } catch (Exception e) {
            log.error("【资方】还款计划查询异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    /**
     * api方式查询还款计划
     *
     * @param queryDto
     * @return
     */
    @Override
    public ResponseResult<RespRepayPlanVo> getApiRepaymentPlanQuery(RepaymentPlanQueryDto queryDto) {
        JSONObject params = new JSONObject();

        params.put("reqSysCode", ziFangProperties.getReqSysCode2());
        params.put("productCode", ziFangProperties.getProductCode2());
        params.put("loanNo", queryDto.getLoanNo());
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.getApiRepaymentPlanQuery,
                ziFangProperties.getChannelCode2());
            if (map == null) {
                log.error("【资方】API还款计划查询失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "API还款计划查询失败，远程接口调用异常");
            }
            RespRepayPlanVo ziFangVo = map.toJavaObject(RespRepayPlanVo.class);
            return ResponseResult.success(ziFangVo);
        } catch (Exception e) {
            log.error("【资方】API还款计划查询异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }
     

    

    @Override
    public ResponseResult<RepaymentApplyVo> getRepaymentApply(RepaymentApplyDto applyDto) {
        String reg = JSONObject.toJSONString(applyDto);
        JSONObject params = JSONObject.parseObject(reg);
        params.put("reqSysCode", ziFangProperties.getReqSysCode());
        params.put("fundCode", applyDto.getFundCode());
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.getRepaymentApply);
            if (map == null) {
                log.error("【资方】还款申请失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "还款申请失败，远程接口调用异常");
            }
            RepaymentApplyVo ziFangVo = map.toJavaObject(RepaymentApplyVo.class);
            return ResponseResult.success(ziFangVo);
        } catch (Exception e) {
            log.error("【资方】还款申请异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    
    }

    @Override
    public ResponseResult<RepaymentResultVo> getRepaymentResult(RepaymentResultDto resultDto) {
        String reg = JSONObject.toJSONString(resultDto);
        JSONObject params = JSONObject.parseObject(reg);
        params.put("reqSysCode", ziFangProperties.getReqSysCode());
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.getRepaymentResult);
            if (map == null) {
                log.error("【资方】还款结果查询失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "还款结果查询失败，远程接口调用异常");
            }
            RepaymentResultVo ziFangVo = map.toJavaObject(RepaymentResultVo.class);
            return ResponseResult.success(ziFangVo);
        } catch (Exception e) {
            log.error("【资方】还款结果查询异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    @Override
    public ResponseResult<BuybackBankInfoVo> getBuybackBankInfo(BuybackBankInfoDto infoDto) {
        String reg = JSONObject.toJSONString(infoDto);
        JSONObject params = JSONObject.parseObject(reg);
        params.put("reqSysCode", ziFangProperties.getReqSysCode());
        try {
            JSONObject map = this.sendRequest(params, ZiFangConstant.getBuybackBankInfo);
            if (map == null) {
                log.error("【资方】获取回购后划扣账户信息失败：sendRequest 返回 null，请求参数：{}", params.toJSONString());
                return ResponseResult.error(ErrorCodeEnum.FAIL, "查询代偿回购打款账户信息失败，远程接口调用异常");
            }
            BuybackBankInfoVo ziFangVo = map.toJavaObject(BuybackBankInfoVo.class);
            return ResponseResult.success(ziFangVo);
        } catch (Exception e) {
            log.error("【资方】获取回购后划扣账户信息异常，请求参数：{}，异常：", params.toJSONString(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

}
