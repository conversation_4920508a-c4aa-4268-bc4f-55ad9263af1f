//package com.rongchen.byh.common.api.zifang.service.impl;
//
//import com.alibaba.fastjson.JSONObject;
//import com.rongchen.byh.common.api.zifang.config.ZiFangConstant;
//import com.rongchen.byh.common.api.zifang.dto.*;
//import com.rongchen.byh.common.api.zifang.service.OtherApi;
//import com.rongchen.byh.common.api.zifang.vo.*;
//import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
//import com.rongchen.byh.common.core.object.ResponseResult;
//import org.springframework.stereotype.Service;
//
///**
// * @ClassName OtherApiImpl
// * @Description TODO
// * <AUTHOR>
// * @Date 2024/12/9 18:03
// * @Version 1.0
// **/
//@Service
//public class OtherApiImpl extends AbstractApi implements OtherApi {
//
//
//    @Override
//    public ResponseResult<SaleApplyVo> getSaleApply(SaleApplyDto applyDto) {
//        String reg = JSONObject.toJSONString(applyDto);
//        JSONObject params = JSONObject.parseObject(reg);
//        params.put("reqSysCode", ziFangProperties.getReqSysCode());
//        try {
//            JSONObject map = this.sendRequest(params, ZiFangConstant.getSaleApply);
//            SaleApplyVo ziFangVo = map.toJavaObject(SaleApplyVo.class);
//            return ResponseResult.success(ziFangVo);
//        } catch (Exception e) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL,e.getMessage());
//        }
//    }
//
//    @Override
//    public ResponseResult<SaleResultVo> getSaleResult(SaleResultDto resultDto) {
//        String reg = JSONObject.toJSONString(resultDto);
//        JSONObject params = JSONObject.parseObject(reg);
//        params.put("reqSysCode", ziFangProperties.getReqSysCode());
//        try {
//            JSONObject map = this.sendRequest(params, ZiFangConstant.getSaleResult);
//            SaleResultVo ziFangVo = map.toJavaObject(SaleResultVo.class);
//            return ResponseResult.success(ziFangVo);
//        } catch (Exception e) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL,e.getMessage());
//        }
//    }
//
//    @Override
//    public ResponseResult<SaleSignedQueryVo> getSaleSignedQuery(SaleSignedQueryDto queryDto) {
//        String reg = JSONObject.toJSONString(queryDto);
//        JSONObject params = JSONObject.parseObject(reg);
//        params.put("reqSysCode", ziFangProperties.getReqSysCode());
//        try {
//            JSONObject map = this.sendRequest(params, ZiFangConstant.getSaleSignedQuery);
//            SaleSignedQueryVo ziFangVo = map.toJavaObject(SaleSignedQueryVo.class);
//            return ResponseResult.success(ziFangVo);
//        } catch (Exception e) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL,e.getMessage());
//        }
//    }
//
//    @Override
//    public ResponseResult<SaleRepayApplyVo> getSaleRepayApply(SaleRepayApplyDto applyDto) {
//        String reg = JSONObject.toJSONString(applyDto);
//        JSONObject params = JSONObject.parseObject(reg);
//        params.put("reqSysCode", ziFangProperties.getReqSysCode());
//        try {
//            JSONObject map = this.sendRequest(params, ZiFangConstant.getSaleRepayApply);
//            SaleRepayApplyVo ziFangVo = map.toJavaObject(SaleRepayApplyVo.class);
//            return ResponseResult.success(ziFangVo);
//        } catch (Exception e) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL,e.getMessage());
//        }
//    }
//
//    @Override
//    public ResponseResult<SaleRepayResultVo> getSaleRepayResult(SaleRepayResultDto applyDto) {
//        String reg = JSONObject.toJSONString(applyDto);
//        JSONObject params = JSONObject.parseObject(reg);
//        params.put("reqSysCode", ziFangProperties.getReqSysCode());
//        try {
//            JSONObject map = this.sendRequest(params, ZiFangConstant.getSaleRepayResult);
//            SaleRepayResultVo ziFangVo = map.toJavaObject(SaleRepayResultVo.class);
//            return ResponseResult.success(ziFangVo);
//        } catch (Exception e) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL,e.getMessage());
//        }
//    }
//
//    @Override
//    public ResponseResult<SaleRepayPlanVo> getSaleRepayPlan(SaleRepayPlanDto planDto) {
//        String reg = JSONObject.toJSONString(planDto);
//        JSONObject params = JSONObject.parseObject(reg);
//        params.put("reqSysCode", ziFangProperties.getReqSysCode());
//        params.put("productCode", ziFangProperties.getProductCode());
//        try {
//            JSONObject map = this.sendRequest(params, ZiFangConstant.getSaleRepayPlan);
//            SaleRepayPlanVo ziFangVo = map.toJavaObject(SaleRepayPlanVo.class);
//            return ResponseResult.success(ziFangVo);
//        } catch (Exception e) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL,e.getMessage());
//        }
//    }
//
//    @Override
//    public ResponseResult<SaleRepayReturnFeeVo> getSaleRepayReturnFee(SaleRepayReturnFeeDto feeDto) {
//        String reg = JSONObject.toJSONString(feeDto);
//        JSONObject params = JSONObject.parseObject(reg);
//        params.put("reqSysCode", ziFangProperties.getReqSysCode());
//        try {
//            JSONObject map = this.sendRequest(params, ZiFangConstant.getSaleRepayReturnFee);
//            SaleRepayReturnFeeVo ziFangVo = map.toJavaObject(SaleRepayReturnFeeVo.class);
//            return ResponseResult.success(ziFangVo);
//        } catch (Exception e) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL,e.getMessage());
//        }
//    }
//
//    @Override
//    public ResponseResult<SaleRefundResultVo> getSaleRefundResult(SaleRefundResultDto dto) {
//        String reg = JSONObject.toJSONString(dto);
//        JSONObject params = JSONObject.parseObject(reg);
//        params.put("reqSysCode", ziFangProperties.getReqSysCode());
//        try {
//            JSONObject map = this.sendRequest(params, ZiFangConstant.getSaleRefundResult);
//            SaleRefundResultVo ziFangVo = map.toJavaObject(SaleRefundResultVo.class);
//            return ResponseResult.success(ziFangVo);
//        } catch (Exception e) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL,e.getMessage());
//        }
//    }
//
//    @Override
//    public ResponseResult<HfUrlVo> getH5Url(HfUrlDto dto) {
//        String reg = JSONObject.toJSONString(dto);
//        JSONObject params = JSONObject.parseObject(reg);
//        params.put("reqSysCode", ziFangProperties.getReqSysCode());
//        params.put("productCode", ziFangProperties.getProductCode());
//        try {
//            JSONObject map = this.sendRequest(params, ZiFangConstant.getH5Url);
//            HfUrlVo ziFangVo = map.toJavaObject(HfUrlVo.class);
//            return ResponseResult.success(ziFangVo);
//        } catch (Exception e) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL,e.getMessage());
//        }
//    }
//
//
//}
