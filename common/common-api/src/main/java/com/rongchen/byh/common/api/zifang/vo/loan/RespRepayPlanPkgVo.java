package com.rongchen.byh.common.api.zifang.vo.loan;

import lombok.Data;

@Data
public class RespRepayPlanPkgVo {
    private String repayTerm; // 还款期数
    private String repayOwnbDate; // 还款起日
    private String repayOwneDate; // 还款止日
    private String repayIntbDate; // 本期起日
    private String repayInteDate; // 本期止日
    private String totalAmt; // 本期应还总金额
    private String termRetPrin; // 本期应还本金
    private String prinAmt; // 本期已还本金
    private String noRetAmt; // 本期未还本金
    private String termRetInt; // 本期应还利息
    private String intAmt; // 本期已还利息
    private String noRetInt; // 本期未还利息
    private String termRetFint; // 本期应还罚息
    private String termFintFinish; // 本期已还罚息
    private String noRetFin; // 本期未还罚息
    private String termRetFee; // 本期应还保费
    private String feeAmt; // 本期已还保费
    private String noRetFee; // 本期未还保费
    private String termGuarantorFee; // 本期应还融担费
    private String guarantorFee; // 本期已还融担费
    private String noGuarantorFee; // 本期未还融担费
    private String termServiceFee; // 本期应还服务费
    private String serviceFee; // 本期已还服务费
    private String noServiceFee; // 本期未还服务费
    private String termStatus; // 计划状态
    private String settleFlag; // 结清标志
    private String datePay; // 实际还款日
    private String termRetOverFee; // 本期应还超次提现手续费
    private String overFee; // 本期已还超次提现手续费
    private String noRetOverFee; // 本期未还超次提现手续费
    private String otherFeeName; // 自定义费用名称
    private String termOtherFee; // 本期应还自定义费用
    private String retOtherFee; // 本期已还自定义费用
    private String noRetOtherFee; // 本期未还自定义费用
}
