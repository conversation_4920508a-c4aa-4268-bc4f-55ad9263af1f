package com.rongchen.byh.common.api.zifang.dto;

import lombok.Data;

/**
 * @ClassName PreRepayApplyDto
 * @Description 还款试算接口
 * <AUTHOR>
 * @Date 2024/12/9 14:36
 * @Version 1.0
 **/
@Data
public class PreRepayApplyDto {



    /**
     * 交易流水号
     */
    private String merserno;

    /**
     * 资金方编码
     */
    private String fundCode;

    /**
     * 用户编号
     */
    private String userId;

    /**
     * 借款订单
     */
    private String loanNo;

    /**
     * 还款类型
     *
     * 1-账单日还款试算
     * 2-提前还当期
     * 3-全部提前还款
     * 4-逾期结清
     * 5-归还逾期
     */
    private String prePayType;

    /**
     * 还款总金额
     *
     * 提前还当期：传当期应还本金
     * 提前全部结清：传借据剩余待还本金。单位元
     */
    private String paytotalamt;

    /**
     * 期数
     *
     * 结清时，可传结清起始期次
     */
    private String term;



}
