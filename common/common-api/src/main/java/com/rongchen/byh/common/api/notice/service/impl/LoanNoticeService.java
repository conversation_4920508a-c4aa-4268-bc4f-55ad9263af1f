package com.rongchen.byh.common.api.notice.service.impl;

import com.rongchen.byh.common.api.notice.config.NoticeSmsContent;
import com.rongchen.byh.common.api.notice.dto.LoanNoticeDto;
import com.rongchen.byh.common.api.notice.dto.NoticeSmsDto;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 借款通知接口
 * @date 2024/12/12 16:55:13
 */
@Service
public class LoanNoticeService extends AbstractNoticeSmsService {
    @Override
    protected String getContent(NoticeSmsDto noticeSmsDto) {
        LoanNoticeDto smsDto = (LoanNoticeDto) noticeSmsDto;
        return smsDto.getUserName() + "##" + smsDto.getContractNum() + "##" + smsDto.getBankAccount()
                + "##" + smsDto.getLoanAmount() + "##" + smsDto.getLoanTerm() + "##" + smsDto.getRepaymentDate();
    }

    @Override
    protected Integer getTemplateId() {
        return NoticeSmsContent.LOANNOTICE;
    }
}
