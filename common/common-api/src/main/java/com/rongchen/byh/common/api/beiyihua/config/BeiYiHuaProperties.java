package com.rongchen.byh.common.api.beiyihua.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025/3/26 18:18:00
 */
@Data
@ConfigurationProperties(prefix = "beiyihua")
public class BeiYiHuaProperties {
    /**
     * 额度推送地址
     */
    private String creditPushUrl;
    /**
     * 授信回调地址
     */
    private String byhCreditNoticeUrl;
    /**
     * 用信回调地址
     */
    private String byhLoanNoticeUrl;
    /**
     * 账单推送地址
     */
    private String byhBillChangeUrl;
    /**
     * 还款回调地址
     */
    private String byhRepayNoticeUrl;
    /**
     * 云樨回调地址
     */
    private String yunXiCallbackUrl;

    /**
     * 老资方授信回调地址
     */
    private String byhOldCreditNoticeUrl;

    /**
     * 老资方用信回调地址
     */
    private String byhOldLoanNoticeUrl;

    /**
     * 老资方还款回调地址
     */
    private String byhOldRepayNoticeUrl;

    /**
     * 老资方赊销回调地址
     */
    private String byhOldSaleNoticeUrl;
}
