package com.rongchen.byh.common.api.notice.config;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 通知短信
 * @date 2024/12/12 10:40:18
 */
@ConfigurationProperties(prefix = "notice.sms")
@Data
public class NoticeSmsProperties {

    private String noticeSmsUrl;

    private String accesskey;

    private String secret;

    private String sign;

    private String noticeJsonSmsUrl;

    private String accountName;

    private String companyId;

    private String loginName;

    private String loginPassword;
}
