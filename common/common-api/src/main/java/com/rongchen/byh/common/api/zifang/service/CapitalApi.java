package com.rongchen.byh.common.api.zifang.service;

import com.rongchen.byh.common.api.zifang.dto.ApiGateWayDto;
import com.rongchen.byh.common.api.zifang.dto.ApplyCheckDto;
import com.rongchen.byh.common.api.zifang.dto.CreditApplyDto;
import com.rongchen.byh.common.api.zifang.dto.CreditAuditDto;
import com.rongchen.byh.common.api.zifang.vo.ApiGateWayListVo;
import com.rongchen.byh.common.api.zifang.vo.ApplyCheckVo;
import com.rongchen.byh.common.api.zifang.vo.CreditApplyVo;
import com.rongchen.byh.common.api.zifang.vo.CreditAuditVo;
import com.rongchen.byh.common.core.object.ResponseResult;

/**
 * 资方对接接口
 */
public interface CapitalApi {


    /**
     * 查询用户初筛结果
     * @return
     */
    ResponseResult<ApplyCheckVo> applyCheck(ApplyCheckDto applyCheckDto);



    /**
     * 用户授信
     * @return
     */
    ResponseResult<CreditApplyVo> creditApply(CreditApplyDto creditApplyDto);

    /**
     * 用户授信审批结果查询
     * @return
     */
    ResponseResult<CreditAuditVo> getCreditAudit(CreditAuditDto creditAuditDto);


    /**
     * 分转  产品查询接口
     * @return
     */
    ResponseResult<ApiGateWayListVo> apiGateWay(ApiGateWayDto apiGateWayDto);



}
