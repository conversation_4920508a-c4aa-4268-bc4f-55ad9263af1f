package com.rongchen.byh.common.api.zifang.dto;

import lombok.Data;

import java.util.List;

/**
 * @ClassName SaleRepayApplyDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/9 18:57
 * @Version 1.0
 **/
@Data
public class SaleRepayApplyDto {

    /**
     * 必填，还款申请流水，备注：用于唯一标识每一次还款申请的流水号，方便后续追踪与处理。
     */
    private String repayApplyNo;
    /**
     * 非必填，用户编号，备注：用于标识具体的用户（若有需要关联用户相关信息时使用）。
     */
    private String userId;
    /**
     * 必填，还款方式，备注：取值为0表示线上还款，1表示线下还款，用于指定此次还款采用的具体途径。
     */
    private String repayMethod;
    /**
     * 必填，还款类型，备注：有PREPAYMENT（提前还当期）、CLEAN（全部结清，提前结清）、OVERDUE（归还逾期）、
     * CURRENT（归还当期到期，账期日还款）、OVER（归还到期，逾期 + 当期到期）、OVERCLEAN（逾期结清）等取值，用于明确此次还款所属的具体类型。
     */
    private String repayType;
    /**
     * 必填，银行预留手机号，备注：用于在还款相关操作中进行必要的验证等流程使用。
     */
    private String bankPhoneNo;
    /**
     * 必填，还款总金额，备注：格式为17位整数2位小数，单位为元，代表此次还款涉及的总金额数目。
     */
    private String amount;
    /**
     * 必填，银行卡号，备注：用于指定此次还款所使用的银行卡信息。
     */
    private String bankCardNo;
    /**
     * 必填，账户卡类型，备注：取值为1表示借记，2表示贷记，用于表明客户账户的借记贷记类型。
     */
    private String accountCardType;
    /**
     * 必填，身份证号码，备注：用于对用户身份进行核实等相关操作。
     */
    private String idNo;
    /**
     * 必填，用户姓名，备注：代表进行此次还款操作的用户的具体姓名。
     */
    private String customerName;
    /**
     * 必填，银行卡支行名称，备注：用于明确该银行卡所属的具体支行相关信息。
     */
    private String branchName;
    /**
     * 必填，银行卡类型，备注：取值为0表示对私，1表示对公，用于区分银行卡对应的账户性质类型。
     */
    private String bankAccountType;

    /**
     * 本次还款的贷款明细列表
     */
    private List<SaleRepayApplyRepayDto> repayList;
    /**
     * 赊销订单号
     */
    private String saleNo;
    /**
     * 外部赊销订单号
     */
    private String outSaleNo;
}
