//package com.rongchen.byh.common.api.zifang.service.impl;
//
//import com.alibaba.fastjson.JSONObject;
//import com.rongchen.byh.common.api.zifang.config.ZiFangConstant;
//import com.rongchen.byh.common.api.zifang.dto.BindBankSmsDto;
//import com.rongchen.byh.common.api.zifang.dto.QueryBindBankResultDto;
//import com.rongchen.byh.common.api.zifang.dto.UsableBankListDto;
//import com.rongchen.byh.common.api.zifang.dto.VerifyBindBankSmsDto;
//import com.rongchen.byh.common.api.zifang.service.BindCardApi;
//import com.rongchen.byh.common.api.zifang.vo.*;
//import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
//import com.rongchen.byh.common.core.object.ResponseResult;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.stereotype.Service;
//
///**
// * @ClassName BindCardApiImpl
// * @Description 绑卡相关接口
// * <AUTHOR>
// * @Date 2024/12/6 18:04
// * @Version 1.0
// **/
//@Slf4j
//@Service
//public class BindCardApiImpl extends AbstractApi implements BindCardApi {
//
//
//    @Override
//    public ResponseResult<UsableBankListVo> getUsableBankList(UsableBankListDto bankListDto) {
//        JSONObject params = new JSONObject();
//        params.put("reqSysCode", ziFangProperties.getReqSysCode());
//        params.put("productCode", ziFangProperties.getProductCode());
//        try {
//            JSONObject map = this.sendRequest(params, ZiFangConstant.getUsableBankListUrl);
//            UsableBankListVo ziFangVo = map.toJavaObject(UsableBankListVo.class);
//            ziFangVo.setResponseCode(map.getString("responseCode"));
//            ziFangVo.setResponseMsg(map.getString("responseMsg"));
//            ziFangVo.setBankCardListsVos(map.getJSONArray("bankCardLists").toJavaList(BankCardListsVo.class));
//            return ResponseResult.success(ziFangVo);
//        } catch (Exception e) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL,e.getMessage());
//        }
//    }
//
//    @Override
//    public ResponseResult<BindBankSmsVo> getBindBankSMS(BindBankSmsDto bindBankSmsDto) {
//        JSONObject params = new JSONObject();
//        params.put("serialNo", bindBankSmsDto.getSerialNo());
//        params.put("reqSysCode", ziFangProperties.getReqSysCode());
//        params.put("productCode", ziFangProperties.getProductCode());
//        params.put("scene" , bindBankSmsDto.getScene());
//        params.put("payChannel" , bindBankSmsDto.getPayChannel());
////        if (StringUtils.isNotEmpty(bindBankSmsDto.getLoanNo()) ){
////            params.put("loanNo", bindBankSmsDto.getLoanNo());
////        }
//        params.put("idNo", bindBankSmsDto.getIdNo());
//        params.put("custName", bindBankSmsDto.getCustName());
//        params.put("phoneNo", bindBankSmsDto.getPhoneNo());
//        params.put("userId", bindBankSmsDto.getUserId());
//        params.put("bankCardNum", bindBankSmsDto.getBankCardNum());
//        params.put("bankName", bindBankSmsDto.getBankName());
//        try {
//            JSONObject map = this.sendRequest(params, ZiFangConstant.getBindBankSMSUrl);
//            BindBankSmsVo bindBankSmsVo = map.toJavaObject(BindBankSmsVo.class);
//            return ResponseResult.success(bindBankSmsVo);
//        } catch (Exception e) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL,e.getMessage());
//        }
//    }
//
//    @Override
//    public ResponseResult<VerifyBindBankSmsVo> getVerifyBindBankSMSUrl(VerifyBindBankSmsDto bindBankSmsDto) {
//        JSONObject params = new JSONObject();
//        params.put("serialNo", bindBankSmsDto.getSerialNo());
//        params.put("reqSysCode", ziFangProperties.getReqSysCode());
//        params.put("productCode", ziFangProperties.getProductCode());
//        params.put("scene" , bindBankSmsDto.getScene());
//        if (StringUtils.isNotEmpty(bindBankSmsDto.getLoanNo())){
//            params.put("loanNo" , bindBankSmsDto.getLoanNo());
//        }
//        params.put("phoneCode" , bindBankSmsDto.getPhoneCode());
//        params.put("messageNo" , bindBankSmsDto.getMessageNo());
//        params.put("bankCardNum" , bindBankSmsDto.getBankCardNum());
//        if (StringUtils.isNotEmpty(bindBankSmsDto.getIsDefault())){
//            params.put("isDefault" , bindBankSmsDto.getIsDefault());
//        }
//        try {
//            JSONObject map = this.sendRequest(params, ZiFangConstant.getVerifyBindBankSMSUrl);
//            VerifyBindBankSmsVo bindBankSmsVo = map.toJavaObject(VerifyBindBankSmsVo.class);
//            return ResponseResult.success(bindBankSmsVo);
//        } catch (Exception e) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL,e.getMessage());
//        }
//    }
//
//    @Override
//    public ResponseResult<QueryBindBankResultVo> getQueryBindBankResult(QueryBindBankResultDto bindBankSmsDto) {
//        JSONObject params = new JSONObject();
//        params.put("reqSysCode", ziFangProperties.getReqSysCode());
//        params.put("messageNo", bindBankSmsDto.getMessageNo());
//        try {
//            JSONObject map = this.sendRequest(params, ZiFangConstant.getQueryBindBankResult);
//            QueryBindBankResultVo bindBankResultVo = map.toJavaObject(QueryBindBankResultVo.class);
//            return ResponseResult.success(bindBankResultVo);
//        } catch (Exception e) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL,e.getMessage());
//        }
//    }
//
//
//}
