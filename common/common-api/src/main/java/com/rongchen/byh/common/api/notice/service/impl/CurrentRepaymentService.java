package com.rongchen.byh.common.api.notice.service.impl;

import cn.hutool.core.util.StrUtil;
import com.rongchen.byh.common.api.notice.config.NoticeSmsContent;
import com.rongchen.byh.common.api.notice.dto.CurrentRepaymentDto;
import com.rongchen.byh.common.api.notice.dto.NoticeSmsDto;
import com.rongchen.byh.common.core.util.MyModelUtil;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 当日到期还款提醒
 * @date 2025/2/7 11:58:05
 */
@Service
public class CurrentRepaymentService extends AbstractNoticeSmsService {
    @Override
    protected String getContent(NoticeSmsDto noticeSmsDto) {
        CurrentRepaymentDto dto = (CurrentRepaymentDto) noticeSmsDto;
        return StrUtil.format(NoticeSmsContent.DICT_MAP.get(NoticeSmsContent.CURRENTREPAYNOTICE), MyModelUtil.beanToMap(dto));
    }

    @Override
    protected Integer getTemplateId() {
        return null;
    }
}
