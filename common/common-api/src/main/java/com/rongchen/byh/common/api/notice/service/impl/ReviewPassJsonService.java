package com.rongchen.byh.common.api.notice.service.impl;

import cn.hutool.core.util.StrUtil;
import com.rongchen.byh.common.api.notice.config.NoticeSmsContent;
import com.rongchen.byh.common.api.notice.dto.NoticeSmsDto;
import com.rongchen.byh.common.api.notice.dto.ReviewPassDto;
import com.rongchen.byh.common.core.util.MyModelUtil;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 复审通过
 * @date 2025/2/11 09:57:18
 */
@Service
public class ReviewPassJsonService extends AbstractNoticeJsonSmsService{
    @Override
    protected String getContent(NoticeSmsDto noticeSmsDto) {
        ReviewPassDto dto = (ReviewPassDto) noticeSmsDto;
        return StrUtil.format(NoticeSmsContent.DICT_MAP.get(NoticeSmsContent.REVIEWPASSJSON), MyModelUtil.beanToMap(dto));
    }
}
