package com.rongchen.byh.common.api.baofu.util.rsa;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.*;
import java.security.cert.Certificate;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.util.Base64;
import java.util.Enumeration;

/**
 * <b>公私钥读取工具</b><br>
 * <br>
 * 
 * <AUTHOR>
 * @version 4.1.0
 */
public final class RsaReadUtil {

	/**
	 * 根据Cer文件读取公钥
	 * 
	 * @param pubCerPath
	 * @return
	 */
	public static PublicKey getPublicKeyFromFile(String pubCerPath) {
		FileInputStream pubKeyStream = null;
		try {
			pubKeyStream = new FileInputStream(pubCerPath);
			byte[] reads = new byte[pubKeyStream.available()];
			pubKeyStream.read(reads);
			pubKeyStream.close();
			return getPublicKeyByText(new String(reads));
		} catch (Exception e) {
			e.printStackTrace();
			// log.error("解析文件，读取公钥失败:", e);
		} finally {
			if (pubKeyStream != null) {
				try {
					pubKeyStream.close();
				} catch (Exception e) {
					e.printStackTrace();
					//
				}
			}
		}
		return null;
	}

	/**
	 * 根据公钥Cer文本串读取公钥
	 * 
	 * @param pubKeyText
	 * @return
	 */
	public static PublicKey getPublicKeyByText(String pubKeyText) {
		try {
			CertificateFactory certificateFactory = CertificateFactory.getInstance("X509");
			BufferedReader br = new BufferedReader(new StringReader(pubKeyText));
			String line = null;
			StringBuilder keyBuffer = new StringBuilder();
			while ((line = br.readLine()) != null) {
				if (!line.startsWith("-")) {
					keyBuffer.append(line);
				}
			}
			Certificate certificate = certificateFactory.generateCertificate(
					new ByteArrayInputStream(Base64.getDecoder().decode(keyBuffer.toString())));
			return certificate.getPublicKey();
		} catch (Exception e) {
			e.printStackTrace();
			// log.error("解析公钥内容失败:", e);
		}
		return null;
	}

	/**
	 * 根据私钥路径读取私钥
	 * 
	 * @param pfxPath
	 * @param priKeyPass
	 * @return
	 * @throws Exception
	 */
	public static PrivateKey getPrivateKeyFromFile(String pfxPath, String priKeyPass) throws Exception {
		InputStream priKeyStream = null;
		try {
			priKeyStream = Files.newInputStream(Paths.get(pfxPath));
			byte[] reads = new byte[priKeyStream.available()];
			priKeyStream.read(reads);
			return getPrivateKeyByStream(reads, priKeyPass);
		} catch (Exception e) {
			// 提供更详细的错误信息
			String errorMsg = "解析文件，读取私钥失败: " + pfxPath;
			if (e instanceof java.nio.file.NoSuchFileException) {
				errorMsg = "私钥文件不存在: " + pfxPath;
			} else if (e instanceof java.io.IOException) {
				errorMsg = "私钥文件IO异常: " + pfxPath + ", 错误: " + e.getMessage();
			}
			throw new Exception(errorMsg, e);
		} finally {
			if (priKeyStream != null) {
				try {
					priKeyStream.close();
				} catch (Exception e) {
					// 忽略关闭流的异常
				}
			}
		}
	}

	/**
	 * 根据PFX私钥字节流读取私钥
	 * 
	 * @param pfxBytes
	 * @param priKeyPass
	 * @return
	 * @throws Exception
	 */
	public static PrivateKey getPrivateKeyByStream(byte[] pfxBytes, String priKeyPass) throws Exception {
		try {
			KeyStore ks = KeyStore.getInstance("PKCS12");
			char[] charPriKeyPass = priKeyPass.toCharArray();
			ks.load(new ByteArrayInputStream(pfxBytes), charPriKeyPass);
			Enumeration<String> aliasEnum = ks.aliases();
			String keyAlias = null;
			if (aliasEnum.hasMoreElements()) {
				keyAlias = (String) aliasEnum.nextElement();
			}
			return (PrivateKey) ks.getKey(keyAlias, charPriKeyPass);
		} catch (IOException e) {
			e.printStackTrace();
			throw new Exception("解析文件，读取私钥失败:", e);
		} catch (KeyStoreException e) {
			e.printStackTrace();
			throw new Exception("解析文件，读取私钥失败:", e);
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
			throw new Exception("解析文件，读取私钥失败:", e);
		} catch (CertificateException e) {
			e.printStackTrace();
			throw new Exception("解析文件，读取私钥失败:", e);
		} catch (UnrecoverableKeyException e) {
			e.printStackTrace();
			throw new Exception("解析文件，读取私钥失败:", e);
		}
	}
}
