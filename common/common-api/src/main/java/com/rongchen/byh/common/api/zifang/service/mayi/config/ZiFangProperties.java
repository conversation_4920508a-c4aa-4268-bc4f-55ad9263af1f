package com.rongchen.byh.common.api.zifang.service.mayi.config;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@ConfigurationProperties(prefix = "zifang")
public class ZiFangProperties {

    /**
     * 请求域名
     */
    private String host;

    /**
     * 请求方
     */
    private String reqSysCode;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 资金方编码
     */
    private String fundCode;

    /**
     * 私钥
     */
    private String privateKey;

    /**
     * 公钥
     */
    private String publicKey;

    /**
     * 外部公钥
     */
    private String outPublicKey;



    /**
     * 请求方
     */
    private String reqSysCode2;

    /**
     * 渠道编码
     */
    private String channelCode2;

    /**
     * 产品编码
     */
    private String productCode2;

}
