package com.rongchen.byh.common.api.zifang.utils;

import java.util.HashMap;
import java.util.Map;

public class ProvinceCodeUtil {

    private ProvinceCodeUtil(){}


    private static final Map<String,String> map = new HashMap<>();

    private static final Map<String,String> provinceCodeMap = new HashMap<>();

    static {
        map.put("北京市","110000");
        map.put("天津市","120000");
        map.put("河北省","130000");
        map.put("山西省","140000");
        map.put("内蒙古自治区","150000");
        map.put("辽宁省","210000");
        map.put("吉林省","220000");
        map.put("黑龙江省","230000");
        map.put("上海市","310000");
        map.put("江苏省","320000");
        map.put("浙江省","330000");
        map.put("安徽省","340000");
        map.put("福建省","350000");
        map.put("江西省","360000");
        map.put("山东省","370000");
        map.put("河南省","410000");
        map.put("湖北省","420000");
        map.put("湖南省","430000");
        map.put("广东省","440000");
        map.put("广西壮族自治区","450000");
        map.put("海南省","460000");
        map.put("重庆市","500000");
        map.put("四川省","510000");
        map.put("贵州省","520000");
        map.put("云南省","530000");
        map.put("西藏自治区","540000");
        map.put("陕西省","610000");
        map.put("甘肃省","620000");
        map.put("青海省","630000");
        map.put("宁夏回族自治区","640000");
        map.put("新疆维吾尔自治区","650000");
        map.put("台湾省","710000");
        map.put("香港特别行政区","810000");
        map.put("澳门特别行政区","820000");
    }

    static {
        map.forEach((k,v)->{
            provinceCodeMap.put(v,k);
        });
    }

    public static String getCodeByName(String name){
        return map.get(name);
    }

    public static String getProvinceByCode(String code){
        return provinceCodeMap.get(code);
    }
}
