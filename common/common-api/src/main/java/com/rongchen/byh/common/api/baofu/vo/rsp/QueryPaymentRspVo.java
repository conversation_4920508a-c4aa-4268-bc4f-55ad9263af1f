package com.rongchen.byh.common.api.baofu.vo.rsp;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 支付结果查询响应VO
 */
@Data
@Accessors(chain = true)
public class QueryPaymentRspVo {
    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 响应码
     */
    private String respCode;

    /**
     * 业务响应码
     */
    private String bizRespCode;

    /**
     * 业务响应消息
     */
    private String bizRespMsg;

    /**
     * 成功金额，单位：分
     */
    private String succAmt;

    /**
     * 成功时间
     */
    private String succTime;

    /**
     * 宝付订单号
     */
    private String orderId;

    /**
     * 商户订单号
     */
    private String transId;

    /**
     * 渠道订单号
     */
    private String channelOrderId;
} 