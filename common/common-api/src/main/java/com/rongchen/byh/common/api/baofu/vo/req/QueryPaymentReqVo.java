package com.rongchen.byh.common.api.baofu.vo.req;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 支付结果查询请求VO
 */
@Data
@Accessors(chain = true)
public class QueryPaymentReqVo {
    /**
     * 商户原始订单号
     */
    private String origTransId;

    /**
     * 交易日期 (格式：yyyy-MM-dd HH:mm:ss)
     */
    private String origTradeDate;

    /**
     * 商户保留域1(可选)
     */
    private String reqReserved1;

    /**
     * 商户保留域2(可选)
     */
    private String reqReserved2;
}