package com.rongchen.byh.common.api.bankCredit.utils;

import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.pdf.CompressionConstants;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.pdf.WriterProperties;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.property.VerticalAlignment;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.IBodyElement;
import org.apache.poi.xwpf.usermodel.UnderlinePatterns;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;

/**
 * Word转PDF工具类 - 简化版
 */
@Slf4j
public class DocumentConvertUtil {

    // Requires itext-font-asian dependency for CJK font support
    private static final String FONT_PATH = "STSong-Light";
    private static final String ENCODING = "UniGB-UCS2-H";
    private static final float DEFAULT_MARGIN = 72f;
    private static final float DEFAULT_FONT_SIZE = 12f;

    public static void main(String[] args) {
        try {
            String caSignAuthDocxPath = "D:\\ProjectCode\\hrzx-jar\\testFile\\doc\\华融资信--征信查询授权书.docx";
            String caSignAuthPdfPath = "D:\\ProjectCode\\hrzx-jar\\testFile\\pdf\\华融资信--征信查询授权书.pdf";
            wordToPdf(caSignAuthDocxPath, caSignAuthPdfPath);
            compressPdf(caSignAuthPdfPath, 1024 * 1024 * 10);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Word转PDF
     */
    public static void wordToPdf(String docxPath, String pdfPath) throws Exception {
        validateSourceFile(docxPath);

        try (XWPFDocument document = new XWPFDocument(new FileInputStream(docxPath));
                PdfDocument pdf = new PdfDocument(new PdfWriter(pdfPath))) {

            PdfFont font = PdfFontFactory.createFont(FONT_PATH, ENCODING);
            Document pdfDocument = new Document(pdf);
            pdfDocument.setMargins(DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN, DEFAULT_MARGIN);
            pdfDocument.setFont(font);

            for (IBodyElement element : document.getBodyElements()) {
                if (element instanceof XWPFParagraph) {
                    processParagraph((XWPFParagraph) element, pdfDocument, font);
                } else if (element instanceof XWPFTable) {
                    processTable((XWPFTable) element, pdfDocument, font);
                }
            }

            pdfDocument.close();
        } catch (Exception e) {
            log.error("PDF转换失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    private static void validateSourceFile(String docxPath) {
        File docxFile = new File(docxPath);
        if (!docxFile.exists()) {
            throw new IllegalArgumentException("源文件不存在: " + docxPath);
        }
        if (docxFile.length() == 0) {
            throw new IllegalArgumentException("源文件为空: " + docxPath);
        }
    }

    private static void processParagraph(XWPFParagraph paragraph, Document pdfDocument, PdfFont font) {
        if (paragraph.getRuns().isEmpty()) {
            return;
        }

        Paragraph p = new Paragraph().setFont(font);

        for (XWPFRun run : paragraph.getRuns()) {
            String text = run.getText(0);
            if (text == null)
                continue;

            Text pdfText = new Text(text)
                    .setFont(font);

            // Use getFontSizeAsDouble() which returns Double, check for null
            Double fontSize = run.getFontSizeAsDouble();
            pdfText.setFontSize(fontSize != null ? fontSize.floatValue() : DEFAULT_FONT_SIZE);

            if (run.isBold())
                pdfText.setBold();
            if (run.isItalic())
                pdfText.setItalic();
            if (run.getUnderline() != UnderlinePatterns.NONE)
                pdfText.setUnderline();

            p.add(pdfText);
        }

        pdfDocument.add(p);
    }

    private static void processTable(XWPFTable table, Document pdfDocument, PdfFont font) {
        Table pdfTable = new Table(table.getRow(0).getTableCells().size());
        pdfTable.useAllAvailableWidth();
        pdfTable.setFont(font);

        for (XWPFTableRow row : table.getRows()) {
            for (XWPFTableCell cell : row.getTableCells()) {
                Cell pdfCell = new Cell();
                pdfCell.setFont(font);

                // 处理单元格内容
                for (XWPFParagraph paragraph : cell.getParagraphs()) {
                    Paragraph cellParagraph = new Paragraph();
                    for (XWPFRun run : paragraph.getRuns()) {
                        if (run.getText(0) != null) {
                            cellParagraph.add(new Text(run.getText(0)));
                        }
                    }
                    pdfCell.add(cellParagraph);
                }

                // 设置垂直对齐
                if (cell.getVerticalAlignment() != null) {
                    pdfCell.setVerticalAlignment(convertVerticalAlignment(cell.getVerticalAlignment()));
                }

                pdfTable.addCell(pdfCell);
            }
        }

        pdfDocument.add(pdfTable);
    }

    private static VerticalAlignment convertVerticalAlignment(XWPFTableCell.XWPFVertAlign align) {
        if (align == null) {
            return VerticalAlignment.TOP;
        }
        switch (align) {
            case CENTER:
                return VerticalAlignment.MIDDLE;
            case BOTTOM:
                return VerticalAlignment.BOTTOM;
            case TOP:
            default:
                return VerticalAlignment.TOP;
        }
    }

    /**
     * 压缩PDF文件到指定大小
     * 
     * @param pdfPath PDF文件路径
     * @param maxSize 目标最大大小（字节）
     * @throws Exception 压缩过程中的异常
     */
    public static void compressPdf(String pdfPath, long maxSize) throws Exception {
        Path sourcePath = Paths.get(pdfPath);
        if (!Files.exists(sourcePath)) {
            throw new IllegalArgumentException("PDF文件不存在: " + pdfPath);
        }

        long sourceSize = Files.size(sourcePath);
        if (sourceSize <= maxSize) {
            log.info("PDF文件大小 ({}) 已经小于或等于目标大小 ({})，无需压缩", sourceSize, maxSize);
            return;
        }

        String tempPath = pdfPath + ".temp";
        Path tempFilePath = Paths.get(tempPath);

        try {
            // 尝试两次压缩：一次默认，一次最佳
            int[] compressionLevels = { CompressionConstants.DEFAULT_COMPRESSION,
                    CompressionConstants.BEST_COMPRESSION };
            boolean success = false;

            for (int compressionLevel : compressionLevels) {
                log.info("尝试压缩PDF，级别: {}",
                        compressionLevel == CompressionConstants.DEFAULT_COMPRESSION ? "DEFAULT" : "BEST");
                try (PdfReader reader = new PdfReader(pdfPath);
                        FileOutputStream fos = new FileOutputStream(tempPath); // 直接写入临时文件
                        PdfWriter writer = new PdfWriter(fos, new WriterProperties()
                                .setCompressionLevel(compressionLevel)
                                .setFullCompressionMode(true)); // 启用完全压缩模式
                        PdfDocument pdfDoc = new PdfDocument(reader, writer)) {

                    // PdfDocument 在关闭时自动应用压缩设置
                    pdfDoc.close(); // 关闭以写入并应用压缩
                } // try-with-resources 自动关闭 reader, fos, writer, pdfDoc

                // 检查压缩后的临时文件大小
                long compressedSize = Files.size(tempFilePath);
                log.info("压缩后临时文件大小: {}", compressedSize);

                if (compressedSize <= maxSize) {
                    // 压缩成功，替换原文件
                    Files.delete(sourcePath);
                    Files.move(tempFilePath, sourcePath); // 重命名临时文件为原文件名
                    log.info("PDF压缩成功: {} -> {} bytes (目标: <= {} bytes)", sourceSize, compressedSize, maxSize);
                    success = true;
                    break; // 达到目标，退出循环
                } else {
                    // 如果第一次压缩（默认）后仍然太大，循环会继续尝试最佳压缩
                    // 如果第二次压缩（最佳）后仍然太大，循环结束，success 仍为 false
                    log.warn("压缩级别 {} 后大小仍超过目标: {} > {}",
                            (compressionLevel == CompressionConstants.DEFAULT_COMPRESSION ? "DEFAULT" : "BEST"),
                            compressedSize, maxSize);
                    // 清理这次尝试产生的临时文件，为下一次尝试（如果还有）或最终清理做准备
                    try {
                        Files.deleteIfExists(tempFilePath);
                    } catch (Exception e) {
                        log.warn("删除临时文件失败: {}", tempPath);
                    }
                }
            }

            if (!success) {
                throw new IllegalStateException(
                        String.format("即使使用最佳压缩级别，也无法将PDF (%d bytes) 压缩到目标大小 (%d bytes) 以下。", sourceSize, maxSize));
            }

        } catch (Exception e) {
            log.error("PDF压缩失败: {}", e.getMessage(), e);
            // 尝试清理临时文件
            try {
                Files.deleteIfExists(tempFilePath);
            } catch (Exception cleanEx) {
                log.warn("压缩失败后清理临时文件也失败: {}", tempPath);
            }
            throw e; // 重新抛出原始异常
        }
        // 注意：成功的流程中临时文件已被重命名，无需在此处清理。失败的流程中，如果 break 未执行，则应在 catch 或循环内部清理。
        // 在循环内部每次压缩后如果不成功就删除临时文件更清晰。
    }

}