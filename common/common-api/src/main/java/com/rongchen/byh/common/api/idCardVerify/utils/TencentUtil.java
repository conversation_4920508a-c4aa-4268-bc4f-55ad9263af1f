package com.rongchen.byh.common.api.idCardVerify.utils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

public class TencentUtil {

    private TencentUtil() {}

    public static String getAuth(
            String secretId, String secretKey, String host, String contentType,
            String timestamp, String body
    ) {
        try {
            String canonicalUri = "/";
            String canonicalQueryString = "";
            String canonicalHeaders = "content-type:" + contentType + "\nhost:" + host + "\n";
            String signedHeaders = "content-type;host";

            String hashedRequestPayload = sha256Hex(body.getBytes(StandardCharsets.UTF_8));
            String canonicalRequest = "POST"
                    + "\n"
                    + canonicalUri
                    + "\n"
                    + canonicalQueryString
                    + "\n"
                    + canonicalHeaders
                    + "\n"
                    + signedHeaders
                    + "\n"
                    + hashedRequestPayload;

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            String date = sdf.format(new Date(Long.valueOf(timestamp + "000")));
            String service = host.split("\\.")[0];
            String credentialScope = date + "/" + service + "/" + "tc3_request";
            String hashedCanonicalRequest =
                    sha256Hex(canonicalRequest.getBytes(StandardCharsets.UTF_8));
            String stringToSign =
                    "TC3-HMAC-SHA256\n" + timestamp + "\n" + credentialScope + "\n" + hashedCanonicalRequest;

            byte[] secretDate = hmac256(("TC3" + secretKey).getBytes(StandardCharsets.UTF_8), date);
            byte[] secretService = hmac256(secretDate, service);
            byte[] secretSigning = hmac256(secretService, "tc3_request");
            String signature =
                    printHexBinary(hmac256(secretSigning, stringToSign)).toLowerCase();
            return "TC3-HMAC-SHA256 "
                    + "Credential="
                    + secretId
                    + "/"
                    + credentialScope
                    + ", "
                    + "SignedHeaders="
                    + signedHeaders
                    + ", "
                    + "Signature="
                    + signature;
        } catch (Exception e) {

        }
        return null;
    }

    public static String sha256Hex(byte[] b) throws NoSuchAlgorithmException {
        MessageDigest md;
        md = MessageDigest.getInstance("SHA-256");
        byte[] d = md.digest(b);
        return printHexBinary(d).toLowerCase();
    }

    private static final char[] hexCode = "0123456789ABCDEF".toCharArray();

    public static String printHexBinary(byte[] data) {
        StringBuilder r = new StringBuilder(data.length * 2);
        for (byte b : data) {
            r.append(hexCode[(b >> 4) & 0xF]);
            r.append(hexCode[(b & 0xF)]);
        }
        return r.toString();
    }

    public static byte[] hmac256(byte[] key, String msg) throws NoSuchAlgorithmException, InvalidKeyException {
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKeySpec = new SecretKeySpec(key, mac.getAlgorithm());
        mac.init(secretKeySpec);
        return mac.doFinal(msg.getBytes(StandardCharsets.UTF_8));
    }
}
