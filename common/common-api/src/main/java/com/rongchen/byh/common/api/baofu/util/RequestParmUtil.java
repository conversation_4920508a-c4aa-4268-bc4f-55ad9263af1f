package com.rongchen.byh.common.api.baofu.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;

public class RequestParmUtil
{
    private static Logger logger = LoggerFactory.getLogger(RequestParmUtil.class);

    public static void getParamLog(HttpServletRequest Request)
    {
        try {
            String paraStr=null;
            String TmpStr = null;
            Enumeration<String> ehead = Request.getHeaderNames();
            while(ehead.hasMoreElements()) {
                paraStr = ehead.nextElement();
                if(paraStr != null){
                    TmpStr = "Key:"+paraStr+",Value:"+Request.getHeader(paraStr);
                }
                logger.info("Header接收参数："+TmpStr);
            }
            Enumeration<String> e = Request.getParameterNames();
            while(e.hasMoreElements()) {
                paraStr = e.nextElement();
                if(paraStr != null){
                    TmpStr = "Key:"+paraStr+",Value:"+Request.getParameter(paraStr);
                }
                logger.info("接收参数："+TmpStr);
            }
        }catch (Exception ex){
            ex.printStackTrace();
        }
    }
}
