package com.rongchen.byh.common.api.zifang.service.fenzhuan;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.common.api.zifang.service.mayi.config.FenZhuanProperties;
import com.rongchen.byh.common.api.zifang.utils.FenZhuanUtils;
import com.rongchen.byh.common.core.util.ApplicationContextHolder;
import com.rongchen.byh.common.core.util.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025/3/2 15:27:52
 */
@Slf4j
public abstract class AbstractFenZhuanService implements CommandLineRunner {

    protected FenZhuanProperties fenZhuanProperties;
    @Override
    public void run(String... args) throws Exception {
        fenZhuanProperties = ApplicationContextHolder.getBean(FenZhuanProperties.class);
    }

    /**
     * 请求接口
     * @param params
     * @param apiCode
     * @return
     * @throws Exception
     */
    protected JSONObject fenZhuanSendRequest(JSONObject params, String apiCode) throws Exception {
        log.info("【分转资方接口】 apiCode：{}", apiCode);
        log.info("【分转资方接口】 加密前参数：{}", params);
        String body = FenZhuanUtils.buildRequest(params, fenZhuanProperties.getFenZhuanAesKey(), fenZhuanProperties.getFenZhuanIv(),
                fenZhuanProperties.getFenZhuanAppid(),fenZhuanProperties.getFenZhuanVersion(), fenZhuanProperties.getFenZhuanAppKey(), apiCode);
        log.info("【分转资方接口】 加密后参数：{}", body);
        String res = HttpUtil.postJson(fenZhuanProperties.getFenZhuanUrl(), body);
        log.info("【分转资方接口】 响应结果：{}", res);
        JSONObject map = JSONObject.parseObject(res);
        return map;
    }

    /**
     * 请求地址
     * @param channel
     * @param path
     * @return
     */
    protected String getUrl(String channel,String path) {
        return StrUtil.replace(path, "{channel}", channel);
    }
}
