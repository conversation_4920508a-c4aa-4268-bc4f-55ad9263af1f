package com.rongchen.byh.common.api.baofu.dto.req;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 支付结果查询请求DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryPaymentReqDto extends BaofuBaseReqDto {
    /**
     * 商户原始订单号
     */
    private String orig_trans_id;

    /**
     * 交易日期
     */
    private String orig_trade_date;

    /**
     * 商户保留域1
     */
    private String req_reserved1;

    /**
     * 商户保留域2
     */
    private String req_reserved2;

    /**
     * 系统保留域1
     */
    private String additional_info1;

    /**
     * 系统保留域2
     */
    private String additional_info2;
}