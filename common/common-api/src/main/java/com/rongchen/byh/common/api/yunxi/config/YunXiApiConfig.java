package com.rongchen.byh.common.api.yunxi.config;

import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 云樨API配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "yunxi.api")
public class YunXiApiConfig {
    /* 云樨API配置测试环境 
     * 
     * appId=2025022800131503
     * appSecret=608ec40507966a0f807968e56376b3e8
     * aesKey=48fb0b4e20ee658cbddc8a6b7168b3e4
     * couponPackageId=mbp1345066127994126336
     * reqUrl=http://yxopenapitest.yunxihuiyuan.com/api/benefit/gateway
     */



    /**
     * 应用ID
     */
    private String appId;

    /**
     * 应用密钥(报文加签密钥)
     */
    private String appSecret;

    /**
     * AES密钥(敏感数据加密)
     */
    private String aesKey;

    /**
     * API地址
     */
    private String apiUrl;

    /**
     * 版本号
     */
    private String version = "2.0";

    /**
     * 标签类型(是否关闭二级嵌套)
     */
    private String flagType = "Y";

    /**
     * 权益券包号
     */
    private List<String> couponPackageId;

}