package com.rongchen.byh.common.api.beiyihua.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.common.api.beiyihua.config.BeiYiHuaProperties;
import com.rongchen.byh.common.api.beiyihua.dto.CreditPushDto;
import com.rongchen.byh.common.api.beiyihua.service.BeiYiHuaService;
import com.rongchen.byh.common.api.beiyihua.vo.CreditPushVo;
import com.rongchen.byh.common.core.util.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025/3/26 18:19:15
 */
@Service
@Slf4j
public class BeiYiHuaServiceImpl implements BeiYiHuaService {
    @Resource
    BeiYiHuaProperties beiYiHuaProperties;
    @Override
    public CreditPushVo push(CreditPushDto creditPushDto) {
        CreditPushVo creditPushVo = new CreditPushVo();
        creditPushVo.setCode(0);
        try {
            String ret = HttpUtil.postJson(beiYiHuaProperties.getCreditPushUrl(), JSONObject.toJSONString(creditPushDto));
            JSONObject json = JSONObject.parseObject(ret);
            Boolean result = json.getBoolean("success");
            if (result) {
                creditPushVo.setCode(1);
            }
            log.info("【倍易花】同步额度，请求参数：{}，结果：{}", JSONObject.toJSONString(creditPushDto), result);
        } catch (Exception e) {
            log.error("【倍易花】同步额度异常", e);
        }
        return creditPushVo;
    }
}
