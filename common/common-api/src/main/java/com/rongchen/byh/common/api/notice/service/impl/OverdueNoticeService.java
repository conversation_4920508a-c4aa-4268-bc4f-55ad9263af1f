package com.rongchen.byh.common.api.notice.service.impl;

import cn.hutool.core.util.StrUtil;
import com.rongchen.byh.common.api.notice.config.NoticeSmsContent;
import com.rongchen.byh.common.api.notice.dto.NoticeSmsDto;
import com.rongchen.byh.common.api.notice.dto.OverdueNoticeDto;
import com.rongchen.byh.common.core.util.MyModelUtil;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 逾期提醒
 * @date 2025/2/7 11:59:38
 */
@Service
public class OverdueNoticeService extends AbstractNoticeSmsService{
    @Override
    protected String getContent(NoticeSmsDto noticeSmsDto) {
        OverdueNoticeDto dto = (OverdueNoticeDto) noticeSmsDto;
        return StrUtil.format(NoticeSmsContent.DICT_MAP.get(NoticeSmsContent.OVERDUENOTICE), MyModelUtil.beanToMap(dto));
    }

    @Override
    protected Integer getTemplateId() {
        return null;
    }
}
