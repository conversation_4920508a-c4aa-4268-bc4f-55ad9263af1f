package com.rongchen.byh.common.api.notice.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.common.api.notice.config.NoticeSmsProperties;
import com.rongchen.byh.common.api.notice.dto.NoticeSmsDto;
import com.rongchen.byh.common.api.notice.service.NoticeSmsService;
import com.rongchen.byh.common.api.notice.vo.NoticeSmsVo;
import com.rongchen.byh.common.core.util.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 通知短信公共处理方法
 * @date 2024/12/12 16:21:00
 */
@Component
@Slf4j
public abstract class AbstractNoticeSmsService implements NoticeSmsService {
    @Resource
    private NoticeSmsProperties noticeSmsProperties;

    @Override
    public NoticeSmsVo sendSms(NoticeSmsDto noticeSmsDto) {
        JSONObject param = buildParam(noticeSmsDto);
        String res = null;
        try {
            res = HttpUtil.postFrom(noticeSmsProperties.getNoticeSmsUrl(), param);
        } catch (Exception e) {
            log.error("通知短信请求异常", e);
        }
        log.info("通知短信请求参数：{}发送结果:{}", param, res);
        return buildResult(res);
    }

    private JSONObject buildParam(NoticeSmsDto noticeSmsDto) {
        JSONObject param = new JSONObject();
        param.put("accesskey", noticeSmsProperties.getAccesskey());
        param.put("secret", noticeSmsProperties.getSecret());
        param.put("sign", noticeSmsProperties.getSign());
        param.put("templateId", getTemplateId());
        param.put("mobile", noticeSmsDto.getPhone());
        param.put("content", getContent(noticeSmsDto));
        return param;
    }

    /**
     * 获取动态参数内容
     * @param noticeSmsDto
     * @return
     */
    protected abstract String getContent(NoticeSmsDto noticeSmsDto);

    /**
     * 获取模板id
     * @return
     */
    protected abstract Integer getTemplateId();

    private NoticeSmsVo buildResult(String res) {
        NoticeSmsVo result = new NoticeSmsVo();
        result.setCode(-1);
        JSONObject data = JSONObject.parseObject(res);
        if ("0".equals(data.getString("code"))) {
            result.setCode(0);
        }
        result.setMsg(data.getString("msg"));
        return result;
    }
}
