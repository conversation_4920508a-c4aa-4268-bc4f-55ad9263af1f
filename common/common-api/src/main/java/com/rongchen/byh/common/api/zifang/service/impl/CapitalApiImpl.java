//package com.rongchen.byh.common.api.zifang.service.impl;
//
//import cn.hutool.core.util.StrUtil;
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.rongchen.byh.common.api.zifang.config.ZiFangConstant;
//import com.rongchen.byh.common.api.zifang.config.ZiFangProperties;
//import com.rongchen.byh.common.api.zifang.dto.ApplyCheckDto;
//import com.rongchen.byh.common.api.zifang.dto.CreditApplyDto;
//import com.rongchen.byh.common.api.zifang.dto.CreditAuditDto;
//import com.rongchen.byh.common.api.zifang.dto.credit.ContactRelation;
//import com.rongchen.byh.common.api.zifang.dto.credit.CreditInfo;
//import com.rongchen.byh.common.api.zifang.dto.credit.DutyInfo;
//import com.rongchen.byh.common.api.zifang.dto.credit.PictureInfo;
//import com.rongchen.byh.common.api.zifang.service.CapitalApi;
//import com.rongchen.byh.common.api.zifang.utils.AreaCodeUtil;
//import com.rongchen.byh.common.api.zifang.utils.CityCodeUtil;
//import com.rongchen.byh.common.api.zifang.utils.ProvinceCodeUtil;
//import com.rongchen.byh.common.api.zifang.utils.ZiFangUtil;
//import com.rongchen.byh.common.api.zifang.vo.ApplyCheckVo;
//import com.rongchen.byh.common.api.zifang.vo.BaseVo;
//import com.rongchen.byh.common.api.zifang.vo.CreditApplyVo;
//import com.rongchen.byh.common.api.zifang.vo.CreditAuditVo;
//import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
//import com.rongchen.byh.common.core.object.ResponseResult;
//import com.rongchen.byh.common.core.util.HttpUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//
//
//@Slf4j
//@Service
//public class CapitalApiImpl extends AbstractApi implements CapitalApi {
//
//
//
//
//
//    @Override
//    public ResponseResult<ApplyCheckVo> applyCheck(ApplyCheckDto applyCheckDto) {
//        JSONObject params = new JSONObject();
//        params.put("serialNo", applyCheckDto.getSerialNo());
//        params.put("reqSysCode", ziFangProperties.getReqSysCode());
//        params.put("productCode", ziFangProperties.getProductCode());
//        params.put("idNo", applyCheckDto.getIdNo());
//        params.put("custName", applyCheckDto.getCustName());
//        params.put("phoneNo", applyCheckDto.getPhoneNo());
//        params.put("type", 0);
//        try {
//            JSONObject map = this.sendRequest(params, ZiFangConstant.applyCheckUrl);
//            ApplyCheckVo vo = map.toJavaObject(ApplyCheckVo.class);
//            return ResponseResult.success(vo);
//        } catch (Exception e) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL,e.getMessage());
//        }
//    }
//
//    @Override
//    public ResponseResult<CreditApplyVo> creditApply(CreditApplyDto creditApplyDto) {
//        JSONObject param = new JSONObject();
//
//        // 生成CreditInfo的所有build参数构造
//        CreditInfo creditInfo = CreditInfo.builder()
//                .creditNo(creditApplyDto.getCreditNo())
//                .custName(creditApplyDto.getCustName())
//                .userId(creditApplyDto.getUserId())
//                .idNo(creditApplyDto.getIdNo())
//                .creditAmount(creditApplyDto.getCreditAmount())
//                .sex(creditApplyDto.getSex())
//                .birthday(creditApplyDto.getBirthday())
//                .nation(creditApplyDto.getNation())
//                .nationality(creditApplyDto.getNationality())
//                .nation(creditApplyDto.getNation())
//
//                .idProvince(creditApplyDto.getProvince())
//                .idCity(creditApplyDto.getCity())
//                .idArea(creditApplyDto.getArea())
//
//                .idProvinceCode(ProvinceCodeUtil.getCodeByName(creditApplyDto.getProvince()))
//                .idCityCode(CityCodeUtil.getCodeByName(creditApplyDto.getCity()))
//                .idAreaCode(AreaCodeUtil.getCodeByName(creditApplyDto.getCity()+"_"+creditApplyDto.getArea()))
//                .idAddress(creditApplyDto.getAddress())
//
//                .signOrganization(creditApplyDto.getSignOrganization())
//                .idValidDateBegin(creditApplyDto.getIdValidDateBegin())
//                .idValidDateEnd(creditApplyDto.getIdValidDateEnd())
//                .idLongTerm(creditApplyDto.getIdLongTerm())
//
//                .phoneNo(creditApplyDto.getPhoneNo())
//                .liveProvinceCode(ProvinceCodeUtil.getCodeByName(creditApplyDto.getProvince()))
//                .liveCityCode(CityCodeUtil.getCodeByName(creditApplyDto.getCity()))
//                .liveAreaCode(AreaCodeUtil.getCodeByName(creditApplyDto.getCity()+"_"+creditApplyDto.getArea()))
//                .liveProvince(creditApplyDto.getProvince())
//                .liveCity(creditApplyDto.getCity())
//                .liveArea(creditApplyDto.getArea())
//                .liveAddress(creditApplyDto.getArea())
//
//                .custEducation(creditApplyDto.getEducation())
//                .degree(formatDegree(creditApplyDto.getEducation()))
//
//                .marryType(creditApplyDto.getMarryType())
//                .childrenStatus("5")
//                .occupation("Y")
//                .income(creditApplyDto.getIncome()+"")
//                .salary(String.valueOf(creditApplyDto.getIncome() * 12))
//                .companyName("个体户")
//
//
//                .companyProvince(creditApplyDto.getProvince())
//                .companyCity(creditApplyDto.getCity())
//                .companyArea(creditApplyDto.getArea())
//                .companyProvinceCode(ProvinceCodeUtil.getCodeByName(creditApplyDto.getProvince()))
//                .companyCityCode(CityCodeUtil.getCodeByName(creditApplyDto.getCity()))
//                .companyAreaCode(AreaCodeUtil.getCodeByName(creditApplyDto.getCity()+"_"+creditApplyDto.getArea()))
//                .companyAddress(creditApplyDto.getAddress())
//
//                .deviceBrand(creditApplyDto.getDeviceBrand())
//                .networkType(creditApplyDto.getNetworkType())
//                .devAlias(creditApplyDto.getDevAlias())
//                .deviceId(creditApplyDto.getDeviceId())
//                .clientIp(creditApplyDto.getClientIp())
//                .coordinateType(creditApplyDto.getCoordinateType())
//                .lng(creditApplyDto.getLng())
//                .lat(creditApplyDto.getLat())
//                .gpsCity(creditApplyDto.getGpsCity())
//                .lbsAddress(creditApplyDto.getLbsAddress())
//                .gpsAddress(creditApplyDto.getGpsAddress())
//                .os(creditApplyDto.getOs())
//                .osVersion(creditApplyDto.getOsVersion())
//                .faceScore(creditApplyDto.getFaceScore())
//                .faceConfidence(creditApplyDto.getFaceConfidence())
//                .faceSource(creditApplyDto.getFaceSource())
//                .faceTime(creditApplyDto.getFaceTime())
//                .build();
//
//
//
//        List<ContactRelation> contactRelationList = new ArrayList<>(2);
//        ContactRelation contact1 = new ContactRelation();
//        contact1.setContactRelation(creditApplyDto.getContactRelation1());
//        contact1.setContactName(creditApplyDto.getContactName1());
//        contact1.setContactPhoneNo(creditApplyDto.getContactPhoneNo1());
//        contactRelationList.add(contact1);
//
//        ContactRelation contact2 = new ContactRelation();
//        contact2.setContactRelation(creditApplyDto.getContactRelation2());
//        contact2.setContactName(creditApplyDto.getContactName2());
//        contact2.setContactPhoneNo(creditApplyDto.getContactPhoneNo2());
//        contactRelationList.add(contact2);
//
//        creditInfo.setContactRelationLists(contactRelationList);
//
//        List<PictureInfo> pictureInfoList = new ArrayList<>(3);
//        PictureInfo picture1 = new PictureInfo();
//        picture1.setMethods("0");
//        picture1.setPictureType("0");
//        picture1.setPictureUrl(creditApplyDto.getIdCardFrontUrl());
//        pictureInfoList.add(picture1);
//
//        PictureInfo picture2 = new PictureInfo();
//        picture2.setMethods("0");
//        picture2.setPictureType("1");
//        picture2.setPictureUrl(creditApplyDto.getIdCardBackUrl());
//        pictureInfoList.add(picture2);
//
//        PictureInfo picture3 = new PictureInfo();
//        picture3.setMethods("0");
//        picture3.setPictureType("2");
//        picture3.setPictureUrl(creditApplyDto.getLiveUrl());
//        pictureInfoList.add(picture3);
//
//        creditInfo.setPictureList(pictureInfoList);
//
//        param.put("creditInfo",creditInfo);
//
//        DutyInfo dutyInfo = new DutyInfo();
//        dutyInfo.setDuty("4");
//        dutyInfo.setTechnical("0");
//        dutyInfo.setCompanyTrade("A");
//        dutyInfo.setCompanyNature("40");
//        dutyInfo.setJobNature("10");
//        dutyInfo.setEmployment("91");
//        dutyInfo.setResidentialStatus("7");
//
//        param.put("dutyInfo",dutyInfo);
//        param.put("reqSysCode", ziFangProperties.getReqSysCode());
//        param.put("productCode", ziFangProperties.getProductCode());
//        System.out.println(param.toJSONString());
//        try {
//            JSONObject map = this.sendRequest(param, ZiFangConstant.creditApplyUrl);
//            CreditApplyVo vo = map.toJavaObject(CreditApplyVo.class);
//            return ResponseResult.success(vo);
//        } catch (Exception e) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL,e.getMessage());
//        }
//    }
//
//    private String formatDegree(String education) {
//        String degree = "";
//        if ("10".equals(education)) {
//            degree = "2";
//        } else if ("20".equals(education)) {
//            degree = "4";
//        } else if ("30".equals(education)) {
//            degree = "0";
//        } else if ("40".equals(education)) {
//            degree = "5";
//        } else if ("60".equals(education)) {
//            degree = "0";
//        } else if ("90".equals(education)) {
//            degree = "0";
//        } else if ("91".equals(education)) {
//            degree = "0";
//        } else if ("99".equals(education)) {
//            degree = "0";
//        }
//        return degree;
//    }
//
//    @Override
//    public ResponseResult<CreditAuditVo> getCreditAudit(CreditAuditDto creditAuditDto) {
//        JSONObject params = new JSONObject();
//        params.put("creditNo", creditAuditDto.getCreditNo());
//        params.put("reqSysCode", ziFangProperties.getReqSysCode());
//        params.put("productCode", ziFangProperties.getProductCode());
//        params.put("userId", creditAuditDto.getUserId());
//        try {
//            JSONObject map = this.sendRequest(params, ZiFangConstant.creditAuditUrl);
//            CreditAuditVo vo = map.toJavaObject(CreditAuditVo.class);
//            return ResponseResult.success(vo);
//        } catch (Exception e) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL,e.getMessage());
//        }
//    }
//}
