package com.rongchen.byh.common.api.yunxi.dto.req;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 权益订单结果通知请求
 */
public class BenefitOrderNotifyReqDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 版本号
     */
    private String version;

    /**
     * 时间戳，毫秒
     */
    private String timestamp;

    /**
     * 签名类型
     */
    private String signType;

    /**
     * 签名
     */
    private String sign;

    /**
     * 权益平台订单号
     */
    private String orderNum;

    /**
     * 支付单号
     */
    private String paymentNo;

    /**
     * 订单金额 单位元
     */
    private BigDecimal orderAmount;

    /**
     * 合作方用户编号
     */
    private String openId;

    /**
     * 合作方用户手机号(AES加密)
     */
    private String userMobile;

    /**
     * 支付方式
     */
    private String payWay;

    /**
     * 券包编号
     */
    private String couponPackageId;

    /**
     * 券包名称
     */
    private String couponPackageName;

    /**
     * 权益订单下单时间
     * 格式yyyy-MM-dd HH:mm:ss
     */
    private String createTime;

    /**
     * 支付完成时间
     * yyyy-MM-dd HH:mm:ss
     */
    private String payTime;

    /**
     * 权益失效时间
     * yyyy-MM-dd HH:mm:ss
     */
    private String expireTime;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 可退状态
     * True-可退
     * False-不可退
     */
    private String refundable;

    /**
     * 退款金额 单位元
     */
    private BigDecimal refundAmount;

    /**
     * 退款时间
     * yyyy-MM-dd HH:mm:ss
     */
    private String refundTime;

    /**
     * 退款单号; 权益平台的退款单号
     */
    private String refundNo;

    /**
     * 合作方订单号
     */
    private String externalOrderNum;

    // Getters and Setters
    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getSignType() {
        return signType;
    }

    public void setSignType(String signType) {
        this.signType = signType;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    public String getPaymentNo() {
        return paymentNo;
    }

    public void setPaymentNo(String paymentNo) {
        this.paymentNo = paymentNo;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getUserMobile() {
        return userMobile;
    }

    public void setUserMobile(String userMobile) {
        this.userMobile = userMobile;
    }

    public String getPayWay() {
        return payWay;
    }

    public void setPayWay(String payWay) {
        this.payWay = payWay;
    }

    public String getCouponPackageId() {
        return couponPackageId;
    }

    public void setCouponPackageId(String couponPackageId) {
        this.couponPackageId = couponPackageId;
    }

    public String getCouponPackageName() {
        return couponPackageName;
    }

    public void setCouponPackageName(String couponPackageName) {
        this.couponPackageName = couponPackageName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getPayTime() {
        return payTime;
    }

    public void setPayTime(String payTime) {
        this.payTime = payTime;
    }

    public String getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(String expireTime) {
        this.expireTime = expireTime;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getRefundable() {
        return refundable;
    }

    public void setRefundable(String refundable) {
        this.refundable = refundable;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getRefundTime() {
        return refundTime;
    }

    public void setRefundTime(String refundTime) {
        this.refundTime = refundTime;
    }

    public String getRefundNo() {
        return refundNo;
    }

    public void setRefundNo(String refundNo) {
        this.refundNo = refundNo;
    }

    public String getExternalOrderNum() {
        return externalOrderNum;
    }

    public void setExternalOrderNum(String externalOrderNum) {
        this.externalOrderNum = externalOrderNum;
    }
}