package com.rongchen.byh.common.api.idCardVerify.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Charsets;
import com.google.common.hash.Hashing;
import com.rongchen.byh.common.api.idCardVerify.config.FaceConstant;
import com.rongchen.byh.common.api.idCardVerify.config.FaceImgConstant;
import com.rongchen.byh.common.api.idCardVerify.config.TencentProperties;
import com.rongchen.byh.common.api.idCardVerify.dto.FaceDto;
import com.rongchen.byh.common.api.idCardVerify.dto.FaceImgDto;
import com.rongchen.byh.common.api.idCardVerify.dto.FaceNewDto;
import com.rongchen.byh.common.api.idCardVerify.service.FaceVerifyService;
import com.rongchen.byh.common.api.idCardVerify.utils.TencentUtil;
import com.rongchen.byh.common.api.idCardVerify.vo.FaceNewVo;
import com.rongchen.byh.common.api.idCardVerify.vo.FaceVo;
import com.rongchen.byh.common.core.util.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;


@Service
@Slf4j
public class FaceVerifyServiceImpl extends QcloudBase implements FaceVerifyService {

    @Resource
    private TencentProperties tencentProperties;

    @Override
    public FaceVo faceVerify(FaceDto faceDto) {
        JSONObject param = new JSONObject(4);
        param.put("LivenessType","SILENT");
        param.put("ImageUrl",faceDto.getImageUrl());
        param.put("ValidateData","");
//        param.put("Optional","{\"BestFrameNum\":2}");
        param.put("VideoUrl",faceDto.getVideoUrl());
        String body = param.toJSONString();
        String endpoint = "https://" + FaceConstant.host;
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String auth = TencentUtil.getAuth(tencentProperties.getSecretId(), tencentProperties.getSecretKey(), FaceConstant.host, FaceConstant.contentType, timestamp, body);
        Map<String,String> header = new HashMap<>(10);
        header.put("Host", FaceConstant.host);
        header.put("X-TC-Timestamp", timestamp);
        header.put("X-TC-Version", FaceConstant.Version);
        header.put("X-TC-Action", FaceConstant.Action);
        header.put("X-TC-Region", "");
        header.put("Authorization", auth);
        String res = HttpUtil.postJson(endpoint, param, header);
        JSONObject object = JSONObject.parseObject(res);
        JSONObject response = object.getJSONObject("Response");
        Float sim = response.getFloat("Sim");
        String bestFrameBase64 = response.getString("BestFrameBase64");
        String description = response.getString("Description");
        FaceVo vo = new FaceVo();
        vo.setSim(sim);
        vo.setBestFrameBase64(bestFrameBase64);
        vo.setMsg(description);
        return vo;
    }

    @Override
    public FaceVo faceImgVerify(FaceImgDto faceImgDto) {
        JSONObject param = new JSONObject(4);
        param.put("IdCard",faceImgDto.getIdCard());
        param.put("Name",faceImgDto.getName());
        param.put("ImageBase64",faceImgDto.getImageBase64());
        String body = param.toJSONString();
        String endpoint = "https://" + FaceImgConstant.host;
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String auth = TencentUtil.getAuth(tencentProperties.getSecretId(), tencentProperties.getSecretKey(), FaceConstant.host, FaceConstant.contentType, timestamp, body);
        Map<String,String> header = new HashMap<>(10);
        header.put("Host", FaceImgConstant.host);
        header.put("X-TC-Timestamp", timestamp);
        header.put("X-TC-Version", FaceImgConstant.Version);
        header.put("X-TC-Action", FaceImgConstant.Action);
        header.put("X-TC-Region", "");
        header.put("Authorization", auth);
        String res = HttpUtil.postJson(endpoint, param, header);
        JSONObject object = JSONObject.parseObject(res);
        JSONObject response = object.getJSONObject("Response");
        Float sim = response.getFloat("Sim");
        String description = response.getString("Description");
        FaceVo vo = new FaceVo();
        vo.setSim(sim);
        vo.setMsg(description);
        return vo;
    }

    @Override
    public FaceNewVo faceImgVerifyNew(FaceNewDto faceNewDto) {

        JSONObject jsonObject = JSONObject.parseObject(getToken());
        String token = jsonObject.getString("access_token");
        JSONObject jsonObject1 = JSONObject.parseObject(getTicket(token));
        Object tickets = jsonObject1.getJSONArray("tickets").get(0);
        JSONObject jsonObject2 = JSONObject.parseObject(tickets.toString());
        String ticket = jsonObject2.getString("value");
        String nonce = UUID.randomUUID().toString().replace("-", "");
        String sign = getSign(ticket, nonce, faceNewDto.getOrderNo());
        JSONObject param = new JSONObject();
        param.put("appId", APPID);
        param.put("version", VERSION);
        param.put("nonce", nonce);
        param.put("orderNo", faceNewDto.getOrderNo());
        param.put("sign", sign);
        param.put("getFile", "2");
        String ret = HttpUtil.postJson("https://kyc1.qcloud.com/api/v2/base/queryfacerecord?orderNo="+faceNewDto.getOrderNo(), param);
        return JSONObject.parseObject(ret, FaceNewVo.class);
    }

    public static void main(String[] args) {
        String APPID = "IDAAoO3M";
        String SECRET = "FshEBgEqp3dYJmSmxeic1B4UdzCCui0MPGe9cvmoVVzGIVbApEU2dhXzLenXbE3g";
        String VERSION = "1.0.0";
        String getToken = HttpUtil.get("https://kyc1.qcloud.com/api/oauth2/access_token?appId="+APPID+"&secret="+SECRET+"&grant_type=client_credential&version=1.0.0");
        JSONObject jsonObject = JSONObject.parseObject(getToken);

        String token = jsonObject.getString("access_token");
        String getTicket = HttpUtil.get("https://kyc1.qcloud.com/api/oauth2/api_ticket?appId="+APPID+"&access_token="+token+"&type=SIGN&version=1.0.0");
        JSONObject jsonObject1 = JSONObject.parseObject(getTicket);
        Object tickets = jsonObject1.getJSONArray("tickets").get(0);
        JSONObject jsonObject2 = JSONObject.parseObject(tickets.toString());
        String ticket = jsonObject2.getString("value");
        List<String> list = new ArrayList<>();
        list.add(APPID);
        list.add("0RdotApDFtwhL5wWFPaKFi9cR93Cvabo");
        list.add(VERSION);
        list.add(ticket);
        String nonce = UUID.randomUUID().toString().replace("-", "");
        list.add(nonce);
        list.removeAll(Collections.singleton(null));// remove null
        java.util.Collections.sort(list);
        StringBuilder sb = new StringBuilder();
        for (String str : list) {
            sb.append(str);
        }
        String res = Hashing.sha1().hashString(sb, Charsets.UTF_8).toString().toUpperCase();
        System.out.println(res);
        JSONObject param = new JSONObject();
        param.put("app_id", APPID);
        param.put("version", VERSION);
        param.put("nonce", nonce);
        param.put("order_no", "0RdotApDFtwhL5wWFPaKFi9cR93Cvabo");
        param.put("sign", res);
        param.put("get_file", "1");
        String ret = HttpUtil.get("https://kyc1.qcloud.com/api/server/getOcrResult", param);
        System.out.println(ret);

    }


}
