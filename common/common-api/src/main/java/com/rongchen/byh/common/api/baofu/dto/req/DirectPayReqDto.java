package com.rongchen.byh.common.api.baofu.dto.req;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 直接支付请求参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DirectPayReqDto extends BaofuBaseReqDto {

    /**
     * 商户订单号
     */
    private String trans_id;

    /**
     * 用户ID
     */
    private String user_id;

    /**
     * 签约协议号(加密)
     */
    private String protocol_no;

    /**
     * 交易金额(单位:分)
     */
    private String txn_amt;

    /**
     * 卡信息(加密)
     */
    private String card_info;

    /**
     * 风控参数
     */
    private String risk_item;

    /**
     * 交易成功通知地址
     */
    private String return_url;

    /**
     * 分账信息
     */
    private String share_info;

    /**
     * 分账结果通知地址
     */
    private String share_notify_url;

    /**
     * 手续费承担方商户号
     */
    private String fee_member_id;

    /**
     * 计费商户号
     */
    private String call_fee_member_id;
}