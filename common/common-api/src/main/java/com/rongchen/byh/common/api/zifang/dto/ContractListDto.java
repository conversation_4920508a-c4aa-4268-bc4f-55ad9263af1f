package com.rongchen.byh.common.api.zifang.dto;

import lombok.Data;

@Data
public class ContractListDto {

    /**
     * 贷款编号/授信编号
     * scene =1时不传
     * scene =2或3传递creditNo授信编号
     */
    private String loanNo;

    /**
     * 用户编号
     */
    private String userId;

    /**
     * 场景
     * 01授信
     * 02借款
     * 03绑卡
     * 04权益
     * 05 融担
     */
    private String scene;

    /**
     * 身份证号
     * 有值必填
     */
    private String ident;

    /**
     * 姓名
     * 有值必填
     */
    private String name;

    /**
     * 借款金额
     * 精确到小数点后两位
     * scene =02时传递
     */
    private String loanAmount;

    /**
     * 借款期限
     * scene =02时传递
     */
    private String loanPeriod;

    /**
     * 借款用途
     * scene =02时传递
     */
    private String applyUse;

    /**
     * 银行卡号
     * scene = 02时必传
     */
    private String bankCardNum;

    /**
     * 银行预留手机号
     * scene = 02时必传
     */
    private String bankPhone;

    /**
     * 银行编码
     * scene = 02时必传
     */
    private String bankName;

    /**
     * 进件单号
     */
    private String orderNo;
}
