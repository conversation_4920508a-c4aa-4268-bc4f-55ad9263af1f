package com.rongchen.byh.common.api.yunxi.service.impl;

import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.common.api.yunxi.config.YunXiApiConfig;
import com.rongchen.byh.common.api.yunxi.dto.YunXiApiRspDto;
import com.rongchen.byh.common.api.yunxi.dto.req.BenefitGatewayReqDto;
import com.rongchen.byh.common.api.yunxi.dto.req.BenefitOrderQueryReqDto;
import com.rongchen.byh.common.api.yunxi.dto.req.BenefitOrderRefundNotifyReqDto;
import com.rongchen.byh.common.api.yunxi.dto.req.BenefitOrderRefundReqDto;
import com.rongchen.byh.common.api.yunxi.dto.req.BenefitOrderRepaymentReqDto;
import com.rongchen.byh.common.api.yunxi.dto.req.BenefitOrderSubmitQkReqDto;
import com.rongchen.byh.common.api.yunxi.dto.req.BenefitOrderSubmitReqDto;
import com.rongchen.byh.common.api.yunxi.dto.req.YunXiBashReqDto;
import com.rongchen.byh.common.api.yunxi.service.YunXiApiService;
import com.rongchen.byh.common.api.yunxi.utils.YunXiApiUtils;
import com.rongchen.byh.common.api.yunxi.vo.YunXiOrderDetailVo;
import com.rongchen.byh.common.core.exception.MyRuntimeException;
import com.rongchen.byh.common.core.util.HttpUtil;
import java.math.BigDecimal;
import java.util.List;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * 云樨API服务实现
 */
@Service
public class YunXiApiServiceImpl implements YunXiApiService {

    private static final Logger log = LoggerFactory.getLogger(YunXiApiServiceImpl.class);

    @Resource
    private YunXiApiConfig config;

    /**
     * 解析云溪API响应
     *
     * @param response 响应字符串
     * @param bizClass 业务内容类型
     * @return 解析后的响应对象
     */
    private <T> YunXiApiRspDto<T> parseResponse(String response, Class<T> bizClass) {
        JSONObject jsonResponse = JSON.parseObject(response);
        YunXiApiRspDto<T> result = new YunXiApiRspDto<>();
        result.setRspCode(jsonResponse.getString("rspCode"));
        result.setRspMsg(jsonResponse.getString("rspMsg"));
        result.setTimestamp(jsonResponse.getString("timestamp"));
        // 处理业务响应
        if ("0000".equals(result.getRspCode())) {
            try {
                JSONObject data = jsonResponse.getJSONObject("data");
                if (data != null && data.containsKey("orderDetail")) {
                    log.info("检测到orderDetail字段，准备解析详细数据");
                    JSONObject orderDetail = data.getJSONObject("orderDetail");
                    if (orderDetail != null) {
                        T bizObj = JSON.parseObject(orderDetail.toJSONString(), bizClass);
                        result.setData(bizObj);
                        log.info("orderDetail解析完成: {}", JSON.toJSONString(bizObj));
                    } else {
                        log.warn("orderDetail字段为null");
                    }
                } else {
                    log.info("未检测到orderDetail字段，直接解析data字段");
                    T bizObj = jsonResponse.getObject("data", bizClass);
                    result.setData(bizObj);
                }
            } catch (Exception e) {
                log.error("解析响应数据异常: {}", e.getMessage(), e);
                throw new MyRuntimeException("解析响应数据异常", e);
            }
        } else {
            log.warn("API响应失败，响应码: {}, 响应消息: {}", result.getRspCode(), result.getRspMsg());
        }
        return result;
    }

    /**
     * 发送云溪API请求
     *
     * @param request  请求对象
     * @param apiCode  API代码
     * @param bizClass 业务响应类型
     * @return API响应
     */
    private <T, R extends YunXiBashReqDto> YunXiApiRspDto<T> sendApiRequest(R request, String apiCode,
            Class<T> bizClass) {
        try {

            // 设置基础配置参数
            request.setAppId(config.getAppId());
            request.setVersion(config.getVersion());
            request.setFlagType(config.getFlagType());
            // 构建通用请求参数并设置签名
            log.info("准备构建通用请求参数, API代码: {}", apiCode);
            R commonParams = YunXiApiUtils.buildCommonParams(apiCode, request, config.getAppSecret());

            String jsonBody = JSON.toJSONString(commonParams);

            String apiUrl = config.getApiUrl();
            log.info("发送API请求开始, 目标URL: {}", apiUrl);
            log.info("完整请求参数: {}", jsonBody);
            // 发送请求
            String response = HttpUtil.postJson(apiUrl, jsonBody);
            log.info("API响应内容: {}", response);
            // 解析响应
            return parseResponse(response, bizClass);
        } catch (Exception e) {
            log.error("API请求异常", e);
            throw new MyRuntimeException("系统繁忙，请稍后再试", e);
        }
    }

    @Override
    public YunXiApiRspDto<YunXiOrderDetailVo> submitBenefitOrder(BenefitOrderSubmitReqDto request) {
        log.info("提交权益订单购买请求开始 - 外部订单号: {}, 订单金额: {}", request.getExternalOrderNum(),
                request.getOrderAmount());

        try {
            // 设置基础配置参数
            request.setAppId(config.getAppId());
            request.setVersion(config.getVersion());
            request.setFlagType(config.getFlagType());
            String userMobile = request.getUserMobile();
            // 是不是 hutool 十一位手机号
            if (!PhoneUtil.isMobile(userMobile)) {
                log.error("手机号格式不正确 - 手机号: {}", userMobile);
                throw new MyRuntimeException("手机号格式不正确");
            }
            // 是 十一位手机号
            request.setUserMobile(encryptMobile(userMobile));
            YunXiApiRspDto<YunXiOrderDetailVo> result = sendApiRequest(request, BenefitOrderSubmitReqDto.API_CODE,
                    YunXiOrderDetailVo.class);

            if (result.isSuccess()) {
                log.info("权益订单提交成功 - 云溪订单号: {}",
                        result.getData() != null ? result.getData().getOrderNum() : "未返回");
                userMobile = result.getData().getUserMobile();
                if (StrUtil.isNotBlank(userMobile)) {
                    result.getData().setUserMobile(decryptAesStr(userMobile));
                }
            } else {
                log.warn("权益订单提交失败, 错误码: {}, 错误信息: {}", result.getRspCode(), result.getRspMsg());
            }

            return result;
        } catch (Exception e) {
            log.error("权益订单提交异常", e);
            throw new RuntimeException("权益订单提交异常", e);
        }
    }

    @Override
    public YunXiApiRspDto<YunXiOrderDetailVo> queryBenefitOrder(BenefitOrderQueryReqDto request) {
        log.info("查询权益订单结果请求开始 - 订单号: {}", request.getOrderNum());

        return sendApiRequest(request, BenefitOrderQueryReqDto.API_CODE, YunXiOrderDetailVo.class);
    }

    @Override
    public YunXiApiRspDto<YunXiOrderDetailVo> refundBenefitOrder(BenefitOrderRefundReqDto request) {
        log.info("申请权益订单退款请求开始 - 订单号: {} 请求详情: {}", request.getOrderNum(),
                JSON.toJSONString(request));

        try {
            // 设置基础配置参数
            request.setAppId(config.getAppId());
            request.setVersion(config.getVersion());
            request.setFlagType(config.getFlagType());
            // 构建通用请求参数
            log.info("准备构建通用请求参数, API代码: {}", BenefitOrderRefundReqDto.API_CODE);
            BenefitOrderRefundReqDto commonParams = YunXiApiUtils.buildCommonParams(BenefitOrderRefundReqDto.API_CODE,
                    request, config.getAppSecret());

            // 添加业务参数
            log.info("添加业务参数 - 订单号: {}", request.getOrderNum());
            String jsonBody = JSON.toJSONString(commonParams);

            // 发送请求
            log.info("发送申请权益订单退款请求, API地址: {}", config.getApiUrl());
            String responseStr = HttpUtil.postJson(config.getApiUrl(), jsonBody);
            log.info("申请权益订单退款响应: {}", responseStr);

            // 解析响应结果
            log.info("开始解析申请权益订单退款响应");
            YunXiApiRspDto<YunXiOrderDetailVo> response = parseResponse(responseStr, YunXiOrderDetailVo.class);

            // 处理结果
            if (response != null && response.isSuccess()) {
                log.info("申请权益订单退款成功 - 订单号: {}, 状态: {}",
                        request.getOrderNum(),
                        response.getData() != null ? response.getData().getOrderStatus() : "未知");
                log.info("申请权益订单退款成功详情: {}", JSON.toJSONString(response.getData()));
            } else {
                log.error("申请权益订单退款失败 - 订单号: {}, 错误码: {}, 错误信息: {}",
                        request.getOrderNum(),
                        response != null ? response.getRspCode() : "未知",
                        response != null ? response.getRspMsg() : "未知");
            }

            return response;
        } catch (Exception e) {
            log.error("申请权益订单退款异常 - 订单号: {}, 异常: {}", request.getOrderNum(), e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public YunXiApiRspDto<Void> notifyBenefitOrderRepayment(BenefitOrderRepaymentReqDto request) {
        log.info("发送权益订单还款通知请求开始 - 外部订单号: {}, 还款金额: {}", request.getExternalOrderNum(),
                request.getRepayAmt());

        try {
            // 设置基础配置参数
            request.setAppId(config.getAppId());
            request.setVersion(config.getVersion());
            request.setFlagType(config.getFlagType());
            // 构建通用请求参数
            log.info("准备构建通用请求参数, API代码: {}", BenefitOrderRepaymentReqDto.API_CODE);
            BenefitOrderRepaymentReqDto commonParams = YunXiApiUtils.buildCommonParams(
                    BenefitOrderRepaymentReqDto.API_CODE, request, config.getAppSecret());
            // 添加业务参数
            log.info("添加业务参数 - 外部订单号: {}, 支付方式: {}", request.getExternalOrderNum(), request.getPayWay());
            String jsonBody = JSON.toJSONString(commonParams);
            // 发送请求
            log.info("发送权益订单还款通知请求, API地址: {}", config.getApiUrl());
            log.info("发送权益订单还款通知请求详情: {}", JSON.toJSONString(request));
            String responseStr = HttpUtil.postJson(config.getApiUrl(), jsonBody);
            log.info("发送权益订单还款通知响应: {}", responseStr);

            // 解析响应结果
            log.info("开始解析发送权益订单还款通知响应");
            YunXiApiRspDto<Void> response = parseResponse(responseStr, Void.class);

            // 处理结果
            if (response != null && response.isSuccess()) {
                log.info("发送权益订单还款通知成功 - 外部订单号: {}, 还款金额: {}", request.getExternalOrderNum(),
                        request.getRepayAmt());
            } else {
                log.error("发送权益订单还款通知失败 - 外部订单号: {}, 错误码: {}, 错误信息: {}",
                        request.getExternalOrderNum(),
                        response != null ? response.getRspCode() : "未知",
                        response != null ? response.getRspMsg() : "未知");
            }

            return response;
        } catch (Exception e) {
            log.error("发送权益订单还款通知异常 - 外部订单号: {}, 异常: {}", request.getExternalOrderNum(),
                    e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public YunXiApiRspDto<YunXiOrderDetailVo> notifyBenefitOrderRefund(BenefitOrderRefundNotifyReqDto request) {
        log.info("发送权益订单退款通知请求开始 - 外部订单号: {}, 订单金额: {}", request.getExternalOrderNum(),
                request.getOrderAmount());
        log.info("发送权益订单退款通知请求详情: {}", JSON.toJSONString(request));

        try {
            // 设置基础配置参数
            request.setAppId(config.getAppId());
            request.setVersion(config.getVersion());
            request.setFlagType(config.getFlagType());
            // 构建通用请求参数
            log.info("准备构建通用请求参数, API代码: {}", BenefitOrderRefundNotifyReqDto.API_CODE);
            BenefitOrderRefundNotifyReqDto commonParams = YunXiApiUtils.buildCommonParams(
                    BenefitOrderRefundNotifyReqDto.API_CODE, request, config.getAppSecret());

            // 添加业务参数
            log.info("添加业务参数 - 外部订单号: {}, 支付单号: {}", request.getExternalOrderNum(),
                    request.getPaymentNo());
            String jsonBody = JSON.toJSONString(commonParams);

            // 发送请求
            log.info("发送权益订单退款通知请求, API地址: {}", config.getApiUrl());
            String responseStr = HttpUtil.postJson(config.getApiUrl(), jsonBody);
            log.info("发送权益订单退款通知响应: {}", responseStr);

            // 解析响应结果
            log.info("开始解析发送权益订单退款通知响应");
            YunXiApiRspDto<YunXiOrderDetailVo> response = parseResponse(responseStr, YunXiOrderDetailVo.class);

            // 处理结果
            if (response != null && response.isSuccess()) {
                log.info("发送权益订单退款通知成功 - 外部订单号: {}, 云樨订单号: {}, 状态: {}",
                        request.getExternalOrderNum(),
                        response.getData() != null ? response.getData().getOrderNum() : "未返回",
                        response.getData() != null ? response.getData().getOrderStatus() : "未知");
                log.info("发送权益订单退款通知成功详情: {}", JSON.toJSONString(response.getData()));
            } else {
                log.error("发送权益订单退款通知失败 - 外部订单号: {}, 错误码: {}, 错误信息: {}",
                        request.getExternalOrderNum(),
                        response != null ? response.getRspCode() : "未知",
                        response != null ? response.getRspMsg() : "未知");
            }

            return response;
        } catch (Exception e) {
            log.error("发送权益订单退款通知异常 - 外部订单号: {}, 异常: {}", request.getExternalOrderNum(),
                    e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public YunXiApiRspDto<String> getBenefitGatewayUrl(BenefitGatewayReqDto request) {
        log.info("获取权益行权地址请求开始 - 订单号: {}, openId: {}", request.getOrderNum(), request.getOpenId());
        log.info("获取权益行权地址请求详情: {}", JSON.toJSONString(request));

        try {
            // 设置基础配置参数
            request.setAppId(config.getAppId());
            request.setVersion(config.getVersion());
            request.setFlagType(config.getFlagType());
            String userMobile = request.getUserMobile();
            if (!PhoneUtil.isMobile(userMobile)) {
                log.error("手机号格式不正确 - 手机号: {}", userMobile);
                throw new MyRuntimeException("手机号格式不正确");
            }
            request.setUserMobile(encryptMobile(userMobile));
            // 构建通用请求参数
            log.info("准备构建通用请求参数, API代码: {}", BenefitGatewayReqDto.API_CODE);
            BenefitGatewayReqDto commonParams = YunXiApiUtils.buildCommonParams(BenefitGatewayReqDto.API_CODE, request,
                    config.getAppSecret());

            // 添加业务参数
            log.info("添加业务参数 - 订单号: {}, openId: {}", request.getOrderNum(), request.getOpenId());
            String jsonBody = JSON.toJSONString(commonParams);

            // 发送请求
            log.info("发送获取权益行权地址请求, API地址: {}", config.getApiUrl());
            String responseStr = HttpUtil.postJson(config.getApiUrl(), jsonBody);
            log.info("获取权益行权地址响应: {}", responseStr);

            // 解析响应结果
            log.info("开始解析获取权益行权地址响应");
            YunXiApiRspDto<String> response = parseResponse(responseStr, String.class);

            // 处理结果
            if (response != null && response.isSuccess()) {
                log.info("获取权益行权地址成功 - 订单号: {}", request.getOrderNum());
                String data = response.getData();
                if (data != null) {
                    String url = JSON.parseObject(data).getString("url");
                    url = decryptAesStr(url);
                    log.info("权益行权地址: {}", url);
                    response.setData(url);
                }

            } else {
                log.error("获取权益行权地址失败 - 订单号: {}, 错误码: {}, 错误信息: {}",
                        request.getOrderNum(),
                        response != null ? response.getRspCode() : "未知",
                        response != null ? response.getRspMsg() : "未知");
            }

            return response;
        } catch (Exception e) {
            log.error("获取权益行权地址异常 - 订单号: {}, 异常: {}", request.getOrderNum(), e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public YunXiApiRspDto<YunXiOrderDetailVo> submitBenefitOrderQk(BenefitOrderSubmitQkReqDto request) {
        log.info("提交权益订单购买-qk请求开始 - 外部订单号: {}, 订单金额: {}, 订单期数: {}",
                request.getExternalOrderNum(), request.getOrderAmount(), request.getOrderPeriods());
        log.info("提交权益订单购买-qk请求详情: {}", JSON.toJSONString(request));

        try {
            // 设置基础配置参数
            request.setAppId(config.getAppId());
            request.setVersion(config.getVersion());
            request.setFlagType(config.getFlagType());
            String userMobile = request.getUserMobile();
            if (!PhoneUtil.isMobile(userMobile)) {
                log.error("手机号格式不正确 - 手机号: {}", userMobile);
                throw new MyRuntimeException("手机号格式不正确");
            }
            request.setUserMobile(encryptMobile(userMobile));
            BigDecimal totalAmount = request.getOrderAmount();
            // 根据金额范围选择对应的权益包ID
            String selectedPackageId = selectCouponPackageId(totalAmount);
            if (StrUtil.isBlank(selectedPackageId)) {
                log.warn("【云樨-权益赊销】根据金额：{}，未找到对应的权益包ID", totalAmount);
                // 尝试直接使用配置中的第一个权益包ID
                List<String> couponPackageIds = config.getCouponPackageId();
                if (couponPackageIds != null && !couponPackageIds.isEmpty()) {
                    String firstId = couponPackageIds.get(0);
                    if (firstId.contains(":")) {
                        selectedPackageId = firstId.substring(firstId.indexOf(":") + 1);
                    } else {
                        selectedPackageId = firstId;
                    }
                    log.info("【云樨-权益赊销】使用默认权益包ID: {}", selectedPackageId);
                } else {
                    log.error("【云樨-权益赊销】未配置任何权益包ID");
                    throw new IllegalArgumentException("未找到对应的权益包ID");
                }
            }

            log.info("【云樨-权益赊销】根据金额：{}，选择权益包ID: {}", totalAmount, selectedPackageId);
            request.setCouponPackageId(selectedPackageId);
            // 参数校验
            if (request.getOrderPeriods() == null || request.getOrderPeriods() <= 0) {
                log.error("提交权益订单购买-qk参数错误 - 订单期数必须大于0, 当前值: {}", request.getOrderPeriods());
                throw new IllegalArgumentException("orderPeriods必须大于0");
            }

            // 构建通用请求参数
            log.info("准备构建通用请求参数, API代码: {}", BenefitOrderSubmitQkReqDto.API_CODE);
            BenefitOrderSubmitQkReqDto commonParams = YunXiApiUtils.buildCommonParams(
                    BenefitOrderSubmitQkReqDto.API_CODE, request, config.getAppSecret());

            // 添加业务参数
            log.info("添加业务参数 - 权益包ID: {}, 支付方式: {}, 期数: {}",
                    request.getCouponPackageId(), request.getPayWay(), request.getOrderPeriods());
            String jsonBody = JSON.toJSONString(commonParams);

            // 发送请求
            log.info("发送提交权益订单购买-qk请求, API地址: {}", config.getApiUrl());
            String responseStr = HttpUtil.postJson(config.getApiUrl(), jsonBody);
            log.info("提交权益订单购买-qk响应: {}", responseStr);

            // 解析响应结果
            log.info("开始解析提交权益订单购买-qk响应");
            YunXiApiRspDto<YunXiOrderDetailVo> response = parseResponse(responseStr, YunXiOrderDetailVo.class);

            // 详细记录响应对象
            log.info("解析后的响应对象: rspCode={}, rspMsg={}, 是否成功={}",
                    response.getRspCode(), response.getRspMsg(), response.isSuccess());
            if (response.getData() != null) {
                log.info("响应数据详情: orderNum={}, orderStatus={}, orderAmount={}",
                        response.getData().getOrderNum(),
                        response.getData().getOrderStatus(),
                        response.getData().getOrderAmount());
            } else {
                log.warn("响应数据为null");
            }

            // 处理结果
            if (response.isSuccess()) {
                log.info("提交权益订单购买-qk成功 - 外部订单号: {}, 云樨订单号: {}, 状态: {}",
                        request.getExternalOrderNum(),
                        response.getData() != null ? response.getData().getOrderNum() : "未返回",
                        response.getData() != null ? response.getData().getOrderStatus() : "未知");
                log.info("提交权益订单购买-qk成功详情: {}", JSON.toJSONString(response.getData()));
            } else {
                log.error("提交权益订单购买-qk失败 - 外部订单号: {}, 错误码: {}, 错误信息: {}",
                        request.getExternalOrderNum(),
                        response.getRspCode(),
                        response.getRspMsg());
            }

            return response;
        } catch (Exception e) {
            log.error("提交权益订单购买-qk异常 - 外部订单号: {}, 异常: {}", request.getExternalOrderNum(),
                    e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 根据金额选择对应的权益包ID
     * 月卡方案一 X<100
     * 月卡方案二 100<=X<200
     * 月卡方案三 200<=X<300
     * 月卡方案四 300<=X<400
     * 月卡方案五 400<=X<500
     * 季卡方案一 500<=X<800
     * 季卡方案二 800<=X<1500
     * 季卡方案三 1500<=X
     *
     * @param amount 金额
     * @return 权益包ID
     */
    private String selectCouponPackageId(BigDecimal amount) {
        List<String> couponPackageIds = config.getCouponPackageId();
        if (couponPackageIds == null || couponPackageIds.isEmpty()) {
            log.warn("【云樨-权益赊销】未配置权益包ID，使用空字符串");
            return "";
        }

        String packageIdValue = null;

        // 根据金额范围选择权益包
        if (amount.compareTo(new BigDecimal("100")) < 0) {
            // 月卡方案一：X < 100
            log.info("【云樨-权益赊销】选择月卡方案一");
            packageIdValue = getPackageIdByName(couponPackageIds, "月卡1");
        } else if (amount.compareTo(new BigDecimal("200")) < 0) {
            // 月卡方案二：100 <= X < 200
            log.info("【云樨-权益赊销】选择月卡方案二");
            packageIdValue = getPackageIdByName(couponPackageIds, "月卡2");
        } else if (amount.compareTo(new BigDecimal("300")) < 0) {
            // 月卡方案三：200 <= X < 300
            log.info("【云樨-权益赊销】选择月卡方案三");
            packageIdValue = getPackageIdByName(couponPackageIds, "月卡3");
        } else if (amount.compareTo(new BigDecimal("400")) < 0) {
            // 月卡方案四：300 <= X < 400
            log.info("【云樨-权益赊销】选择月卡方案四");
            packageIdValue = getPackageIdByName(couponPackageIds, "月卡4");
        } else if (amount.compareTo(new BigDecimal("500")) < 0) {
            // 月卡方案五：400 <= X < 500
            log.info("【云樨-权益赊销】选择月卡方案五");
            packageIdValue = getPackageIdByName(couponPackageIds, "月卡5");
        } else if (amount.compareTo(new BigDecimal("800")) < 0) {
            // 季卡方案一：500 <= X < 800
            log.info("【云樨-权益赊销】选择季卡方案一");
            packageIdValue = getPackageIdByName(couponPackageIds, "季卡1");
        } else if (amount.compareTo(new BigDecimal("1500")) < 0) {
            // 季卡方案二：800 <= X < 1500
            log.info("【云樨-权益赊销】选择季卡方案二");
            packageIdValue = getPackageIdByName(couponPackageIds, "季卡2");
        } else {
            // 季卡方案三：1500 <= X
            log.info("【云樨-权益赊销】选择季卡方案三");
            packageIdValue = getPackageIdByName(couponPackageIds, "季卡3");
        }

        // 如果仍然没有找到合适的权益包ID，则使用第一个可用的权益包ID
        if (StrUtil.isBlank(packageIdValue) && !couponPackageIds.isEmpty()) {
            String firstId = couponPackageIds.get(0);
            if (firstId.contains(":")) {
                packageIdValue = firstId.substring(firstId.indexOf(":") + 1);
                log.warn("【云樨-权益赊销】未找到合适的权益包ID，使用第一个可用的ID: {}", packageIdValue);
            } else {
                packageIdValue = firstId;
                log.warn("【云樨-权益赊销】未找到合适的权益包ID，直接使用: {}", packageIdValue);
            }
        }

        return packageIdValue;
    }

    /**
     * 根据权益包名称从配置中获取对应的packageId
     *
     * @param packageIds  权益包ID列表（格式如："月卡1:mbp1346560459859296256"）
     * @param packageName 权益包名称
     * @return 权益包ID
     */
    private String getPackageIdByName(List<String> packageIds, String packageName) {
        for (String item : packageIds) {
            if (item.startsWith(packageName + ":")) {
                return item.substring(item.indexOf(":") + 1);
            }
        }
        log.warn("未找到名称为{}的权益包ID，使用第一个配置项", packageName);
        // 如果没找到对应的包名，返回列表中的第一个ID（如果存在）
        if (!packageIds.isEmpty() && packageIds.get(0).contains(":")) {
            return packageIds.get(0).substring(packageIds.get(0).indexOf(":") + 1);
        }
        return "";
    }

    @Override
    public String encryptMobile(String mobile) {
        log.info("开始加密手机号, 原始手机号长度: {}", mobile != null ? mobile.length() : 0);

        if (mobile == null || mobile.isEmpty()) {
            log.warn("手机号为空，跳过加密");
            return null;
        }

        try {
            log.info("使用AES密钥加密手机号, 密钥长度: {}", config.getAesKey().length());
            String encryptedMobile = YunXiApiUtils.aesEncrypt(mobile, config.getAesKey());
            log.info("手机号加密完成, 加密后长度: {}", encryptedMobile.length());
            return encryptedMobile;
        } catch (Exception e) {
            log.error("手机号加密异常: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 解密AES
     */
    @Override
    public String decryptAesStr(String content) {
        try {
            return YunXiApiUtils.aesDecrypt(content, config.getAesKey());
        } catch (Exception e) {
            log.error("解密AES异常: {}", e.getMessage(), e);
            throw e;
        }
    }
}