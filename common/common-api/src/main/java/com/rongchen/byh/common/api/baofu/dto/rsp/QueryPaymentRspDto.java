package com.rongchen.byh.common.api.baofu.dto.rsp;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 支付结果查询响应DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryPaymentRspDto extends BaofuBaseRspDto {
    /**
     * 成功金额，单位：分
     */
    private String succ_amt;

    /**
     * 成功时间
     */
    private String succ_time;

    /**
     * 宝付订单号
     */
    private String order_id;

    /**
     * 商户订单号
     */
    private String trans_id;

    /**
     * 商户保留域1
     */
    private String req_reserved1;

    /**
     * 商户保留域2
     */
    private String req_reserved2;

    /**
     * 系统保留域1
     */
    private String additional_info1;

    /**
     * 系统保留域2
     */
    private String additional_info2;

    /**
     * 渠道订单号
     */
    private String channel_order_id;
}