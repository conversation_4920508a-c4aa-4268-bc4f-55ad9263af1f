package com.rongchen.byh.common.api.zifang.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 还款结果状态
 * @date 2025/3/4 12:12:52
 */
public class RepayStatusConstant {
    /**
     * 待还款
     */
    public static final String WAIT = "WAIT";

    /**
     * 已还款
     */
    public static final String SETTLE = "SETTLE";

    /**
     * 还款中
     */
    public static final String PAYING = "PAYING";

    /**
     * 已逾期
     */
    public static final String OVERDUE = "OVERDUE";

    private static final Map<Object, String> DICT_MAP = new HashMap<>(4);
    //可选值：N - 正常，G - 宽限期，O - 逾期，L - 呆滞（逾期90天以上），B - 呆账（逾期180天以上）。若无呆滞呆账状态则作逾期状态返回。
    static {
        DICT_MAP.put(WAIT, "N");
        DICT_MAP.put(SETTLE, "N");
        DICT_MAP.put(PAYING, "N");
        DICT_MAP.put(OVERDUE, "0");
    }

    public static String getStatus(String status) {
        if (DICT_MAP.containsKey(status)) {
            return DICT_MAP.get(status);
        }
        return "";
    }
}
