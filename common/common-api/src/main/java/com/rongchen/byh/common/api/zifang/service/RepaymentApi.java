package com.rongchen.byh.common.api.zifang.service;


import com.rongchen.byh.common.api.zifang.dto.*;
import com.rongchen.byh.common.api.zifang.vo.*;
import com.rongchen.byh.common.api.zifang.vo.loan.RespRepayPlanVo;
import com.rongchen.byh.common.core.object.ResponseResult;

/**
 * @ClassName RepaymentApi
 * @Description 还款接口
 * <AUTHOR>
 * @Date 2024/12/9 12:22
 * @Version 1.0
 **/
public interface RepaymentApi {
    
    /**
     * 还款试算接口
     * @date 2024/12/9 14:34
     *       
     * @param applyDto
     * @return java.lang.String
     */
    ResponseResult<PreRepayApplyVo> getPreRepayApply(PreRepayApplyDto applyDto);

    /**
     * 还款计划查询接口
     * @date 2024/12/9 14:34
     *
     * @param queryDto
     * @return java.lang.String
     */
    ResponseResult<RepaymentPlanQueryVo> getRepaymentPlanQuery(RepaymentPlanQueryDto queryDto);

    /**
     * api还款计划查询接口
     */
    ResponseResult<RespRepayPlanVo> getApiRepaymentPlanQuery(RepaymentPlanQueryDto queryDto);

    /**
     * 用户还款请求接口
     * @date 2024/12/9 14:34
     *
     * @param applyDto
     * @return java.lang.String
     */
    ResponseResult<RepaymentApplyVo> getRepaymentApply(RepaymentApplyDto applyDto);

    /**
     * 还款结果查询接口
     * @date 2024/12/9 14:34
     *
     * @param resultDto
     * @return java.lang.String
     */
    ResponseResult<RepaymentResultVo> getRepaymentResult(RepaymentResultDto resultDto);

    /**
     * 2.4.16.获取回购后划扣账户信息接口
     * @date 2024/12/9 14:34
     *
     * @param infoDto
     * @return java.lang.String
     */
    ResponseResult<BuybackBankInfoVo> getBuybackBankInfo(BuybackBankInfoDto infoDto);
}
