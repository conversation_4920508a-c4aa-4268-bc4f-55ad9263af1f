package com.rongchen.byh.common.api.idCardVerify.service;

import com.rongchen.byh.common.api.idCardVerify.dto.OcrDto;
import com.rongchen.byh.common.api.idCardVerify.vo.OcrNewVo;
import com.rongchen.byh.common.api.idCardVerify.vo.OcrVo;

public interface IdCardOcrService {

    /**
     *
     * @param ocrDto
     * @return
     */
    OcrVo idCardOcr(OcrDto ocrDto);


    /**
     * 新ocr识别
     * @param orderNo
     * @return
     */
    OcrNewVo idCardOcrNew(String orderNo);
}
