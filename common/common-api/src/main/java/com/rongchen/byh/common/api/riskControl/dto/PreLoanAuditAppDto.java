package com.rongchen.byh.common.api.riskControl.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.List;

/**
 * @ClassName PreLoanAuditAppDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/11 17:43
 * @Version 1.0
 **/
@Data
public class PreLoanAuditAppDto {
    /**
     * 申请人身份证，备注：用于标识申请人的身份证号码相关信息。
     */
    private String idcard_no;
    /**
     * 申请人手机号，备注：记录申请人的手机号码，用于联系等相关业务场景。
     */
    private String mobile;
    /**
     * 申请人姓名，备注：代表申请人的具体姓名信息。
     */
    private String name;
    /**
     * 申请ID，备注：用于唯一标识每一次的授信申请操作，方便后续查询、管理等流程。
     */
    private String credit_id;
    /**
     * 授信申请时间，备注：记录授信申请操作发生的具体时间。
     */
    private String credit_time;
    /**
     * 渠道ID，备注：用于明确此次申请是通过哪个具体渠道发起的，方便业务统计与管理。
     */
    private String channel;
    /**
     * 产品代码，备注：代表对应的产品编号，用于区分不同的产品业务类型。
     */
    private Integer product_code;
    /**
     * 申请人身份证号开始时间，备注：具体业务含义（需根据实际业务场景确定其准确作用），可能与身份证有效期等相关。
     */
    private String cert_start;
    /**
     * 申请人身份证号结束时间，备注：具体业务含义（需根据实际业务场景确定其准确作用），可能与身份证有效期等相关。
     */
    private String cert_end;
    /**
     * 申请人民族(OCR)，备注：通过OCR等技术识别获取的申请人民族信息，以数字等形式体现（需明确对应编码规则）。
     */
    private Integer nation_ocr;
    /**
     * 申请人户籍地址(OCR)，备注：通过OCR等技术识别获取的申请人户籍地址信息。
     */
    private String idcard_address_ocr;
    /**
     * 学历，备注：以数字等形式体现申请人的学历情况（需明确对应编码规则）。
     */
    private Integer education;
    /**
     * 婚姻状况，备注：以数字等形式体现申请人的婚姻状况（需明确对应编码规则）。
     */
    private Integer marriage_state;
    /**
     * 申请人居住地址，备注：记录申请人实际居住的地址信息。
     */
    private String live_address;
    /**
     * 证件归属城市编码，备注：用于标识申请人证件所属的城市对应的编码，方便区域相关业务处理。
     */
    private String idcard_city_code;
    /**
     * 申请人居住城市编码，备注：用于标识申请人居住城市对应的编码，方便区域相关业务处理。
     */
    private String live_city_code;
    /**
     * 签发机关，备注：记录申请人身份证的签发机关相关信息。
     */
    private String issued_org_ocr;
    /**
     * 年收入，备注：以数字形式体现申请人的年收入情况，用于相关业务风险评估等场景。
     */
    private Integer annual_income;
    /**
     * 是否h5注册，备注：用于判断申请人是否通过h5页面进行了注册操作，以特定格式（如字符串形式的标识）体现。
     */
    private String if_register;
    /**
     * 是否签已署融资服务合同，备注：用于判断申请人是否已经签署了融资服务合同，以特定格式（如字符串形式的标识）体现。
     */
    private String if_sign;
    /**
     * 本机号码，备注：记录对应的本机电话号码，具体作用（需结合实际业务场景确定）。
     */
    private String phone_number;
    /**
     * 开机时间，备注：记录相关设备的开机时间信息，具体业务用途（需结合实际业务场景确定）。
     */
    private String Mobile_startime;
    /**
     * wifi地址是否包含“金融”、“中介”等敏感词汇，备注：用于判断wifi地址中是否存在敏感词汇，以特定格式（如字符串形式的标识）体现。
     */
    private String wifi_sensitive;
    /**
     * 通讯录个数，备注：记录申请人通讯录中联系人的数量情况，用于相关业务分析等场景。
     */
    private Integer address_book_num;
    /**
     * 通讯录中11位手机号数量，备注：记录申请人通讯录中11位手机号的具体数量，用于相关业务分析等场景。
     */
    private Integer address_book_num11;
    /**
     * 通讯录去重后有标记“贷”或“借”或“催收”或“中介”或“口子”或“黑户”字样，备注：用于判断通讯录去重后是否存在特定标记字样，以特定格式（如字符串形式的标识）体现。
     */
    private String address_book_sensitive;
    /**
     * 填写的两个紧急联系人有一个及以上不在运营商通话详单中，备注：用于判断紧急联系人是否在运营商通话详单内的情况，以特定格式（如字符串形式的标识）体现。
     */
    private String contact_operator;
    /**
     * 最近60天逾期的短信条数，备注：统计申请人最近60天内逾期相关短信的数量情况，用于风险评估等业务场景。
     */
    private Integer overdue_message_m60;
    /**
     * 住房信息，备注：以JSON格式存储申请人的住房相关详细信息（具体内容结构需根据实际业务确定）。
     */
    private JSONObject house_info;
    /**
     * 住房情况，备注：以数字等形式体现申请人的住房情况（需明确对应编码规则）。
     */
    private List<PreLoanAuditAppRelationsDto> relations;
    /**
     * 企业信息
     */
    private JSONObject company_info;
}
