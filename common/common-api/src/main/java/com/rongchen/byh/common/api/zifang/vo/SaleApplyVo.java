package com.rongchen.byh.common.api.zifang.vo;

import com.rongchen.byh.common.api.yunxi.vo.YunXiOrderDetailVo;
import lombok.Data;

/**
 * @ClassName SaleApplyVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/9 18:10
 * @Version 1.0
 **/
@Data
public class SaleApplyVo {

    /**
     * 必填，响应码，用于标识请求处理后的对应状态结果。
     */
    private String responseCode;
    /**
     * 非必填，响应消息，用于对响应码对应的情况做更详细的文字说明。
     */
    private String responseMsg;

    /**
     * 外部赊销订单号
     */
    private String saleNo;

    private YunXiOrderDetailVo yunXiOrderDetail;
}
