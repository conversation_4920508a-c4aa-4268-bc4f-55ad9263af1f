package com.rongchen.byh.common.api.zifang.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName BindBankSmsVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/7 0:29
 * @Version 1.0
 **/
@Data
public class BindBankSmsVo {

    /**
     * 响应码
     * 0000成功
     * P00001 处理中 返回处理中时，不需要调2.2.4绑卡验证码提交接口，直接调2.2.5绑卡结果查询绑卡结果
     */
    @Schema(description = "响应码 0000成功 , P00001 处理中 返回处理中时，不需要调 绑卡-提交验证码 接口，直接调 绑卡-结果查询接口")
    private String responseCode;

    /**
     * 备注
     */
    private String responseMsg;

    /**
     * 短信验证码流水号
     */
    private String messageNo;

    /**
     * 绑卡状态 分转需要
     */
    private String bindStatus;

    /**
     * 分转资方返回
     */
    private String bindId;

}
