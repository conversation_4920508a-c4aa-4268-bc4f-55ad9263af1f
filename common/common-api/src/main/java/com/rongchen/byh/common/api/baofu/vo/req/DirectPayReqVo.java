package com.rongchen.byh.common.api.baofu.vo.req;

import lombok.Data;

import java.util.List;

/**
 * 直接支付请求VO
 */
@Data
public class DirectPayReqVo {
    
    /**
     * 商户订单号
     */
    private String transId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 签约协议号
     */
    private String protocolNo;
    
    /**
     * 交易金额(单位:分)
     */
    private String txnAmt;
    
    /**
     * 卡信息(信用卡有效期|安全码)
     */
    private String cardInfo;
    
    /**
     * 风控参数
     */
    private RiskItemReqVo riskItem;
    
    /**
     * 交易成功通知地址(最多三个,用|分隔)
     */
    private String returnUrl;
    
    /**
     * 分账信息(格式:商户1,金额1;商户2,金额2)
     */
    private String shareInfo;
    
    /**
     * 分账结果通知地址
     */
    private String shareNotifyUrl;
    
    /**
     * 手续费承担方商户号(多个用;分隔)
     */
    private String feeMemberId;
    
    /**
     * 计费商户号
     */
    private String callFeeMemberId;
} 