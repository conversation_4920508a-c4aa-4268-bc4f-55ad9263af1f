package com.rongchen.byh.common.api.notice.service.impl;

import cn.hutool.core.util.StrUtil;
import com.rongchen.byh.common.api.notice.config.NoticeSmsContent;
import com.rongchen.byh.common.api.notice.dto.NoticeSmsDto;
import com.rongchen.byh.common.api.notice.dto.ThreeDayRepaymentDto;
import com.rongchen.byh.common.core.util.MyModelUtil;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 到期前3天还款提醒
 * @date 2025/2/7 11:45:29
 */
@Service
public class ThreeDayRepaymentService extends AbstractNoticeSmsService{
    @Override
    protected String getContent(NoticeSmsDto noticeSmsDto) {
        ThreeDayRepaymentDto dto = (ThreeDayRepaymentDto) noticeSmsDto;
        return StrUtil.format(NoticeSmsContent.DICT_MAP.get(NoticeSmsContent.THREEDAYREPAYNOTICE), MyModelUtil.beanToMap(dto));
    }

    @Override
    protected Integer getTemplateId() {
        return null;
    }
}
