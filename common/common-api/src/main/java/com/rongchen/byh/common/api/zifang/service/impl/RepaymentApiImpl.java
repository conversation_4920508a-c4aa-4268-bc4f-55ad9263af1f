//package com.rongchen.byh.common.api.zifang.service.impl;
//
//import com.alibaba.fastjson.JSONObject;
//import com.rongchen.byh.common.api.zifang.config.ZiFangConstant;
//import com.rongchen.byh.common.api.zifang.dto.*;
//import com.rongchen.byh.common.api.zifang.service.RepaymentApi;
//import com.rongchen.byh.common.api.zifang.vo.*;
//import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
//import com.rongchen.byh.common.core.object.ResponseResult;
//import org.springframework.stereotype.Service;
//
///**
// * @ClassName RepaymentApiImpl
// * @Description TODO
// * <AUTHOR>
// * @Date 2024/12/9 12:23
// * @Version 1.0
// **/
//@Service
//public class RepaymentApiImpl extends AbstractApi implements RepaymentApi {
//
//
//    @Override
//    public ResponseResult<PreRepayApplyVo> getPreRepayApply(PreRepayApplyDto applyDto) {
//        JSONObject params = new JSONObject();
//        params.put("reqSysCode", ziFangProperties.getReqSysCode());
//        params.put("fundCode",applyDto.getFundCode());
//        params.put("merserno", applyDto.getMerserno());
//        params.put("userId", applyDto.getUserId());
//        params.put("loanNo", applyDto.getLoanNo());
//        params.put("prePayType", applyDto.getPrePayType());
//        params.put("paytotalamt", applyDto.getPaytotalamt());
//        params.put("term", applyDto.getTerm());
//        try {
//            JSONObject map = this.sendRequest(params, ZiFangConstant.getPreRepayApply);
//            PreRepayApplyVo ziFangVo = map.toJavaObject(PreRepayApplyVo.class);
//            return ResponseResult.success(ziFangVo);
//        } catch (Exception e) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL,e.getMessage());
//        }
//    }
//
//    @Override
//    public ResponseResult<RepaymentPlanQueryVo> getRepaymentPlanQuery(RepaymentPlanQueryDto queryDto) {
//        JSONObject params = new JSONObject();
//        params.put("reqSysCode", ziFangProperties.getReqSysCode());
//        params.put("productCode", ziFangProperties.getProductCode());
//        params.put("userId", queryDto.getUserId());
//        params.put("loanNo", queryDto.getLoanNo());
//        try {
//            JSONObject map = this.sendRequest(params, ZiFangConstant.getRepaymentPlanQuery);
//            RepaymentPlanQueryVo ziFangVo = map.toJavaObject(RepaymentPlanQueryVo.class);
//            return ResponseResult.success(ziFangVo);
//        } catch (Exception e) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL,e.getMessage());
//        }
//    }
//
//    @Override
//    public ResponseResult<RepaymentApplyVo> getRepaymentApply(RepaymentApplyDto applyDto) {
//        String reg = JSONObject.toJSONString(applyDto);
//        JSONObject params = JSONObject.parseObject(reg);
//        params.put("reqSysCode", ziFangProperties.getReqSysCode());
//        params.put("fundCode", applyDto.getFundCode());
//        try {
//            JSONObject map = this.sendRequest(params, ZiFangConstant.getRepaymentApply);
//            RepaymentApplyVo ziFangVo = map.toJavaObject(RepaymentApplyVo.class);
//            return ResponseResult.success(ziFangVo);
//        } catch (Exception e) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL,e.getMessage());
//        }
//    }
//
//    @Override
//    public ResponseResult<RepaymentResultVo> getRepaymentResult(RepaymentResultDto resultDto) {
//        String reg = JSONObject.toJSONString(resultDto);
//        JSONObject params = JSONObject.parseObject(reg);
//        params.put("reqSysCode", ziFangProperties.getReqSysCode());
//        try {
//            JSONObject map = this.sendRequest(params, ZiFangConstant.getRepaymentResult);
//            RepaymentResultVo ziFangVo = map.toJavaObject(RepaymentResultVo.class);
//            return ResponseResult.success(ziFangVo);
//        } catch (Exception e) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL,e.getMessage());
//        }
//    }
//
//    @Override
//    public ResponseResult<BuybackBankInfoVo> getBuybackBankInfo(BuybackBankInfoDto infoDto) {
//        String reg = JSONObject.toJSONString(infoDto);
//        JSONObject params = JSONObject.parseObject(reg);
//        params.put("reqSysCode", ziFangProperties.getReqSysCode());
//        try {
//            JSONObject map = this.sendRequest(params, ZiFangConstant.getBuybackBankInfo);
//            BuybackBankInfoVo ziFangVo = map.toJavaObject(BuybackBankInfoVo.class);
//            return ResponseResult.success(ziFangVo);
//        } catch (Exception e) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL,e.getMessage());
//        }
//    }
//
//}
