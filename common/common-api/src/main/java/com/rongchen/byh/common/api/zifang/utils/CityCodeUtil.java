package com.rongchen.byh.common.api.zifang.utils;

import java.util.HashMap;
import java.util.Map;

public class CityCodeUtil {

    private CityCodeUtil(){}

    private static final Map<String,String> map = new HashMap<>();

    private static final Map<String,String> cityCodeMap =  new HashMap<>();

    static {
        map.put("北京市","110100");
        map.put("天津市","120100");
        map.put("石家庄市","130100");
        map.put("唐山市","130200");
        map.put("秦皇岛市","130300");
        map.put("邯郸市","130400");
        map.put("邢台市","130500");
        map.put("保定市","130600");
        map.put("张家口市","130700");
        map.put("承德市","130800");
        map.put("沧州市","130900");
        map.put("廊坊市","131000");
        map.put("衡水市","131100");
        map.put("太原市","140100");
        map.put("大同市","140200");
        map.put("阳泉市","140300");
        map.put("长治市","140400");
        map.put("晋城市","140500");
        map.put("朔州市","140600");
        map.put("晋中市","140700");
        map.put("运城市","140800");
        map.put("忻州市","140900");
        map.put("临汾市","141000");
        map.put("吕梁市","141100");
        map.put("呼和浩特市","150100");
        map.put("包头市","150200");
        map.put("乌海市","150300");
        map.put("赤峰市","150400");
        map.put("通辽市","150500");
        map.put("鄂尔多斯市","150600");
        map.put("呼伦贝尔市","150700");
        map.put("巴彦淖尔市","150800");
        map.put("乌兰察布市","150900");
        map.put("兴安盟","152200");
        map.put("锡林郭勒盟","152500");
        map.put("阿拉善盟","152900");
        map.put("沈阳市","210100");
        map.put("大连市","210200");
        map.put("鞍山市","210300");
        map.put("抚顺市","210400");
        map.put("本溪市","210500");
        map.put("丹东市","210600");
        map.put("锦州市","210700");
        map.put("营口市","210800");
        map.put("阜新市","210900");
        map.put("辽阳市","211000");
        map.put("盘锦市","211100");
        map.put("铁岭市","211200");
        map.put("朝阳市","211300");
        map.put("葫芦岛市","211400");
        map.put("长春市","220100");
        map.put("吉林市","220200");
        map.put("四平市","220300");
        map.put("辽源市","220400");
        map.put("通化市","220500");
        map.put("白山市","220600");
        map.put("松原市","220700");
        map.put("白城市","220800");
        map.put("延边朝鲜族自治州","222400");
        map.put("哈尔滨市","230100");
        map.put("齐齐哈尔市","230200");
        map.put("鸡西市","230300");
        map.put("鹤岗市","230400");
        map.put("双鸭山市","230500");
        map.put("大庆市","230600");
        map.put("伊春市","230700");
        map.put("佳木斯市","230800");
        map.put("七台河市","230900");
        map.put("牡丹江市","231000");
        map.put("黑河市","231100");
        map.put("绥化市","231200");
        map.put("大兴安岭地区","232700");
        map.put("上海市","310100");
        map.put("上海市县","310200");
        map.put("南京市","320100");
        map.put("无锡市","320200");
        map.put("徐州市","320300");
        map.put("常州市","320400");
        map.put("苏州市","320500");
        map.put("南通市","320600");
        map.put("连云港市","320700");
        map.put("淮安市","320800");
        map.put("盐城市","320900");
        map.put("扬州市","321000");
        map.put("镇江市","321100");
        map.put("泰州市","321200");
        map.put("宿迁市","321300");
        map.put("杭州市","330100");
        map.put("宁波市","330200");
        map.put("温州市","330300");
        map.put("嘉兴市","330400");
        map.put("湖州市","330500");
        map.put("绍兴市","330600");
        map.put("金华市","330700");
        map.put("衢州市","330800");
        map.put("舟山市","330900");
        map.put("台州市","331000");
        map.put("丽水市","331100");
        map.put("合肥市","340100");
        map.put("芜湖市","340200");
        map.put("蚌埠市","340300");
        map.put("淮南市","340400");
        map.put("马鞍山市","340500");
        map.put("淮北市","340600");
        map.put("铜陵市","340700");
        map.put("安庆市","340800");
        map.put("黄山市","341000");
        map.put("滁州市","341100");
        map.put("阜阳市","341200");
        map.put("宿州市","341300");
        map.put("六安市","341500");
        map.put("亳州市","341600");
        map.put("池州市","341700");
        map.put("宣城市","341800");
        map.put("福州市","350100");
        map.put("厦门市","350200");
        map.put("莆田市","350300");
        map.put("三明市","350400");
        map.put("泉州市","350500");
        map.put("漳州市","350600");
        map.put("南平市","350700");
        map.put("龙岩市","350800");
        map.put("宁德市","350900");
        map.put("南昌市","360100");
        map.put("景德镇市","360200");
        map.put("萍乡市","360300");
        map.put("九江市","360400");
        map.put("新余市","360500");
        map.put("鹰潭市","360600");
        map.put("赣州市","360700");
        map.put("吉安市","360800");
        map.put("宜春市","360900");
        map.put("抚州市","361000");
        map.put("上饶市","361100");
        map.put("济南市","370100");
        map.put("青岛市","370200");
        map.put("淄博市","370300");
        map.put("枣庄市","370400");
        map.put("东营市","370500");
        map.put("烟台市","370600");
        map.put("潍坊市","370700");
        map.put("济宁市","370800");
        map.put("泰安市","370900");
        map.put("威海市","371000");
        map.put("日照市","371100");
        map.put("莱芜市","371200");
        map.put("临沂市","371300");
        map.put("德州市","371400");
        map.put("聊城市","371500");
        map.put("滨州市","371600");
        map.put("菏泽市","371700");
        map.put("郑州市","410100");
        map.put("开封市","410200");
        map.put("洛阳市","410300");
        map.put("平顶山市","410400");
        map.put("安阳市","410500");
        map.put("鹤壁市","410600");
        map.put("新乡市","410700");
        map.put("焦作市","410800");
        map.put("濮阳市","410900");
        map.put("许昌市","411000");
        map.put("漯河市","411100");
        map.put("三门峡市","411200");
        map.put("南阳市","411300");
        map.put("商丘市","411400");
        map.put("信阳市","411500");
        map.put("周口市","411600");
        map.put("驻马店市","411700");
        map.put("河南省直辖县级行政区划","419000");
        map.put("武汉市","420100");
        map.put("黄石市","420200");
        map.put("十堰市","420300");
        map.put("宜昌市","420500");
        map.put("襄阳市","420600");
        map.put("鄂州市","420700");
        map.put("荆门市","420800");
        map.put("孝感市","420900");
        map.put("荆州市","421000");
        map.put("黄冈市","421100");
        map.put("咸宁市","421200");
        map.put("随州市","421300");
        map.put("恩施土家族苗族自治州","422800");
        map.put("湖北省直辖县级行政区划","429000");
        map.put("长沙市","430100");
        map.put("株洲市","430200");
        map.put("湘潭市","430300");
        map.put("衡阳市","430400");
        map.put("邵阳市","430500");
        map.put("岳阳市","430600");
        map.put("常德市","430700");
        map.put("张家界市","430800");
        map.put("益阳市","430900");
        map.put("郴州市","431000");
        map.put("永州市","431100");
        map.put("怀化市","431200");
        map.put("娄底市","431300");
        map.put("湘西土家族苗族自治州","433100");
        map.put("广州市","440100");
        map.put("韶关市","440200");
        map.put("深圳市","440300");
        map.put("珠海市","440400");
        map.put("汕头市","440500");
        map.put("佛山市","440600");
        map.put("江门市","440700");
        map.put("湛江市","440800");
        map.put("茂名市","440900");
        map.put("肇庆市","441200");
        map.put("惠州市","441300");
        map.put("梅州市","441400");
        map.put("汕尾市","441500");
        map.put("河源市","441600");
        map.put("阳江市","441700");
        map.put("清远市","441800");
        map.put("东莞市","441900");
        map.put("中山市","442000");
        map.put("潮州市","445100");
        map.put("揭阳市","445200");
        map.put("云浮市","445300");
        map.put("南宁市","450100");
        map.put("柳州市","450200");
        map.put("桂林市","450300");
        map.put("梧州市","450400");
        map.put("北海市","450500");
        map.put("防城港市","450600");
        map.put("钦州市","450700");
        map.put("贵港市","450800");
        map.put("玉林市","450900");
        map.put("百色市","451000");
        map.put("贺州市","451100");
        map.put("河池市","451200");
        map.put("来宾市","451300");
        map.put("崇左市","451400");
        map.put("海口市","460100");
        map.put("三亚市","460200");
        map.put("三沙市","460300");
        map.put("儋州市","460400");
        map.put("海南省直辖县级行政区划","469000");
        map.put("重庆市","500100");
        map.put("重庆市县","500200");
        map.put("成都市","510100");
        map.put("自贡市","510300");
        map.put("攀枝花市","510400");
        map.put("泸州市","510500");
        map.put("德阳市","510600");
        map.put("绵阳市","510700");
        map.put("广元市","510800");
        map.put("遂宁市","510900");
        map.put("内江市","511000");
        map.put("乐山市","511100");
        map.put("南充市","511300");
        map.put("眉山市","511400");
        map.put("宜宾市","511500");
        map.put("广安市","511600");
        map.put("达州市","511700");
        map.put("雅安市","511800");
        map.put("巴中市","511900");
        map.put("资阳市","512000");
        map.put("阿坝藏族羌族自治州","513200");
        map.put("甘孜藏族自治州","513300");
        map.put("凉山彝族自治州","513400");
        map.put("贵阳市","520100");
        map.put("六盘水市","520200");
        map.put("遵义市","520300");
        map.put("安顺市","520400");
        map.put("毕节市","520500");
        map.put("铜仁市","520600");
        map.put("黔西南布依族苗族自治州","522300");
        map.put("黔东南苗族侗族自治州","522600");
        map.put("黔南布依族苗族自治州","522700");
        map.put("昆明市","530100");
        map.put("曲靖市","530300");
        map.put("玉溪市","530400");
        map.put("保山市","530500");
        map.put("昭通市","530600");
        map.put("丽江市","530700");
        map.put("普洱市","530800");
        map.put("临沧市","530900");
        map.put("楚雄彝族自治州","532300");
        map.put("红河哈尼族彝族自治州","532500");
        map.put("文山壮族苗族自治州","532600");
        map.put("西双版纳傣族自治州","532800");
        map.put("大理白族自治州","532900");
        map.put("德宏傣族景颇族自治州","533100");
        map.put("怒江傈僳族自治州","533300");
        map.put("迪庆藏族自治州","533400");
        map.put("拉萨市","540100");
        map.put("日喀则市","540200");
        map.put("昌都市","540300");
        map.put("林芝市","540400");
        map.put("山南市","540500");
        map.put("那曲地区","542400");
        map.put("阿里地区","542500");
        map.put("西安市","610100");
        map.put("铜川市","610200");
        map.put("宝鸡市","610300");
        map.put("咸阳市","610400");
        map.put("渭南市","610500");
        map.put("延安市","610600");
        map.put("汉中市","610700");
        map.put("榆林市","610800");
        map.put("安康市","610900");
        map.put("商洛市","611000");
        map.put("兰州市","620100");
        map.put("嘉峪关市","620200");
        map.put("金昌市","620300");
        map.put("白银市","620400");
        map.put("天水市","620500");
        map.put("武威市","620600");
        map.put("张掖市","620700");
        map.put("平凉市","620800");
        map.put("酒泉市","620900");
        map.put("庆阳市","621000");
        map.put("定西市","621100");
        map.put("陇南市","621200");
        map.put("临夏回族自治州","622900");
        map.put("甘南藏族自治州","623000");
        map.put("西宁市","630100");
        map.put("海东市","630200");
        map.put("海北藏族自治州","632200");
        map.put("黄南藏族自治州","632300");
        map.put("海南藏族自治州","632500");
        map.put("果洛藏族自治州","632600");
        map.put("玉树藏族自治州","632700");
        map.put("海西蒙古族藏族自治州","632800");
        map.put("银川市","640100");
        map.put("石嘴山市","640200");
        map.put("吴忠市","640300");
        map.put("固原市","640400");
        map.put("中卫市","640500");
        map.put("乌鲁木齐市","650100");
        map.put("克拉玛依市","650200");
        map.put("吐鲁番市","650400");
        map.put("哈密市","650500");
        map.put("昌吉回族自治州","652300");
        map.put("博尔塔拉蒙古自治州","652700");
        map.put("巴音郭楞蒙古自治州","652800");
        map.put("阿克苏地区","652900");
        map.put("克孜勒苏柯尔克孜自治州","653000");
        map.put("喀什地区","653100");
        map.put("和田地区","653200");
        map.put("伊犁哈萨克自治州","654000");
        map.put("塔城地区","654200");
        map.put("阿勒泰地区","654300");
        map.put("自治区直辖县级行政区划","659000");
        map.put("中国香港特别行政区","810100");
        map.put("中国澳门特别行政区","910100");
    }

    static {
        map.forEach((k,v)->{
            cityCodeMap.put(v,k);
        });
    }

    public static String getCodeByName(String name){
        return map.get(name);
    }

    public static String getCityByCode(String code){
        return cityCodeMap.get(code);
    }
}
