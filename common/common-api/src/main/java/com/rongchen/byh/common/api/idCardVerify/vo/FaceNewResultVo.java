package com.rongchen.byh.common.api.idCardVerify.vo;

import lombok.Data;

/**
 * @ClassName FaceNewResultVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/23 20:51
 * @Version 1.0
 **/
@Data
public class FaceNewResultVo {


    // 订单编号
    private String orderNo;
    // 活体检测得分
    private String liveRate;
    // 人脸比对得分
    private String similarity;
    // 进行刷脸的时间
    private String occurredTime;
    // 人脸核身时的照片，base64位编码
    private String photo;
    // 人脸核身时的视频，base64位编码
    private String video;
    // 人脸核身时的sdk版本号
    private String sdkVersion;
    // Trtc渠道刷脸则标识"Y"
    private String trtcFlag;
    // 腾讯云控制台申请的appid
    private String appId;
}
