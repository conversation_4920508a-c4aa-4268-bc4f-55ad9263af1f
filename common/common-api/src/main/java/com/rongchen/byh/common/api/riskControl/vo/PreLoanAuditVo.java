package com.rongchen.byh.common.api.riskControl.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/12/11 12:26:18
 */
@Data
public class PreLoanAuditVo {
    /**
     * 0:通过，-1:拒绝，-2：待审核 1 进行中 2 卡单中 500 接口失败
     */
    private Integer result;

    /**
     * 信息
     */
    private String message;

    /**
     * 授信金额 （对方是分 需要转 元）
     */
    private BigDecimal amount;

    /**
     *  客服风险等级 A1 , A2 , A3 , A4 , A5。默认B
     */
    private String creditRating;
}
