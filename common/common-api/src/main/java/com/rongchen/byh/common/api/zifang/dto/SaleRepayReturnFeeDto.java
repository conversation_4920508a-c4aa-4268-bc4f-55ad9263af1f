package com.rongchen.byh.common.api.zifang.dto;

import lombok.Data;

/**
 * @ClassName SaleRepayReturnFeeDto
 * @Description 赊销还款退费
 * <AUTHOR>
 * @Date 2024/12/10 11:59
 * @Version 1.0
 **/
@Data
public class SaleRepayReturnFeeDto {


    /**
     * 必填，还款申请流水，备注：用于标识用户还款请求或线下还款接口里的还款申请流水，方便对还款相关操作进行追踪与管理。
     */
    private String repayApplyNo;
    /**
     * 必填，退费申请流水，备注：用于唯一标识每一次的退费申请操作，便于后续查询、处理等流程。
     */
    private String refundApplyNo;
    /**
     * 必填，退费类型，备注：取值有0（退费回滚）、1（客诉减免退费），用于明确此次退费属于哪种具体类型。
     */
    private String refundType;
    /**
     * 非必填，当退费类型为1（客诉减免退费）时必传,退费金额，备注：格式为17位整数2位小数，正数，单位为元，代表此次退费涉及的具体金额数目。
     */
    private String refundAmt;
}
