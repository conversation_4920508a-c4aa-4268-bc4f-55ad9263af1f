package com.rongchen.byh.common.api.baofu.util;

import com.rongchen.byh.common.api.baofu.constant.BaofuConstant;
import com.rongchen.byh.common.api.baofu.util.rsa.RsaCodingUtil;
import com.rongchen.byh.common.api.baofu.util.rsa.SignatureUtils;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.security.PublicKey;
import java.security.cert.Certificate;
import java.security.cert.CertificateFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;

/**
 * 宝付支付加解密工具类
 */
@Slf4j
public class BaofuEncryptUtil {

    /**
     * 生成AES密钥，16位长度
     */
    public static String generateAESKey() throws Exception {
        return FormatUtil.CreateAeskey();
    }

    /**
     * 生成指定长度的AES密钥
     */
    public static String generateAESKey(int length) throws Exception {
        return FormatUtil.CreateAeskey(length);
    }

    /**
     * 创建数字信封
     *
     * @param aesKey AES密钥
     * @param isTest 是否测试环境
     * @return 加密后的数字信封
     */
    public static String createDigitalEnvelope(String aesKey, boolean isTest) throws Exception {
        // 创建数字信封，格式："01|AES密钥"，01代表AES
        String dgtlEnvlp = BaofuConstant.ALGORITHM_AES + "|" + aesKey;
        // log.info("数字信封原始内容: {}", dgtlEnvlp);

        // 使用Base64编码
        String base64Content = SecurityUtil.Base64Encode(dgtlEnvlp);

        // 使用宝付公钥加密
        String certPath = isTest ? BaofuConstant.TEST_BAOFU_PUBLIC_KEY_PATH : BaofuConstant.PROD_BAOFU_PUBLIC_KEY_PATH;
        String encryptedEnvelope = RsaCodingUtil.encryptByPubCerFile(base64Content, getCertPathFromClasspath(certPath));

        // log.info("数字信封加密后: 长度={}, 内容预览={}",
        // encryptedEnvelope.length(),
        // encryptedEnvelope.length() > 20 ? encryptedEnvelope.substring(0, 20) + "..."
        // : encryptedEnvelope);

        return encryptedEnvelope;
    }

    /**
     * 解密数字信封
     *
     * @param digitalEnvelope 加密的数字信封
     * @param isTest          是否测试环境
     * @return 解密后的数字信封内容
     */
    public static String decryptDigitalEnvelope(String digitalEnvelope, boolean isTest) throws Exception {
        log.info("开始解密数字信封: 长度={}, 预览={}",
            digitalEnvelope.length(),
            digitalEnvelope.length() > 20 ? digitalEnvelope.substring(0, 20) + "..." : digitalEnvelope);

        // 使用商户私钥解密
        String pfxPath = isTest ? BaofuConstant.TEST_MERCHANT_PRIVATE_KEY_PATH
            : BaofuConstant.PROD_MERCHANT_PRIVATE_KEY_PATH;
        String pfxPwd = isTest ? BaofuConstant.TEST_CERT_PASSWORD : BaofuConstant.PROD_CERT_PASSWORD;

        // 首先尝试直接从classpath加载私钥
        try {
            java.security.PrivateKey privateKey = getPrivateKeyFromClasspath(pfxPath, pfxPwd);
            log.info("成功从classpath直接加载私钥");

            // 使用私钥解密
            byte[] encryptedBytes = hexToBytes(digitalEnvelope);
            javax.crypto.Cipher cipher = javax.crypto.Cipher.getInstance("RSA/ECB/PKCS1Padding");
            cipher.init(javax.crypto.Cipher.DECRYPT_MODE, privateKey);
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
            String decrypted = new String(decryptedBytes, "UTF-8");

            // Base64解码
            String result = SecurityUtil.Base64Decode(decrypted);
            log.info("数字信封解密结果: {}", result);

            return result;
        } catch (Exception e) {
            log.warn("使用新方法解密数字信封失败，尝试使用旧方法: {}", e.getMessage());

            // 如果新方法失败，回退到旧方法
            try {
                String pfxFilePath = getPfxPathFromClasspath(pfxPath);
                log.info("获取到私钥文件路径: {}", pfxFilePath);
                String decrypted = RsaCodingUtil.decryptByPriPfxFile(digitalEnvelope, pfxFilePath, pfxPwd);

                // Base64解码
                String result = SecurityUtil.Base64Decode(decrypted);
                log.info("数字信封解密结果(旧方法): {}", result);

                return result;
            } catch (Exception ex) {
                log.error("解密数字信封过程发生异常: {}", ex.getMessage(), ex);
                throw new Exception("解密数字信封失败: " + ex.getMessage(), ex);
            }
        }
    }

    /**
     * 获取数字信封中的AES密钥
     */
    public static String getAesKeyFromEnvelope(String envelope) throws Exception {
        return FormatUtil.getAesKey(envelope);
    }

    /**
     * AES加密
     *
     * @param content 待加密内容
     * @param aesKey  AES密钥
     * @return 加密后的内容
     */
    public static String encryptByAES(String content, String aesKey) throws Exception {
        log.info("AES加密内容: 长度={}", content.length());

        // 先Base64编码，再AES加密
        String base64Content = SecurityUtil.Base64Encode(content);
        String encrypted = SecurityUtil.AesEncrypt(base64Content, aesKey);

        // log.info("AES加密结果: 长度={}, 预览={}",
        // encrypted.length(),
        // encrypted.length() > 20 ? encrypted.substring(0, 20) + "..." : encrypted);

        return encrypted;
    }

    /**
     * AES解密
     *
     * @param encryptedContent 加密内容
     * @param aesKey           AES密钥
     * @return 解密后的内容
     */
    public static String decryptByAES(String encryptedContent, String aesKey) throws Exception {
        // log.info("AES解密内容: 长度={}, 预览={}",
        // encryptedContent.length(),
        // encryptedContent.length() > 20 ? encryptedContent.substring(0, 20) + "..." :
        // encryptedContent);

        // 先AES解密，再Base64解码
        String decrypted = SecurityUtil.AesDecrypt(encryptedContent, aesKey);
        String result = SecurityUtil.Base64Decode(decrypted);

        log.info("AES解密结果: 长度={}, 内容={}", result.length(), result);

        return result;
    }

    /**
     * 签名
     *
     * @param content 待签名内容
     * @param isTest  是否测试环境
     * @return 签名结果
     */
    public static String sign(String content, boolean isTest) throws Exception {
        String pfxPath = isTest ? BaofuConstant.TEST_MERCHANT_PRIVATE_KEY_PATH
            : BaofuConstant.PROD_MERCHANT_PRIVATE_KEY_PATH;
        String pfxPwd = isTest ? BaofuConstant.TEST_CERT_PASSWORD : BaofuConstant.PROD_CERT_PASSWORD;

        log.info("签名内容: {}", content);

        // 首先尝试直接从classpath加载私钥
        try {
            java.security.PrivateKey privateKey = getPrivateKeyFromClasspath(pfxPath, pfxPwd);
            log.info("成功从classpath直接加载私钥");

            // 使用私钥进行签名
            java.security.Signature signature = java.security.Signature.getInstance(BaofuConstant.SIGNATURE_ALGORITHM);
            signature.initSign(privateKey);
            signature.update(content.getBytes("UTF-8"));
            byte[] signBytes = signature.sign();
            String signResult = bytesToHex(signBytes);

            log.info("签名结果: {}", signResult);
            return signResult;
        } catch (Exception e) {
            log.warn("使用新方法签名失败，尝试使用旧方法: {}", e.getMessage());

            // 如果新方法失败，回退到旧方法
            try {
                String pfxFilePath = getPfxPathFromClasspath(pfxPath);
                log.info("获取到私钥文件路径: {}", pfxFilePath);
                String signature = SignatureUtils.encryptByRSA(content, pfxFilePath, pfxPwd);
                log.info("签名结果(旧方法): {}", signature);
                return signature;
            } catch (Exception ex) {
                log.error("签名过程发生异常: {}", ex.getMessage(), ex);
                throw new Exception("签名失败: " + ex.getMessage(), ex);
            }
        }
    }

    /**
     * 验签
     *
     * @param content      原始内容
     * @param signatureStr 签名字符串
     * @param isTest       是否测试环境
     * @return 验签结果
     */
    public static boolean verify(String content, String signatureStr, boolean isTest) throws Exception {
        String certPath = isTest ? BaofuConstant.TEST_BAOFU_PUBLIC_KEY_PATH : BaofuConstant.PROD_BAOFU_PUBLIC_KEY_PATH;

        log.info("验签内容: {}", content);
        log.info("签名值: {}", signatureStr);

        try {
            // 优先使用新的方法从JAR包内读取证书
            PublicKey publicKey = getPublicKeyFromClasspath(certPath);
            boolean result = SignatureUtils.verify(content.getBytes("UTF-8"), publicKey.getEncoded(), signatureStr);
            log.info("验签结果: {}", result);
            return result;
        } catch (Exception e) {
            // 如果新方法失败，回退到老方法
            log.warn("使用新方法验签失败，尝试使用旧方法", e);
            boolean result = SignatureUtils.verifySignature(getCertPathFromClasspath(certPath), content, signatureStr);
            log.info("验签结果(旧方法): {}", result);
            return result;
        }
    }

    /**
     * SHA-1摘要
     */
    public static String sha1Digest(String content) throws Exception {
        return SecurityUtil.sha1X16(content, BaofuConstant.CHARSET);
    }

    /**
     * 从classpath获取证书绝对路径
     */
    private static String getCertPathFromClasspath(String classPath) throws Exception {
        try {
            ClassPathResource resource = new ClassPathResource(classPath);
            if (resource.isFile()) {
                // 如果资源是文件系统中的文件，则返回绝对路径
                String absolutePath = resource.getFile().getAbsolutePath();
                log.info("证书路径: {}", absolutePath);
                return absolutePath;
            } else {
                // 如果资源不是文件系统中的文件（如JAR中的资源），则创建临时文件
                log.info("证书在JAR包内，创建临时文件: {}", classPath);
                File tempFile = File.createTempFile("cert_", ".tmp");
                tempFile.deleteOnExit();

                try (InputStream inputStream = resource.getInputStream();
                    FileOutputStream outputStream = new FileOutputStream(tempFile)) {
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }
                }

                String tempPath = tempFile.getAbsolutePath();
                log.info("临时证书路径: {}", tempPath);
                return tempPath;
            }
        } catch (Exception e) {
            log.error("获取证书路径失败: {}", classPath, e);
            throw e;
        }
    }

    /**
     * 直接从classpath获取证书的PublicKey对象
     * 该方法不依赖文件系统，可以从JAR包内读取证书
     */
    private static PublicKey getPublicKeyFromClasspath(String classPath) throws Exception {
        try {
            ClassPathResource resource = new ClassPathResource(classPath);
            try (InputStream inputStream = resource.getInputStream()) {
                CertificateFactory certificateFactory = CertificateFactory.getInstance("X509");
                Certificate certificate = certificateFactory.generateCertificate(inputStream);
                PublicKey publicKey = certificate.getPublicKey();
                log.info("成功从classpath读取证书: {}", classPath);
                return publicKey;
            }
        } catch (Exception e) {
            log.error("从classpath获取证书PublicKey失败: {}", classPath, e);
            throw e;
        }
    }

    /**
     * 从classpath获取私钥绝对路径
     */
    private static String getPfxPathFromClasspath(String classPath) throws Exception {
        try {
            ClassPathResource resource = new ClassPathResource(classPath);
            if (resource.isFile()) {
                // 如果资源是文件系统中的文件，则返回绝对路径
                String absolutePath = resource.getFile().getAbsolutePath();
                log.info("私钥路径: {}", absolutePath);
                return absolutePath;
            } else {
                // 如果资源不是文件系统中的文件（如JAR中的资源），则创建临时文件
                log.info("私钥在JAR包内，创建临时文件: {}", classPath);
                File tempFile = File.createTempFile("pfx_", ".tmp");
                tempFile.deleteOnExit();

                try (InputStream inputStream = resource.getInputStream();
                    FileOutputStream outputStream = new FileOutputStream(tempFile)) {
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }
                }

                String tempPath = tempFile.getAbsolutePath();
                log.info("临时私钥路径: {}", tempPath);
                return tempPath;
            }
        } catch (Exception e) {
            log.error("获取私钥路径失败: {}", classPath, e);
            throw e;
        }
    }

    /**
     * 将字节数组转换为十六进制字符串
     */
    public static String bytesToHex(byte[] bytes) {
        return FormatUtil.byte2Hex(bytes);
    }

    /**
     * 将十六进制字符串转换为字节数组
     */
    public static byte[] hexToBytes(String hex) {
        return FormatUtil.hex2Bytes(hex);
    }

    /**
     * 用于日志输出，预览部分字节内容
     */
    private static String previewBytes(byte[] bytes, int maxLength) {
        if (bytes == null) {
            return "null";
        }

        int previewLength = Math.min(bytes.length, maxLength);
        StringBuilder sb = new StringBuilder();
        sb.append("长度=").append(bytes.length).append(", 部分内容=");

        for (int i = 0; i < previewLength; i++) {
            String hex = Integer.toHexString(bytes[i] & 0xFF);
            if (hex.length() == 1) {
                sb.append('0');
            }
            sb.append(hex);
        }

        if (bytes.length > maxLength) {
            sb.append("...(省略)");
        }

        return sb.toString();
    }

    /**
     * 直接从classpath获取私钥对象
     * 该方法不依赖文件系统，可以从JAR包内读取私钥
     */
    private static java.security.PrivateKey getPrivateKeyFromClasspath(String classPath, String password)
        throws Exception {
        try {
            ClassPathResource resource = new ClassPathResource(classPath);
            try (InputStream inputStream = resource.getInputStream()) {
                byte[] pfxBytes = new byte[inputStream.available()];
                inputStream.read(pfxBytes);

                // 使用字节数组直接加载私钥
                java.security.KeyStore ks = java.security.KeyStore.getInstance("PKCS12");
                char[] charPassword = password.toCharArray();
                ks.load(new java.io.ByteArrayInputStream(pfxBytes), charPassword);

                java.util.Enumeration<String> aliasEnum = ks.aliases();
                String keyAlias = null;
                if (aliasEnum.hasMoreElements()) {
                    keyAlias = aliasEnum.nextElement();
                }

                java.security.PrivateKey privateKey = (java.security.PrivateKey) ks.getKey(keyAlias, charPassword);
                log.info("成功从classpath直接读取私钥: {}", classPath);
                return privateKey;
            }
        } catch (Exception e) {
            log.error("从classpath获取私钥对象失败: {}", classPath, e);
            throw e;
        }
    }
}