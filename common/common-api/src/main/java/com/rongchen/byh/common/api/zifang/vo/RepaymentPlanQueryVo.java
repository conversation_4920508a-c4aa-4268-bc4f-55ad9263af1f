package com.rongchen.byh.common.api.zifang.vo;

import lombok.Data;

import java.util.List;

/**
 * @ClassName RepaymentPlanQueryVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/9 15:06
 * @Version 1.0
 **/
@Data
public class RepaymentPlanQueryVo {

    /**
     * 响应码，数据类型为String，长度为4，此字段必填（Y表示必填），用于标识响应的状态码
     */
    private String responseCode;
    /**
     * 响应消息，数据类型为String，长度最大可为128，此字段非必填（N表示非必填），用于展示相应的提示消息内容
     */
    private String responseMsg;
    /**
     * 贷款编号，数据类型为String，长度为32，此字段必填（Y表示必填），用于唯一标识一笔贷款
     */
    private String loanNo;

    List<RepaymentPlanQueryPkgVo> pkgList;

    List<RepaymentPlanQueryPrivilegeVo> privilegeList;
}
