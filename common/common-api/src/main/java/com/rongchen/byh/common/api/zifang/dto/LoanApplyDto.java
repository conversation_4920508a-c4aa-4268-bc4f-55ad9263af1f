package com.rongchen.byh.common.api.zifang.dto;


import lombok.Data;

@Data
public class LoanApplyDto {

    /**
     * 贷款编号
     */
    private String loanNo;
    /**
     * 身份证
     */
    private String idNo;
    /**
     * 交易流水号
     */
    private String serialNo;
    /**
     * 授信流水号
     */
    private String creditNo;
    /**
     * 申请金额
     */
    private String applyAmount;
    /**
     * 申请期限
     */
    private String applyTerm;
    /**
     * 期限类型
     * 01	日
     * 02	月
     * 03	年
     */
    private String termType;
    /**
     * 借款用途
     * DEC	装修
     * EDU	教育
     * HEA	家用电器
     * MAR	婚庆
     * REN	租房
     * MOD	手机数码
     * TRA	旅游
     * MED	医疗
     */
    private String applyUse;
    /**
     * 其他贷款用途
     */
//    private String otherPurpose;
    /**
     * 用户编号
     */
    private String userId;
    /**
     * 还款方式编码
     * R9930	按月付息,按季还本
     * R9929	按月付息,,到期还本
     * R9926	等额本息
     * R9927	等额本金
     * R9928	随借随还
     * R9925	等本等息
     */
    private String payWay;
    /**
     * 银行卡号
     */
    private String bankCardNum;
    /**
     * 银行预留手机号
     */
    private String bankPhone;
    /**
     * 银行编码
     */
    private String bankName;
    /**
     * 申请时间
     * yyyy-MM-dd HH:mm:ss
     */
    private String applyTime;
}
