package com.rongchen.byh.common.api.zifang.service.mayi.config;

public interface ZiFangConstant {

    // 初筛接口
    String applyCheckUrl = "/channel/{channel}/applyCheck";

    //2授信申请
    String creditApplyUrl = "/channel/{channel}/creditApply";
    // 授信审批查询
    String creditAuditUrl = "/channel/{channel}/getCreditAudit";




/*************************************************************************************************************/

    /**
     * 2.2.1.支持银行列表查询接口
     */
    String getUsableBankListUrl = "/channel/{channel}/getUsableBankList";

    /**
     * 2.2.3.绑卡获取验证码接口
     */
    String getBindBankSMSUrl = "/channel/{channel}/getBindBankSMS";

    /**
     * 2.2.4.绑卡验证码提交接口
     */
    String getVerifyBindBankSMSUrl = "/channel/{channel}/verifyBindBankSMS";

    /**
     * 2.2.5.绑卡结果查询
     * 流量方调用资金方绑卡结果，此接口为实时接口
     */
    String getQueryBindBankResult = "/channel/{channel}/queryBindBankResult";

    /**
     * 2.3.1.api还款计划查询接口 -合作方查询资金方还款计划
     * 接口由资金方提供，流量方调用。查询单笔贷款还款计划
     */
    String getApiRepaymentPlanQuery = "/channel/{channel}/getRepayPlan";


    /**
     * 2.4.1.还款试算接口
     * 接口由资金方提供，流量方调用。查询用户所有待还贷款信息列表
     */
    String getPreRepayApply = "/channel/{channel}/preRepayApply";

    /**
     * 2.4.3.还款计划查询接口-合作方查询资金方还款计划
     * 接口由资金方提供，流量方调用。查询单笔贷款还款计划
     */
    String getRepaymentPlanQuery = "/channel/{channel}/repaymentPlanQuery";

    /**
     * 2.4.5.用户还款请求接口
     * 接口由资金方提供，流量方调用。流量方向资金方发起还款请求。
     */
    String getRepaymentApply = "/channel/{channel}/repaymentApply";

    /**
     * 2.4.6.还款结果查询接口
     * 接口由资金方提供，流量方调用。流量方查询还款结果
     */
    String getRepaymentResult = "/channel/{channel}/repaymentResult";

    /**
     * 2.4.16.获取回购后划扣账户信息接口
     * 接口由资金方提供，流量方调用。流量方向资金方获取回购后银行卡信息
     */
    String getBuybackBankInfo = "/channel/{channel}/buybackBankInfo";





/*************************************************************************************************************/

    /**
     * 2.8.4.赊销订单申请接口
     * 流量方调用资金方申请赊销订单
     */
    String getSaleApply = "/channel/{channel}/saleApply";

    /**
     * 2.4.3.api流程赊销订单获取接口
     * 流量方调用资金方查询赊销订单
     */
    String getApiSaleApply = "/channel/{channel}/getSaleRepayPlan";

    /**
     * 2.8.5.赊销订单结果查询接口
     * 流量方调用资金方查询赊销订单申请结果
     */
    String getSaleResult = "/channel/{channel}/saleResult";


    /**
     * 2.8.6.赊销合同查询
     * 放款成功后，流量方调用资金方进行已签协议查询，此接口为实时接口。
     */
    String getSaleSignedQuery = "/channel/{channel}/saleSignedQuery";


    /**
     * 2.8.7.赊销订单还款请求接口
     * 接口由资金方提供，流量方调用。流量方向资金方发起赊销订单还款请求
     */
    String getSaleRepayApply = "/channel/{channel}/saleRepayApply";


    /**
     * 2.8.8.赊销还款结果查询
     * 接口由资金方提供，流量方调用。流量方向资金方发起赊销订单还款请求
     */
    String getSaleRepayResult = "/channel/{channel}/saleRepayResult";


    /**
     * 2.8.10.赊销还款计划查询接口-合作方查询资金方赊销还款计划
     * 接口由资金方提供，流量方调用。查询单笔赊销还款计划
     */
    String getSaleRepayPlan = "/channel/{channel}/getSaleRepayPlan";


    /**
     * 2.8.11.赊销还款退费接口
     * 接口由资金方提供，流量方调用。流量方向资金方赊销还款退费申请。
     */
    String getSaleRepayReturnFee = "/channel/{channel}/saleRepayReturnFee";


    /**
     * 2.8.12.赊销还款退费结果查询
     * 接口由资金方提供，流量方调用。流量方向资金方查询赊销还款退费结果。
     */
    String getSaleRefundResult = "/channel/{channel}/saleRefundResult";


    /**
     * 2.8.19.H5页面获取接口
     * 接口由资金方提供，流量方调用。流量方向资金方获取相关H5页面链接
     */
    String getH5Url = "/channel/{channel}/getH5Url";







/*************************************************************************************************************/

    /**
     * 2.3.1.用信信息传输接口
     */
    String loanApplyUrl = "/channel/{channel}/loanApply";

    /**
     * 2.3.2.用信结果查询接口
     */
    String useLoanQueryUrl = "/channel/{channel}/useLoanQuery";

    /**
     * 2.3.5.借款试算接口
     */
    String repayPlanCalcUrl = "/channel/{channel}/repayPlanCalc";

/*************************************************************************************************************/

    /**
     * 2.5.1.合同列表查询接口
     */
    String contractListQueryUrl = "/channel/{channel}/contractListQuery";

    /**
     * 2.5.3.已签合同查询
     */
    String contractSignedQueryUrl = "/channel/{channel}/contractSignedQuery";

    /**
     * 2.5.4.结清证明申请
     */
    String settlementApplyUrl = "/channel/{channel}/settlementApply";

    /**
     * 2.5.5.结清证明下载
     */
    String settlementDownloadUrl = "/channel/{channel}/settlementDownload";

    /**
     * 2.5.6.借款要素查询
     */
    String getLoanElementUrl = "/channel/{channel}/getLoanElement";


/***********************************************************************************************************/



}
