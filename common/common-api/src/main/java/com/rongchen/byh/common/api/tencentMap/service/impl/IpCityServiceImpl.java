package com.rongchen.byh.common.api.tencentMap.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.common.api.tencentMap.config.IpCityProperties;
import com.rongchen.byh.common.api.tencentMap.dto.IpCityDto;
import com.rongchen.byh.common.api.tencentMap.service.IpCityService;
import com.rongchen.byh.common.api.tencentMap.vo.IpCityVo;
import com.rongchen.byh.common.core.util.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/12/13 10:56:29
 */
@Service
@Slf4j
public class IpCityServiceImpl implements IpCityService {
    @Resource
    private IpCityProperties ipCityProperties;
    @Override
    public IpCityVo getIpCity(IpCityDto ipCityDto) {
        JSONObject param = buildParam(ipCityDto);
        String res = HttpUtil.get(ipCityProperties.getIpCityUrl(), param);
        log.info("腾讯地图根据ip获取城市请求参数：{}，响应结果：{}", param, res);
        return buildResult(res);
    }

    private JSONObject buildParam(IpCityDto ipCityDto) {
        JSONObject param = new JSONObject();
        param.put("key", ipCityProperties.getKey());
        param.put("ip", ipCityDto.getIp());
        return param;
    }

    private IpCityVo buildResult(String res) {
        IpCityVo ipCityVo = new IpCityVo();
        JSONObject data = JSONObject.parseObject(res);
        ipCityVo.setCode(-1);
        ipCityVo.setMsg(data.getString("message"));
        ipCityVo.setIp(data.getString("query"));
        if ("0".equals(data.getString("status"))) {
            JSONObject result = data.getJSONObject("result");
            ipCityVo.setCode(0);
            ipCityVo.setIp(result.getString("ip"));
            ipCityVo.setCity(result.getJSONObject("ad_info").getString("city"));
        }
        return ipCityVo;
    }
}
