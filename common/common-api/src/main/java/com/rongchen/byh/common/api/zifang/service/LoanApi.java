package com.rongchen.byh.common.api.zifang.service;


import com.rongchen.byh.common.api.zifang.dto.LoanApplyDto;
import com.rongchen.byh.common.api.zifang.dto.RepayPlanCalcDto;
import com.rongchen.byh.common.api.zifang.dto.UseLoanQueryDto;
import com.rongchen.byh.common.api.zifang.vo.BaseVo;
import com.rongchen.byh.common.api.zifang.vo.CreditApplyVo;
import com.rongchen.byh.common.api.zifang.vo.RepayPlanCalcVo;
import com.rongchen.byh.common.api.zifang.vo.UseLoanQueryVo;
import com.rongchen.byh.common.core.object.ResponseResult;

/**
 * 借款相关接口
 */
public interface LoanApi {

    /**
     * 用信信息传输接口
     */
    ResponseResult<CreditApplyVo> loanApply(LoanApplyDto loanApplyDto);


    /**
     * 用信结果查询接口
     * @param useLoanQueryDto
     * @return
     */
    ResponseResult<UseLoanQueryVo> useLoanQuery(UseLoanQueryDto useLoanQueryDto);


    /**
     * 2.3.5.借款试算接口
     * @return
     */
    ResponseResult<RepayPlanCalcVo> repayPlanCalc(RepayPlanCalcDto repayPlanCalcDto);
}
