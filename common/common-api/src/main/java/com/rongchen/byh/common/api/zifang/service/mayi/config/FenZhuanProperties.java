package com.rongchen.byh.common.api.zifang.service.mayi.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 分转资方配置
 * @date 2025/3/5 16:14:28
 */
@Data
@ConfigurationProperties(prefix = "fenzhuan")
public class FenZhuanProperties {

    /**
     * 分转资方url
     */
    private String fenZhuanUrl;

    /**
     * 分转资方aeskey
     */
    private String fenZhuanAesKey;

    /**
     * 分转资方iv
     */
    private String fenZhuanIv;

    /**
     * 分转资方appid
     */
    private String fenZhuanAppid;

    /**
     * 分转资方版本
     */
    private String fenZhuanVersion;

    /**
     * 分转资方appkey
     */
    private String fenZhuanAppKey;

}
