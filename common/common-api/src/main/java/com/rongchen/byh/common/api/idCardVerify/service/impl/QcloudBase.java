package com.rongchen.byh.common.api.idCardVerify.service.impl;

import com.google.common.base.Charsets;
import com.google.common.hash.Hashing;
import com.rongchen.byh.common.core.util.HttpUtil;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

public class QcloudBase {

    protected final String APPID = "IDAAoO3M";
    protected final String SECRET = "FshEBgEqp3dYJmSmxeic1B4UdzCCui0MPGe9cvmoVVzGIVbApEU2dhXzLenXbE3g";
    protected final String VERSION = "1.0.0";

    protected String getToken() {

        String getToken = HttpUtil.get("https://kyc1.qcloud.com/api/oauth2/access_token?appId="+APPID+"&secret="+SECRET+"&grant_type=client_credential&version=1.0.0");
        return getToken;
    }

    protected String getTicket(String accessToken) {
        String getTicket = HttpUtil.get("https://kyc1.qcloud.com/api/oauth2/api_ticket?appId="+APPID+"&access_token="+accessToken+"&type=SIGN&version=1.0.0");
        return getTicket;
    }

    protected String getSign(String ticket,String nonce,String orderNo) {
        List<String> list = new ArrayList<>();
        list.add(APPID);
        list.add(orderNo);
        list.add(VERSION);
        list.add(ticket);

        list.add(nonce);
        list.removeAll(Collections.singleton(null));// remove null
        java.util.Collections.sort(list);
        StringBuilder sb = new StringBuilder();
        for (String str : list) {
            sb.append(str);
        }
        String res = Hashing.sha1().hashString(sb, Charsets.UTF_8).toString().toUpperCase();
        return res;
    }
}
