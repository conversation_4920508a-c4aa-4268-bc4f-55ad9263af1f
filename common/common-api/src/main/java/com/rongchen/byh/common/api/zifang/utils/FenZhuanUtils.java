package com.rongchen.byh.common.api.zifang.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * @ClassName FenZhuanUtils
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/1 12:25
 * @Version 1.0
 **/
@Slf4j
public class FenZhuanUtils {

    private static final int BIT_SIZE = 0x10;
    private static final int BIZ_ZERO = 0X00;
    private static char[][] charArrays = new char[256][];
    private static MessageDigest MD5;
    static {
        try {
            MD5 = MessageDigest.getInstance("MD5");
            int v;
            char[] ds;
            String temp;
            for (int i = 0; i < charArrays.length; i++) {
                ds = new char[2];
                v = i & 0xFF;
                temp = Integer.toHexString(v);
                if (v < BIT_SIZE) {
                    ds[0] = '0';
                    ds[1] = temp.charAt(0);
                } else {
                    ds[0] = temp.charAt(0);
                    ds[1] = temp.charAt(1);
                }
                charArrays[i] = ds;
            }
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
    }

    public static synchronized String getMd5(String msg) {
        return getMd5(msg.getBytes()).toUpperCase();
    }

    private static synchronized String getMd5(byte[] msg) {
        MD5.update(msg);
        return bytesToHexString(MD5.digest());
    }

    private static String bytesToHexString(byte[] src) {
        return bytesToHexString(src, 0, src.length);
    }

    private static String bytesToHexString(byte[] src, int posction, int length) {
        HexAppender helper = new HexAppender(src.length * 2);
        if (src == null || src.length <= BIZ_ZERO) {
            return null;
        }
        int v;
        int lengthR = src.length > length ? length : src.length;
        char[] temp;
        for (int i = posction; i < lengthR; i++) {
            v = src[i] & 0xFF;
            temp = charArrays[v];
            helper.append(temp[0], temp[1]);
        }
        return helper.toString();
    }

    private static class HexAppender {
        private int offerSet = 0;
        private char[] charData;

        public HexAppender(int size) {
            charData = new char[size];
        }

        public void append(char a, char b) {
            charData[offerSet++] = a;
            charData[offerSet++] = b;
        }

        @Override
        public String toString() {
            return new String(charData, 0, offerSet);
        }
    }

    public static JSONObject sort(JSONObject json0bject) {
        final ArrayList<String> keys = new ArrayList<>(json0bject.keySet());
        Collections.sort(keys);
        final JSONObject sortedJsonObject = new JSONObject(true);
        for (String key : keys) {
            final Object value = json0bject.get(key);
            if (value instanceof JSONObject) {
                sortedJsonObject.put(key, sort((JSONObject)value));
            } else if (value instanceof JSONArray) {
                final JSONArray valueJsonArray = (JSONArray)value;
                final JSONArray jsonArray = new JSONArray(valueJsonArray.size());
                valueJsonArray.forEach(o -> {
                    if (o instanceof JSONObject) {
                        jsonArray.add(sort((JSONObject)o));
                    } else {
                        jsonArray.add(o);
                    }
                });
                sortedJsonObject.put(key, jsonArray);
            } else {
                sortedJsonObject.put(key, value);
            }
        }
        return sortedJsonObject;
    }

    public static String sign(JSONObject jsonObject, String appKey) {
        StringBuilder resStr = new StringBuilder();
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            if (entry.getValue() == null || StringUtils.isBlank(entry.getValue().toString())) {
                continue;
            }
            resStr.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        resStr.append("key=" + appKey);
        return getMd5(resStr.toString());
    }

    public static String packMsg(String appId, TreeMap<String, Object> bodyMap) {
        TreeMap<String, Object> reqMap = new TreeMap<>();
        reqMap.put("appId", appId);
        reqMap.put("version", "1.0");
        reqMap.put("timestamp", System.currentTimeMillis());
        reqMap.put("signType", "MD5");
        reqMap.putAll(bodyMap);

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Object> entry : reqMap.entrySet()) {
            if (entry.getValue() == null || StringUtils.isBlank(entry.getValue().toString())) {
                continue;
            }
            sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        sb.append("key=").append("21801e2ea0052192a246b4c16f6aa9c1");
        log.info("待签数据:{}", sb);

        String sysSign = getMd5(sb.toString()).toUpperCase();
        reqMap.put("sign", sysSign);
        return JSONObject.toJSONString(reqMap);
    }

    public static String buildRequest(JSONObject param, String aesKey, String iv, String appId, String version, String appKey, String apiCode) {
        try {

            TreeMap<String, Object> reqMap = new TreeMap<>();
            reqMap.put("appId", appId);
            reqMap.put("apiCode", apiCode);
            reqMap.put("version", version);
            reqMap.put("timestamp", System.currentTimeMillis());
            reqMap.put("signType", "MD5");
            reqMap.putAll(param);

            StringBuilder sb = new StringBuilder();
            for (Map.Entry<String, Object> entry : reqMap.entrySet()) {
                if (entry.getValue() == null || StringUtils.isBlank(entry.getValue().toString())) {
                    continue;
                }
                sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
            sb.append("key=").append(appKey);
            log.info("待签数据:{}", sb);

            String sysSign = getMd5(sb.toString()).toUpperCase();
            reqMap.put("sign", sysSign);
            return JSONObject.toJSONString(reqMap);

            // String str = packMsg(appId, new TreeMap<>(sort(param)));
            //
            // JSONObject sendData = new JSONObject();
            // sendData.put("appId", appId);
            // sendData.put("version", version);
            // sendData.put("apiCode", apiCode);
            // sendData.put("signType", "MD5");
            // sendData.put("timestamp", System.currentTimeMillis());
            // ArrayList<String> keyList = new ArrayList<>(param.keySet());
            // if (iv.length() > 16) {
            // iv = iv.substring(0, 16);
            // }
            // sendData.putAll(param);
            // String sign = sign(sort(sendData), appKey);
            // sendData.put("sign", sign);
            // return sendData.toJSONString();
        } catch (Exception e) {
            log.info("加密失败", e);
        }
        return "";
    }
}
