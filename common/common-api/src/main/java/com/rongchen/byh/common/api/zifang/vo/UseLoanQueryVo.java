package com.rongchen.byh.common.api.zifang.vo;

import lombok.Data;

@Data
public class UseLoanQueryVo extends BaseVo{

    /**
     * 00	初始状态
     * 01	审核中
     * 02	待用户确认
     * 03	待放款
     * 04	放款失败
     * 05	放款中（无需用户确认或用户已确认，资金方通知流量方此状态）
     * 06	放款成功-起息
     * 07	放款失败
     * 08	订单不存在
     * 09	结清
     * 10	放款成功-到账
     * 用信结果
     */
    private String loanResult;

    /**
     * 用信结果描述
     */
    private String loanResultDesc;

    /**
     * 借款合同签约地址
     */
    private String agreementUrl;

    /**
     * 批复金额
     */
    private String approvedAmount;

    /**
     * 放款成功时间
     */
    private String loanTime;

    /**
     * 贷款到期日
     */
    private String loanMaturity;

    /**
     * 资方订单号
     */
    private String contractId;

    /**
     * 行方贷款合同号
     */
    private String fundOrderId;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 资金方编码
     */
    private String fundCode;
}
