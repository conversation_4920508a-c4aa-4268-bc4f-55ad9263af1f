package com.rongchen.byh.common.api.notice.service.impl;

import com.rongchen.byh.common.api.notice.config.NoticeSmsContent;
import com.rongchen.byh.common.api.notice.dto.DeductionFailedDto;
import com.rongchen.byh.common.api.notice.dto.NoticeSmsDto;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 扣款失败短信
 * @date 2024/12/12 16:52:40
 */
@Service
public class DeductionFailedNoticeService extends AbstractNoticeSmsService {
    @Override
    protected String getContent(NoticeSmsDto noticeSmsDto) {
        DeductionFailedDto smsDto = (DeductionFailedDto) noticeSmsDto;
        return smsDto.getUserName() + "##" + smsDto.getContractNum()
                + "##" + smsDto.getDeductionDate() + "##" + smsDto.getPeriodAmount() + "##" + smsDto.getRemainAmount();
    }

    @Override
    protected Integer getTemplateId() {
        return NoticeSmsContent.DEDUCATIONFAIL;
    }
}
