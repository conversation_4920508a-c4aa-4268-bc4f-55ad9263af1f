package com.rongchen.byh.common.api.riskControl.service;

import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.common.api.riskControl.dto.ApiCreditPreLoanAuditDto;
import com.rongchen.byh.common.api.riskControl.dto.CreditPreLoanAuditDto;
import com.rongchen.byh.common.api.riskControl.dto.PreLoanAuditAppDto;
import com.rongchen.byh.common.api.riskControl.dto.PreLoanAuditDto;
import com.rongchen.byh.common.api.riskControl.vo.PreLoanAuditVo;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 风控业务
 * @date 2024/12/11 14:12:27
 */
public interface RiskControlService {
    /**
     * 贷前审核事件接口（风控初筛接口）
     * @param preLoanAuditDto
     * @return
     */
    PreLoanAuditVo preLoanAudit(PreLoanAuditDto preLoanAuditDto);


    /**
     * 贷前审核事件接口（风控初筛接口  app版本）
     * @param auditAppDto
     * @return
     */
    PreLoanAuditVo preLoanAuditApp(PreLoanAuditAppDto auditAppDto);

    /**
     * 线下模式贷前审核事件接口（风控初筛接口 ）
     * @param preLoanAuditDto
     * @return
     */
    PreLoanAuditVo offlineH5PreLoanAudit(PreLoanAuditDto preLoanAuditDto);

    /**
     * 空中放款模式贷前审核事件接口（风控初筛接口 ）
     * @param preLoanAuditDto
     * @return
     */
    PreLoanAuditVo flyH5PreLoanAudit(PreLoanAuditDto preLoanAuditDto);

    /**
     * 央行征信风控
     * @param creditPreLoanAuditDto
     * @return
     */
    PreLoanAuditVo creditPreLoanAudit(CreditPreLoanAuditDto creditPreLoanAuditDto);

    /**
     * api模式央行征信风控
     * @param apiCreditPreLoanAuditDto
     * @return
     */
    PreLoanAuditVo apiCreditPreLoanAudit(ApiCreditPreLoanAuditDto apiCreditPreLoanAuditDto);

    /**
     * 查询授信结果
     * @param creditId
     * @return
     */
    PreLoanAuditVo queryCreditResult(String creditId);
}
