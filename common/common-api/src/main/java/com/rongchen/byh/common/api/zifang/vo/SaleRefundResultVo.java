package com.rongchen.byh.common.api.zifang.vo;

import lombok.Data;

/**
 * @ClassName SaleRefundResultVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/10 12:07
 * @Version 1.0
 **/
@Data
public class SaleRefundResultVo {

    /**
     * 必填，响应码，备注：用于标识退费相关操作返回的响应代码，不同代码对应不同处理情况。
     */
    private String responseCode;
    /**
     * 非必填，响应描述，备注：用于对响应码对应的情况做进一步文字说明。
     */
    private String responseMsg;
    /**
     * 必填，退费申请流水，备注：用于唯一标识一次退费申请操作，方便后续业务关联与查询等操作。
     */
    private String refundApplyNo;
    /**
     * 非必填，退费金额，备注：格式为17位整数2位小数，单位为元，代表此次退费涉及的金额数目（若有）。
     */
    private String refundAmt;
    /**
     * 必填，退款状态，备注：有SUCCESS（成功）、PROCESSING（处理中）、FAIL（失败）这些可选值，用于体现退款当前所处的状态情况。
     */
    private String status;
    /**
     * 非必填，退款结果描述，备注：退款失败时必填，用于返回失败原因，对退款失败情况做详细解释。
     */
    private String result;
    /**
     * 非必填，退款时间，备注：格式为yyyy-MM-dd HH:mm:ss，用于记录退款实际发生的时间（若有）。
     */
    private String refundSuccTime;

}
