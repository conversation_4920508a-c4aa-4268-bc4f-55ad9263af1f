package com.rongchen.byh.common.api.zifang.vo;

import lombok.Data;

/**
 * @ClassName SaleRepayResultVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/10 10:11
 * @Version 1.0
 **/
@Data
public class SaleRepayResultVo {

    /**
     * 必填，响应码，备注：用于标识还款相关操作返回的响应代码，例如E00004代表申请号不存在等不同含义。
     */
    private String responseCode;
    /**
     * 非必填，响应描述，备注：用于对响应码对应的情况做进一步说明。
     */
    private String responseMsg;
    /**
     * 必填，还款流水号，备注：用于唯一标识一次还款操作的流水记录。
     */
    private String repayApplyNo;
    /**
     * 必填，赊销订单号，备注：用于明确此次还款对应的赊销订单。
     */
    private String saleNo;
    /**
     * 必填，还款金额，备注：代表此次还款操作涉及的金额数量，单位为元。
     */
    private String amt;
    /**
     * 必填，还款状态，备注：有SUCCESS（成功）、REPAYING（还款中）、FAIL（失败）、WAITING（还款待确认）、COLSE（还款关闭）这些可选值，用于体现还款所处的状态情况。
     */
    private String status;
    /**
     * 非必填，还款结果描述，备注：还款失败时必填，用于返回失败原因。
     */
    private String result;
    /**
     * 必填，还款时间，备注：按格式yyyy-MM-dd HH:mm:ss记录还款实际发生的时间。
     */
    private String repayTime;
}
