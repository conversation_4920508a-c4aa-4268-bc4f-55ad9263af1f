package com.rongchen.byh.common.api.zifang.service;

import com.rongchen.byh.common.api.zifang.dto.BindBankSmsDto;
import com.rongchen.byh.common.api.zifang.dto.QueryBindBankResultDto;
import com.rongchen.byh.common.api.zifang.dto.UsableBankListDto;
import com.rongchen.byh.common.api.zifang.dto.VerifyBindBankSmsDto;
import com.rongchen.byh.common.api.zifang.vo.*;
import com.rongchen.byh.common.core.object.ResponseResult;

/**
 * @ClassName BindCardApi
 * @Description 绑卡相关接口
 * <AUTHOR>
 * @Date 2024/12/6 18:04
 * @Version 1.0
 **/
public interface BindCardApi {

    /**
     * 查询支持银行列表查询接口
     * 流量方调用资金方查询所支持的银行卡信息，此接口为实时接口
     * @date 2024/12/6 18:22
     *
     * @param bankListDto
     * @return com.rongchen.byh.common.core.object.ResponseResult<com.rongchen.byh.common.api.zifang.vo.UsableBankListVo>
     */
    ResponseResult<UsableBankListVo> getUsableBankList(UsableBankListDto bankListDto);

    /**
     * 2.2.3.绑卡获取验证码接口
     * 流量方调用资金方进行绑卡，此接口接收到请求会异步发送短信
     * @date 2024/12/6 18:22
     *
     * @param bindBankSmsDto
     * @return com.rongchen.byh.common.core.object.ResponseResult<com.rongchen.byh.common.api.zifang.vo.UsableBankListVo>
     */
    ResponseResult<BindBankSmsVo> getBindBankSMS(BindBankSmsDto bindBankSmsDto);

    /**
     * 2.2.4.绑卡验证码提交接口
     * 流量方调用资金方进行绑卡验证码提交，此接口为异步接口，是否绑卡成功需要查询绑卡结果
     * @date 2024/12/6 18:22
     *
     * @param bindBankSmsDto
     * @return com.rongchen.byh.common.core.object.ResponseResult<com.rongchen.byh.common.api.zifang.vo.UsableBankListVo>
     */
    ResponseResult<VerifyBindBankSmsVo> getVerifyBindBankSMSUrl(VerifyBindBankSmsDto bindBankSmsDto);

    /**
     * 2.2.5.绑卡结果查询
     * 流量方调用资金方进行绑卡验证码提交，此接口为异步接口，是否绑卡成功需要查询绑卡结果
     * @date 2024/12/6 18:22
     *
     * @param bindBankSmsDto
     * @return com.rongchen.byh.common.core.object.ResponseResult<com.rongchen.byh.common.api.zifang.vo.UsableBankListVo>
     */
    ResponseResult<QueryBindBankResultVo> getQueryBindBankResult(QueryBindBankResultDto bindBankSmsDto);
}
