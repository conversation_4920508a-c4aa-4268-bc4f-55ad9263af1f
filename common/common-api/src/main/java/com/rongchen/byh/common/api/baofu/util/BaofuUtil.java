package com.rongchen.byh.common.api.baofu.util;

import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.common.api.baofu.constant.BaofuConstant;
import com.rongchen.byh.common.api.baofu.dto.req.BaofuBaseReqDto;
import com.rongchen.byh.common.api.baofu.vo.req.RiskItemReqVo;
import com.rongchen.byh.common.api.baofu.vo.rsp.BaoFUBankCardProtocolRspVo;
import com.rongchen.byh.common.core.util.HttpUtil;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import lombok.extern.slf4j.Slf4j;

/**
 * 宝付工具类
 */
@Slf4j
public class BaofuUtil {

    /**
     * 生成签名字符串
     */
    public static String generateSignContent(Map<String, String> params) {
        // 按照key排序
        TreeMap<String, String> sortedMap = new TreeMap<>(params);
        // 移除签名字段
        sortedMap.remove("signature");

        // 构建待签名字符串
        StringBuilder sb = new StringBuilder();
        boolean first = true;
        for (Map.Entry<String, String> entry : sortedMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();

            // 值为空的不参与签名
            if (value == null || value.isEmpty()) {
                continue;
            }

            if (first) {
                first = false;
            } else {
                sb.append("&");
            }

            sb.append(key).append("=").append(value);
        }

        return sb.toString();
    }

    /**
     * 生成SHA-1摘要
     */
    public static String sha1(String input) throws Exception {
        MessageDigest md = MessageDigest.getInstance("SHA-1");
        byte[] digest = md.digest(input.getBytes(StandardCharsets.UTF_8));
        return BaofuEncryptUtil.bytesToHex(digest);
    }

    /**
     * 生成签名
     */
    public static String generateSignature(Map<String, String> params, boolean isTest) throws Exception {
        String content = generateSignContent(params);
        String sha1Hex = sha1(content);
        return BaofuEncryptUtil.sign(sha1Hex, isTest);
    }

    /**
     * 验证签名
     */
    public static boolean verifySignature(Map<String, String> params, boolean isTest) throws Exception {
        String signature = params.get("signature");
        String content = generateSignContent(params);
        String sha1Hex = sha1(content);
        return BaofuEncryptUtil.verify(sha1Hex, signature, isTest);
    }

    /**
     * 获取当前时间字符串
     */
    public static String getCurrentTimeString() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(new Date());
    }

    /**
     * 对象转Map
     */
    public static TreeMap<String, String> objectToMap(Object obj) {
        JSONObject jsonObject = (JSONObject) JSONObject.toJSON(obj);
        TreeMap<String, String> map = new TreeMap<>();
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            if (entry.getValue() != null) {
                map.put(entry.getKey(), entry.getValue().toString());
            }
        }
        return map;
    }

    /**
     * 发送请求
     */
    public static String sendRequest(BaofuBaseReqDto request, boolean isTest) throws Exception {
        String url = isTest ? BaofuConstant.TEST_URL : BaofuConstant.PROD_URL;

        // 对象转Map
        TreeMap<String, String> params = objectToMap(request);

        // 生成签名
        String signature = generateSignature(params, isTest);
        params.put("signature", signature);

        return sendRequest(params, isTest);
    }

    /**
     * 发送请求（直接使用参数Map）
     */
    public static String sendRequest(TreeMap<String, String> params, boolean isTest) throws Exception {
        String url = isTest ? BaofuConstant.TEST_URL : BaofuConstant.PROD_URL;

        // 转换为key1=value1&key2=value2格式
        StringBuilder sb = new StringBuilder();
        boolean first = true;
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (entry.getValue() == null || entry.getValue().isEmpty()) {
                continue;
            }

            if (first) {
                first = false;
            } else {
                sb.append("&");
            }

            sb.append(entry.getKey()).append("=").append(entry.getValue());
        }

        // 将TreeMap<String, String>转换为JSONObject
        JSONObject jsonParams = new JSONObject();
        jsonParams.putAll(params);

        // 发送请求
        log.info("宝付请求URL: {}", url);
        log.info("宝付请求参数: {}", sb);
        String response = HttpUtil.postFrom(url, jsonParams);
        log.info("宝付响应结果: {}", response);

        return response;
    }

    /**
     * 解析响应字符串为Map
     */
    public static Map<String, String> parseResponseToMap(String responseStr) {
        TreeMap<String, String> resultMap = new TreeMap<>();
        String[] pairs = responseStr.split("&");
        for (String pair : pairs) {
            String[] keyValue = pair.split("=", 2);
            if (keyValue.length == 2) {
                resultMap.put(keyValue[0], keyValue[1]);
            }
        }
        return resultMap;
    }

    /**
     * 转换风控参数为JSON字符串
     *
     * @param riskItem 风控参数对象
     * @return JSON字符串
     */
    public static String convertRiskItemToJson(RiskItemReqVo riskItem) {
        if (riskItem == null) {
            return "{}";
        }

        // 移除值为空的属性
        JSONObject jsonObject = (JSONObject) JSONObject.toJSON(riskItem);
        JSONObject result = new JSONObject();
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            if (entry.getValue() != null && !entry.getValue().toString().isEmpty()) {
                result.put(entry.getKey(), entry.getValue());
            }
        }

        return result.toJSONString();
    }


    /**
     * 解析协议列表
     *
     * @param protocols 协议列表字符串
     * @return 协议列表对象
     */
    public static List<BaoFUBankCardProtocolRspVo> parseProtocols(String protocols) {
        List<BaoFUBankCardProtocolRspVo> result = new ArrayList<>();
        if (protocols == null || protocols.isEmpty()) {
            return result;
        }

        String[] protocolArray = protocols.split(";");
        for (String protocol : protocolArray) {
            String[] items = protocol.split("\\|");
            if (items.length >= 5) {
                BaoFUBankCardProtocolRspVo vo = new BaoFUBankCardProtocolRspVo();
                vo.setProtocolNo(items[0]);
                vo.setUserId(items[1]);
                vo.setBankCardNo(items[2]);
                vo.setBankCode(items[3]);
                vo.setBankName(items[4]);
                result.add(vo);
            }
        }

        return result;
    }

}