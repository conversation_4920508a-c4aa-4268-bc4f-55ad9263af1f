package com.rongchen.byh.common.api.zifang.dto;

import lombok.Data;

/**
 * @ClassName RepaymentApplyDetailDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/9 15:59
 * @Version 1.0
 **/
@Data
public class RepaymentApplyDetailDto {

    /**
     * 还款期数，数据类型为String，长度为2，此字段必填（Y表示必填），用于表示单期的还款期数情况。
     */
    private String repayterm;
    /**
     * 当期还款本金，数据类型为String，格式为17位整数2位小数，单位为元，此字段必填（Y表示必填），代表当期需要偿还的本金金额数目。
     */
    private String printAmt;
    /**
     * 当期还款利息，数据类型为String，格式为17位整数2位小数，单位为元，此字段必填（Y表示必填），代表当期需要偿还的利息金额数目。
     */
    private String intAmt;
    /**
     * 当期还款罚息，数据类型为String，格式为17位整数2位小数，单位为元，此字段非必填（N表示非必填），代表当期需要偿还的罚息金额数目（若有）。
     */
    private String forfeitAmt;
    /**
     * 当期还款融担费，数据类型为String，格式为17位整数2位小数，单位为元，此字段非必填（N表示非必填），代表当期需要偿还的融担费金额数目（若有）。
     */
    private String guarantorFee;
    /**
     * 当期还款服务费，数据类型为String，格式为17位整数2位小数，单位为元，此字段非必填（N表示非必填），代表当期需要偿还的服务费金额数目（若有）。
     */
    private String serviceAmt;
    /**
     * 违约金，数据类型为String，格式为17位整数2位小数，单位为元，此字段非必填（N表示非必填），代表当期需要支付的违约金金额数目（若有）。
     */
    private String breachFee;
}
