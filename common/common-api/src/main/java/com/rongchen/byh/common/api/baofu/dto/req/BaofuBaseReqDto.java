package com.rongchen.byh.common.api.baofu.dto.req;

import lombok.Data;

/**
 * 宝付基础请求参数
 */
@Data
public class BaofuBaseReqDto {

    /**
     * 报文发送日期时间
     */
    private String send_time;

    /**
     * 报文流水号
     */
    private String msg_id;

    /**
     * 报文编号/版本号
     */
    private String version;

    /**
     * 终端号
     */
    private String terminal_id;

    /**
     * 交易类型
     */
    private String txn_type;

    /**
     * 商户号
     */
    private String member_id;

    /**
     * 数字信封
     */
    private String dgtl_envlp;

    /**
     * 签名域
     */
    private String signature;

    /**
     * 商户保留域1
     */
    private String req_reserved1;

    /**
     * 商户保留域2
     */
    private String req_reserved2;

    /**
     * 系统保留域1
     */
    private String additional_info1;

    /**
     * 系统保留域2
     */
    private String additional_info2;
}