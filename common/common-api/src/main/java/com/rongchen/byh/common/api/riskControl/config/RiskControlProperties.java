package com.rongchen.byh.common.api.riskControl.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 属性配置
 * @date 2024/12/11 14:17:30
 */
@Data
@ConfigurationProperties(prefix = "risk.control")
public class RiskControlProperties {
    private String preLoanAuditUrl;

    private String aesKey;

    private String rsaPrivateKey;

    private String sourceId;

    private String channel;

    private Integer productCode;

    private String crmPushUrl;

    private String crmPushSource;

    private String crmPushSecret;

    private String appAesKey;

    private String appRsaPrivateKey;

    private String appSourceId;

    private String appChannel;

    private String appProductCode;

    private String offlineH5AesKey;

    private String offlineH5RsaPrivateKey;

    private String offlineH5SourceId;

    private String offlineH5Channel;

    private Integer offlineH5ProductCode;

    private String flyH5AesKey;

    private String flyH5RsaPrivateKey;

    private String flyH5SourceId;

    private String flyH5Channel;

    private Integer flyH5ProductCode;

    private String creditSourceId;

    private String creditAesKey;

    private String creditChannel;

    private String creditProductCode;

    private String creditRsaPrivateKey;

    private String apiCreditSourceId;

    private String apiCreditAesKey;

    private String apiCreditChannel;

    private Integer apiCreditProductCode;

    private String apiCreditRsaPrivateKey;

    private String apiCreditCallbackUrl;

    private String preLoanQueryAuditUrl;
}
