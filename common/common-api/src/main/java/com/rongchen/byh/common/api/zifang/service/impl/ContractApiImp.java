//package com.rongchen.byh.common.api.zifang.service.impl;
//
//
//import com.alibaba.fastjson.JSONObject;
//import com.rongchen.byh.common.api.zifang.config.ZiFangConstant;
//import com.rongchen.byh.common.api.zifang.dto.ContractListDto;
//import com.rongchen.byh.common.api.zifang.dto.ContractSignedQueryDto;
//import com.rongchen.byh.common.api.zifang.dto.LoanElementDto;
//import com.rongchen.byh.common.api.zifang.service.ContractApi;
//import com.rongchen.byh.common.api.zifang.vo.ApplyCheckVo;
//import com.rongchen.byh.common.api.zifang.vo.ContractListVo;
//import com.rongchen.byh.common.api.zifang.vo.ContractSignedQueryVo;
//import com.rongchen.byh.common.api.zifang.vo.LoanElementVo;
//import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
//import com.rongchen.byh.common.core.object.ResponseResult;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//
//@Service
//@Slf4j
//public class ContractApiImp extends AbstractApi implements ContractApi {
//
//    @Override
//    public ResponseResult<ContractListVo> contractListQuery(ContractListDto contractListDto) {
//        String jsonString = JSONObject.toJSONString(contractListDto);
//        JSONObject params = JSONObject.parseObject(jsonString);
//        params.put("reqSysCode", ziFangProperties.getReqSysCode());
//        params.put("productCode", ziFangProperties.getProductCode());
//        try {
//            JSONObject map = this.sendRequest(params, ZiFangConstant.contractListQueryUrl);
//            ContractListVo vo = map.toJavaObject(ContractListVo.class);
//            return ResponseResult.success(vo);
//        } catch (Exception e) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL,e.getMessage());
//        }
//    }
//
//    @Override
//    public ResponseResult<ContractSignedQueryVo> contractSignedQuery(ContractSignedQueryDto contractSignedQueryDto) {
//        String jsonString = JSONObject.toJSONString(contractSignedQueryDto);
//        JSONObject params = JSONObject.parseObject(jsonString);
//        params.put("reqSysCode", ziFangProperties.getReqSysCode());
//        params.put("productCode", ziFangProperties.getProductCode());
//        try {
//            JSONObject map = this.sendRequest(params, ZiFangConstant.contractSignedQueryUrl);
//            ContractSignedQueryVo vo = map.toJavaObject(ContractSignedQueryVo.class);
//            return ResponseResult.success(vo);
//        } catch (Exception e) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL,e.getMessage());
//        }
//    }
//
//    @Override
//    public ResponseResult<LoanElementVo> loanElement(LoanElementDto loanElementDto) {
//        JSONObject params = new JSONObject();
//        params.put("reqSysCode", ziFangProperties.getReqSysCode());
//        params.put("productCode", ziFangProperties.getProductCode());
//        params.put("creditNo", loanElementDto.getCreditNo());
//        params.put("userId", loanElementDto.getUserId());
//        try {
//            JSONObject map = this.sendRequest(params, ZiFangConstant.getLoanElementUrl);
//            LoanElementVo vo = map.toJavaObject(LoanElementVo.class);
//            return ResponseResult.success(vo);
//        } catch (Exception e) {
//            return ResponseResult.error(ErrorCodeEnum.FAIL,e.getMessage());
//        }
//    }
//}
