package com.rongchen.byh.common.api.yunxi.service;

import com.rongchen.byh.common.api.yunxi.dto.YunXiApiRspDto;
import com.rongchen.byh.common.api.yunxi.dto.req.BenefitOrderSubmitReqDto;
import com.rongchen.byh.common.api.yunxi.dto.req.BenefitOrderQueryReqDto;
import com.rongchen.byh.common.api.yunxi.dto.req.BenefitOrderRefundReqDto;
import com.rongchen.byh.common.api.yunxi.dto.req.BenefitOrderRepaymentReqDto;
import com.rongchen.byh.common.api.yunxi.dto.req.BenefitOrderRefundNotifyReqDto;
import com.rongchen.byh.common.api.yunxi.dto.req.BenefitGatewayReqDto;
import com.rongchen.byh.common.api.yunxi.dto.req.BenefitOrderSubmitQkReqDto;
import com.rongchen.byh.common.api.yunxi.vo.YunXiOrderDetailVo;

/**
 * 云樨API服务接口
 */
public interface YunXiApiService {

    /**
     * 获取权益行权地址
     *
     * @param request 请求参数
     * @return 响应结果，data中包含url字段（AES加密的权益行权地址）
     */
    YunXiApiRspDto<String> getBenefitGatewayUrl(BenefitGatewayReqDto request);

    /**
     * 权益订单购买提交-qk
     *
     * @param request 请求参数
     * @return 响应结果
     */
    YunXiApiRspDto<YunXiOrderDetailVo> submitBenefitOrderQk(BenefitOrderSubmitQkReqDto request);

    /**
     * 权益订单购买提交
     *
     * @param request 请求参数
     * @return 响应结果
     */
    YunXiApiRspDto<YunXiOrderDetailVo> submitBenefitOrder(BenefitOrderSubmitReqDto request);

    /**
     * 权益订单结果查询
     *
     * @param request 请求参数
     * @return 响应结果
     */
    YunXiApiRspDto<YunXiOrderDetailVo> queryBenefitOrder(BenefitOrderQueryReqDto request);

    /**
     * 权益订单退款申请
     *
     * @param request 请求参数
     * @return 响应结果
     */
    YunXiApiRspDto<YunXiOrderDetailVo> refundBenefitOrder(BenefitOrderRefundReqDto request);

    /**
     * 权益订单还款通知
     *
     * @param request 请求参数
     * @return 响应结果
     */
    YunXiApiRspDto<Void> notifyBenefitOrderRepayment(BenefitOrderRepaymentReqDto request);

    /**
     * 权益订单退款通知
     *
     * @param request 请求参数
     * @return 响应结果
     */
    YunXiApiRspDto<YunXiOrderDetailVo> notifyBenefitOrderRefund(BenefitOrderRefundNotifyReqDto request);

    /**
     * 加密用户手机号
     *
     * @param mobile 用户手机号
     * @return 加密后的手机号
     */
    String encryptMobile(String mobile);
    /**
     * 解密AES
     */
    String decryptAesStr(String content);
}