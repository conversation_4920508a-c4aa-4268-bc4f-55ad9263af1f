package com.rongchen.byh.common.api.zifang.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName VerifyBindBankSmsVo
 * @Description 绑卡验证码提交
 * <AUTHOR>
 * @Date 2024/12/7 0:49
 * @Version 1.0
 **/
@Data
public class VerifyBindBankSmsVo {

    /**
     *响应码
     */
    @Schema(description = "响应码 0000成功")
    private String responseCode;

    /**
     *备注
     */
    @Schema(description = "备注")
    private String responseMsg;

    /**
     *支付渠道
     */
    @Schema(description = "支付渠道")
    private String channel;

    /**
     *共享协议号
     */
    @Schema(description = "共享协议号")
    private String agreementNumber;

    /**
     *是否需要发送验证码
     */
    @Schema(description = "是否需要发送验证码")
    private String needSmsCode;

    /**
     *短信验证码流水号
     */
    @Schema(description = "短信验证码流水号")
    private String messageNo;

    /**
     * 状态
     * VALID：成功
     * FREEZE：处理中/
     * 失败
     */
    @Schema(description = "状态")
    private String bindStatus;

    /**
     * 平台绑卡 id
     * 成功返回，入库供
     * 借款时选
     */
    @Schema(description = "状态")
    private String bankCardBindId;

}
