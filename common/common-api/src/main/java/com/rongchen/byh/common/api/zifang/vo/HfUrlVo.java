package com.rongchen.byh.common.api.zifang.vo;

import lombok.Data;

/**
 * @ClassName HfUrlVo
 * @Description H5页面获取 响应参数
 * <AUTHOR>
 * @Date 2024/12/10 12:17
 * @Version 1.0
 **/
@Data
public class HfUrlVo {
    /**
     * 必填，响应码，备注：用于标识相关操作返回的结果代码，取值如0000表示成功，9998表示已结清，不同代码对应不同业务情况。
     */
    private String responseCode;
    /**
     * 非必填，响应描述，备注：用于对响应码对应的具体情况做更详细的文字解释说明，辅助理解业务处理结果。
     */
    private String responseMsg;
    /**
     * 非必填，页面url，备注：用于指定相关的页面链接地址（若有需要），比如在一些需要跳转页面的业务场景中使用。
     */
    private String h5Url;
    /**
     * 非必填，用信结果，备注：用于体现用信相关操作最终的结果情况（若有涉及用信业务场景）。
     */
    private String loanResult;
}
