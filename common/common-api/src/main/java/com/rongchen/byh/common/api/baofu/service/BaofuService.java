package com.rongchen.byh.common.api.baofu.service;

import com.rongchen.byh.common.api.baofu.dto.rsp.ConfirmBindCardRspDto;
import com.rongchen.byh.common.api.baofu.dto.rsp.PreBindCardRspDto;
import com.rongchen.byh.common.api.baofu.dto.rsp.QueryBindCardRspDto;
import com.rongchen.byh.common.api.baofu.vo.req.BankCardBindReqVo;
import com.rongchen.byh.common.api.baofu.vo.req.BankCardConfirmReqVo;
import com.rongchen.byh.common.api.baofu.vo.req.DirectPayReqVo;
import com.rongchen.byh.common.api.baofu.vo.rsp.DirectPayRspVo;
import com.rongchen.byh.common.api.baofu.vo.req.QueryPaymentReqVo;
import com.rongchen.byh.common.api.baofu.vo.rsp.QueryPaymentRspVo;

/**
 * 宝付支付服务接口
 */
public interface BaofuService {

    /**
     * 预绑定银行卡
     *
     * @param bindVO 绑卡信息
     * @return 预绑卡响应
     */
    PreBindCardRspDto preBindCard(BankCardBindReqVo bindVO) throws Exception;

    /**
     * 确认绑定银行卡
     *
     * @param confirmVO 确认绑卡信息
     * @return 确认绑卡响应
     */
    ConfirmBindCardRspDto confirmBindCard(BankCardConfirmReqVo confirmVO) throws Exception;

    /**
     * 查询绑卡结果
     *
     * @param userId     用户ID
     * @param bankCardNo 银行卡号
     * @return 查询绑卡结果响应
     */
    QueryBindCardRspDto queryBindCard(String userId, String bankCardNo) throws Exception;

    /**
     * 直接支付
     *
     * @param payReqVo 支付信息
     * @return 支付响应
     */
    DirectPayRspVo directPay(DirectPayReqVo payReqVo) throws Exception;

    /**
     * 查询支付结果
     *
     * @param queryPaymentReqVo 支付查询请求信息
     * @return 支付查询响应
     */
    QueryPaymentRspVo queryPayment(QueryPaymentReqVo queryPaymentReqVo) throws Exception;
}
