package com.rongchen.byh.common.api.baofu.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * 金额转换工具类
 * 
 * <p>
 * 用于金额的元、分之间的转换
 * </p>
 * <p>
 * 单位说明：
 * </p>
 * <ul>
 * <li>元：如 10.65 元</li>
 * <li>分：如 1065 分</li>
 * </ul>
 * 
 * <AUTHOR>
 */
public class AmountUtil {

    /** 1元 = 100分 */
    private static final int CENT_FACTOR = 100;

    /** 默认金额格式，保留两位小数 */
    private static final DecimalFormat AMOUNT_FORMAT = new DecimalFormat("0.00");

    /**
     * 分转元，返回BigDecimal
     *
     * @param cent 分
     * @return 元（BigDecimal）
     */
    public static BigDecimal centToYuan(long cent) {
        return BigDecimal.valueOf(cent).divide(BigDecimal.valueOf(CENT_FACTOR), 2, RoundingMode.HALF_UP);
    }

    /**
     * 分转元，返回BigDecimal
     *
     * @param cent 分（字符串格式）
     * @return 元（BigDecimal）
     */
    public static BigDecimal centToYuan(String cent) {
        if (cent == null || cent.isEmpty()) {
            return BigDecimal.ZERO;
        }
        return centToYuan(Long.parseLong(cent));
    }

    /**
     * 分转元，返回字符串，保留两位小数
     *
     * @param cent 分
     * @return 元（字符串，如"10.65"）
     */
    public static String centToYuanStr(long cent) {
        return AMOUNT_FORMAT.format(centToYuan(cent));
    }

    /**
     * 分转元，返回字符串，保留两位小数
     *
     * @param cent 分（字符串格式）
     * @return 元（字符串，如"10.65"）
     */
    public static String centToYuanStr(String cent) {
        if (cent == null || cent.isEmpty()) {
            return "0.00";
        }
        return centToYuanStr(Long.parseLong(cent));
    }

    /**
     * 元转分，返回长整型
     *
     * @param yuan 元（BigDecimal）
     * @return 分（长整型）
     */
    public static long yuanToCent(BigDecimal yuan) {
        if (yuan == null) {
            return 0L;
        }
        return yuan.multiply(BigDecimal.valueOf(CENT_FACTOR)).longValue();
    }

    /**
     * 元转分，返回长整型
     *
     * @param yuan 元（字符串格式，如"10.65"）
     * @return 分（长整型）
     */
    public static long yuanToCent(String yuan) {
        if (yuan == null || yuan.isEmpty()) {
            return 0L;
        }
        return yuanToCent(new BigDecimal(yuan));
    }

    /**
     * 元转分，返回字符串
     *
     * @param yuan 元（BigDecimal）
     * @return 分（字符串）
     */
    public static String yuanToCentStr(int yuan) {
        return String.valueOf(yuanToCent(new BigDecimal(yuan)));
    }

    /**
     * 元转分，返回字符串
     *
     * @param yuan 元（字符串格式，如"10.65"）
     * @return 分（字符串）
     */
    public static String yuanToCentStr(String yuan) {
        return String.valueOf(yuanToCent(yuan));
    }

    /**
     * 格式化金额（元），保留两位小数
     *
     * @param yuan 元（BigDecimal）
     * @return 格式化后的金额字符串
     */
    public static String formatYuan(BigDecimal yuan) {
        if (yuan == null) {
            return "0.00";
        }
        return AMOUNT_FORMAT.format(yuan);
    }

    /**
     * 格式化金额（元），保留两位小数
     *
     * @param yuan 元（字符串格式）
     * @return 格式化后的金额字符串
     */
    public static String formatYuan(String yuan) {
        if (yuan == null || yuan.isEmpty()) {
            return "0.00";
        }
        try {
            return formatYuan(new BigDecimal(yuan));
        } catch (NumberFormatException e) {
            return "0.00";
        }
    }

    /**
     * 验证金额格式是否正确（最多两位小数）
     *
     * @param amount 金额字符串
     * @return 是否符合金额格式
     */
    public static boolean isValidAmount(String amount) {
        if (amount == null || amount.isEmpty()) {
            return false;
        }
        return amount.matches("^\\d+(\\.\\d{1,2})?$");
    }
}