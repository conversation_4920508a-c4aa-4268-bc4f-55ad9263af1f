package com.rongchen.byh.common.api.yunxi.dto.req;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

/**
 * 权益订单购买提交-qk请求
 * API Code: AP109
 */
@Data
public class BenefitOrderSubmitQkReqDto extends YunXiBashReqDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * API编码
     */
    public static final String API_CODE = "AP109";

    /**
     * 权益券包号,由权益平台提供
     */
    private String couponPackageId;

    /**
     * 合作方用户编号，须保证唯一性
     */
    private String openId;

    /**
     * 合作方用户手机号（AES加密）
     */
    private String userMobile;

    /**
     * 支付方式
     * BAOFU_PROXY_PAY-宝付
     * SUNING_PROXY_PAY-苏宁
     */
    private String payWay;

    /**
     * 支付流水号
     */
    private String paymentNo;

    /**
     * 合作方订单号
     */
    private String externalOrderNum;

    /**
     * 订单金额，单位元
     */
    private BigDecimal orderAmount;

    /**
     * 订单期数
     */
    private Integer orderPeriods;

   
}