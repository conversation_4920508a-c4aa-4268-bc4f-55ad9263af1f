package com.rongchen.byh.common.api.zifang.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName VerifyBindBankSmsDto
 * @Description 绑卡验证码提交
 * <AUTHOR>
 * @Date 2024/12/7 0:44
 * @Version 1.0
 **/
@Data
public class VerifyBindBankSmsDto {

    /**
     *进件单号
     */
    @Schema(description = "进件单号" , hidden = true)
    private String creditNo;

    @Schema(description = "代扣通道")
    private String payChannel;

    /**
     *流水号
     */
    @Schema(hidden = true)
    private String serialNo;

    /**
     *场景
     */
    @Schema(hidden = true)
    private String scene;

    /**
     *贷款编号
     * 当scene=03 该字段必填
     */
    @Schema(hidden = true)
    private String loanNo;

    /**
     *验证码
     *
     */
    @Schema(description = "验证码")
    private String phoneCode;

    /**
     *短信验证码流水号
     */
    @Schema(description = "短信验证码流水号")
    private String messageNo;

    /**
     *银行卡号
     */
    @Schema(description = "银行卡号")
    private String bankCardNum;

    /**
     *是否默认卡
     *
     * 0：否
     * 1：是
     * 当scene=3 该字段有效
     */
    @Schema(hidden = true)
    private String isDefault;


}
