package com.rongchen.byh.common.api.notice.service.impl;

import com.rongchen.byh.common.api.notice.config.NoticeSmsContent;
import com.rongchen.byh.common.api.notice.dto.NoticeSmsDto;
import com.rongchen.byh.common.api.notice.dto.RepaymentSuccessDto;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 还款成功通知
 * @date 2024/12/12 16:57:17
 */
@Service
public class RepaymentSuccessService extends AbstractNoticeSmsService {
    @Override
    protected String getContent(NoticeSmsDto noticeSmsDto) {
        RepaymentSuccessDto smsDto = (RepaymentSuccessDto) noticeSmsDto;
        return smsDto.getContractNum() + "##" + smsDto.getRepaymentDate() + "##" + smsDto.getRepaymentAmount();
    }

    @Override
    protected Integer getTemplateId() {
        return NoticeSmsContent.REPAYMENTNOTICE;
    }
}
