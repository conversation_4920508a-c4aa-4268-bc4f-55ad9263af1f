package com.rongchen.byh.common.api.riskControl.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.rongchen.byh.common.api.riskControl.config.RiskControlProperties;
import com.rongchen.byh.common.api.riskControl.dto.ApiCreditPreLoanAuditDto;
import com.rongchen.byh.common.api.riskControl.dto.CreditPreLoanAuditDto;
import com.rongchen.byh.common.api.riskControl.dto.PreLoanAuditAppDto;
import com.rongchen.byh.common.api.riskControl.dto.PreLoanAuditDto;
import com.rongchen.byh.common.api.riskControl.service.RiskControlService;
import com.rongchen.byh.common.api.riskControl.vo.PreLoanAuditVo;
import com.rongchen.byh.common.core.util.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.MessageDigest;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.interfaces.RSAPrivateKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 风控业务
 * @date 2024/12/11 14:12:49
 */
@Service
@Slf4j
public class RiskControlServiceImpl implements RiskControlService {

    @Resource
    private RiskControlProperties riskControlProperties;

    /**
     * 默认重试等待时间(毫秒)
     */
    private static final long DEFAULT_RETRY_WAIT_MS = 300;

    /**
     * 最大重试次数
     */
    private static final int MAX_RETRY_TIMES = 5;

    /**
     * 最大等待时间(毫秒)
     */
    private static final long MAX_WAIT_MS = 15000;


    @Override
    public PreLoanAuditVo apiCreditPreLoanAudit(ApiCreditPreLoanAuditDto apiCreditPreLoanAuditDto) {
        // 组装请求参数
        JSONObject param = buildApiCreditParam(apiCreditPreLoanAuditDto);
        // 发送请求
        String res = HttpUtil.postJson(riskControlProperties.getPreLoanAuditUrl(), param);
        // 组装响应结果
        log.info("api模式央行征信贷前审核事件接口（风控初筛接口）用户：{} , 请求参数：{}，响应结果：{}" , apiCreditPreLoanAuditDto.getMobile() , param.toJSONString(), res);
        return buildApiCreditResult(res);
    }

    @Override
    public PreLoanAuditVo queryCreditResult(String creditId) {
        // 组装请求参数
        JSONObject param = new JSONObject();
        param.put("source_id", riskControlProperties.getApiCreditSourceId());
        JSONObject content = new JSONObject();
        content.put("credit_id", creditId);
        param.put("content", encryptByAes(content.toJSONString(), riskControlProperties.getApiCreditAesKey()));
        param.put("event_code", 403);
        // "RSA"
        param.put("signature", getApiFlySign(param));
        // 发送请求
        String res = HttpUtil.postJson(riskControlProperties.getPreLoanQueryAuditUrl(), param);
        log.info("api央行征信贷前审核事件接口（风控初筛接口）creditId：{} , 请求参数：{}，响应结果：{}" , creditId , param.toJSONString(), res);
        return buildApiQueryCreditResult(res);
    }
    @Override
    public PreLoanAuditVo creditPreLoanAudit(CreditPreLoanAuditDto creditPreLoanAuditDto) {
        // 组装请求参数
        JSONObject param = buildCreditParam(creditPreLoanAuditDto);
        // 发送请求并添加重试机制
        String res = executeWithRetry("央行征信贷前审核",
            () -> HttpUtil.postJson(riskControlProperties.getPreLoanAuditUrl(), param));
        // 组装响应结果
        log.info("央行征信贷前审核事件接口（风控初筛接口）用户：{} , 请求参数：{}，响应结果：{}", creditPreLoanAuditDto.getMobile(), param.toJSONString(),
            res);
        return buildCreditResult(res);
    }

    private PreLoanAuditVo buildCreditResult(String res) {
        PreLoanAuditVo preLoanAuditVo = new PreLoanAuditVo();
        JSONObject result = JSONObject.parseObject(res);
        if (!"1000".equals(result.getString("status"))) {
            preLoanAuditVo.setResult(500);
            preLoanAuditVo.setMessage(result.getString("message"));
            return preLoanAuditVo;
        }
        String contentStr = decryptByAes(result.getString("content"), riskControlProperties.getAesKey());
        log.info("央行征信贷前审核事件接口（风控初筛接口）解密结果：{}", contentStr);
        JSONObject contentResult = JSONObject.parseObject(contentStr);
        preLoanAuditVo.setMessage(contentStr);
        if (contentResult.getInteger("result") == 102) {
            preLoanAuditVo.setResult(-1);
            return preLoanAuditVo;
        }
        if (contentResult.getInteger("result") == 103) {
            preLoanAuditVo.setResult(2);
            return preLoanAuditVo;
        }
        JSONObject amountDetail = contentResult.getJSONObject("amount_detail");
        List<JSONObject> list = JSONObject.parseObject(amountDetail.getString("amount_units"),
            new TypeReference<List<JSONObject>>() {
            });
        list.forEach(item -> {
            if ("credit_rating".equals(item.get("name"))) {
                preLoanAuditVo.setCreditRating(item.getString("amount"));
            }
            if ("limit_amount".equals(item.get("name"))) {
                preLoanAuditVo.setAmount(item.getBigDecimal("amount").divide(BigDecimal.valueOf(100)));
            }
        });
        if (contentResult.getInteger("result") == 100) {
            preLoanAuditVo.setResult(0);
            return preLoanAuditVo;
        }
        if (contentResult.getInteger("result") == 101) {
            preLoanAuditVo.setResult(1);
            if (StrUtil.isEmpty(preLoanAuditVo.getCreditRating())) {
                preLoanAuditVo.setCreditRating("B");
            }
            return preLoanAuditVo;
        }

        preLoanAuditVo.setResult(-2);
        return preLoanAuditVo;
    }

    private JSONObject buildCreditParam(CreditPreLoanAuditDto creditPreLoanAuditDto) {
        creditPreLoanAuditDto.setChannel(riskControlProperties.getCreditChannel());
        creditPreLoanAuditDto.setProduct_code(riskControlProperties.getProductCode());
        String s = JSONObject.toJSONString(creditPreLoanAuditDto);
        JSONObject content = JSONObject.parseObject(s);
        JSONObject param = new JSONObject();
        param.put("source_id", riskControlProperties.getCreditSourceId());
        param.put("content", encryptByAes(content.toJSONString(), riskControlProperties.getCreditAesKey()));
        param.put("event_code", 53);
        // "RSA"
        param.put("signature", getFlySign(param));
        return param;
    }

    private JSONObject buildApiCreditParam(ApiCreditPreLoanAuditDto apiCreditPreLoanAuditDto) {
        apiCreditPreLoanAuditDto.setChannel(riskControlProperties.getApiCreditChannel());
        apiCreditPreLoanAuditDto.setProduct_code(riskControlProperties.getApiCreditProductCode());
        apiCreditPreLoanAuditDto.setResult_callback(riskControlProperties.getApiCreditCallbackUrl());
        String s = JSONObject.toJSONString(apiCreditPreLoanAuditDto);
        JSONObject param = new JSONObject();
        param.put("source_id", riskControlProperties.getApiCreditSourceId());
        param.put("content", encryptByAes(s, riskControlProperties.getApiCreditAesKey()));
        param.put("event_code", 50);
        // "RSA"
        param.put("signature", getApiFlySign(param));
        return param;
    }

    private PreLoanAuditVo buildApiQueryCreditResult(String res) {
        PreLoanAuditVo preLoanAuditVo = new PreLoanAuditVo();
        JSONObject result = JSONObject.parseObject(res);
        if (!"1000".equals(result.getString("status"))) {
            preLoanAuditVo.setResult(500);
            preLoanAuditVo.setMessage(result.getString("message"));
            return preLoanAuditVo;
        }
        String contentStr = decryptByAes(result.getString("content"), riskControlProperties.getApiCreditAesKey());
        log.info("api模式央行征信贷前审核事件接口（风控初筛接口）解密结果：{}", contentStr);
        preLoanAuditVo.setMessage(contentStr);
        preLoanAuditVo.setResult(0);
        JSONObject contentResult = JSONObject.parseObject(contentStr);
        if (contentResult.getInteger("credit_status") == 102) {
            preLoanAuditVo.setResult(-1);
            return preLoanAuditVo;
        }
        if (contentResult.getInteger("credit_status") == 103) {
            preLoanAuditVo.setResult(2);
            return preLoanAuditVo;
        }
        JSONObject amountDetail = contentResult.getJSONObject("amount_detail");
        if (ObjectUtil.isEmpty(amountDetail)) {
            preLoanAuditVo.setCreditRating("B");
            preLoanAuditVo.setAmount(BigDecimal.valueOf(10000));
            preLoanAuditVo.setResult(0);
            return preLoanAuditVo;
        }
        List<JSONObject> list = JSONObject.parseObject(amountDetail.getString("amount_units"), new TypeReference<List<JSONObject>>() {
        });
        list.forEach(item -> {
            if ("credit_rating".equals(item.get("name"))) {
                preLoanAuditVo.setCreditRating(item.getString("amount"));
            }
            if ("limit_amount".equals(item.get("name"))) {
                preLoanAuditVo.setAmount(item.getBigDecimal("amount").divide(BigDecimal.valueOf(100)));
            }
        });
        if (contentResult.getInteger("credit_status") == 100) {
            preLoanAuditVo.setResult(0);
            return preLoanAuditVo;
        }
        if (contentResult.getInteger("credit_status") == 101) {
            preLoanAuditVo.setResult(1);
            if (StrUtil.isEmpty(preLoanAuditVo.getCreditRating())) {
                preLoanAuditVo.setCreditRating("B");
            }
            return preLoanAuditVo;
        }

        preLoanAuditVo.setResult(-2);
        return preLoanAuditVo;
    }

    private PreLoanAuditVo buildApiCreditResult(String res) {
        PreLoanAuditVo preLoanAuditVo = new PreLoanAuditVo();
        JSONObject result = JSONObject.parseObject(res);
        if (!"1000".equals(result.getString("status"))) {
            preLoanAuditVo.setResult(500);
            preLoanAuditVo.setMessage(result.getString("message"));
            return preLoanAuditVo;
        }
        preLoanAuditVo.setResult(1);
        return preLoanAuditVo;
    }

    /**
     * 贷前审核事件接口（风控初筛接口）
     *
     * @param preLoanAuditDto
     * @return
     */
    @Override
    public PreLoanAuditVo preLoanAudit(PreLoanAuditDto preLoanAuditDto) {
        // 组装请求参数
        JSONObject param = buildParam(preLoanAuditDto);
        // 发送请求并添加重试机制
        String res = executeWithRetry("贷前审核",
            () -> HttpUtil.postJson(riskControlProperties.getPreLoanAuditUrl(), param));
        // 组装响应结果
        log.info("贷前审核事件接口（风控初筛接口）用户：{} , 请求参数：{}，响应结果：{}", preLoanAuditDto.getMobile(), param.toJSONString(), res);
        return buildResult(res);
    }

    @Override
    public PreLoanAuditVo preLoanAuditApp(PreLoanAuditAppDto auditAppDto) {
        // 组装请求参数
        JSONObject param = buildParamApp(auditAppDto);
        // 发送请求并添加重试机制
        String res = executeWithRetry("App贷前审核",
            () -> HttpUtil.postJson(riskControlProperties.getPreLoanAuditUrl(), param));
        // 组装响应结果
        log.info("App贷前审核事件接口（风控初筛接口）用户：{} , 请求参数：{}，响应结果：{}", auditAppDto.getMobile(), param.toJSONString(), res);
        return buildResultApp(res);
    }

    @NotNull
    private PreLoanAuditVo buildResult(String res) {
        PreLoanAuditVo preLoanAuditVo = new PreLoanAuditVo();
        JSONObject result = JSONObject.parseObject(res);
        if (!"1000".equals(result.getString("status"))) {
            preLoanAuditVo.setResult(500);
            preLoanAuditVo.setMessage(result.getString("message"));
            return preLoanAuditVo;
        }
        String contentStr = decryptByAes(result.getString("content"), riskControlProperties.getAesKey());
        log.info("线上H5贷前审核事件接口（风控初筛接口）解密结果：{}", contentStr);
        JSONObject contentResult = JSONObject.parseObject(contentStr);
        preLoanAuditVo.setMessage(contentResult.getString("message"));
        if (contentResult.getInteger("result") == 102) {
            preLoanAuditVo.setResult(-1);
            return preLoanAuditVo;
        }
        if (contentResult.getInteger("result") == 103) {
            preLoanAuditVo.setResult(2);
            return preLoanAuditVo;
        }
        JSONObject amountDetail = contentResult.getJSONObject("amount_detail");
        List<JSONObject> list = JSONObject.parseObject(amountDetail.getString("amount_units"),
            new TypeReference<List<JSONObject>>() {
            });
        list.forEach(item -> {
            if ("credit_rating".equals(item.get("name"))) {
                preLoanAuditVo.setCreditRating(item.getString("amount"));
            }
            if ("limit_amount".equals(item.get("name"))) {
                preLoanAuditVo.setAmount(item.getBigDecimal("amount").divide(BigDecimal.valueOf(100)));
            }
        });
        if (contentResult.getInteger("result") == 100) {
            preLoanAuditVo.setResult(0);
            return preLoanAuditVo;
        }
        if (contentResult.getInteger("result") == 101) {
            preLoanAuditVo.setResult(1);
            if (StrUtil.isEmpty(preLoanAuditVo.getCreditRating())) {
                preLoanAuditVo.setCreditRating("B");
            }
            return preLoanAuditVo;
        }

        preLoanAuditVo.setResult(-2);
        return preLoanAuditVo;
    }

    private PreLoanAuditVo buildFlyResult(String res) {
        PreLoanAuditVo preLoanAuditVo = new PreLoanAuditVo();
        JSONObject result = JSONObject.parseObject(res);
        if (!"1000".equals(result.getString("status"))) {
            preLoanAuditVo.setResult(500);
            preLoanAuditVo.setMessage(result.getString("message"));
            return preLoanAuditVo;
        }
        String contentStr = decryptByAes(result.getString("content"), riskControlProperties.getAesKey());
        log.info("空中飞行H5贷前审核事件接口（风控初筛接口）解密结果：{}", contentStr);
        JSONObject contentResult = JSONObject.parseObject(contentStr);
        preLoanAuditVo.setMessage(contentResult.getString("message"));
        if (contentResult.getInteger("result") == 102) {
            preLoanAuditVo.setResult(-1);
            return preLoanAuditVo;
        }
        if (contentResult.getInteger("result") == 103) {
            preLoanAuditVo.setResult(2);
            return preLoanAuditVo;
        }
        JSONObject amountDetail = contentResult.getJSONObject("amount_detail");
        List<JSONObject> list = JSONObject.parseObject(amountDetail.getString("amount_units"),
            new TypeReference<List<JSONObject>>() {
            });
        list.forEach(item -> {
            if ("credit_rating".equals(item.get("name"))) {
                preLoanAuditVo.setCreditRating(item.getString("amount"));
            }
            if ("limit_amount".equals(item.get("name"))) {
                preLoanAuditVo.setAmount(item.getBigDecimal("amount").divide(BigDecimal.valueOf(100)));
            }
        });
        if (contentResult.getInteger("result") == 100) {
            preLoanAuditVo.setResult(0);
            return preLoanAuditVo;
        }
        if (contentResult.getInteger("result") == 101) {
            preLoanAuditVo.setResult(1);
            if (StrUtil.isEmpty(preLoanAuditVo.getCreditRating())) {
                preLoanAuditVo.setCreditRating("B");
            }
            return preLoanAuditVo;
        }

        preLoanAuditVo.setResult(-2);
        return preLoanAuditVo;
    }

    @NotNull
    private PreLoanAuditVo buildResultApp(String res) {
        PreLoanAuditVo preLoanAuditVo = new PreLoanAuditVo();
        JSONObject result = JSONObject.parseObject(res);
        if (result == null) {
            preLoanAuditVo.setResult(500);
            preLoanAuditVo.setMessage("失败");
            return preLoanAuditVo;
        }
        if (!"1000".equals(result.getString("status"))) {
            preLoanAuditVo.setResult(500);
            preLoanAuditVo.setMessage(result.getString("message"));
            return preLoanAuditVo;
        }
        String contentStr = decryptByAes(result.getString("content"), riskControlProperties.getAppAesKey());
        log.info("App贷前审核事件接口（风控初筛接口）解密结果：{}", contentStr);
        JSONObject contentResult = JSONObject.parseObject(contentStr);
        preLoanAuditVo.setMessage(contentResult.toJSONString());
        if (contentResult.getInteger("result") == 102) {
            preLoanAuditVo.setResult(-1);
            return preLoanAuditVo;
        }
        if (contentResult.getInteger("result") == 103) {
            preLoanAuditVo.setResult(2);
            return preLoanAuditVo;
        }
        BigDecimal amount = contentResult.getBigDecimal("amount");
        if (amount != null) {
            preLoanAuditVo.setAmount(amount.divide(BigDecimal.valueOf(100)));
        }
        if (contentResult.getInteger("result") == 100) {
            preLoanAuditVo.setResult(0);
            return preLoanAuditVo;
        }
        if (contentResult.getInteger("result") == 101) {
            preLoanAuditVo.setResult(1);
            if (StrUtil.isEmpty(preLoanAuditVo.getCreditRating())) {
                preLoanAuditVo.setCreditRating("B");
            }
            return preLoanAuditVo;
        }
        preLoanAuditVo.setResult(-2);
        return preLoanAuditVo;
    }

    @NotNull
    private JSONObject buildParam(PreLoanAuditDto preLoanAuditDto) {
        JSONObject param = new JSONObject();
        JSONObject content = new JSONObject();
        content.put("idcard_no", preLoanAuditDto.getIdNumber());
        content.put("mobile", preLoanAuditDto.getMobile());
        content.put("name", preLoanAuditDto.getName());
        content.put("credit_id", IdUtil.fastSimpleUUID());
        content.put("credit_time", DateUtil.now());
        content.put("channel", riskControlProperties.getChannel());
        content.put("product_code", riskControlProperties.getProductCode());
        param.put("source_id", riskControlProperties.getSourceId());
        param.put("content", encryptByAes(content.toJSONString(), riskControlProperties.getAesKey()));
        param.put("event_code", 53);
        // "RSA"
        param.put("signature", getSign(param));
        return param;
    }

    @NotNull
    private JSONObject buildParamApp(PreLoanAuditAppDto auditAppDto) {
        String s = JSONObject.toJSONString(auditAppDto);
        JSONObject content = JSONObject.parseObject(s);
        content.put("Mobile_startime", auditAppDto.getMobile_startime());
        JSONObject param = new JSONObject();
        param.put("source_id", riskControlProperties.getAppSourceId());
        param.put("content", encryptByAes(content.toJSONString(), riskControlProperties.getAppAesKey()));
        param.put("event_code", 53);
        // "RSA"
        param.put("signature", getAppSign(param));
        return param;
    }

    @Override
    public PreLoanAuditVo offlineH5PreLoanAudit(PreLoanAuditDto preLoanAuditDto) {
        // 组装请求参数
        JSONObject param = buildOfflineH5Param(preLoanAuditDto);
        // 发送请求并添加重试机制
        String res = executeWithRetry("线下模式贷前审核", () -> {
            try {
                return HttpUtil.postJson(riskControlProperties.getPreLoanAuditUrl(), param);
            } catch (Exception e) {
                log.error("线下模式贷前审核事件接口（风控初筛接口）请求异常", e);
                return null;
            }
        });
        // 组装响应结果
        log.info("线下模式贷前审核事件接口（风控初筛接口）请求参数：{}，响应结果：{}", param.toJSONString(), res);
        return buildOfflineH5Result(res);
    }

    @Override
    public PreLoanAuditVo flyH5PreLoanAudit(PreLoanAuditDto preLoanAuditDto) {
        // 组装请求参数
        JSONObject param = buildFlyParam(preLoanAuditDto);
        // 发送请求并添加重试机制
        String res = executeWithRetry("空中飞行模式贷前审核",
            () -> HttpUtil.postJson(riskControlProperties.getPreLoanAuditUrl(), param));
        // 组装响应结果
        log.info("空中飞行模式贷前审核事件接口（风控初筛接口）用户：{} , 请求参数：{}，响应结果：{}", preLoanAuditDto.getMobile(), param.toJSONString(),
            res);
        return buildFlyResult(res);
    }

    private JSONObject buildFlyParam(PreLoanAuditDto preLoanAuditDto) {
        JSONObject param = new JSONObject();
        JSONObject content = new JSONObject();
        content.put("idcard_no", preLoanAuditDto.getIdNumber());
        content.put("mobile", preLoanAuditDto.getMobile());
        content.put("name", preLoanAuditDto.getName());
        content.put("credit_id", IdUtil.fastSimpleUUID());
        content.put("credit_time", DateUtil.now());
        content.put("channel", riskControlProperties.getFlyH5Channel());
        content.put("product_code", riskControlProperties.getFlyH5ProductCode());
        param.put("source_id", riskControlProperties.getFlyH5SourceId());
        param.put("content", encryptByAes(content.toJSONString(), riskControlProperties.getFlyH5AesKey()));
        param.put("event_code", 53);
        // "RSA"
        param.put("signature", getFlySign(param));
        return param;
    }

    private JSONObject buildOfflineH5Param(PreLoanAuditDto preLoanAuditDto) {
        JSONObject param = new JSONObject();
        JSONObject content = new JSONObject();
        content.put("idcard_no", preLoanAuditDto.getIdNumber());
        content.put("mobile", preLoanAuditDto.getMobile());
        content.put("name", preLoanAuditDto.getName());
        content.put("credit_id", IdUtil.fastSimpleUUID());
        content.put("credit_time", DateUtil.now());
        content.put("channel", riskControlProperties.getOfflineH5Channel());
        content.put("product_code", riskControlProperties.getOfflineH5ProductCode());
        param.put("source_id", riskControlProperties.getOfflineH5SourceId());
        param.put("content", encryptByAes(content.toJSONString(), riskControlProperties.getOfflineH5AesKey()));
        param.put("event_code", 53);
        // "RSA"
        param.put("signature", getOfflineSign(param));
        return param;
    }

    private PreLoanAuditVo buildOfflineH5Result(String res) {
        PreLoanAuditVo preLoanAuditVo = new PreLoanAuditVo();
        if (StrUtil.isEmpty(res)) {
            preLoanAuditVo.setResult(500);
            preLoanAuditVo.setMessage("失败");
            return preLoanAuditVo;
        }
        JSONObject result = JSONObject.parseObject(res);
        if (!"1000".equals(result.getString("status"))) {
            preLoanAuditVo.setResult(500);
            preLoanAuditVo.setMessage(result.getString("message"));
            return preLoanAuditVo;
        }
        String contentStr = decryptByAes(result.getString("content"), riskControlProperties.getOfflineH5AesKey());
        log.info("线下模式贷前审核事件接口（风控初筛接口）解密结果：{}", contentStr);
        JSONObject contentResult = JSONObject.parseObject(contentStr);
        preLoanAuditVo.setMessage(contentResult.getString("message"));
        if (contentResult.getInteger("result") == 102) {
            preLoanAuditVo.setResult(-1);
            return preLoanAuditVo;
        }
        if (contentResult.getInteger("result") == 103) {
            preLoanAuditVo.setResult(2);
            return preLoanAuditVo;
        }
        // JSONObject amountDetail = contentResult.getJSONObject("amount_detail");
        // List<JSONObject> list =
        // JSONObject.parseObject(amountDetail.getString("amount_units"), new
        // TypeReference<List<JSONObject>>() {
        // });
        // list.forEach(item -> {
        // if ("credit_rating".equals(item.get("name"))) {
        // preLoanAuditVo.setCreditRating(item.getString("amount"));
        // }
        // if ("limit_amount".equals(item.get("name"))) {
        // preLoanAuditVo.setAmount(item.getBigDecimal("amount").divide(BigDecimal.valueOf(100)));
        // }
        // });
        if (contentResult.getInteger("result") == 100) {
            preLoanAuditVo.setResult(0);
            return preLoanAuditVo;
        }
        if (contentResult.getInteger("result") == 101) {
            preLoanAuditVo.setResult(1);
            if (StrUtil.isEmpty(preLoanAuditVo.getCreditRating())) {
                preLoanAuditVo.setCreditRating("B");
            }
            return preLoanAuditVo;
        }

        preLoanAuditVo.setResult(-2);
        return preLoanAuditVo;
    }

    private String getSign(JSONObject param) {
        try {
            String md5Str = getMd5(param.getString("content"));
            return sign(md5Str, riskControlProperties.getRsaPrivateKey(), false);
        } catch (Exception e) {
            log.error("rsa签名失败", e);
        }
        return null;
    }

    private String getOfflineSign(JSONObject param) {
        try {
            String md5Str = getMd5(param.getString("content"));
            return sign(md5Str, riskControlProperties.getOfflineH5RsaPrivateKey(), false);
        } catch (Exception e) {
            log.error("rsa签名失败", e);
        }
        return null;
    }

    private String getApiFlySign(JSONObject param) {
        try {
            String md5Str = getMd5(param.getString("content"));
            return sign(md5Str, riskControlProperties.getApiCreditRsaPrivateKey(), false);
        } catch (Exception e) {
            log.error("rsa签名失败", e);
        }
        return null;
    }

    private String getFlySign(JSONObject param) {
        try {
            String md5Str = getMd5(param.getString("content"));
            return sign(md5Str, riskControlProperties.getFlyH5RsaPrivateKey(), false);
        } catch (Exception e) {
            log.error("rsa签名失败", e);
        }
        return null;
    }

    private String getAppSign(JSONObject param) {
        try {
            String md5Str = getMd5(param.getString("content"));
            return sign(md5Str, riskControlProperties.getAppRsaPrivateKey(), false);
        } catch (Exception e) {
            log.error("rsa签名失败", e);
        }
        return null;
    }

    /**
     * 要私钥签名
     *
     * @throws InvalidKeySpecException
     * @throws Exception
     */
    public static String sign(String content, String privateKey, boolean isRsa2) throws Exception {
        PrivateKey priKey = getPrivateKey(privateKey);
        Signature signature = Signature.getInstance(getAlgorithms(isRsa2));
        signature.initSign(priKey);
        signature.update(content.getBytes("UTF-8"));
        byte[] signed = signature.sign();
        return java.util.Base64.getEncoder().encodeToString(signed);
    }

    public static RSAPrivateKey getPrivateKey(String privateKey) throws Exception {
        byte[] keyBytes = java.util.Base64.getDecoder().decode(privateKey);
        PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return (RSAPrivateKey) keyFactory.generatePrivate(spec);
    }

    private static String getAlgorithms(boolean isRsa2) {
        return isRsa2 ? "SHA256WithRSA" : "SHA1WithRSA";
    }

    /**
     * 私钥加密
     *
     * @param privateKeyText
     * @param text
     * @return
     * @throws Exception
     */
    private static String encryptByPrivateKey(String privateKeyText, String text) {
        try {
            PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKeyText));
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PrivateKey privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec);
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.ENCRYPT_MODE, privateKey);
            byte[] result = cipher.doFinal(text.getBytes());
            return Base64.encodeBase64String(result);
        } catch (Exception e) {
            log.error("rsa私钥加密失败", e);
        }
        return null;
    }

    private static String encryptByAes(String str, String key) {
        try {
            byte[] raw = key.getBytes(StandardCharsets.UTF_8);
            SecretKeySpec secretKeySpec = new SecretKeySpec(raw, "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec);
            byte[] encrypted = cipher.doFinal(str.getBytes(StandardCharsets.UTF_8));
            return Base64.encodeBase64String(encrypted);
        } catch (Exception e) {
            log.error("AES 加密异常", e);
        }
        return null;
    }

    public static String decryptByAes(String str, String key) {
        try {
            byte[] raw = key.getBytes(StandardCharsets.UTF_8);
            SecretKeySpec secretKeySpec = new SecretKeySpec(raw, "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);
            byte[] encrypted = cipher.doFinal(Base64.decodeBase64(str));
            return new String(encrypted);
        } catch (Exception e) {
            log.error("AES 解密异常", e);
        }
        return null;
    }

    public static String getMd5(String plainText) {
        try {
            // 生成实现指定摘要算法的 MessageDigest 对象。
            MessageDigest md = MessageDigest.getInstance("MD5");
            // 使用指定的字节数组更新摘要。
            md.update(plainText.getBytes());
            // 通过执行诸如填充之类的最终操作完成哈希计算。
            byte b[] = md.digest();
            // 生成具体的md5密码到buf数组
            int i;
            StringBuffer buf = new StringBuffer("");
            for (int offset = 0; offset < b.length; offset++) {
                i = b[offset];
                if (i < 0)
                    i += 256;
                if (i < 16)
                    buf.append("0");
                buf.append(Integer.toHexString(i));
            }
            return buf.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    public static void main(String[] args) throws Exception {
        String rsaPrivateKey = "MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAL2kBdHvba6+ZEMd6CeJfxerjgD5MWGR4xwaSpTDNmhcwE6nYCKNHddZpu51xjQjaacpr1ixkItwpQbXyB+kTathiPR6H14JFWP9HUmmLmR8eLfCEFFo1W+RSHXR6QT3C391dHK5G5LzuznP9c2phBdLx0PnOP6+WthAFt/DNIelAgMBAAECgYEAs4WKXOojEsD5eO/ezU7EGUw16YX80TihngDliV4jKzhidBLOVubv0OT1udeUAddkNPKpI3U0OEwybP/oWsvXGTlIcKRO1YUoRjoEb+6d0ZuJLI9mojyYttS8N1KyfUJx7I/hw18n6Xbla6NwO0kJqFlBAUDElkfWjBJ0bKwdngECQQD7GGOVps5DK1jwoxA5/G1gXpwb0BZ6/EN03QwnIR2FmdjyasiUvBHGtkx5OGv6kS/oKAqkk/ps5bi0B4VB1FTVAkEAwVhT97fvY67GRwcj2pfWD2oPZmT/ZaIHpPktl3ZEpU66m6u8KYrP+loeMQZ5Ha4i5Vl2DygKkm+pCJu4QRgPkQJBAJV+2NOhw45UQZjLzP5pHwnQamtYwfmpNdRfQzwMyFHh3ju+ffun2YGQygkmIYvGY1p6dJO2EtRBFW4CSEGAVJ0CQQCfQkGQGBuBMbKzSQdYhJ9XfRaIUoMpVUkUtAfA7jNnMy11EwXf9i6QWnDqExnqv8iQwJsFqwbiTkWfCJ3CSK5xAkBdqSIYELl9/lzHzbBDnclwnDYoo/Wxy4UzI+oHPTyqeFPaVsnFOaZstveY61TY3COnXt9KP2tgHDX70zfjDbAU";
        String aesKey = "EtYaUhSYnltLMw9G";
        // 组装请求参数
        JSONObject param = new JSONObject();
        // JSONObject content = new JSONObject();
        // content.put("idcard_no", "210102196408195804");
        // content.put("mobile", "13954221045");
        // content.put("name", "金晨轩");
        // content.put("credit_id", UUID.randomUUID().toString());
        // content.put("credit_time", DateUtil.now());
        // content.put("channel", "3");
        // content.put("product_code", 3);
        // JSONObject content =
        // JSONObject.parseObject("{\"credit_id\":\"1868910943853961218\",\"mobile\":\"18520802718\",\"name\":\"潘登\",\"channel\":\"3\",\"product_code\":3,\"idcard_no\":\"610303198909182439\",\"credit_time\":\"2024-12-17
        // 14:47:46\"}");
        // param.put("source_id", "12qzf_grxfxyd_0001");
        // param.put("event_code", 53);
        //
        // param.put("content", encryptByAes(content.toJSONString(), aesKey));
        // // "RSA"
        //
        // param.put("signature", sign(getMd5(param.getString("content")),
        // rsaPrivateKey, false));
        // // 发送请求
        // String res = HttpUtil.postJson("http://**************:1112/x_engine/credit",
        // param);
        // // 组装响应结果
        // log.info("贷前审核事件接口（风控初筛接口）请求参数：{}，响应结果：{}", param.toJSONString(), res);

        String contentStr = decryptByAes(
            "xRTPoQMQkqDo9mtDaiyDRn7SHQpXXV5kZEKOG5JC2NQ0A15DYY9oiTpbTiQzfIZQnU6kvQKKyxt9DtLsePOniZpHN0bY/5b6fAIlHeAWM6tAg1Zy/bEDujJGfEzZ/jFfiUQIqwoEgKbitMoDoaA64T5bMIvydY8XnT7pgFLV6k9BVU1KqKJ5Ulgil3ABTAQ2jfCveuSRD6MLQvqfQlsnBE4hZxFbBXtc0wFWf9JTxR/QJl8gNdhZUs3Vz3nODo0VDDF5Enknlr1JwN5ooCTSEj6h9oGrykcg6/0Di62VmW9qfuZL++eAjMC2eswCHvMCplbpNc7AHaS+PuYBc3+wqtgDs3I2BsLvwKgOBQR0safsQCV07SEAhzfb1CumzDadZqMoVjr4AGcpFMuzIO+X8Xt++usTX0L5BS/MFssSw1MPd4XUGdnxpKhHarQ7KpxfxgpZ4YVk3Rxhvk2lQXy2wTFD09ukWRy1hSsM29MH84vnu3aYoSeAmvaGvllCrSIC4i5/oWFS7iFrwMAoXFiDNl+fw5CfWeuIREVnLlK3Q5ddZ0GegefU1wKq10vVO+x2jwtrUpenPIXGtERR8as9ZL9rQ7+/2tLpx0/apofft26RBGegiBQXBg7GvwdWM/MVoUGz9ptBkcLuJ/V1v8okZxa7hTimtgIV02DUV6xRwhZN3susxDyzscwrenGStfZK8uwQ8+PTq/kSRsT8eoOkRnQA4AE2oU9YXYYaFlvND39yjbEp1ov9lwAeRsSpp56Ha8pFb95O8k6fgad7sjkuyhP9OAtvYoKSghHUhATGl0vesQbsp2fHGMH2l3EpSlzYtEp72oEpr5W7r+CXK/v1HE2uAMdyqZp9FJE+IvrUaYEo0Y6s7iaFOKjr49J7SGuX0SPzYhL3JmXFW4OH9KL3AJpR7AfgZwfNdgWBUoLBOxoQQPRRB9tb5QJK09rmVsbnE8glO0fF2OfTaXWf4kifSnjHcaMbd2MuVo91Gb9wlzmfkREf4bBs9fzLfO3KyRCmvz13FvtlKZZYZHB5/VT2bVmqh1qVB99rPszmy5UdvHHwXfmnn91lZx86lJtAjwodbZJnWf1EZZ+745BFlD6jDZCMDi+A2lKHN3zjYMEU5XlM4AitHVZfpF2sB6OAgklkJFKjdvnUGSlFjTUUizeVtVbm5BJ0ibBx9JVQG4LBB6Md/0L588cc3EOUORVij2rVLd7zK6/d6Yyw5rbwvKUsXMoy8cr7bKmA6VWQphnYlEoOpAHB/g5S38QQveS8WZN3ziBONVopBTy4IiDsbzUPUt2j/9VX1M8I1fTuNmuugd9PyAb/OzT2Eh3oz/h2oAtuNDiPNwWMXo0sNG2Kn0csWH72NrLK6fAoorn0uZfDjNc3TdGk5UmzHFLm6tJ4BGy7aYNT/pWoK0BylGk+fFp7Mitdq4iWIGgBvcn0h8GaRZbbZXCYHbhEaszCkAODEujr856C0Z9PNvJBtrRhkgj+mYlECKsKBICm4rTKA6GgOuE+WzCL8nWPF50+6YBS1epPXYGBAr+uAmVkGYJbN+JF1Y3wr3rkkQ+jC0L6n0JbJwSBxQ8NyXL9D5cbGm9sxjM6X1oEXwUzbb8/s67fWKG4vAwxeRJ5J5a9ScDeaKAk0hI+ofaBq8pHIOv9A4utlZlvan7mS/vngIzAtnrMAh7zAr0rsK1JeDeYBDmOxC6tRBOQ+xuxDskVhqIfDj0Mt4abr6ZFFQf7S1LQUvErllShh9F47yIXsE5LLv67gb4zOIqW7ei53QHjCTk0axzexjLyql+5h0yr0Vv+7mdKOgMKhGw+nOsmE+uwj1E1YIdlbF6QFVFxEjdDz0/Ki146KL0gy0ARI2Nx9UaXEOn6XQl/QpRFImdUSFWv393WjEOusvwH5GPtUWSfoaq5Az0sNLMZ0P+3xuO7x11JvNdcLz1L+HIQSFePXzVd7Vr0mAdapQeYpH4h8Db3iA7Ij9W4wvzLQXrQXkd8IGu9lihlBy6gMjp+Qx+euXuOaayiufvm83oquouTGj8MpM2p9cINOV4/HOQJCAtTLRBo+owd4q6URcW9XHPaRZUsOyoRXgZLnM/fR8zgCk12gvQ/CjxMlPrPqb9zxKUpbVUWExfbWuEIDqae7vjdjqNoD/sYVSJXT+znlUzeQhWd8g2Rjup+lTxHuyd5vthxbgwMY3UqhsjENnr+MfFvMwMhj3X3LJEnbULL8CuA8sb4y5IWeNy55y31XEfu+sW98n+3C5IeZsrH9UBpbctFzc53a2E2COEW9I5AxSTNclEbn5hK528JynhNeumY6jqtYMGZgpxGJAtBHzeS4YEQEwMwsti0Ad0dYVQPM1Q8mcioLV6uh5jIAeeEgn5sXGjm6NuQkymRQqZJn84gTjVaKQU8uCIg7G81D1JrzptaVSxHoV6jVVu1CSrN5N1Y0gksQWiN0MCqzsn+632LbEPKUcO/SgHE+9I7rt6oHYoxfMKFY4CmFp95vk6q6PckLBA0Hck29Hk6lISao508+e0UFzU+CR3OhvY0csdXoCdEZa7F/WtZaBnu+T5mO+MEjtAOEEzBAI5UdclgD2xBxbpUisSDYtn+hs3DB9F1vAzaJ1PF9KsBRNQMREUkr0ur4ObQPp/m0M7MH8q1zKfEhwYaHO3+SjKSyxEZ3AaQjxqc7jPnqu5wLUfSVjExb8P70YtMDzvfuWQQwGBj1BlRsPN5kcRoRwXrPdkd2Gu18kYRWmVznzk7rzo8a7YSmTx+HrNR56NKtJB4ehkECggE5evp2MU9SctWPglt3ndyEEhXj181Xe1a9JgHWqUHCcKCmS+LA21JbfIjTsagkFxzoG73yxaWbIlgsbZRusIXKOuSxXpZoc8CXvjOxh8PG6XXAM9ruhmPdzAhrUQdnGj+YPcEJ5473eU4lVQNtDSjp9JD4L1noQdvc+neYGAdO3a2oHa7XKqPKTT4m29emFEgkEnGDrYkkjR1e8o34JAndw8Z2W9Tn0BzyXq82KB0xNzCio3tpYZwhOfF3HjViQgd0xd07nvJQ+lAJwbRYDrAXhaT+qz1KgpMbH9jGFc0ps/sUiSvdN7JIh7olcvJ/+v5CvKxU61CSc8ykNZqIWy6/KhH5Vjt4NrHRy1u4ANqewH2qoEynb0SerwRIxJYDgol5AMX/70LW/URG8BJ4G/XwKpWJWsKMfPT85vp0/cCr0ur4ObQPp/m0M7MH8q1zGSQV1MhQgIz8lAZNGAHBN68O8mWFg8eKMv5bGwz5X6kAdMqu/SngDzahOExW+Bw6x5Q5ws+AhfRUVompf4IQjk3TdGk5UmzHFLm6tJ4BGy7Gj4cbcic41EQiiT+BLKmuX4hdobke0rOxTTRSaSOOl3xnMjg8v37x2W4rtL5gZCNcAHUUN6EnzWd3rr3S6Oamr+ZzNBbqgSkVIsCCzhhuBR1R0Pe3k3lt5pB66/W0Dnp23QY0Z7ED1P/Ck9DMb/PJPIoTh75/3Fv517fp0Ig0OHPAUY/2L/d3j0pBy9xOotXvTWRGwxvywd/WtC7CD6s58d+Iaq23j806p0/QtwIgCuSzigKCQxsFZcfeeCr2+59rH9CxDDLC2qsmXYuS8Yk3W/D+9GLTA8737lkEMBgY9QZUbDzeZHEaEcF6z3ZHdhrJzxkHJhmr+QyWyIC0cciy8dq7vbyFfOIV++MewA2yVEIBOXr6djFPUnLVj4Jbd53chBIV49fNV3tWvSYB1qlBwnCgpkviwNtSW3yI07GoJBcc6Bu98sWlmyJYLG2UbrCFyjrksV6WaHPAl74zsYfDxul1wDPa7oZj3cwIa1EHZxo/mD3BCeeO93lOJVUDbQ0o6fSQ+C9Z6EHb3Pp3mBgHeZ7RFFkg9XRwIF2ectgjdjzx0h24zz+JHyTqsHIlD1O1r4faj3LiHTGgLw+aMMBHF5KXJ5RGaJLG9zkkHPzbqMx9KUMrZIytJNXJtwG+BLUM9M7NgNb1gSQdOs2rXk4emIK4UFt41AZ6vUOdyXcMm4u/FcMqFAdLI/GL8QTIgXrOTqrvTwJjkflvWJejCINHEpjvOZajERPNKpezg1xsID5vqv5MV30w1S5dezK5Uq3YnMMLYctrhqJZBw9ct68PwEVxWPH10YjkFixgUOzvEOvOK5H9FitwfYhOpwv6Tx4eyLOX0bysVBlUbf/zsxeyyRsZd/A/8ECdQ25KMRfM60L6W10b0xI8gBD6py9wXEsM6enAntcfKMMyA64JqB2A3/CXOrtFfhfZO8E+xO5pyMOvEZxSkEfZtfJ5q+3VOznFbeImtxDiB5nxSppNqsaVIEJPYOydfDSvKo8ooTnhze0TMoJQdnFdGd5CMd/SzJBva6RECLTPegD7gZJkvhtDJD7G7EOyRWGoh8OPQy3hpuvpkUVB/tLUtBS8SuWVKGH0XjvIhewTksu/ruBvjM4ik0sgyFuVYvbokCdEXbMkvph8ige94131cYo0pfXwpv/1SHBSMXOx14AElsQJrhgLp2IRPRR4C4M540bIx3pmmMc6wiAUdpU2bazasC6bLca3pbXcyaYbb9RoqyId2nwVjvginBZqBinAKgMfimc5Mfag2xlh21xc0N9JwZY9hZkTmGP0zce0zXTJpiUbfd/g1rzcsP11ccilcRiTevnBx+mCh/KqzTQh+ijLg9HcekRhiWK1OOOHvuWCQkN+I8PF2E/ya7KJEaZfs8tb3IQQ/9sgCAbxAH4UWFoFy9t0bvU2EljYUo4idBJtxyYpt7naCGz5eDn1fHVXhFLJlHZv0UKz17TxbMSZMuUCe7sGO2x9Ay4yI1Ch/kf819IaT+Rwto0VMqZ9CUiORteQqe5b1w/rMi4wiIFVnpROfmj9pcbnYNdmw5kQsC5HgDRd6XEg6j96EApvkQGM27RaBIgwRmhfjmjdTJoC4GzDBdqXr7W3rEG7KdnxxjB9pdxKUpc2LRKe9qBKa+Vu6/glyv79RzIykKh8S3NmlMcw1UtbsheOZGLaB9OM+uIfnkfXE3phidKHZDNt7rX2k6bIY3eUzDtxWuFrF19M9C+6bJD3HXhuWKQAQNN521P1Qfl4P0x3hdF1+3MOj8Q2QwCYQGIYglTnRus951VhdjHGlv9BMNLpvaiPMOVvhL1tb13lLRzL6WB0QhDvP/ONIz29u+czhXpwILJkKnunMf+u8ziI+a9Dyf5OLjuZN/UjKsnSiD1lmHsTFDsMnCt2bcnyE8u5064F6xj85tEeexYv82JnkvK7tjnaDaMwB3IfJvr/mFnHKHu7ViAioCz3Qi4awa3rmploKq8I9v0FIRw9829BGCp3Vzsl6yYDVjzFfSPGZjysj25TG59wdfflK43tS9sJAK9++4zyaNEFsVOrj9TRpEaK3vSV4mNlwAdVDXzya90cA68RnFKQR9m18nmr7dU7OeSWMYu6m457wORVetwWUEnIFccdtbThqa3AWFSyqjZRsIskq2tTvxXMpnp5hmq6qgJZs5XPbAMUxpTu4ir81mxbkQPg7yCfSGOu03hkUOdWNgDs3I2BsLvwKgOBQR0safsQCV07SEAhzfb1CumzDadYXf7Kqcr3JJUflOMOvKJYvf0FNXqJ6arz/ODHYUdcjv/iG2aUpDKu+gl1cnEcP6koHFcpKEZD3NjY5o835A77DOSxpnPGXL2XuAOUqoBKmPAzCO1jp/eO6RO6scyXBhw3Wtosu4A4sbQ4vGNths0+TVeAiBK3FX5hl4OOW86QN9XZ/2kWV/tKLj1SxaQeScDaQkYMbr6D2YYGGMm2BLQOn6Eq+JIr05ua6SgAUhoKvgQ7c6hsre0/eC9mzcHNnooQXrQXkd8IGu9lihlBy6gMjp+Qx+euXuOaayiufvm83qaiqgPevzQ2bu6Y5FZ8pvRVsfT8iGiG36zo57E43fK1EBDw7qn/SUvhz+ozco7DVN5d3A+Hh/3hw51oOnLGcuAJPWuUXm5fKyWvjOgFOz7eUNK3FET+6fgITLqXzn0iujnlUzeQhWd8g2Rjup+lTxHzWsdXpPT17xdIcNCX6Uuqf6KdMcshEU7SW+jAzoZmz+nKEACUoUJkNdxIMfzOtRu5N1Y0gksQWiN0MCqzsn+632LbEPKUcO/SgHE+9I7rt6oHYoxfMKFY4CmFp95vk6q6PckLBA0Hck29Hk6lISao508+e0UFzU+CR3OhvY0csdmfFSUZEV64Iv/FQhhwinqO+MEjtAOEEzBAI5UdclgD+wu/c2VH0M8Qtiy+pMIxMhiHT7XLnAwfFn3VdXfD3GWfsEU9bohUefQ8u4MbkWz5XewKEJYlFLCHfCCrbXcztYeaqScmYZXsHY1tUQ9Y/eTivW01LisduulYC6/9ASk/6hiigz+eeNGWLfggfLO8Q8AjEbHmFxx8C0K07fgfJmrgy9A7iPnVbgE3osy4lN9HaGZqO4nCefSiu3Y2M733hL+8Oe3d3UFR7J8Ta1tK2Rw4z1F/56buyFFfyCBT6Gc3XI+e6ljULRgMFn0kiLttpmFI17+2MHiJRIA8j5bW1mwBeZe5HYh/ajFts/ZOo8j1DfVJvafyPIX7eZnv/d0gMxnhb9EKzKNHPc3rVwQOU3NVUrUWzRprxeH3Em8Xpe4U8MwL/hHIAC8IA8IDOcKhLKoH+o6EugFUS9zrBUcyu4653TLKSKGIVS7uijWy2rNn7cAjhv/ewNf9PQVdox7sjMCZdDXA7C3Wl73dnBwai5FOTP8dvpFyXcEq2kgjiEcD8+tnFz/nv8kHo5glcA5xsMgPTbqQZIaRifZeYTFVgTcVZQoTIKD22hTbgcd4l3z3rBHRKfDSjPwIWdZJs5/jV2JCDEIX9mHExfPwhwYij5FpPhPSLH+fyppgwwQQs25ZX7BFPW6IVHn0PLuDG5Fs+V3sChCWJRSwh3wgq213M7Ws6wnHAmhmQY/oRy127woRima8lEae96D/yHfErbS1gdpTWnN4WUqDmdnuUtf+9SsvAcqTEUvCajEX6zNuJ3p+66neGjSPGTZsP6szsyTWx3GpEEWrkA7avExFbmYxEzBaoXZbSSA0T8KWgzgU6rapQafI5QIum4BBciav53QApTtRIxKrplgEXhVIk12qd0ttRfjm0mBce7KCHFAe5ySE9Ow6rFj8dRRqKJXzV3NIpzcCAie3T0Tk15mw290blKMw7MHMHSHswqY3OiSN/Z1+tjw6Ka/+9ub1kCm9fqOSl+7J3m+2HFuDAxjdSqGyMQ2ev4x8W8zAyGPdfcskSdtQsvwK4DyxvjLkhZ43LnnLfWJCDEIX9mHExfPwhwYij5FpPhPSLH+fyppgwwQQs25ZSRFzwqB5wlD/FeLiUv/ChJhCmYLKkuwY6TvdUzXZpDn5wH9x8T/fgm9vYBoI1cE5ExTTq43mh/L5USRBx/40C9TFtDQ3tBXByK7iycXv4zZ1vqWWn6kEq1nh7pfapPHk6j96EApvkQGM27RaBIgwRmhfjmjdTJoC4GzDBdqXr7WjxI6G/fWNRv30PhwPuEMSRI0qOb3XYhEwE9EIuEXIg+dCyPIb/OKghdSEm8Q+BCFXm1FiCBKjKXu9cmCEtIwi9lBwp6rnbjVSPzMved/OCBf1eUjgEuRwYQiyV4mQp6HWXlQuWs8UH4YOTbo7Isz0bjdXmrtAE9GMTpM2yTcMmEObjTYi89vyewpxHKWtAOEHk1rfEwHCHMiyC5ErTzxHXQOAc09zvSu4sstU08C+Yn5FneLhEkb+B2ccKf+zOdu4oPK+NWPWRNo+69Dw1cSPV4z8yk5cqnFIfEYNHDc0arxQbJ7scUF8WG3tqAg15WF50PjfNM2/c+SxNIBNPoe9eqSwdKpKEb35DUHIngD30Lb8jvPbeVZcW7/o3eGTF60uWvb1dtusE42BGYD+Kl9VDdN0aTlSbMcUubq0ngEbLsaPhxtyJzjURCKJP4Esqa5YbllnFIvOFTypxN1eVvbVb5nRr/QtGBhFh9D7TXRFImGYJaG4139BVkrk7G6AvKSvM/4DODjcqUm/gOjC8i8P2zjIX5uSHetOZW2mvKjSZuh9f+zBRpUZkNnJaoTugCw65rowQtrA8TBUKngUqxMaOpe6SJGonAs1mKIgaGbaIjPJzIjGojueDXP8trsVjsmLvxXDKhQHSyPxi/EEyIF6zk6q708CY5H5b1iXowiDRzFdEo4Jl+Ui/ds4zJnviCKD5BI0LKeoYOyckax5WldHM2vLZjeLg6qSBSSP21WVHqEsDnG6q70de6r6XTQDxfRy22J7z1VbniP+ozmEFXIaiW63W/DRfF4U6ppQylXv34UOy2KqdYltyrJmpY2dLkkcMMozrFoTKvAYYyPnrdColqSbE6tjxSP6C0vizRjtoVCZ0xdOc85nQ1nI0DK87HbXUWisQAWIsv5nFKTFWIS3Eq7fK7/0dTkXV9yMtI6ivUNLK0oBSpC/rTYt63N/PLYhJ3CzZB/Mj5fu7GmFTHudOMkqdEwoLl9iJpwMJyUmwChlVNUs35eONqFUVUHAl4is6wnHAmhmQY/oRy127woRima8lEae96D/yHfErbS1gdpTWnN4WUqDmdnuUtf+9SsvAcqTEUvCajEX6zNuJ3p+66neGjSPGTZsP6szsyTWx0kR4GEaPg2/07jVjafBoXRhRojDePosnfRSIgtglFL9iqbpuoCPHlxfCaqsArV6y4TsoAgYPBGVZRv25UlRevybAXcc6Z5mc2w7p2zl7dvTDtInNrNkFR55dcNrcBjqVDb5k+FI9PNf32uApvnveLwLLh3Pt0kfFOsjHsfErCF62fC0fbHnCiFN0wU62NysxWQaJLleJdMPOcDArH5owjDaLHCavRPQVESpKo+OnocwdGxdplj3fnKSZDVTdp72yEo2/xdJQF35yuTpN8TDatVDyf5OLjuZN/UjKsnSiD1lmHsTFDsMnCt2bcnyE8u5064F6xj85tEeexYv82JnkvK7tjnaDaMwB3IfJvr/mFnHKHu7ViAioCz3Qi4awa3rmploKq8I9v0FIRw9829BGCpodQmbrpNM2T+kh19xjjjeFSzJpwjquPIGRJw71QU6NaU8adDObpvnUFjRFmtGkgjFe1C5HPodfsCk7C7mq5CSzUGCUaOcgd5ik/ha32LBmFKoX0Oq7mgmMOa3PglF6k+gNVMs35479jmJBV3NO459kxFUNmZIUynEYLDiCFBDAWup3ho0jxk2bD+rM7Mk1sdJEeBhGj4Nv9O41Y2nwaF0eGq9Qg9bALfMS2apEOO1HEzlNepextobFz52PA5eTabtJAyQ1xsSbALRdvtM50sLJFG0U353swJWz5bh0n9EnsogQrJiJrt2UPV6LZ/6wyLoMCNToFrMtdw/83L3nQOZ5YMBHLJ5bF9mxPYxDO9qTKzKPoOiIRPBi7Q8mPX8jHL3Luld8aOWKeBp/qstZp0yEf1XQiydtxJBHsVwGnwTd5bT9mt5u0lfniHYkFgtFYbLo1NivaFHB24vfxPHtvSmDwwXMsobBujIJNJWRRwP7DQOKGu4wQ+2U5nMlAudRTcy4TSSWXLkbZKXlxOY5atrSnH3776T25HBnd8qK91vubiKWx0x7k5sSx72T4p6WssiIRYrf8mufjOwKerxo9qR8CNX005U9QCaW+4mKaIQbiUBVrFuWT4ZnuxGI5b5ngurkMB/nmFKmdaZ7VzNYyIDQ==",
            aesKey);

        System.out.println(contentStr);
    }

    // public static void main(String[] args) throws Exception {
    // String rsaPrivateKey =
    // "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBALYgBQ7bEg81mIWGwgF/AfjnBfkl1uXliNxBAyJVMKvDp+AitWoR+49Vn8qWFBAzuW3a5yE7uP8GW0GMHrOu/tLbpg0ipJh0FTtEjENn+mFsfEoQHKsFP1xf9TNLd3m7OecCUoTinkZAnTFQIjavlbF/KR6+T88sZKvEBMiwwoXlAgMBAAECgYBNJk8+Ncr2qmuL1MQGQjkhqSu8mSzpgkxxkRC00IWnnWBV8B4NasS3uTvRY7XoDyEzyhEy4MvDLHwnziWLVEwZaqL9XET+cOtHeHLemeACzmKA9CkiasFWmgK9ABofMY308yFA6gcGQ+JwIfhdl8dcFAn1kKbZtDd6ijVzW+gvWQJBAO5D+SZJlot11R77pgboQOl0vKN6VhKaNcRI5Kum5tUc5oLB419muS7k0UqBC4kBrC7nZbxssOwCJoN/povnFY8CQQDDrlG87bX2n4GOBRsv15pNYv8Pjdr4ZsQOuSuvvRrljKXmGm7D+BZ5yNzMzb+AKbg6wKEGyf8JeH85Zr6BVPtLAkEAjGJ0bECzeNwmhCjVfABgLq1fcBrml/NQdqRUR2cBXtO2ZZiDtXQ67AxZ3EIIX+MiZkhkww5vd78UnioaIRg4tQJAUSFhOlfEbfBMIrnzP67Ahv1YivZhp0PUXdZgSoi5MhtSXUjwS8f8aZniEffsQhMKEzHykYIGf8K53O7AZZb6aQJAIex4xqMipFWjsGxWdpbvWDf+o3enui/MoqB4XQMnnbI6MyCexHn5oVR182iSCNBMK9UwMV6dklq6SflCmTo4vA==";
    // String aesKey = "ipXXXJPDL7Jk89wv";
    // // 组装请求参数
    // String str =
    // "{\"cert_end\":\"2035-01-01\",\"idcard_city_code\":\"110105\",\"education\":4,\"wifi_sensitive\":\"1\",\"channel\":\"3\",\"product_code\":3,\"address_book_sensitive\":\"1\",\"overdue_message_m60\":1,\"if_register\":\"1\",\"credit_id\":\"hsd0001\",\"address_book_num11\":1,\"live_city_code\":\"110105\",\"idcard_no\":\"513022199208167179\",\"annual_income\":300000,\"cert_start\":\"2015-01-01\",\"live_address\":\"北京市海淀区上地家园2号楼一单元102室\",\"mobile\":\"18614098008\",\"contact_operator\":\"1\",\"issued_org_ocr\":\"\",\"house_info\":{\"house\":99},\"if_sign\":\"1\",\"marriage_state\":1,\"address_book_num\":1,\"idcard_address_ocr\":\"北京市海淀区上地家园一号楼一单元102室\",\"name\":\"张某某\",\"Mobile_startime\":\"1\",\"phone_number\":\"1\",\"relations\":[{\"relation_mobile\":\"18614098009\",\"relation_type\":20,\"relation_name\":\"刘某某\"}],\"nation_ocr\":1,\"credit_time\":\"2020-01-01
    // 11:11:11\"}";
    // JSONObject param = new JSONObject();
    // JSONObject content = JSONObject.parseObject(str);
    // System.out.println(content);
    // param.put("source_id", "12qzf_grxfxyd_0002");
    // param.put("event_code", 53);
    //
    //
    // param.put("signature", sign(getMd5(param.getString("content")),
    // rsaPrivateKey, false));
    // // 发送请求
    // String res = HttpUtil.postJson("http://**************:1112/x_engine/credit",
    // param);
    // // 组装响应结果
    // log.info("贷前审核事件接口（风控初筛接口）请求参数：{}，响应结果：{}", param.toJSONString(), res);
    //
    // String contentStr =
    // decryptByAes(JSONObject.parseObject(res).getString("content"), aesKey);
    //
    // System.out.println(contentStr);
    // }




    /**
     * 通用重试方法，带指数退避策略
     *
     * @param apiName 接口名称(用于日志)
     * @param apiCall 接口调用函数
     * @return 接口调用结果
     */
    private String executeWithRetry(String apiName, Supplier<String> apiCall) {
        int retryCount = 0;
        long waitTimeMs = DEFAULT_RETRY_WAIT_MS;
        String response = null;
        long startTime = System.currentTimeMillis();

        while (retryCount <= MAX_RETRY_TIMES) {
            try {
                if (retryCount > 0) {
                    log.info("{}接口第{}次重试", apiName, retryCount);
                }

                response = apiCall.get();

                // 检查是否为特定错误需要重试
                if (isRetryNeeded(response)) {
                    log.warn("{}接口返回需要重试的错误: {}", apiName, response);
                    retryCount++;

                    if (retryCount > MAX_RETRY_TIMES) {
                        break;
                    }

                    // 指数退避策略
                    waitTimeMs = Math.min(waitTimeMs * 2, MAX_WAIT_MS);
                    log.info("{}接口将在{}ms后重试", apiName, waitTimeMs);

                    try {
                        TimeUnit.MILLISECONDS.sleep(waitTimeMs);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("重试等待被中断", e);
                    }
                } else {
                    break;
                }
            } catch (Exception e) {
                log.error("{}接口调用异常", apiName, e);
                retryCount++;

                if (retryCount > MAX_RETRY_TIMES) {
                    break;
                }

                // 指数退避策略
                waitTimeMs = Math.min(waitTimeMs * 2, MAX_WAIT_MS);
                log.info("{}接口将在{}ms后重试", apiName, waitTimeMs);

                try {
                    TimeUnit.MILLISECONDS.sleep(waitTimeMs);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.warn("重试等待被中断", ie);
                }
            }
        }

        long endTime = System.currentTimeMillis();
        log.info("{}接口调用完成, 总耗时: {}ms, 重试次数: {}", apiName, (endTime - startTime), retryCount);

        return response;
    }

    /**
     * 判断是否需要重试
     *
     * @param response 接口响应
     * @return 是否需要重试
     */
    private boolean isRetryNeeded(String response) {
        if (StrUtil.isEmpty(response)) {
            return true;
        }

        try {
            JSONObject result = JSONObject.parseObject(response);
            // 服务器调用异常需要重试
            return "9001".equals(result.getString("status"));
        } catch (Exception e) {
            log.error("解析响应结果异常", e);
            return true;
        }
    }

}
