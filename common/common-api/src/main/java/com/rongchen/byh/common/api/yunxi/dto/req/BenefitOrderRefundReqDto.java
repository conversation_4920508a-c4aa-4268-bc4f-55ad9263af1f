package com.rongchen.byh.common.api.yunxi.dto.req;

import java.io.Serializable;

import lombok.Data;

/**
 * 权益订单退款申请请求
 * API Code: AP103
 */
@Data
public class BenefitOrderRefundReqDto extends YunXiBashReqDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * API编码
     */
    public static final String API_CODE = "AP103";

    /**
     * 权益平台单号
     */
    private String orderNum;

    /**
     * 退款原因
     */
    private String description;

  
}