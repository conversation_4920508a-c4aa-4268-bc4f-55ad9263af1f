package com.rongchen.byh.common.api.idCardVerify.vo;


import lombok.Data;

@Data
public class VerifyVo {

    /**
     * 认证结果
     * 0: 姓名和身份证号一致
     * -1: 姓名和身份证号不一致
     * -2: 非法身份证号（长度、校验位等不正确）
     * -3: 非法姓名（长度、格式等不正确）
     * -4: 证件库服务异常
     * -5: 证件库中无此身份证记录
     * -6: 权威比对系统升级中，请稍后再试
     * -7: 认证次数超过当日限制
     */
    private Integer code;

    /**
     * 认证结果描述
     */
    private String msg;
}
