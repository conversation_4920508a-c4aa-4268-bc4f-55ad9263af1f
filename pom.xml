<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns="http://maven.apache.org/POM/4.0.0"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.rongchen.byh</groupId>
    <artifactId>byh</artifactId>
    <version>1.0.0</version>
    <name>byh</name>
    <packaging>pom</packaging>

    <properties>
        <spring-boot.version>2.7.10</spring-boot.version>
        <spring-boot-admin.version>2.7.10</spring-boot-admin.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <java.version>1.8</java.version>
        <application.name>byh</application.name>
        <!-- 工具库版本 -->
        <qlexpress.version>3.3.4</qlexpress.version>
        <joda-time.version>2.10.13</joda-time.version>
        <guava.version>20.0</guava.version>
        <commons-io.version>2.11.0</commons-io.version>
        <commons-collections4.version>4.4</commons-collections4.version>
        <common-csv.version>1.8</common-csv.version>
        <poi-ooxml.version>5.2.0</poi-ooxml.version>
        <hutool.version>5.8.23</hutool.version>
        <jjwt.version>0.12.3</jjwt.version>
        <fastjson.version>1.2.83</fastjson.version>
        <bean.query.version>1.1.5</bean.query.version>
        <caffeine.version>2.9.3</caffeine.version>
        <lombok.version>1.18.20</lombok.version>
        <hibernate-validator.version>6.2.0.Final</hibernate-validator.version>
        <redisson.version>3.15.4</redisson.version>
        <minio.version>8.4.5</minio.version>
        <qdox.version>2.0.0</qdox.version>
        <knife4j.version>4.5.0</knife4j.version>
        <jackson.version>2.15.3</jackson.version>
        <itext.version>7.1.15</itext.version>
        <!-- 数据库工具版本 -->
        <druid.version>1.2.16</druid.version>
        <mybatisplus.version>3.5.4.1</mybatisplus.version>
        <pagehelper.version>1.4.7</pagehelper.version>
        <groovy.version>3.0.19</groovy.version>
        <xxljob.version>2.3.0</xxljob.version>
        <okhttp.version>3.6.0</okhttp.version>
        <thumbnailator.version>0.4.8</thumbnailator.version>
    </properties>

    <modules>
        <module>application-webadmin</module>
        <module>common</module>
        <module>application-app</module>
        <module>application-sale</module>
    </modules>

    <dependencies>
        <!--web
        模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!--server-api-->
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
        </dependency>
        <!-- 日志模块 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
        </dependency>
        <!-- aop模块 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <!-- 缓存模块 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>
        <!--配置文件处理器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <!--加密-->
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-crypto</artifactId>
        </dependency>
        <!--监控-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <!--监控客户端-->
        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
            <version>${spring-boot-admin.version}</version>
        </dependency>
        <!-- Prometheus 指标 -->
        <dependency>
            <artifactId>micrometer-registry-prometheus</artifactId>
            <groupId>io.micrometer</groupId>
        </dependency>
        <!-- api参数验证 -->
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>${hibernate-validator.version}</version>
        </dependency>
        <!--Lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <!--测试依赖-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <artifactId>aviator</artifactId>
            <groupId>com.googlecode.aviator</groupId>
            <version>5.4.3</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.0</version>
                <configuration>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                    <target>${maven.compiler.target}</target>
                    <source>${maven.compiler.source}</source>
                    <encoding>UTF-8</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>