#!/bin/bash
#
# application-webadmin 服务启动脚本
# 作者: XuYu
# 创建时间: 2025-03-19
#

# 定义应用名称和相关变量
APP_NAME="application-webadmin"
APP_JAR_NAME="application-webadmin.jar"

JAR_PATH="./$APP_JAR_NAME"
LOG_DIR="./zzlogs/$APP_NAME"
LOG_FILE="$LOG_DIR/$APP_NAME.log"
PID_FILE="/app/qyc/pid/$APP_NAME.pid"

# Java 9 兼容的JVM参数
JVM_OPTS="-Xms1024m -Xmx1024m -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=256m \
-XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:G1HeapRegionSize=16m \
-XX:InitiatingHeapOccupancyPercent=45 -XX:+HeapDumpOnOutOfMemoryError \
-XX:HeapDumpPath=${LOG_DIR}/heapdump_$(date +%Y%m%d%H%M%S).hprof \
-XX:+DisableExplicitGC \
-Xlog:gc*=info:file=${LOG_DIR}/gc.log:time,uptime,level,tags:filecount=5,filesize=20m \
-XX:+ExplicitGCInvokesConcurrent \
-XX:+ParallelRefProcEnabled \
-XX:ErrorFile=${LOG_DIR}/hs_err_pid%p.log \
-Djava.awt.headless=true -Dfile.encoding=UTF-8 \
-Djava.security.egd=file:/dev/./urandom \
-XX:ThreadStackSize=256k -XX:MaxDirectMemorySize=512m"

# 测试环境
JAVA_OPTS="-Dfile.encoding=utf-8 -Dspring.profiles.active=test"
# 生产环境
#JAVA_OPTS="-Dfile.encoding=utf-8 -Dspring.profiles.active=prod"

# 确保必要的目录存在
mkdir -p $LOG_DIR
mkdir -p "$(dirname $PID_FILE)"

# 打印彩色输出函数
print_green() {
  echo -e "\033[32m$1\033[0m"
}

print_red() {
  echo -e "\033[31m$1\033[0m"
}

print_yellow() {
  echo -e "\033[33m$1\033[0m"
}

# 检查服务状态
check_status() {
  # 方法1: 通过PID文件检查
  if [ -f $PID_FILE ]; then
    PID=$(cat $PID_FILE)
    if kill -0 $PID > /dev/null 2>&1; then
      # 进一步确认这个PID确实是我们的应用进程
      if ps -p $PID -o cmd= | grep -q "$APP_JAR_NAME"; then
        return 0  # 服务正在运行，且确认是我们的应用
      else
        print_yellow "PID文件存在但进程($PID)不是$APP_NAME，正在清理..."
        rm -f $PID_FILE  # PID不是我们的应用，删除无效的PID文件
        # 继续检查，看是否有同名应用在运行
      fi
    else
      rm -f $PID_FILE  # 移除无效的PID文件
      # 继续检查，看是否有同名应用在运行
    fi
  fi
  
  # 方法2: 即使PID文件不存在或无效，也通过ps检查是否有应用运行
  # 使用pgrep避免匹配到grep自身
  APP_PID=$(pgrep -f "$APP_JAR_NAME" 2>/dev/null)
  if [ -n "$APP_PID" ]; then
    print_yellow "发现 $APP_NAME 正在运行(PID=$APP_PID)，但PID文件不存在或无效"
    # 写入PID文件
    echo $APP_PID > $PID_FILE
    return 0  # 服务正在运行
  fi
  
  return 1  # 服务未运行
}

# 启动服务
start() {
  print_yellow "正在检查 $APP_NAME 状态..."
  
  if check_status; then
    print_yellow "$APP_NAME 已经在运行中，PID: $(cat $PID_FILE)"
    print_yellow "先停止运行中的服务..."
    stop
  fi
  
  print_yellow "正在启动 $APP_NAME..."
  
  # 检查JAR文件是否存在
  if [ ! -f "$JAR_PATH" ]; then
    print_red "错误: JAR文件不存在: $JAR_PATH"
    return 1
  fi

  print_yellow "使用命令: java $JVM_OPTS $JAVA_OPTS -jar $JAR_PATH"
  print_yellow "日志目录: $(pwd)/$LOG_DIR"
  
  # 启动应用 - 使用带时间戳的日志文件名
  # 添加启动时间戳分隔符
  echo -e "\n\n========== 应用启动 - $(date '+%Y-%m-%d %H:%M:%S') ==========\n" >> $LOG_FILE
  # 启动应用 - 所有输出追加到主日志文件
  nohup java $JVM_OPTS $JAVA_OPTS -jar $JAR_PATH >> $LOG_FILE 2>&1 &
  echo $! > $PID_FILE
  
  # 验证服务是否成功启动
  sleep 5
  if check_status; then
    print_green "$APP_NAME 启动成功，PID: $(cat $PID_FILE)"
    print_yellow "请查看启动日志: tail -f $LOG_FILE"
  else
    print_red "$APP_NAME 启动失败，请检查启动日志: $LOG_FILE"
    return 1
  fi
}

# 停止服务
stop() {
  print_yellow "正在停止 $APP_NAME..."
  
  # 再次检查状态，确保找到所有可能的进程
  check_status
  
  if [ ! -f $PID_FILE ]; then
    print_yellow "$APP_NAME 未运行"
    return 0
  fi
  
  PID=$(cat $PID_FILE)
  
  # 尝试优雅停止 - 发送SIGTERM信号
  print_yellow "正在尝试优雅停止应用..."
  kill -15 $PID
  
  # 等待服务停止，最多等待30秒
  for i in {1..30}; do
    if kill -0 $PID > /dev/null 2>&1; then
      print_yellow "等待服务停止中($i/30)..."
      sleep 1
    else
      print_green "$APP_NAME 已停止"
      rm -f $PID_FILE
      return 0
    fi
  done
  
  # 强制终止进程
  print_yellow "服务在30秒内未能停止，正在强制终止..."
  kill -9 $PID
  
  sleep 2
  if kill -0 $PID > /dev/null 2>&1; then
    print_red "无法停止 $APP_NAME，请手动检查"
    return 1
  else
    print_green "$APP_NAME 已停止"
    rm -f $PID_FILE
    return 0
  fi
}

# 重启服务
restart() {
  print_yellow "正在重启 $APP_NAME..."
  stop
  start
}

# 查看服务状态
status() {
  if check_status; then
    print_green "$APP_NAME 正在运行，PID: $(cat $PID_FILE)"
    
    # 显示更详细的进程信息
    PID=$(cat $PID_FILE)
    print_yellow "进程详情:"
    ps -p $PID -o pid,ppid,user,stat,start,time,cmd
  else
    print_red "$APP_NAME 未运行"
  fi
}

# 查看服务日志
logs() {
  if [ -f "$LOG_FILE" ]; then
    tail -n ${2:-100} -f $LOG_FILE
  else
    print_yellow "指定的日志文件不存在: $LOG_FILE"
    print_yellow "尝试查找可能的日志文件位置..."
    
    # 尝试其他可能的日志位置
    POSSIBLE_LOG_FILES=(
      "./zzlogs/$APP_NAME/$APP_NAME.log"
      "./zzlogs/application-webadmin.log"
    )
    
    for LOG in "${POSSIBLE_LOG_FILES[@]}"; do
      if [ -f "$LOG" ]; then
        print_green "找到日志文件: $LOG"
        tail -n ${2:-100} -f "$LOG"
        return 0
      fi
    done
    
    print_red "无法找到任何日志文件"
  fi
}

# 脚本用法说明
usage() {
  echo "用法: $0 {start|stop|restart|status|logs}"
  echo "  start   - 启动服务"
  echo "  stop    - 停止服务"
  echo "  restart - 重启服务"
  echo "  status  - 查看服务状态"
  echo "  logs [行数] - 查看日志（默认100行）"
  echo "  无参数时默认执行start命令"
}

# 主函数
case "$1" in
  start)
    start
    ;;
  stop)
    stop
    ;;
  restart)
    restart
    ;;
  status)
    status
    ;;
  logs)
    logs "$@"
    ;;
  help)
    usage
    ;;
  *)
    # 默认执行start
    start
    ;;
esac

exit 0 