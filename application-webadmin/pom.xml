<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
        <groupId>com.rongchen.byh</groupId>
        <artifactId>byh</artifactId>
        <version>1.0.0</version>
	</parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>application-webadmin</artifactId>
    <version>1.0.0</version>
    <name>application-webadmin</name>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- 业务组件依赖 -->
        <dependency>
            <groupId>com.rongchen.byh</groupId>
            <artifactId>common-satoken</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.rongchen.byh</groupId>
            <artifactId>common-ext</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.rongchen.byh</groupId>
            <artifactId>common-redis</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.rongchen.byh</groupId>
            <artifactId>common-log</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.rongchen.byh</groupId>
            <artifactId>common-minio</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.rongchen.byh</groupId>
            <artifactId>common-sequence</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.rongchen.byh</groupId>
            <artifactId>common-swagger</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.rongchen.byh</groupId>
            <artifactId>common-dict</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.rongchen.byh</groupId>
            <artifactId>common-api</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.rongchen.byh</groupId>
            <artifactId>common-xxljob</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
    </build>
</project>
