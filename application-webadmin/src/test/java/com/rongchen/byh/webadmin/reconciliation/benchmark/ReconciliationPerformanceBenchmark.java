package com.rongchen.byh.webadmin.reconciliation.benchmark;

import com.rongchen.byh.webadmin.reconciliation.job.ReconciliationJob;
import com.rongchen.byh.webadmin.upms.model.DzChannelReconConfigEntity;
import com.rongchen.byh.webadmin.upms.service.DzChannelReconConfigService;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.util.StopWatch;

/**
 * 对账系统性能基准测试
 * 用于建立性能基线和验证优化效果
 */
@SpringBootTest
@ActiveProfiles("test")
public class ReconciliationPerformanceBenchmark {

    private static final Logger logger = LoggerFactory.getLogger(ReconciliationPerformanceBenchmark.class);

    @Autowired
    private ReconciliationJob reconciliationJob;

    @Autowired
    private DzChannelReconConfigService dzChannelReconConfigService;

    private List<DzChannelReconConfigEntity> testConfigs;
    private LocalDate testDate;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testDate = LocalDate.now().minusDays(1);
        testConfigs = createTestConfigs();
        logger.info("基准测试初始化完成，测试日期: {}, 配置数量: {}", testDate, testConfigs.size());
    }

    /**
     * 单日对账性能基准测试
     */
    @Test
    void benchmarkDailyReconciliation() {
        logger.info("=== 开始单日对账性能基准测试 ===");

        StopWatch stopWatch = new StopWatch("单日对账基准测试");
        stopWatch.start("总体执行时间");

        try {
            // 执行单日对账
            reconciliationJob.processDailyReconciliationForConfigs(
                    testDate, testConfigs, null);

            stopWatch.stop();

            // 输出性能指标
            logPerformanceMetrics("单日对账", stopWatch, testConfigs.size());

        } catch (Exception e) {
            stopWatch.stop();
            logger.error("单日对账基准测试执行失败", e);
            throw e;
        }

        logger.info("=== 单日对账性能基准测试完成 ===");
    }


    /**
     * 内存使用基准测试
     */
    @Test
    void benchmarkMemoryUsage() {
        logger.info("=== 开始内存使用基准测试 ===");

        Runtime runtime = Runtime.getRuntime();

        // 执行GC获取准确的内存基线
        System.gc();
        long memoryBefore = runtime.totalMemory() - runtime.freeMemory();

        StopWatch stopWatch = new StopWatch("内存使用基准测试");
        stopWatch.start("执行时间");

        try {
            reconciliationJob.processDailyReconciliationForConfigs(
                    testDate, testConfigs, null);

            stopWatch.stop();

            // 计算内存使用
            long memoryAfter = runtime.totalMemory() - runtime.freeMemory();
            long memoryUsed = memoryAfter - memoryBefore;

            logger.info("内存使用基准测试结果:");
            logger.info("  执行时间: {} ms", stopWatch.getTotalTimeMillis());
            logger.info("  内存使用: {} MB", memoryUsed / (1024 * 1024));
            logger.info("  执行前内存: {} MB", memoryBefore / (1024 * 1024));
            logger.info("  执行后内存: {} MB", memoryAfter / (1024 * 1024));
            logger.info("  最大可用内存: {} MB", runtime.maxMemory() / (1024 * 1024));

        } catch (Exception e) {
            stopWatch.stop();
            logger.error("内存使用基准测试执行失败", e);
            throw e;
        }

        logger.info("=== 内存使用基准测试完成 ===");
    }

    /**
     * 数据库操作性能基准测试
     */
    @Test
    void benchmarkDatabaseOperations() {
        logger.info("=== 开始数据库操作性能基准测试 ===");

        // 这里可以添加针对具体数据库操作的性能测试
        // 如数据加载、差异保存、摘要保存等操作的独立性能测试

        StopWatch stopWatch = new StopWatch("数据库操作基准测试");

        // 测试数据库连接和基本查询性能
        stopWatch.start("配置查询");
        List<DzChannelReconConfigEntity> configs = dzChannelReconConfigService.list();
        stopWatch.stop();

        logger.info("数据库操作基准测试结果:");
        logger.info("  配置查询时间: {} ms", stopWatch.getLastTaskTimeMillis());
        logger.info("  查询到配置数量: {}", configs.size());

        logger.info("=== 数据库操作性能基准测试完成 ===");
    }

    /**
     * 创建测试配置
     */
    private List<DzChannelReconConfigEntity> createTestConfigs() {
        List<DzChannelReconConfigEntity> configs = new ArrayList<>();

        // 创建不同类型的测试配置
        String[] channelCodes = { "TEST_CHANNEL_1", "TEST_CHANNEL_2", "TEST_CHANNEL_3" };
        String[] transactionTypes = { "LOAN", "REPAYMENT", "CREDIT" };

        for (String channelCode : channelCodes) {
            for (String transactionType : transactionTypes) {
                DzChannelReconConfigEntity config = new DzChannelReconConfigEntity();
                config.setChannelCode(channelCode);
                config.setTransactionType(transactionType);
                config.setReconEnabled(true);
                configs.add(config);
            }
        }

        return configs;
    }

    /**
     * 记录性能指标
     */
    private void logPerformanceMetrics(String testName, StopWatch stopWatch, int configCount) {
        long totalTimeMs = stopWatch.getTotalTimeMillis();
        double avgTimePerConfig = configCount > 0 ? (double) totalTimeMs / configCount : 0;

        logger.info("=== {} 性能指标 ===", testName);
        logger.info("  总执行时间: {} ms ({} 秒)", totalTimeMs, totalTimeMs / 1000.0);
        logger.info("  处理配置数量: {}", configCount);
        logger.info("  平均每配置处理时间: {:.2f} ms", avgTimePerConfig);

        if (configCount > 0) {
            logger.info("  处理吞吐量: {:.2f} 配置/秒", configCount * 1000.0 / totalTimeMs);
        }

        // 性能评级
        String performanceRating = performanceRating(avgTimePerConfig);
        logger.info("  性能评级: {}", performanceRating);

        logger.info("=== {} 性能指标结束 ===", testName);
    }

    /**
     * 根据平均处理时间给出性能评级
     */
    private String performanceRating(double avgTimeMs) {
        if (avgTimeMs < 1000) {
            return "优秀 (<1秒/配置)";
        } else if (avgTimeMs < 5000) {
            return "良好 (1-5秒/配置)";
        } else if (avgTimeMs < 10000) {
            return "一般 (5-10秒/配置)";
        } else {
            return "需要优化 (>10秒/配置)";
        }
    }

    /**
     * 生成性能基准报告
     */
    public void generateBaselineReport() {
        logger.info("=== 生成性能基准报告 ===");

        // 运行所有基准测试
        benchmarkDailyReconciliation();
        benchmarkMemoryUsage();
        benchmarkDatabaseOperations();

        logger.info("=== 性能基准报告生成完成 ===");
        logger.info("请保存此日志作为后续优化效果对比的基线数据");
    }
}