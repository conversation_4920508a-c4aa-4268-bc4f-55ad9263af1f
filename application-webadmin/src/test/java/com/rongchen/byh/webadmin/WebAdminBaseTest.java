package com.rongchen.byh.webadmin;

import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

/**
 * 集成测试的基类。
 * <p>
 * 配置了Spring Boot测试环境，可以被具体的集成测试类继承。
 */
@ExtendWith(SpringExtension.class) // JUnit 5 Spring支持
@SpringBootTest(classes = WebAdminApplication.class) // 加载Spring Boot上下文
// 如果您有application-test.properties或application-test.yml，可以使用@ActiveProfiles("test")
@ActiveProfiles("test")
public abstract class WebAdminBaseTest {
    // 这里可以放置所有集成测试通用的配置或辅助方法
    // 例如，通用的 @MockBean 定义（但不推荐，最好在具体测试类中按需 mock）
    // 或者一些测试数据的加载逻辑等
}