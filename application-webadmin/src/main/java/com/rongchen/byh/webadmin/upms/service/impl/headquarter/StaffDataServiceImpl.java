package com.rongchen.byh.webadmin.upms.service.impl.headquarter;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.page.PageMethod;
import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.MyPageUtil;
import com.rongchen.byh.webadmin.upms.dao.StaffDataMapper;
import com.rongchen.byh.webadmin.upms.dto.Headquarters.StaffDataDto;
import com.rongchen.byh.webadmin.upms.model.StaffData;
import com.rongchen.byh.webadmin.upms.service.headquarter.StaffDataService;
import com.rongchen.byh.webadmin.upms.vo.order.UserStaffVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 销售员工表(StaffData)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-27 14:59:17
 */
@Service
public class StaffDataServiceImpl implements StaffDataService {

    @Resource
    private StaffDataMapper staffDataMapper;

    @Override
    public ResponseResult<MyPageData<StaffData>> staffDataList(StaffData staffData, MyPageParam pageParam) {
        if (pageParam != null){
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        }
        List<StaffData> list = staffDataMapper.staffDataList(staffData);
        return ResponseResult.success(MyPageUtil.makeResponseData(list, StaffData.class));
    }


    /**
     * 新增数据
     *
     * @param staffData 实例对象
     * @return 实例对象
     */
    @Override
    public Boolean insert(StaffData staffData) {
        LambdaQueryWrapper<StaffData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StaffData::getUserName, staffData.getUserName())
                .or()
                .eq(StaffData::getMobile, staffData.getMobile())
                .or()
                .eq(StaffData::getInviteCode, staffData.getInviteCode());
        Long l = staffDataMapper.selectCount(queryWrapper);
        if (l > 0) {return false;}
        //随机生成四位数字
        staffData.setInviteCode(String.valueOf((int) (Math.random() * 9000 + 1000)));
        return staffDataMapper.insert(staffData) > 0;
    }

    /**
     * 修改数据
     *
     * @param staffData 实例对象
     * @return 实例对象
     */
    @Override
    public Boolean update(StaffData staffData) {
        LambdaQueryWrapper<StaffData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StaffData::getUserName, staffData.getUserName())
                .eq(StaffData::getMobile, staffData.getMobile())
                .eq(StaffData::getId, staffData.getId())
                .eq(StaffData::getSysUserId, staffData.getSysUserId());
        Long l = staffDataMapper.selectCount(queryWrapper);
        if (l <= 0) {
            StaffData data = new StaffData();
            data.setUserName(staffData.getUserName());
            data.setMobile(staffData.getMobile());
            data.setUpdateTime(new Date());
            data.setId(staffData.getId());
            data.setSysUserId(staffData.getSysUserId());
            return staffDataMapper.updateById(data) > 0;
        }
        return false;
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public Boolean deleteById(Long id) {
        return staffDataMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean freezeStaffStatus(StaffData dto) {
        LambdaQueryWrapper<StaffData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StaffData::getId, dto.getId())
                .eq(StaffData::getMobile, dto.getMobile())
                .eq(StaffData::getInviteCode, dto.getInviteCode());
        StaffData staffData = staffDataMapper.selectOne(queryWrapper);
        if (staffData != null) {
            StaffData data = new StaffData();
            data.setStaffStatus(staffData.getStaffStatus() > 0 ? 0 : 1);
            data.setInviteFlag(staffData.getInviteFlag() > 0 ? 0 : 1);
            data.setUpdateTime(new Date());
            return staffDataMapper.update(data, queryWrapper) > 0;
        }
        return false;
    }

    /**
     * 修改销售对应订单
     * @param dto
     * @return
     */
    @Override
    public Integer ordersStaff(StaffDataDto dto) {
        List<UserStaffVo> userStaffVos = staffDataMapper.selectStaffList(dto.getOldStaffId());
        if (userStaffVos != null && userStaffVos.size() > 0) {
            int i = staffDataMapper.ordersStaff(dto);
            return i > 0 ? 1 : 2;
        }
        return 0;
    }

    @Override
    public List<StaffData> staffList() {
        return staffDataMapper.selectList(null);
    }
}
