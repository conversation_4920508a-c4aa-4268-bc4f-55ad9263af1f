package com.rongchen.byh.webadmin.reconciliation.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 对账汇总列表vo
 * @date 2025/5/23 09:55:10
 */
@Data
public class ReconciliationSummaryListVo {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "对账批次ID")
    private String reconBatchId;

    @Schema(description = "对账的业务处理日期")
    private Date processingDate;

    @Schema(description = "渠道编码")
    private String channelCode;

    @Schema(description = "渠道名")
    private String channelName;

    @Schema(description = "业务交易类型")
    private String transactionType;

    @Schema(description = "业务交易类型名")
    private String transactionTypeName;

    @Schema(description = "我方参与对账的总笔数")
    private Integer ourRecordsCount;

    @Schema(description = "资方参与对账的总笔数")
    private Integer partnerRecordsCount;

    @Schema(description = "我方对账总金额")
    private BigDecimal ourTotalAmount;

    @Schema(description = "资方对账总金额")
    private BigDecimal partnerTotalAmount;

    @Schema(description = "差异总笔数")
    private Integer diffRecordsCount;

    @Schema(description = "对账结果状态")
    private String reconStatus;

    @Schema(description = "对账结果状态名")
    private String reconStatusName;

    @Schema(description = "此摘要记录的生成/更新时间")
    private Date summaryTime;
    private Integer count;
    private String remark;
}
