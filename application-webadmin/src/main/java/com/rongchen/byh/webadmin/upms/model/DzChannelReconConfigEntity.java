package com.rongchen.byh.webadmin.upms.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**

 * 项目名称：byh_java
 * 文件名称: DzChannelReconConfigEntity
 * 创建时间: 2025-05-22 15:04
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.model
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
/**
 * 渠道对账与同步配置表 
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "`dz_channel_recon_config`")
public class DzChannelReconConfigEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 渠道编码
     */
    @TableField(value = "`channel_code`")
    private String channelCode;

    /**
     * 交易类型 (LOAN, REPAYMENT, CREDIT, etc.)
     */
    @TableField(value = "`transaction_type`")
    private String transactionType;

    /**
     * 数据源类型 (PARTNER_SIDE, OUR_SIDE), NULL表示对账任务的通用配置
     */
    @TableField(value = "`data_source_type`")
    private String dataSourceType;

    /**
     * 此配置是否用于对账流程
     */
    @TableField(value = "`recon_enabled`")
    private Boolean reconEnabled;

    /**
     * 此配置是否用于SFTP数据同步
     */
    @TableField(value = "`sftp_sync_enabled`")
    private Boolean sftpSyncEnabled;

    /**
     * 执行优先级 (数字越小越高)
     */
    @TableField(value = "`priority`")
    private Integer priority;

    /**
     * 额外配置参数 (JSON格式)
     */
    @TableField(value = "`additional_config_json`")
    private String additionalConfigJson;

    /**
     * 备注
     */
    @TableField(value = "`description`")
    private String description;

    /**
     * 创建时间
     */
    @TableField(value = "`create_time`",fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "`update_time`",fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}