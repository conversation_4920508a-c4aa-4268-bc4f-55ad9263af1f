package com.rongchen.byh.webadmin.reconciliation.dto;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 重新对账dto
 * @date 2025/5/29 11:51:27
 */
@Data
public class ReReconciliationDto {
    @Schema(description = "差异详情id")
    private Long id;

    /**
     * 批次ID (可选)
     */
    @Schema(description = "批次ID(可选)")
    @Hidden
    private String batchId;

    /**
     * 交易类型(必填)
     */
    @Schema(description = "交易类型(必填) LOAN=放款, REPAYMENT=还款, CREDIT=授信")
    @Hidden
    private String transactionType;

    /**
     * 我司订单号
     */
    @Schema(description = "我司订单号(可选)")
    @Hidden
    private String myOrderNo;

    /**
     * 我方授信号
     */
    @Schema(description = "我方授信号(可选)")
    @Hidden
    private String myCreditNo;

    /**
     * 还款流水号
     */
    @Schema(description = "还款流水号(可选)")
    @Hidden
    private String myRepaymentId;
}
