package com.rongchen.byh.webadmin.upms.dto.bill;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 账单列表dto
 * @date 2025/1/23 10:54:06
 */
@Data
public class BillListDto {
    @Schema(description = "支用订单id")
    private Long disburseOrderId;

    @Schema(description = "账单号")
    private Long billId;

    @Schema(description = "客户名称")
    private String userName;

    @Schema(description = "客户手机号")
    private String mobile;

    @Schema(description = "产品id")
    private Long productId;

    @Schema(description = "订单状态 100 授信中  200 授信失败   300 放款中  400  放款失败   500 还款中   600 已结清")
    private Integer status;

    @Schema(description = "账单状态：已还，未还，部分还")
    private String combinedFlag;

    @Schema(description = "开始时间")
    private String createStartTime;

    @Schema(description = "结束时间")
    private String createEndTime;

    @Schema(description = "还款开始时间")
    private String repayStartTime;

    @Schema(description = "还款结束时间")
    private String repayEndTime;

    @Schema(description = "最近还款开始时间")
    private String latestRepayStartTime;

    @Schema(description = "最近还款结束时间")
    private String latestRepayEndTime;

    @Schema(description = "账单取消开始时间")
    private String cancelStartTime;

    @Schema(description = "账单取消结束时间")
    private String cancelEndTime;

    @Schema(description = "系统用户id")
    private Long sysUserId;

    @Schema(description = "系统用户名称")
    private String sysRoleName;

    @Schema(description = "门店名称")
    private String  storeName;

    @Schema(description = "销售员名称")
    private String  saleUserName;

    @Schema(description = "小组名称")
    private String  groupName;

    @Schema(description = "资方名称")
    private String capitalName;

    @Schema(description = "渠道id")
    private String channelId;

    @Schema(description = "代偿类型 1 代偿 2 回购")
    private Integer compensateStatus;
}
