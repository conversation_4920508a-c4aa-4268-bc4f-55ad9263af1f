package com.rongchen.byh.webadmin.upms.dto.cp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName DisburseOrderListDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/1/23 11:31
 * @Version 1.0
 **/
@Data
public class DisburseDataRequestListDto {

    @Schema(description = "订单号")
    private Long disburseId;

    @Schema(description = "客户名称")
    private String userName;

    @Schema(description = "客户手机号")
    private String mobile;

    @Schema(description = "身份证号")
    private String idNumber;

    @Schema(description = "审核状态 100-授信中 200-授信失败 300-放款中 400-放款失败 500-还款中 600-已结清")
    private Integer auditStatus;

    @Schema(description = "开始时间")
    private String timeStart;

    @Schema(description = "结束时间")
    private String timeEnd;

    @Schema(description = "风险等级")
    private String riskLevel;

    @Schema(description = "渠道ID")
    private String channelId;

    @Schema(description = "渠道名称")
    private String channeName;

    @Schema(description = "门店名称")
    private String  storeName;

    @Schema(description = "销售员名称")
    private String  saleUserName;

    @Schema(description = "小组名称")
    private String  groupName;

    @Schema(description = "系统用户id")
    private Long sysUserId;

    @Schema(description = "系统用户名称")
    private String sysRoleName;

    @Schema(description = "放款成功开始时间")
    private String timeLoanTimeStart;

    @Schema(description = "放款成功结束时间")
    private String timeLoanTimeEnd;

    @Schema(description = "注册开始时间")
    private String timeRegisterTimeStart;

    @Schema(description = "注册结束时间")
    private String timeRegisterTimeEnd;


}
