package com.rongchen.byh.webadmin.upms.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rongchen.byh.webadmin.reconciliation.dto.ReconciliationSummeryListDto;
import com.rongchen.byh.webadmin.reconciliation.vo.ReconciliationSummaryListVo;
import com.rongchen.byh.webadmin.upms.model.DzReconciliationSummaryEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 项目名称：byh_java
 * 文件名称: DzReconciliationSummaryMapper
 * 创建时间: 2025-05-26 10:50
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.dao
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
public interface DzReconciliationSummaryMapper extends BaseMapper<DzReconciliationSummaryEntity> {

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(DzReconciliationSummaryEntity record);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(DzReconciliationSummaryEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(DzReconciliationSummaryEntity record);

    int updateBatch(@Param("list") List<DzReconciliationSummaryEntity> list);

    int updateBatchSelective(@Param("list") List<DzReconciliationSummaryEntity> list);

    int batchInsert(@Param("list") List<DzReconciliationSummaryEntity> list);

    int batchInsertOrUpdate(@Param("list") List<DzReconciliationSummaryEntity> list);

    List<ReconciliationSummaryListVo> selectAll(ReconciliationSummeryListDto dto);
}