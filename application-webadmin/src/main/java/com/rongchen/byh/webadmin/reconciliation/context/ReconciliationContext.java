package com.rongchen.byh.webadmin.reconciliation.context;

import java.time.LocalDate;
import lombok.Builder;
import lombok.Getter;
import org.springframework.context.ApplicationContext;
// import org.springframework.context.ApplicationContext; // 可选，如果需要访问Spring Beans

/**
 * 对账流程上下文信息
 */
@Getter
@Builder
public class ReconciliationContext {

    /**
     * 当前对账批次的唯一ID
     */
    private String batchId;

    /**
     * 对账的处理日期 (通常是T+1中的T日)
     */
    private LocalDate processingDate;

    /**
     * 当前处理的渠道编码
     */
    private String channelCode;

    /**
     * 当前处理的交易类型 (例如 "LOAN", "REPAYMENT")
     */
    private String transactionType;

    // 如果在对账策略或数据提供者中需要访问Spring容器管理的其他bean，可以取消下面的注释
    /**
     * Spring应用上下文，用于获取其他Beans (可选)
     */
    private ApplicationContext springContext;

}