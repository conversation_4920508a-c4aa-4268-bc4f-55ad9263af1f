package com.rongchen.byh.webadmin.upms.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rongchen.byh.webadmin.upms.model.DzChannelReconConfigEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**

 * 项目名称：byh_java
 * 文件名称: DzChannelReconConfigMapper
 * 创建时间: 2025-05-22 15:04
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.dao
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
public interface DzChannelReconConfigMapper extends BaseMapper<DzChannelReconConfigEntity> {
    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(DzChannelReconConfigEntity record);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(DzChannelReconConfigEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(DzChannelReconConfigEntity record);

    int updateBatch(@Param("list") List<DzChannelReconConfigEntity> list);

    int updateBatchSelective(@Param("list") List<DzChannelReconConfigEntity> list);

    int batchInsert(@Param("list") List<DzChannelReconConfigEntity> list);

    int batchInsertOrUpdate(@Param("list") List<DzChannelReconConfigEntity> list);
}