package com.rongchen.byh.webadmin.upms.controller.headquarters;


import com.rongchen.byh.common.core.annotation.MyRequestBody;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.webadmin.upms.dto.Headquarters.HeadquartersDto;
import com.rongchen.byh.webadmin.upms.service.headquarter.HeadquartersService;
import com.rongchen.byh.webadmin.upms.vo.headquarters.HeadquartersVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Tag(name = "总部信息相关接口")
@RestController
@RequestMapping("/admin/upms/headquarters")
public class HeadquartersController {
    @Resource
    HeadquartersService headquartersService;


    @Operation(summary = "总部信息-列表-查询")
    @PostMapping("/list")
    public ResponseResult<MyPageData<HeadquartersVo>> list(
            @MyRequestBody HeadquartersDto dto,
            @MyRequestBody MyPageParam pageParam) {
        return headquartersService.headquartersList(dto, pageParam);
    }

    @Operation(summary = "总部信息- 新增门店小组销售")
    @PostMapping("/insertHeadquarters")
    public ResponseResult<Void> insertHeadquarters(@MyRequestBody HeadquartersDto dto) {
        headquartersService.insertHeadquarters(dto);
        return ResponseResult.success();
    }

    @Operation(summary = "总部信息- 修改门店小组销售")
    @PostMapping("/updateHeadquarters")
    public ResponseResult<Void> updateHeadquarters(@MyRequestBody HeadquartersDto dto) {
        Boolean updated = headquartersService.updateHeadquarters(dto);
        if (updated) {
            return ResponseResult.success();
        }
        return ResponseResult.error(ErrorCodeEnum.FAIL);
    }
}
