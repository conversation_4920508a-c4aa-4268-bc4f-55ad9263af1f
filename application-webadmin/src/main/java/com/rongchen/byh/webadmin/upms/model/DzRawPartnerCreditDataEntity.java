package com.rongchen.byh.webadmin.upms.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**

 * 项目名称：byh_java
 * 文件名称: DzRawPartnerCreditDataEntity
 * 创建时间: 2025-05-23 11:51
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.model
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "`dz_raw_partner_credit_data`")
public class DzRawPartnerCreditDataEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * SFTP同步批次ID
     */
    @TableField(value = "`sync_batch_id`")
    private String syncBatchId;

    /**
     * 渠道编码 (如 "XHY")
     */
    @TableField(value = "`channel_code`")
    private String channelCode;

    /**
     * 原始文件对应的业务处理日期 (T日)
     */
    @TableField(value = "`processing_date`")
    private Date processingDate;

    /**
     * 交易类型 (固定为LOAN)
     */
    @TableField(value = "`transaction_type`")
    private String transactionType;

    /**
     * 原始文件名 (可选)
     */
    @TableField(value = "`original_filename`")
    private String originalFilename;

    /**
     * 合作方授信单号 (XHY: creditNo)
     */
    @TableField(value = "`partner_credit_no`")
    private String partnerCreditNo;

    /**
     * 授信日期
     */
    @TableField(value = "`credit_date`")
    private Date creditDate;

    /**
     * 授信金额
     */
    @TableField(value = "`credit_amount`")
    private BigDecimal creditAmount;

    /**
     * 授信状态
     */
    @TableField(value = "`credit_status`")
    private String creditStatus;

    /**
     * 存储所有其他原始字段的JSON对象
     */
    @TableField(value = "`extended_fields_json`")
    private String extendedFieldsJson;

    /**
     * 数据加载到此表的时间
     */
    @TableField(value = "`load_time`")
    private Date loadTime;
}