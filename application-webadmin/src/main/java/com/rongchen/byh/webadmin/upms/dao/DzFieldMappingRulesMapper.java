package com.rongchen.byh.webadmin.upms.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rongchen.byh.webadmin.upms.model.DzFieldMappingRulesEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**

 * 项目名称：byh_java
 * 文件名称: DzFieldMappingRulesMapper
 * 创建时间: 2025-05-21 11:50
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.dao
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
public interface DzFieldMappingRulesMapper extends BaseMapper<DzFieldMappingRulesEntity> {
    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(DzFieldMappingRulesEntity record);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(DzFieldMappingRulesEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(DzFieldMappingRulesEntity record);

    int updateBatch(@Param("list") List<DzFieldMappingRulesEntity> list);

    int updateBatchSelective(@Param("list") List<DzFieldMappingRulesEntity> list);

    int batchInsert(@Param("list") List<DzFieldMappingRulesEntity> list);

    int batchInsertOrUpdate(@Param("list") List<DzFieldMappingRulesEntity> list);
}