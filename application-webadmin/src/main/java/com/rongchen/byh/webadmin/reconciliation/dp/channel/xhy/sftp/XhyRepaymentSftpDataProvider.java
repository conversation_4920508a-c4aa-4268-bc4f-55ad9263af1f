package com.rongchen.byh.webadmin.reconciliation.dp.channel.xhy.sftp;

import com.rongchen.byh.webadmin.reconciliation.context.ReconciliationContext;
import com.rongchen.byh.webadmin.reconciliation.dataProvider.base.AbstractSftpCsvDataProvider;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import org.apache.commons.csv.CSVFormat;
import org.springframework.stereotype.Component;

/**
 * XHY渠道还款数据SFTP提供者。
 */
@Component
public class XhyRepaymentSftpDataProvider extends AbstractSftpCsvDataProvider {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    // TODO: 这些配置信息应该从外部配置源（如数据库、配置文件、Nacos等）动态加载，后面写到配置表里面，现在先临时写死
    private static final String XHY_SFTP_HOST = "***************";
    private static final int XHY_SFTP_PORT = 22;
    private static final String XHY_SFTP_USERNAME = "xhy";
    private static final String XHY_SFTP_PASSWORD = "xinheyuan168";
    private static final String XHY_SFTP_BASE_DIRECTORY = "files/trans";

    @Override
    protected SftpConfig getSftpConfig(ReconciliationContext context) {
        // 注意：在多目录模式下，这里的baseDirectory将被getBaseDirectories()方法返回的目录列表覆盖
        return new SftpConfig(
                XHY_SFTP_HOST,
                XHY_SFTP_PORT,
                XHY_SFTP_USERNAME,
                XHY_SFTP_PASSWORD,
                XHY_SFTP_BASE_DIRECTORY);
    }

    @Override
    protected List<String> getBaseDirectories(ReconciliationContext context) {
        // 支持多个目录：/files/trans 和 /files/zc/trans
        return Arrays.asList("files/trans", "files/zc/trans");
    }

    @Override
    protected String getRemoteDataFilePath(ReconciliationContext context) {
        String dateStr = context.getProcessingDate().format(DATE_FORMATTER);
        // 文件目录：/trans/yyyyMMdd/（D日）
        // 文件名称：loanrepay_yyyyMMdd.txt(还款日期)
        return dateStr + "/loanrepay_" + dateStr + ".txt";
    }

    @Override
    protected String getOkFilePath(ReconciliationContext context) {
        return getRemoteDataFilePath(context) + ".ok";
    }

    @Override
    protected CSVFormat getCsvFormat(ReconciliationContext context) {
        return CSVFormat.DEFAULT
                .withDelimiter('|')
                .withTrim(true)
                .withIgnoreEmptyLines(true);
    }

    @Override
    protected String[] getCsvHeaders(ReconciliationContext context) {
        // 根据您提供的XHY还款文件字段定义 (顺序必须与文件列顺序一致)
        return new String[] {
                "transSeqno", "transDate", "repayEndDate", "installCnt", "repayMode",
                "repayChannel", "CompensatoryStatus", "repayType", "repayAmt",
                "repayPrincipal", "repayInterest", "repayFee", "repayOverdueInterest",
                "repaySeqNo"
        };
    }

    @Override
    public String getChannelCode() {
        return "XHY";
    }

    @Override
    public String getTransactionType() {
        return "REPAYMENT"; // 此提供者专门用于还款
    }

    @Override
    public String getDataSourceType() {
        return "PARTNER_SIDE"; // XHY是资方数据
    }
}