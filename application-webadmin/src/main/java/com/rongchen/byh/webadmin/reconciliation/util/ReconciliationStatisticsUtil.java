package com.rongchen.byh.webadmin.reconciliation.util;

import com.rongchen.byh.webadmin.reconciliation.model.DiffResult;
import com.rongchen.byh.webadmin.reconciliation.model.DifferenceType;
import com.rongchen.byh.webadmin.reconciliation.model.NormalizedTransaction;
import java.math.BigDecimal;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

/**
 * 对账统计工具类。
 * <p>
 * 提供对账结果的统计分析和格式化输出功能。
 */
public class ReconciliationStatisticsUtil {

    private static final Logger logger = LoggerFactory.getLogger(ReconciliationStatisticsUtil.class);

    /**
     * 分析对账结果，生成详细的统计信息。
     *
     * @param diffResults 对账差异结果列表
     * @return 统计信息对象
     */
    public static ReconciliationStatistics analyzeReconciliationResults(List<DiffResult> diffResults) {
        ReconciliationStatistics stats = new ReconciliationStatistics();
        
        if (CollectionUtils.isEmpty(diffResults)) {
            return stats;
        }
        
        stats.setTotalRecords(diffResults.size());
        
        for (DiffResult diffResult : diffResults) {
            DifferenceType type = diffResult.getDifferenceType();
            if (type == null) {
                continue;
            }
            
            switch (type) {
                case BALANCED:
                    stats.setBalancedCount(stats.getBalancedCount() + 1);
                    stats.setBalancedAmount(stats.getBalancedAmount().add(getReconciliationAmount(diffResult.getOurRecordSnapshot())));
                    break;
                    
                case MISSING_OUR_SIDE:
                    stats.setMissingOurSideCount(stats.getMissingOurSideCount() + 1);
                    stats.setMissingOurSideAmount(stats.getMissingOurSideAmount().add(getReconciliationAmount(diffResult.getPartnerRecordSnapshot())));
                    stats.setTotalDifferences(stats.getTotalDifferences() + 1);
                    break;
                    
                case MISSING_PARTNER_SIDE:
                    stats.setMissingPartnerSideCount(stats.getMissingPartnerSideCount() + 1);
                    stats.setMissingPartnerSideAmount(stats.getMissingPartnerSideAmount().add(getReconciliationAmount(diffResult.getOurRecordSnapshot())));
                    stats.setTotalDifferences(stats.getTotalDifferences() + 1);
                    break;
                    
                case MISMATCHED_FIELDS:
                    stats.setMismatchedFieldsCount(stats.getMismatchedFieldsCount() + 1);
                    stats.setMismatchedOurAmount(stats.getMismatchedOurAmount().add(getReconciliationAmount(diffResult.getOurRecordSnapshot())));
                    stats.setMismatchedPartnerAmount(stats.getMismatchedPartnerAmount().add(getReconciliationAmount(diffResult.getPartnerRecordSnapshot())));
                    stats.setTotalDifferences(stats.getTotalDifferences() + 1);
                    break;
                    
                case DUPLICATE_OUR_SIDE:
                    stats.setDuplicateOurSideCount(stats.getDuplicateOurSideCount() + 1);
                    stats.setDuplicateOurSideAmount(stats.getDuplicateOurSideAmount().add(getReconciliationAmount(diffResult.getOurRecordSnapshot())));
                    stats.setTotalDifferences(stats.getTotalDifferences() + 1);
                    break;
                    
                case DUPLICATE_PARTNER_SIDE:
                    stats.setDuplicatePartnerSideCount(stats.getDuplicatePartnerSideCount() + 1);
                    stats.setDuplicatePartnerSideAmount(stats.getDuplicatePartnerSideAmount().add(getReconciliationAmount(diffResult.getPartnerRecordSnapshot())));
                    stats.setTotalDifferences(stats.getTotalDifferences() + 1);
                    break;
                    
                default:
                    logger.warn("未知的差异类型: {}", type);
                    stats.setTotalDifferences(stats.getTotalDifferences() + 1);
                    break;
            }
        }
        
        return stats;
    }

    /**
     * 从标准化交易记录中提取对账金额。
     *
     * @param record 标准化交易记录
     * @return 对账金额，如果记录为null或金额为null则返回0
     */
    private static BigDecimal getReconciliationAmount(NormalizedTransaction record) {
        if (record == null || record.getReconciliationAmount() == null) {
            return BigDecimal.ZERO;
        }
        return record.getReconciliationAmount();
    }

    /**
     * 对账结果统计信息类
     */
    @Setter
    @Getter
    public static class ReconciliationStatistics {
        // Getter and Setter methods
        private int balancedCount = 0;
        private BigDecimal balancedAmount = BigDecimal.ZERO;

        private int missingOurSideCount = 0;
        private BigDecimal missingOurSideAmount = BigDecimal.ZERO;

        private int missingPartnerSideCount = 0;
        private BigDecimal missingPartnerSideAmount = BigDecimal.ZERO;

        private int mismatchedFieldsCount = 0;
        private BigDecimal mismatchedOurAmount = BigDecimal.ZERO;
        private BigDecimal mismatchedPartnerAmount = BigDecimal.ZERO;

        private int duplicateOurSideCount = 0;
        private BigDecimal duplicateOurSideAmount = BigDecimal.ZERO;

        private int duplicatePartnerSideCount = 0;
        private BigDecimal duplicatePartnerSideAmount = BigDecimal.ZERO;

        private int totalRecords = 0;
        private int totalDifferences = 0;

        /**
         * 格式化输出统计信息
         *
         * @param batchId 批次ID
         * @return 格式化的统计信息字符串
         */
        public String formatOutput(String batchId) {
            StringBuilder sb = new StringBuilder();
            sb.append("\n");
            sb.append("对账完成统计 [批次: ").append(batchId).append("]:\n");
            sb.append("┌─────────────────────────────────────────────────────────────────────────────┐\n");

            double balancedPercent = totalRecords > 0 ? (balancedCount * 100.0 / totalRecords) : 0.0;
            sb.append(String.format("│ 对平记录：   %6d笔    金额：%15s元    (%5.1f%%)      │\n",
                    balancedCount, formatAmount(balancedAmount), balancedPercent));

            double missingOurPercent = totalRecords > 0 ? (missingPartnerSideCount * 100.0 / totalRecords) : 0.0;
            sb.append(String.format("│ 我方多出：   %6d笔    金额：%15s元    (%5.1f%%)      │\n",
                    missingPartnerSideCount, formatAmount(missingPartnerSideAmount), missingOurPercent));

            double missingPartnerPercent = totalRecords > 0 ? (missingOurSideCount * 100.0 / totalRecords) : 0.0;
            sb.append(String.format("│ 资方多出：   %6d笔    金额：%15s元    (%5.1f%%)      │\n",
                    missingOurSideCount, formatAmount(missingOurSideAmount), missingPartnerPercent));

            if (mismatchedFieldsCount > 0) {
                sb.append(String.format("│ 字段不匹配： %6d笔    我方：%15s元    资方：%15s元 │\n",
                        mismatchedFieldsCount, formatAmount(mismatchedOurAmount), formatAmount(mismatchedPartnerAmount)));
            } else {
                sb.append(String.format("│ 字段不匹配： %6d笔    金额：%15s元    (%5.1f%%)      │\n",
                        mismatchedFieldsCount, formatAmount(BigDecimal.ZERO), 0.0));
            }

            if (duplicateOurSideCount > 0 || duplicatePartnerSideCount > 0) {
                sb.append(String.format("│ 我方重复：   %6d笔    金额：%15s元                        │\n",
                        duplicateOurSideCount, formatAmount(duplicateOurSideAmount)));
                sb.append(String.format("│ 资方重复：   %6d笔    金额：%15s元                        │\n",
                        duplicatePartnerSideCount, formatAmount(duplicatePartnerSideAmount)));
            }

            sb.append("├─────────────────────────────────────────────────────────────────────────────┤\n");
            double diffPercent = totalRecords > 0 ? (totalDifferences * 100.0 / totalRecords) : 0.0;
            sb.append(String.format("│ 总记录数：   %6d笔    总差异：%6d笔    差异率：%5.1f%%              │\n",
                    totalRecords, totalDifferences, diffPercent));
            sb.append("└─────────────────────────────────────────────────────────────────────────────┘");

            return sb.toString();
        }

        /**
         * 格式化金额显示
         *
         * @param amount 金额
         * @return 格式化的金额字符串
         */
        private String formatAmount(BigDecimal amount) {
            if (amount == null) {
                return "0.00";
            }
            return String.format("%,.2f", amount);
        }
    }
}
