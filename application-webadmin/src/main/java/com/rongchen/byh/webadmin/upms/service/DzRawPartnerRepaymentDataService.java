package com.rongchen.byh.webadmin.upms.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rongchen.byh.webadmin.upms.dao.DzRawPartnerRepaymentDataMapper;
import com.rongchen.byh.webadmin.upms.model.DzRawPartnerRepaymentDataEntity;
import java.util.List;
import org.springframework.stereotype.Service;
/**

 * 项目名称：byh_java
 * 文件名称: DzRawPartnerRepaymentDataService
 * 创建时间: 2025-05-21 17:26
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.service
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
@Service
public class DzRawPartnerRepaymentDataService extends ServiceImpl<DzRawPartnerRepaymentDataMapper, DzRawPartnerRepaymentDataEntity> {

    
    public int insertSelective(DzRawPartnerRepaymentDataEntity record) {
        return baseMapper.insertSelective(record);
    }
    
    public int updateByPrimaryKeySelective(DzRawPartnerRepaymentDataEntity record) {
        return baseMapper.updateByPrimaryKeySelective(record);
    }
    
    public int updateByPrimaryKey(DzRawPartnerRepaymentDataEntity record) {
        return baseMapper.updateByPrimaryKey(record);
    }
    
    public int updateBatch(List<DzRawPartnerRepaymentDataEntity> list) {
        return baseMapper.updateBatch(list);
    }
    
    public int updateBatchSelective(List<DzRawPartnerRepaymentDataEntity> list) {
        return baseMapper.updateBatchSelective(list);
    }
    
    public int batchInsert(List<DzRawPartnerRepaymentDataEntity> list) {
        return baseMapper.batchInsert(list);
    }
    
    public int batchInsertOrUpdate(List<DzRawPartnerRepaymentDataEntity> list) {
        return baseMapper.batchInsertOrUpdate(list);
    }
}
