package com.rongchen.byh.webadmin.reconciliation.mapper.transform;

import java.util.Map;

/**
 * 字段值转换函数接口。
 * <p>
 * 实现此接口以定义自定义的字段值转换逻辑，例如日期格式化、字符串修剪、
 * 特定编码转换、数据类型转换等。
 */
@FunctionalInterface // 这是一个函数式接口，只有一个抽象方法
public interface TransformFunction {

    /**
     * 对源字段值应用转换逻辑。
     *
     * @param sourceValue 从原始记录中获取的源字段值。
     * @param params      转换函数可能需要的额外参数，这些参数通常在字段映射规则中定义。
     *                    例如，对于日期解析函数，params可能包含 "sourceFormat" 和 "targetFormat"。
     * @return 转换后的目标字段值。
     * @throws Exception 如果转换过程中发生错误。
     */
    Object apply(Object sourceValue, Map<String, String> params) throws Exception;

    // 默认方法可以提供一些通用的辅助功能，但对于此接口不是必需的
    // default String getName() { return this.getClass().getSimpleName(); }
}