package com.rongchen.byh.webadmin.upms.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName user_detail
 */
@TableName(value ="user_detail")
@Data
public class UserDetail implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户主id
     */
    private Long userId;

    /**
     * 姓名
     */
    private String webName;

    /**
     * 身份证号
     */
    private String webIdCard;

    /**
     * h5二要素验证结果  0 未通过  ，1 通过
     */
    private Integer webTwoElements;

    /**
     * 客服风险等级 1:A1 , 2:A2 , 3:A3 , 4:A4 , 5:A5
     */
    private String riskLevel;

    /**
     * 用户头像
     */
    private String headUrl;

    /**
     * 身份证姓名
     */
    private String appName;

    /**
     * 性别
     */
    private String sex;

    /**
     * 民族
     */
    private String nation;

    /**
     * 出生年月日
     */
    private String birth;

    /**
     * 地址
     */
    private String address;

    /**
     * 公民身份号码
     */
    private String idNumber;

    /**
     * 发证机关
     */
    private String authority;

    /**
     * 证件有效期
     */
    private String validDate;

    /**
     * 身份证正面图片地址
     */
    private String idCardFrondUrl;

    /**
     * 身份证反面图片地址
     */
    private String idCardReverseUrl;

    /**
     * ocr结果  0 未通过 ， 1 通过
     */
    private Integer ocrResult;

    /**
     * 人脸图片地址
     */
    private String faceUrl;

    /**
     * 人脸识别分数
     */
    private String faceScore;

    /**
     * 人脸校验置信区间
     */
    private String faceConfidence;

    /**
     * 人脸识别渠道
     */
    private String faceSource;

    /**
     * 人脸完成时间
     */
    private String faceTime;

    /**
     * 人脸验证结果 0 未通过 1 已通过
     */
    private Integer faceResult;

    /**
     * 教育程度
     */
    private String educationLevel;

    /**
     * 婚姻状态
     */
    private String maritalStatus;

    /**
     * 住房情况
     */
    private String houseStatus;

    /**
     * 个人住址（详细）
     */
    private String custAddress;

    /**
     * 个人住址（省）
     */
    private String custAddressProvice;

    /**
     * 个人住址（市）
     */
    private String custAddressCity;

    /**
     * 个人住址（区）
     */
    private String custAddressCounty;

    /**
     * 个人月收入
     */
    private String incomeMonth;

    /**
     * 紧急联系人关系1
     */
    private String relationshipOne;

    /**
     * 紧急联系人姓名1
     */
    private String emergencyNameOne;

    /**
     * 紧急联系人手机号码1
     */
    private String emergencyMobileOne;

    /**
     * 紧急联系人关系2
     */
    private String relationshipTwo;

    /**
     * 紧急联系人姓名2
     */
    private String emergencyNameTwo;

    /**
     * 紧急联系人手机号码2
     */
    private String emergencyMobileTwo;

    /**
     * 表单提交标识 0 未提交 1 已提交
     */
    private Integer formFlag;

    /**
     * 表单完成时间
     */
    private String formTime;

    /**
     * 本机号码
     */
    private String phoneNumber;

    /**
     * 开机时间
     */
    private Date mobileStartTime;

    /**
     * wifi地址是否包含“金融”、“中介”等敏感词汇  Y-是；N-否
     */
    private String wifiSensitive;

    /**
     * 通讯录个数
     */
    private Integer addressBookNum;

    /**
     * 通讯录中11位手机号数量
     */
    private Integer addressBookMobileNum;

    /**
     * 通讯录去重后有标记“贷”或“借”或“催收”或“中介”或“口子”或“黑户”字样的个数   Y-是；N-否
     */
    private String addressBookSensitive;

    /**
     * 填写的两个紧急联系人有一个及以上不在运营商通话详单中  Y-是；N-否
     */
    private String contactOperator;

    /**
     * 最近60天逾期的短信条数
     */
    private Integer overdueMessageNum;

    /**
     * GPS 经度
     */
    private String longitude;

    /**
     * GPS 纬度
     */
    private String latitude;

    /**
     * 设备品牌
     */
    private String deviceBrand;

    /**
     * 设备网络类型
     */
    private String networkType;

    /**
     * 手机型号
     */
    private String devAlias;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * ip地址
     */
    private String clientIp;

    /**
     * 经纬度坐标系类型
     */
    private String coordinateType;

    /**
     * 设备GPS定位城市
     */
    private String gpsCity;

    /**
     * LBS定位地址
     */
    private String lbsAddress;

    /**
     * GPS定位地址
     */
    private String gpsAddress;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 手机系统版本号
     */
    private String osVersion;


    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 职业 1: 上班族2：企业主3：个体户4：自由职业
     */
    private Integer professional;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}