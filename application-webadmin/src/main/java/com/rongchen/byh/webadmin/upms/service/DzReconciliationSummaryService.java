package com.rongchen.byh.webadmin.upms.service;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rongchen.byh.common.core.exception.MyRuntimeException;
import com.rongchen.byh.webadmin.reconciliation.dto.ReconciliationSummaryDetailDto;
import com.rongchen.byh.webadmin.reconciliation.dto.ReconciliationSummeryListDto;
import com.rongchen.byh.webadmin.reconciliation.model.DifferenceType;
import com.rongchen.byh.webadmin.reconciliation.vo.ReconciliationSummaryDetailDifferentVo;
import com.rongchen.byh.webadmin.reconciliation.vo.ReconciliationSummaryDetailVo;
import com.rongchen.byh.webadmin.reconciliation.vo.ReconciliationSummaryListVo;
import com.rongchen.byh.webadmin.upms.dao.DzReconciliationDiffResultsMapper;
import com.rongchen.byh.webadmin.upms.dao.DzReconciliationSummaryMapper;
import com.rongchen.byh.webadmin.upms.model.DzReconciliationSummaryEntity;
import java.util.List;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**

 * 项目名称：byh_java
 * 文件名称: DzReconciliationSummaryService
 * 创建时间: 2025-05-22 16:48
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.service
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
@Service
public class DzReconciliationSummaryService extends ServiceImpl<DzReconciliationSummaryMapper, DzReconciliationSummaryEntity> {
    @Resource
    private DzReconciliationDiffResultsMapper dzReconciliationDiffResultsMapper;
    
    public int insertSelective(DzReconciliationSummaryEntity record) {
        return baseMapper.insertSelective(record);
    }
    
    public int updateByPrimaryKeySelective(DzReconciliationSummaryEntity record) {
        return baseMapper.updateByPrimaryKeySelective(record);
    }
    
    public int updateByPrimaryKey(DzReconciliationSummaryEntity record) {
        return baseMapper.updateByPrimaryKey(record);
    }
    
    public int updateBatch(List<DzReconciliationSummaryEntity> list) {
        return baseMapper.updateBatch(list);
    }
    
    public int updateBatchSelective(List<DzReconciliationSummaryEntity> list) {
        return baseMapper.updateBatchSelective(list);
    }
    
    public int batchInsert(List<DzReconciliationSummaryEntity> list) {
        return baseMapper.batchInsert(list);
    }
    
    public int batchInsertOrUpdate(List<DzReconciliationSummaryEntity> list) {
        return baseMapper.batchInsertOrUpdate(list);
    }

    public ReconciliationSummaryDetailVo getAllDetail(ReconciliationSummaryDetailDto dto) {
        DzReconciliationSummaryEntity entity = this.baseMapper.selectById(dto.getId());
        if (ObjectUtil.isEmpty(entity)) {
            throw new MyRuntimeException("数据不存在");
        }
        StringBuilder sb = new StringBuilder();
        String reconBatchId = entity.getReconBatchId();
        String transactionType = entity.getTransactionType();
        String reconStatus = entity.getReconStatus();
        ReconciliationSummaryDetailVo result = new ReconciliationSummaryDetailVo();
        if (!DifferenceType.BALANCED.name().equals(reconStatus)) {
            //1.查询拆错账的数据
            List<ReconciliationSummaryDetailDifferentVo> list = dzReconciliationDiffResultsMapper.getSummeryDetailDifferent(reconBatchId, transactionType);
            for (ReconciliationSummaryDetailDifferentVo one : list) {
                String recon = one.getReconStatus();
                if (DifferenceType.MISSING_OUR_SIDE.name().equals(recon)) {
                    sb.append(String.format("我司单边：%d笔,%s元\r\n", one.getOurRecordsCount(), one.getOurTotalAmount()));
                } else if (DifferenceType.MISSING_PARTNER_SIDE.name().equals(recon)) {
                    sb.append(String.format("渠道单边：%d笔,%s元\r\n", one.getPartnerRecordsCount(), one.getPartnerTotalAmount()));
                } else if (DifferenceType.MISMATCHED_FIELDS.name().equals(recon)) {
                    sb.append(String.format("全额不等：%d笔,我司金额：%s元，渠道金额：%s元", one.getOurRecordsCount(), one.getOurTotalAmount(), one.getPartnerTotalAmount()));
                }
                one.setOurRecordsCount(one.getOurRecordsCount());
                one.setPartnerRecordsCount(one.getPartnerRecordsCount());
                one.setOurTotalAmount(one.getOurTotalAmount());
                one.setPartnerTotalAmount(one.getPartnerTotalAmount());
            }
            result.setDifferenceList(list);
        }
        result.setDifferenceSummary(sb.toString());
        return result;
    }

    public List<ReconciliationSummaryListVo> selectAll(ReconciliationSummeryListDto dto) {
        return baseMapper.selectAll(dto);
    }
}
