package com.rongchen.byh.webadmin.reconciliation.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.rongchen.byh.common.core.util.JsonUtils;
import com.rongchen.byh.webadmin.reconciliation.model.DiffResult;
import com.rongchen.byh.webadmin.upms.model.DzReconciliationDiffResultsEntity;
import com.rongchen.byh.webadmin.upms.service.DzReconciliationDiffResultsService;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 差异存储服务。
 * <p>
 * 负责将 {@link ReconciliationEngineService} 生成的 {@link DiffResult}列表
 * 持久化到数据库中 (通过用户生成的 {@link DzReconciliationDiffResultsService})。
 */
@Service
public class DiffStorageService {

    private static final Logger logger = LoggerFactory.getLogger(DiffStorageService.class);

    private final DzReconciliationDiffResultsService dzDiffResultsService; // 用户生成的Service

    @Autowired
    public DiffStorageService(DzReconciliationDiffResultsService dzDiffResultsService) {
        this.dzDiffResultsService = dzDiffResultsService;
    }

    /**
     * 保存对账差异结果列表。
     *
     * @param diffResults 要保存的差异结果列表。
     * @param batchId     当前的对账批次ID，用于填充到每个差异实体中。
     */
    public void saveDiffs(List<DiffResult> diffResults, String batchId) {
        if (CollectionUtils.isEmpty(diffResults)) {
            logger.info("没有差异结果需要保存，批次ID: {}", batchId);
            return;
        }

        List<DzReconciliationDiffResultsEntity> entitiesToSave = new ArrayList<>();
        for (DiffResult diffResult : diffResults) {
            try {
                DzReconciliationDiffResultsEntity entity = convertToEntity(diffResult, batchId);
                entitiesToSave.add(entity);
            } catch (JsonProcessingException e) {
                logger.error("序列化差异记录中的快照或字段差异为JSON失败，批次ID: {}，差异: {}，错误: {}",
                        batchId, diffResult, e.getMessage(), e);
                // TODO: 决定如何处理单个转换失败的记录，是跳过还是中断整个保存过程
            }
        }

        if (!CollectionUtils.isEmpty(entitiesToSave)) {
            try {
                boolean success = dzDiffResultsService.saveBatch(entitiesToSave);
                if (success) {
                    logger.info("成功保存 {} 条差异结果到数据库，批次ID: {}", entitiesToSave.size(), batchId);
                } else {
                    logger.error("批量保存差异结果失败，批次ID: {}", batchId);
                    // TODO: 处理保存失败的情况，例如尝试逐条保存或抛出异常
                }
            } catch (Exception e) {
                logger.error("保存差异结果到数据库时发生异常，批次ID: {} : {}", batchId, e.getMessage(), e);
                // TODO: 异常处理逻辑
            }
        }
    }

    /**
     * 将内部的 DiffResult 模型对象转换为数据库实体 DzReconciliationDiffResultsEntity。
     *
     * @param diffResult 内部差异模型对象。
     * @param batchId    对账批次ID。
     * @return 转换后的数据库实体。
     * @throws JsonProcessingException 如果序列化JSON字段失败。
     */
    private DzReconciliationDiffResultsEntity convertToEntity(DiffResult diffResult, String batchId)
            throws JsonProcessingException {
        DzReconciliationDiffResultsEntity entity = new DzReconciliationDiffResultsEntity();

        // 基本属性复制，可以使用BeanUtils或手动设置
        // 注意：这里假设DiffResult中的字段名与DzReconciliationDiffResultsEntity中的大部分匹配
        // 对于不匹配或需要特殊处理的字段，需要手动设置
        // BeanUtils.copyProperties(diffResult, entity);

        entity.setBatchId(batchId != null ? batchId : diffResult.getBatchId()); // 优先使用传入的batchId
        entity.setRuleIdUsed(diffResult.getRuleIdUsed());
        entity.setRuleVersionUsed(diffResult.getRuleVersionUsed());
        if (diffResult.getDifferenceType() != null) {
            entity.setDifferenceType(diffResult.getDifferenceType().name()); // 枚举转字符串
        }
        entity.setPrimaryMatchingKeyJson(diffResult.getPrimaryMatchingKeyJson());

        // 序列化记录快照和字段差异为JSON字符串
        if (diffResult.getOurRecordSnapshot() != null) {
            entity.setOurRecordSnapshot(JsonUtils.toJsonString(diffResult.getOurRecordSnapshot()));
        }
        if (diffResult.getPartnerRecordSnapshot() != null) {
            entity.setPartnerRecordSnapshot(JsonUtils.toJsonString(diffResult.getPartnerRecordSnapshot()));
        }
        if (!CollectionUtils.isEmpty(diffResult.getFieldDifferences())) {
            entity.setFieldDifferencesJson(JsonUtils.toJsonString(diffResult.getFieldDifferences()));
        }

        entity.setConfirmationStatus(
                diffResult.getConfirmationStatus() != null ? diffResult.getConfirmationStatus() : "UNCONFIRMED");
        entity.setConfirmedBy(diffResult.getConfirmedBy());
        if (diffResult.getConfirmationTimestamp() != null) {
            entity.setConfirmationTimestamp(java.sql.Timestamp.valueOf(diffResult.getConfirmationTimestamp()));
        }
        entity.setComments(diffResult.getComments());

        // created_timestamp 通常由数据库自动生成或在插入时由MyBatisPlus填充
        // 如果需要在这里设置，可以 entity.setCreatedTimestamp(LocalDateTime.now());
        // 但要注意数据库实体中createdTimestamp字段的类型和自动填充策略
        // if (diffResult.getCreatedTimestamp() != null) { //
        // DiffResult中的createdTimestamp目前不会被引擎设置，而是由DB或这里设置
        // entity.setCreatedTimestamp(java.sql.Timestamp.valueOf(diffResult.getCreatedTimestamp()));
        // }
        // 统一在这里设置创建时间，确保不为null，并转换为实体期望的类型
        entity.setCreatedTimestamp(java.sql.Timestamp.valueOf(LocalDateTime.now()));

        return entity;
    }

    // TODO: 可能还需要提供查询差异、更新差异确认状态等方法
}