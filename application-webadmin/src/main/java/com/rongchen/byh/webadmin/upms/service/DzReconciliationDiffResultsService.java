package com.rongchen.byh.webadmin.upms.service;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.webadmin.reconciliation.dto.ReReconciliationDto;
import com.rongchen.byh.webadmin.reconciliation.model.DifferenceType;
import com.rongchen.byh.webadmin.reconciliation.service.ReconciliationRetryService;
import com.rongchen.byh.webadmin.reconciliation.vo.ReReconciliationVo;
import com.rongchen.byh.webadmin.reconciliation.vo.ReconciliationVo;
import com.rongchen.byh.webadmin.reconciliation.dto.ReconciliationDto;
import com.rongchen.byh.webadmin.upms.dao.DzReconciliationDiffResultsMapper;
import com.rongchen.byh.webadmin.upms.model.DzReconciliationDiffResultsEntity;

import java.util.*;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**

 * 项目名称：byh_java
 * 文件名称: DzReconciliationDiffResultsService
 * 创建时间: 2025-05-21 11:50
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.service
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
@Service
public class DzReconciliationDiffResultsService extends ServiceImpl<DzReconciliationDiffResultsMapper, DzReconciliationDiffResultsEntity> {
    @Resource
    private ReconciliationRetryService reconciliationRetryService;

    
    public int insertSelective(DzReconciliationDiffResultsEntity record) {
        return baseMapper.insertSelective(record);
    }
    
    public int updateByPrimaryKeySelective(DzReconciliationDiffResultsEntity record) {
        return baseMapper.updateByPrimaryKeySelective(record);
    }
    
    public int updateByPrimaryKey(DzReconciliationDiffResultsEntity record) {
        return baseMapper.updateByPrimaryKey(record);
    }
    
    public int updateBatch(List<DzReconciliationDiffResultsEntity> list) {
        return baseMapper.updateBatch(list);
    }
    
    public int updateBatchSelective(List<DzReconciliationDiffResultsEntity> list) {
        return baseMapper.updateBatchSelective(list);
    }
    
    public int batchInsert(List<DzReconciliationDiffResultsEntity> list) {
        return baseMapper.batchInsert(list);
    }
    
    public int batchInsertOrUpdate(List<DzReconciliationDiffResultsEntity> list) {
        return baseMapper.batchInsertOrUpdate(list);
    }

    public List<DzReconciliationDiffResultsEntity> selectList(
            LambdaQueryWrapper<DzReconciliationDiffResultsEntity> queryWrapper) {
        return baseMapper.selectList(queryWrapper);
    }

    public List<ReconciliationVo> selectCreditsList(ReconciliationDto vo) {
        return baseMapper.selectCreditsList(vo);
    }

    public List<ReconciliationVo> selectLoansList(ReconciliationDto vo) {
        return baseMapper.selectLoansList(vo);
    }

    public List<ReconciliationVo> selectRepaysList(ReconciliationDto vo) {
        return baseMapper.selectRepaysList(vo);
    }

    public ReReconciliationVo reReconciliation(ReReconciliationDto dto) {
        ReReconciliationVo vo = new ReReconciliationVo();
        vo.setIsBalance(false);
        ReconciliationVo data = baseMapper.selectById(dto.getId());
        if (ObjectUtil.isEmpty(data)) {
            return vo;
        }
        ReconciliationRetryService.RetryResult result = reconciliationRetryService.retryByBatchId(data.getBatchId(), "后台手动发起");
        if (!result.isSuccess()) {
            return vo;
        }
        if ("CREDIT".equals(data.getTransactionType())) {
            dto.setMyCreditNo(data.getMyCreditNo());
        } else if ("LOAN".equals(data.getTransactionType())) {
            dto.setMyOrderNo(data.getMyOrderNo());
        } else if ("REPAYMENT".equals(data.getTransactionType())) {
            dto.setMyRepaymentId(data.getMyRepaymentId());
        }
        ReconciliationVo reconciliationVo = baseMapper.selectByTypeAndNo(dto);
        if (ObjectUtil.isEmpty(reconciliationVo) || DifferenceType.BALANCED.name().equals(reconciliationVo.getDifferenceTypeName())) {
            vo.setIsBalance(true);
        }
        return vo;
    }
}
