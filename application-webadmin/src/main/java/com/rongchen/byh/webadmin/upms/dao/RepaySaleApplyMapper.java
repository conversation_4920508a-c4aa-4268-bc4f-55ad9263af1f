package com.rongchen.byh.webadmin.upms.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rongchen.byh.webadmin.upms.model.RepaySaleApply;

import java.util.List;


/**
* <AUTHOR>
* @description 针对表【repay_sale_apply(赊销结果表)】的数据库操作Mapper
* @createDate 2024-12-15 18:48:25
* @Entity generator.domain.RepaySaleApply
*/
public interface RepaySaleApplyMapper extends BaseMapper<RepaySaleApply> {
    List<RepaySaleApply> selectByRepayApplyNo(String repayApplyNo);
}




