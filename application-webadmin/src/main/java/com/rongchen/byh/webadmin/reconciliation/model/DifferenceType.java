package com.rongchen.byh.webadmin.reconciliation.model;

/**
 * 对账差异类型枚举
 */
public enum DifferenceType {
    /**
     * 账已对平
     */
    BALANCED,
    /**
     * 我方无，资方有 (资方多)
     */
    MISSING_OUR_SIDE,

    /**
     * 我方有，资方无 (我方多)
     */
    MISSING_PARTNER_SIDE,

    /**
     * 双方共有，但字段不一致
     */
    MISMATCHED_FIELDS,

    /**
     * 我方数据重复 (根据匹配键判断)
     */
    DUPLICATE_OUR_SIDE,

    /**
     * 资方数据重复 (根据匹配键判断)
     */
    DUPLICATE_PARTNER_SIDE
}