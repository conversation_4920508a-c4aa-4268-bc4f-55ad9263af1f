package com.rongchen.byh.webadmin.upms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rongchen.byh.webadmin.upms.dao.DzReconciliationRulesMapper;
import com.rongchen.byh.webadmin.upms.model.DzReconciliationRulesEntity;
import java.util.List;
import org.springframework.stereotype.Service;
/**

 * 项目名称：byh_java
 * 文件名称: DzReconciliationRulesService
 * 创建时间: 2025-05-21 11:50
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.service
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
@Service
public class DzReconciliationRulesService extends ServiceImpl<DzReconciliationRulesMapper, DzReconciliationRulesEntity> {

    
    public int insertSelective(DzReconciliationRulesEntity record) {
        return baseMapper.insertSelective(record);
    }
    
    public int updateByPrimaryKeySelective(DzReconciliationRulesEntity record) {
        return baseMapper.updateByPrimaryKeySelective(record);
    }
    
    public int updateByPrimaryKey(DzReconciliationRulesEntity record) {
        return baseMapper.updateByPrimaryKey(record);
    }
    
    public int updateBatch(List<DzReconciliationRulesEntity> list) {
        return baseMapper.updateBatch(list);
    }
    
    public int updateBatchSelective(List<DzReconciliationRulesEntity> list) {
        return baseMapper.updateBatchSelective(list);
    }
    
    public int batchInsert(List<DzReconciliationRulesEntity> list) {
        return baseMapper.batchInsert(list);
    }
    
    public int batchInsertOrUpdate(List<DzReconciliationRulesEntity> list) {
        return baseMapper.batchInsertOrUpdate(list);
    }

    public DzReconciliationRulesEntity findActiveRuleByChannelAndType(String channelCode, String transactionType) {
        return baseMapper.selectOne(new LambdaQueryWrapper<DzReconciliationRulesEntity>()
            .eq(DzReconciliationRulesEntity::getChannelCode, channelCode)
            .eq(DzReconciliationRulesEntity::getTransactionType, transactionType));
    }
}
