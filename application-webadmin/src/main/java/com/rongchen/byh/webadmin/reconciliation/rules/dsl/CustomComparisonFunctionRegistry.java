package com.rongchen.byh.webadmin.reconciliation.rules.dsl;

import java.util.HashMap;
import java.util.Map;
import javax.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 自定义比较函数注册表。
 * <p>
 * 负责注册和提供各种 {@link CustomComparisonFunction} 实现。
 * {@link com.rongchen.byh.webadmin.reconciliation.strategy.JsonDslComparisonStrategy}
 * 将使用此注册表来查找和应用在JSON DSL规则中指定的自定义比较函数。
 */
@Component
public class CustomComparisonFunctionRegistry {

    private static final Logger logger = LoggerFactory.getLogger(CustomComparisonFunctionRegistry.class);

    private final Map<String, CustomComparisonFunction> functions = new HashMap<>();

    /**
     * 注册一个自定义比较函数。
     *
     * @param name     函数的唯一名称，在JSON DSL规则中通过此名称引用。
     * @param function {@link CustomComparisonFunction} 的实例。
     */
    public void register(String name, CustomComparisonFunction function) {
        if (name == null || name.trim().isEmpty() || function == null) {
            logger.warn("注册自定义比较函数失败：名称或函数实例不能为空。");
            return;
        }
        if (functions.containsKey(name)) {
            logger.warn("自定义比较函数名称 '{}' 已存在，旧的将被覆盖。", name);
        }
        functions.put(name, function);
        logger.info("已注册自定义比较函数: {}", name);
    }

    /**
     * 根据名称获取一个已注册的自定义比较函数。
     *
     * @param name 函数的名称。
     * @return {@link CustomComparisonFunction} 的实例，如果未找到则返回null。
     */
    public CustomComparisonFunction get(String name) {
        if (name == null) {
            return null;
        }
        CustomComparisonFunction function = functions.get(name);
        if (function == null) {
            logger.warn("未找到名称为 '{}' 的自定义比较函数。", name);
        }
        return function;
    }

    /**
     * 初始化方法，可以预注册一些通用的自定义比较函数，
     * 或者用于通过Spring Bean发现机制自动注册所有CustomComparisonFunction类型的Bean（如果采用该方式）。
     */
    @PostConstruct
    public void init() {
        // 示例：预注册一个简单的自定义函数 (实际中您可能会将自定义函数实现为单独的类并注册其实例)
        // register("EXAMPLE_CUSTOM_COMPARE", (ourVal, partnerVal, params, context) -> {
        // logger.info("执行示例自定义比较函数: ourVal={}, partnerVal={}, params={}", ourVal,
        // partnerVal, params);
        // return Objects.equals(ourVal, partnerVal); // 简单示例
        // });

        // TODO: 在此处注册您项目中实际需要的自定义比较函数，或者配置为自动发现。

        logger.info("CustomComparisonFunctionRegistry 初始化完成，当前共注册 {} 个自定义函数。", functions.size());
    }

    /**
     * 获取所有已注册函数的名称和实现类（主要用于调试或管理界面显示）。
     * 
     * @return 函数名称到函数实现的映射
     */
    public Map<String, CustomComparisonFunction> getAllFunctions() {
        return new HashMap<>(functions); // 返回副本以防外部修改
    }
}