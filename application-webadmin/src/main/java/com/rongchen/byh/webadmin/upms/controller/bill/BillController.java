package com.rongchen.byh.webadmin.upms.controller.bill;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.rongchen.byh.common.api.zifang.vo.RepaymentResultVo;
import com.rongchen.byh.common.api.zifang.vo.SaleRepayResultVo;
import com.rongchen.byh.common.core.annotation.MyRequestBody;
import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.webadmin.upms.dto.bill.*;
import com.rongchen.byh.webadmin.upms.model.LateCollectionsLog;
import com.rongchen.byh.webadmin.upms.service.DisburseDataService;
import com.rongchen.byh.webadmin.upms.vo.bill.*;
import com.rongchen.byh.webadmin.upms.vo.order.LateCollectionsLogVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 账单相关接口
 * @date 2025/1/23 10:21:32
 */
@Tag(name = "账单相关接口")
@RestController
@RequestMapping("/admin/upms/bill")
public class BillController {
    @Resource
    private DisburseDataService disburseDataService;

    @Operation(summary = "列表")
    @PostMapping("/list")
    public ResponseResult<MyPageData<BillListVo>> list(
            @MyRequestBody BillListDto dto,
            @MyRequestBody MyPageParam pageParam) {
        return disburseDataService.billList(dto, pageParam);
    }


    @Operation(summary = "详情")
    @PostMapping("/detail")
    public ResponseResult<BillDetailVo> list(@MyRequestBody BillDetailDto dto) {
        return disburseDataService.billDetail(dto);
    }

    @Operation(summary = "还款记录详情")
    @PostMapping("/billRepayRecordList")
    public ResponseResult<List<BillRepayRecordVo>> list(@MyRequestBody BillRepayRecordDto dto) {
        return disburseDataService.billRepayRecordList(dto);
    }

    @Operation(summary = "线下还款入账")
    @PostMapping("/repay")
    public ResponseResult<Void> repay(@MyRequestBody BillRepayDto dto) {
        return disburseDataService.repay(dto);
    }

    @Operation(summary = "逾期账单列表")
    @PostMapping("/overdueList")
    public ResponseResult<MyPageData<OverdueListVo>> overdueList(@MyRequestBody OverdueListDto dto, @MyRequestBody MyPageParam pageParam) {
        return disburseDataService.overdueList(dto, pageParam);
    }

    @Operation(summary = "逾期账单详情")
    @PostMapping("/overdueDetailList")
    public ResponseResult<BillDetailVo> overdueDetailList(@MyRequestBody BillDetailDto dto) {
        return disburseDataService.overdueDetailList(dto);
    }

    @Operation(summary = "逾期账单单期还款记录详情")
    @PostMapping("/overdueRepayRecordList")
    public ResponseResult<List<BillRepayRecordVo>> overdueRepayRecordList(@MyRequestBody BillRepayRecordDto dto) {
        return disburseDataService.overdueRepayRecordList(dto);
    }

    @Operation(summary = "线上还款入账")
    @PostMapping("/onlineRepay")
    public ResponseResult<Void> onlineRepay(@MyRequestBody BillRepayDto dto) {
        return disburseDataService.onlineRepay(dto);
    }

    @Operation(summary = "催收记录")
    @PostMapping("/lateCollectionsLogList")
    public ResponseResult<MyPageData<LateCollectionsLogVo>> lateCollectionsLogList(@MyRequestBody BillDetailDto dto, @MyRequestBody MyPageParam pageParam) {
        return disburseDataService.LateCollectionsLogList(dto, pageParam);
    }

    @Operation(summary = "催收记录 - 添加")
    @PostMapping("/insertLateCollectionsLog")
    public ResponseResult<Void> insertLateCollectionsLog(@MyRequestBody LateCollectionsLog dto) {
        return disburseDataService.insertLateCollectionsLog(dto);
    }

    @Operation(summary = "提前结清 - 提前全部结清 试算")
    @PostMapping("/settlement")
    public ResponseResult<BillPreRepayApplyVo> settlement(@MyRequestBody SettleRepayApplyDto dto) {
        return disburseDataService.settlement(dto);
    }

    @Operation(summary = "提前结清 - 提前全部结清")
    @PostMapping("/settle")
    public ResponseResult<Void> settle(@MyRequestBody SettleRepayApplyDto dto) {
        return disburseDataService.settle(dto);
    }

    @Operation(summary = "提前结清 - 提前全部结清本息 结果查询")
    @PostMapping("/repaymentResult")
    public ResponseResult<RepaymentResultVo> repaymentResult(@MyRequestBody SettleRepayApplyDto dto) {
        return disburseDataService.repaymentResult(dto);
    }

    @Operation(summary = "提前结清 - 提前全部结清权益 结果查询")
    @PostMapping("/saleResult")
    public ResponseResult<SaleRepayResultVo> saleResult(@MyRequestBody SettleRepayApplyDto dto) {
        return disburseDataService.saleResult(dto);
    }

}
