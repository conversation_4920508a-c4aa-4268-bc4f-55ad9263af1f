package com.rongchen.byh.webadmin.upms.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rongchen.byh.webadmin.upms.dao.RepayScheduleMapper;
import com.rongchen.byh.webadmin.upms.model.RepaySchedule;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025/5/14 17:30:18
 */
@Service
public class RepayScheduleService extends ServiceImpl<RepayScheduleMapper, RepaySchedule> {

    public int insertSelective(RepaySchedule record) {
        return baseMapper.insertSelective(record);
    }

    public int updateByPrimaryKeySelective(RepaySchedule record) {
        return baseMapper.updateByPrimaryKeySelective(record);
    }

    public int updateByPrimaryKey(RepaySchedule record) {
        return baseMapper.updateByPrimaryKey(record);
    }

    public int updateBatch(List<RepaySchedule> list) {
        return baseMapper.updateBatch(list);
    }

    public int updateBatchSelective(List<RepaySchedule> list) {
        return baseMapper.updateBatchSelective(list);
    }

    public int batchInsert(List<RepaySchedule> list) {
        return baseMapper.batchInsert(list);
    }

    public int batchInsertOrUpdate(List<RepaySchedule> list) {
        return baseMapper.batchInsertOrUpdate(list);
    }
}

