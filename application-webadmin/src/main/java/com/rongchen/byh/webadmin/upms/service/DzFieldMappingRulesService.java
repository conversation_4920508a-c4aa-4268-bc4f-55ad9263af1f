package com.rongchen.byh.webadmin.upms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rongchen.byh.webadmin.upms.dao.DzFieldMappingRulesMapper;
import com.rongchen.byh.webadmin.upms.model.DzFieldMappingRulesEntity;
import java.util.List;
import org.springframework.stereotype.Service;
/**

 * 项目名称：byh_java
 * 文件名称: DzFieldMappingRulesService
 * 创建时间: 2025-05-21 11:50
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.service
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
@Service
public class DzFieldMappingRulesService extends ServiceImpl<DzFieldMappingRulesMapper, DzFieldMappingRulesEntity> {

    
    public int insertSelective(DzFieldMappingRulesEntity record) {
        return baseMapper.insertSelective(record);
    }
    
    public int updateByPrimaryKeySelective(DzFieldMappingRulesEntity record) {
        return baseMapper.updateByPrimaryKeySelective(record);
    }
    
    public int updateByPrimaryKey(DzFieldMappingRulesEntity record) {
        return baseMapper.updateByPrimaryKey(record);
    }
    
    public int updateBatch(List<DzFieldMappingRulesEntity> list) {
        return baseMapper.updateBatch(list);
    }
    
    public int updateBatchSelective(List<DzFieldMappingRulesEntity> list) {
        return baseMapper.updateBatchSelective(list);
    }
    
    public int batchInsert(List<DzFieldMappingRulesEntity> list) {
        return baseMapper.batchInsert(list);
    }
    
    public int batchInsertOrUpdate(List<DzFieldMappingRulesEntity> list) {
        return baseMapper.batchInsertOrUpdate(list);
    }

    public List<DzFieldMappingRulesEntity> findActiveRulesByChannelAndType(String channelCode, String transactionType) {
        return baseMapper.selectList(new LambdaQueryWrapper<DzFieldMappingRulesEntity>()
            .eq(DzFieldMappingRulesEntity::getChannelCode, channelCode)
            .eq(DzFieldMappingRulesEntity::getTransactionType, transactionType));
    }
}
