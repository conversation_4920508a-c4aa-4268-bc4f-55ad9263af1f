package com.rongchen.byh.webadmin.reconciliation.vo;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 后台对账列表的返回字段dto
 * @date 2025/5/22 15:37
 */
@Data
public class ReconciliationVo {

    @Schema(description = "主键id")
    private Long id;

    /**
     * 批次ID
     */
    @Schema(description = "批次ID")
    private String batchId;

    /**
     * 合作方订单号
     */
    @Schema(description = "合作方订单号")
    private String outOrderNo;
    /**
     * 我司订单号
     */
    @Schema(description = "我司订单号")
    private String myOrderNo;


    @Schema(description = "我司授信单号")
    private String myCreditNo;


    @Schema(description = "合作方授信单号")
    private String outCreditNo;


    @Schema(description = "我司还款流水号")
    @Hidden
    private String myRepaymentId;


    @Schema(description = "合作方还款流水号")
    @Hidden
    private String outRepaymentId;

    @Schema(description = "渠道名")
    private String channelName;

    /**
     * 交易类型
     */
    @Schema(description = "交易类型")
    private String transactionType;
    /**
     * 交易完成时间
     */
    @Schema(description = "交易完成时间")
    private Date finishTime;
    /**
     * 我司金额
     */
    @Schema(description = "我司金额")
    private BigDecimal myAmount;
    /**
     * 对方金额
     */
    @Schema(description = "对方金额")
    private BigDecimal outAmount;
    /**
     * 差异类型
     */
    @Schema(description = "差异类型")
    private String differenceTypeName;

    /**
     * 对账结果
     */
    @Schema(description = "对账结果")
    private String reconciliationResult;

    /**
     * 创建结束日期
     */
    @Schema(description = "创建结束日期")
    private Date endDate;
    /**
     * 处理状态
     */
    @Schema(description = "处理状态")
    private String processStatus;
}