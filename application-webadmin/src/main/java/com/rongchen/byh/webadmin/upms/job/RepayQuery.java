package com.rongchen.byh.webadmin.upms.job;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rongchen.byh.common.api.zifang.dto.RepaymentResultDto;
import com.rongchen.byh.common.api.zifang.service.RepaymentApi;
import com.rongchen.byh.common.api.zifang.vo.RepaymentResultVo;
import com.rongchen.byh.common.core.constant.SettleFlagConstant;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.webadmin.config.ZifangFactory;
import com.rongchen.byh.webadmin.upms.dao.*;
import com.rongchen.byh.webadmin.upms.model.*;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


/**
 * 赊销账单提前结清结果查询
 */
@Component
@Slf4j
public class RepayQuery {


    @Resource
    RepayScheduleMapper repayScheduleMapper;
    @Resource
    CapitalDataMapper capitalDataMapper;
    @Resource
    ZifangFactory zifangFactory;
    @Resource
    DisburseDataMapper disburseDataMapper;
    @Resource
    RepayScheduleApplyMapper repayScheduleApplyMapper;


    @XxlJob("repayQueryHandler")
    public void repayQueryHandler() {
        LambdaQueryWrapper<RepayScheduleApply> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RepayScheduleApply::getRepayType, "8")
                .eq(RepayScheduleApply::getRepayStatus, 0);
        List<RepayScheduleApply> repayScheduleApplies = repayScheduleApplyMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(repayScheduleApplies)) {
            log.info("本息提前结清还款中查询 没有提前结清还款申请记录");
            return;
        }
        List<String> list = new ArrayList<>();
        for (RepayScheduleApply repayScheduleApply : repayScheduleApplies) {
            list.add(repayScheduleApply.getRepayApplyNo());
        }
        LambdaQueryWrapper<RepaySchedule> queryWrapperWrapper =  new LambdaQueryWrapper<>();
        queryWrapperWrapper.in(RepaySchedule::getRepayApplyNo, list)
                .eq(RepaySchedule::getSettleFlag, SettleFlagConstant.REPAYING);
        List<RepaySchedule> repaySchedules = repayScheduleMapper.selectList(queryWrapperWrapper);
        if (CollectionUtil.isEmpty(repaySchedules)) {
            log.info("本息提前结清还款中查询 没有还款中订单");
            return;
        }
        repaySchedules.forEach(this::queryProcess);
    }

    private void queryProcess(RepaySchedule repayVo) {
        try {
            Long disburseId = repayVo.getDisburseId();
            DisburseData disburseData = disburseDataMapper.selectById(disburseId);
            CapitalData capitalData = capitalDataMapper.selectById(disburseData.getCapitalId());
            if (capitalData == null) {
                log.error("本息还款中查询 - 未找到资方信息: repayVo={}", repayVo);
                return;
            }
            RepaymentApi repaymentApi = zifangFactory.getApi(capitalData.getBeanName(), RepaymentApi.class);


            RepaymentResultDto applyDto = new RepaymentResultDto();
            applyDto.setUserId(String.valueOf(repayVo.getUserId()));
            applyDto.setRepayApplyNo(repayVo.getRepayApplyNo());
            ResponseResult<RepaymentResultVo> repaymentResult = repaymentApi.getRepaymentResult(applyDto);
            if (repaymentResult.isSuccess()) {

                RepaymentResultVo data = repaymentResult.getData();
                int repayStatus = 0;
                String reason = "";
                if ("0000".equals(data.getResponseCode())) {
                    RepaySchedule repaySchedule = new RepaySchedule();
                    repaySchedule.setId(repayVo.getId());
                    if ("SUCCESS".equals(data.getStatus())) {
                        //成功
                        repaySchedule.setSettleFlag(SettleFlagConstant.CLOSE);
                        repaySchedule.setDatePay(DateUtil.today());
                        repaySchedule.setDatePayTime(data.getRepayTime());
                        repayStatus = 1;
                    } else if ("FAIL".equals(data.getStatus())) {
                        // 失败
                        repaySchedule.setSettleFlag(SettleFlagConstant.RUNNING);
                        repayStatus = 2;
                        reason = data.getResult();
                    } else if ("CLOSE".equals(data.getStatus())) {
                        // 关闭
                        repaySchedule.setSettleFlag(SettleFlagConstant.CLOSE);
                        repaySchedule.setDatePay(DateUtil.today());
                        repaySchedule.setDatePayTime(data.getRepayTime());
                        repayStatus = 1;
                    } else {
                        log.info("本息还款中查询 - 订单号: {}, 状态: {}", repayVo.getRepayApplyNo(), data.getStatus());
                        return;
                    }
                    repayScheduleMapper.updateById(repaySchedule);
                    List<RepayScheduleApply> repaySchedules = repayScheduleMapper.repayScheduleApplyList(repayVo.getRepayApplyNo());
                    for (RepayScheduleApply repayScheduleApply : repaySchedules) {
                        if (repayScheduleApply != null) {
                            repayScheduleApply.setRepayStatus(repayStatus);
                            repayScheduleApply.setReason(reason);
                            repayScheduleApply.setResponseTime(DateUtil.date());
                            repayScheduleApplyMapper.updateById(repayScheduleApply);
                        } else {
                            log.info("本息还款中查询 - 订单号: {}, 资方查询响应失败：{}", repayVo.getRepayApplyNo(), data);
                            return;
                        }
                    }
                    if ("SUCCESS".equals(data.getStatus())) {
                        log.info("本息还款中查询 - 订单号: {}, 资方查询成功：{}", repayVo.getRepayApplyNo(), data);
                        disburseDataMapper.updateCreditStatus(disburseId);
                    }
                }
                log.info("本息还款中查询 - 订单号: {}, 资方查询失败：{}", repayVo.getRepayApplyNo(), repaymentResult);
                return;
            }
        } catch (Exception e) {
            log.info("本息还款中查询 - 订单号: {}, 异常: {}", repayVo.getRepayApplyNo(),e);
            return;
        }
    }
}
