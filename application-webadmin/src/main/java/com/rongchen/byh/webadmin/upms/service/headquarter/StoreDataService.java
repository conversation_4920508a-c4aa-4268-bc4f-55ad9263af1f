package com.rongchen.byh.webadmin.upms.service.headquarter;

import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.webadmin.upms.model.StoreData;

import java.util.List;

/**
 * 门店小组表(StoreData)表服务接口
 *
 * <AUTHOR>
 * @since 2025-02-27 14:20:25
 */
public interface StoreDataService {
    ResponseResult<MyPageData<StoreData>> storeDataList(StoreData dto, MyPageParam pageParam);

    Boolean insertStoreData(StoreData storeData);

    Boolean updateStoreData(StoreData storeData);

    Boolean deleteStoreData(StoreData storeData);

    List<StoreData> storeList();
}

