package com.rongchen.byh.webadmin.upms.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rongchen.byh.webadmin.upms.model.DzBatchExecutionLogEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 项目名称：byh_java
 * 文件名称: DzBatchExecutionLogMapper
 * 创建时间: 2025-05-27 15:00
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.dao
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
public interface DzBatchExecutionLogMapper extends BaseMapper<DzBatchExecutionLogEntity> {

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(DzBatchExecutionLogEntity record);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(DzBatchExecutionLogEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(DzBatchExecutionLogEntity record);

    int updateBatch(@Param("list") List<DzBatchExecutionLogEntity> list);

    int updateBatchSelective(@Param("list") List<DzBatchExecutionLogEntity> list);

    int batchInsert(@Param("list") List<DzBatchExecutionLogEntity> list);

    int batchInsertOrUpdate(@Param("list") List<DzBatchExecutionLogEntity> list);
}