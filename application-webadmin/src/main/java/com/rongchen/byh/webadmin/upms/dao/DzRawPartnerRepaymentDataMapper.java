package com.rongchen.byh.webadmin.upms.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rongchen.byh.webadmin.upms.model.DzRawPartnerRepaymentDataEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 项目名称：byh_java
 * 文件名称: DzRawPartnerRepaymentDataMapper
 * 创建时间: 2025-05-23 11:30
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.dao
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
public interface DzRawPartnerRepaymentDataMapper extends BaseMapper<DzRawPartnerRepaymentDataEntity> {

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(DzRawPartnerRepaymentDataEntity record);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(DzRawPartnerRepaymentDataEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(DzRawPartnerRepaymentDataEntity record);

    int updateBatch(@Param("list") List<DzRawPartnerRepaymentDataEntity> list);

    int updateBatchSelective(@Param("list") List<DzRawPartnerRepaymentDataEntity> list);

    int batchInsert(@Param("list") List<DzRawPartnerRepaymentDataEntity> list);

    int batchInsertOrUpdate(@Param("list") List<DzRawPartnerRepaymentDataEntity> list);
}