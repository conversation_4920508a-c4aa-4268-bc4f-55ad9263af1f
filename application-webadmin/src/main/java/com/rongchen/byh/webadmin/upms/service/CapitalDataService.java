package com.rongchen.byh.webadmin.upms.service;

import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.webadmin.upms.model.CapitalData;
import com.rongchen.byh.webadmin.upms.model.GroupData;

import java.util.List;

/**
 * 资方信息表(CapitalData)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-03 17:29:59
 */
public interface CapitalDataService {

    ResponseResult<MyPageData<CapitalData>> capitalList(CapitalData capitalData, MyPageParam myPageParam);

    /**
     * 新增数据
     *
     * @param capitalData 实例对象
     * @return 实例对象
     */
    boolean insert(CapitalData capitalData);

    /**
     * 修改数据
     *
     * @param capitalData 实例对象
     * @return 实例对象
     */
    boolean update(CapitalData capitalData);

    /**
     * 资方字典
     * @return
     */
    List<CapitalData> capitalDictList();

}
