package com.rongchen.byh.webadmin.upms.dto.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName CreditOrderListDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/6 15:32
 * @Version 1.0
 **/
@Data
public class CreditOrderListDto {

    @Schema(description = "客户名称")
    private String userName;

    @Schema(description = "客户手机号")
    private String mobile;

    @Schema(description = "授信状态")
    private Integer creditStatus;

    @Schema(description = "开始时间 yyyy-mm-ss")
    private String timeStart;

    @Schema(description = "结束时间")
    private String timeEnd;

    @Schema(description = "风险等级")
    private String riskLevel;

    @Schema(description = "渠道id")
    private Long channelId;

    @Schema(description = "系统用户id")
    private Long sysUserId;

    @Schema(description = "系统用户名称")
    private String sysRoleName;

    @Schema(description = "用户来源")
    private String userSource;

    @Schema(description = "门店名称")
    private String  storeName;

    @Schema(description = "销售员名称")
    private String  saleUserName;

    @Schema(description = "小组名称")
    private String  groupName;
}
