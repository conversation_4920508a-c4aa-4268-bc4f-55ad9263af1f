package com.rongchen.byh.webadmin.reconciliation.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rongchen.byh.webadmin.reconciliation.mapper.transform.TransformFunction;
import com.rongchen.byh.webadmin.reconciliation.mapper.transform.TransformFunctionRegistry;
import com.rongchen.byh.webadmin.reconciliation.model.NormalizedTransaction;
import com.rongchen.byh.webadmin.upms.model.DzFieldMappingRulesEntity;
import com.rongchen.byh.webadmin.upms.service.DzFieldMappingRulesService;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 记录映射服务。
 * <p>
 * 负责将从
 * {@link com.rongchen.byh.webadmin.reconciliation.dataProvider.DataProvider}
 * 加载的原始数据 ({@code List<Map<String, Object>>}) 转换为统一的对账数据模型列表
 * (例如 {@code List<NormalizedLoanRecord>})。
 * <p>
 * 此过程涉及查询字段映射规则 (通过 {@link DzFieldMappingRulesService}) 和应用字段值转换
 * (通过 {@link TransformFunctionRegistry})。
 */
@Service
public class RecordMapperService {

    private static final Logger logger = LoggerFactory.getLogger(RecordMapperService.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    private final DzFieldMappingRulesService dzFieldMappingRulesService;
    private final TransformFunctionRegistry transformFunctionRegistry;

    @Autowired
    public RecordMapperService(DzFieldMappingRulesService dzFieldMappingRulesService,
            TransformFunctionRegistry transformFunctionRegistry) {
        this.dzFieldMappingRulesService = dzFieldMappingRulesService;
        this.transformFunctionRegistry = transformFunctionRegistry;
    }

    /**
     * 将原始记录列表映射到指定类型的统一数据模型列表。
     *
     * @param rawRecords      从DataProvider加载的原始数据记录列表。
     * @param channelCode     当前处理的渠道编码。
     * @param transactionType 当前处理的交易类型。
     * @param targetClass     目标统一数据模型的Class对象 (例如 NormalizedLoanRecord.class)。
     *                        该Class必须实现 NormalizedTransaction 接口。
     * @param <T>             目标统一数据模型的类型，必须是 NormalizedTransaction 的子类型。
     * @return 映射后的统一数据模型列表。
     * @throws Exception 如果映射过程中发生严重错误。
     */
    public <T extends NormalizedTransaction> List<T> mapRecords(
            List<Map<String, Object>> rawRecords,
            String channelCode,
            String transactionType,
            Class<T> targetClass) throws Exception {
        if (CollectionUtils.isEmpty(rawRecords)) {
            return new ArrayList<>();
        }

        List<DzFieldMappingRulesEntity> mappingRules = dzFieldMappingRulesService
                .findActiveRulesByChannelAndType(channelCode, transactionType);

        if (CollectionUtils.isEmpty(mappingRules)) {
            logger.warn("未找到渠道 '{}' 和交易类型 '{}' 的有效字段映射规则。原始数据将无法映射。", channelCode, transactionType);
            throw new IllegalStateException("未配置映射规则: channel=" + channelCode + ", type=" + transactionType);
        }

        List<T> normalizedList = new ArrayList<>();
        for (Map<String, Object> rawRecord : rawRecords) {
            try {
                T normalizedRecord = targetClass.getDeclaredConstructor().newInstance();

                // 设置 NormalizedTransaction 接口定义的通用元数据字段
                normalizedRecord.setSourceChannel(channelCode);
                // String originalId = extractOriginalId(rawRecord); // 假设有一个方法从rawRecord提取原始ID
                // normalizedRecord.setOriginalRecordId(originalId);
                // normalizedRecord.setAdditionalData(new HashMap<>()); // 初始化 additionalData

                for (DzFieldMappingRulesEntity rule : mappingRules) {
                    Object sourceValue = rawRecord.get(rule.getSourceFieldName());
                    Object targetValue = sourceValue;

                    if (StringUtils.isNotBlank(rule.getTransformFunctionName())) {
                        TransformFunction function = transformFunctionRegistry.get(rule.getTransformFunctionName());
                        if (function != null) {
                            Map<String, String> transformParams = parseTransformParams(rule.getDefaultValueExpr());
                            targetValue = function.apply(sourceValue, transformParams);
                        } else {
                            logger.warn("字段映射规则 [源字段: {}] 指定了未注册的转换函数: {}", rule.getSourceFieldName(),
                                    rule.getTransformFunctionName());
                        }
                    } else if (sourceValue == null && StringUtils.isNotBlank(rule.getDefaultValueExpr())) {
                        targetValue = rule.getDefaultValueExpr();
                        logger.debug("字段 '{}' 应用默认值: {}", rule.getNormalizedFieldName(), targetValue);
                    }

                    try {
                        PropertyUtils.setProperty(normalizedRecord, rule.getNormalizedFieldName(), targetValue);
                    } catch (NoSuchMethodException e) {
                        // 如果属性不存在，并且目标是NormalizedTransaction，尝试放入additionalData
                        if (normalizedRecord.getAdditionalData() != null) {
                            logger.debug("目标类 '{}' 没有属性 '{}'，尝试存入additionalData。源字段: '{}'",
                                    targetClass.getSimpleName(), rule.getNormalizedFieldName(),
                                    rule.getSourceFieldName());
                            normalizedRecord.getAdditionalData().put(rule.getNormalizedFieldName(), targetValue);
                        } else {
                            logger.error("设置属性失败: 目标类 '{}' 没有属性 '{}' (或setter方法缺失) 且additionalData为null。源字段: '{}'",
                                    targetClass.getSimpleName(), rule.getNormalizedFieldName(),
                                    rule.getSourceFieldName());
                        }
                    } catch (Exception e) {
                        logger.error("设置属性 '{}' 到目标类 '{}' 时发生未知错误。源字段: '{}', 值: '{}'",
                                rule.getNormalizedFieldName(), targetClass.getSimpleName(), rule.getSourceFieldName(),
                                targetValue, e);
                    }
                }
                normalizedList.add(normalizedRecord);
            } catch (ReflectiveOperationException e) {
                logger.error("创建目标类型 '{}' 实例失败。请确保它有一个无参构造函数。", targetClass.getSimpleName(), e);
            } catch (Exception e) {
                logger.error("处理原始记录失败: {} (渠道: {}, 类型: {})", rawRecord, channelCode, transactionType, e);
            }
        }
        return normalizedList;
    }

    /**
     * 从规则定义的字符串中解析转换函数参数。
     * 这是一个示例性的辅助方法，您需要根据实际存储参数的方式来实现。
     * 例如，如果 defaultValueExpr 存储的是 JSON 格式的参数: {"format":"yyyy-MM-dd"}
     */
    private Map<String, String> parseTransformParams(String paramsString) {
        if (StringUtils.isBlank(paramsString)) {
            return Collections.emptyMap();
        }
        try {
            return objectMapper.readValue(paramsString, new TypeReference<Map<String, String>>() {
            });
        } catch (JsonProcessingException e) {
            logger.warn("解析转换函数参数JSON失败: '{}'. 将返回空参数Map. 错误: {}", paramsString, e.getMessage());
            return Collections.emptyMap();
        }
    }
    // private String extractOriginalId(Map<String, Object> rawRecord) { ... }
}