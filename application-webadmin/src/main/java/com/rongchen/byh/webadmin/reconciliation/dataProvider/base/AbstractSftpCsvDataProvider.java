package com.rongchen.byh.webadmin.reconciliation.dataProvider.base;

import com.rongchen.byh.webadmin.reconciliation.context.ReconciliationContext;
import com.rongchen.byh.webadmin.reconciliation.dataProvider.DataProvider;
import com.rongchen.byh.webadmin.reconciliation.util.sftp.SFTPUtils;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * SFTP CSV/TXT 数据提供者的抽象基类。
 * 封装了通用的SFTP连接、文件下载和CSV解析逻辑。
 */
public abstract class AbstractSftpCsvDataProvider implements DataProvider {
    private static final Logger logger = LoggerFactory.getLogger(AbstractSftpCsvDataProvider.class);

    /**
     * 子类提供SFTP连接配置。
     *
     * @param context 对账上下文
     * @return SFTP配置对象
     */
    protected abstract SftpConfig getSftpConfig(ReconciliationContext context);

    /**
     * 子类提供目标数据文件在SFTP服务器上的相对路径（相对于SftpConfig中定义的baseDirectory）。
     * 例如："20250122/loan_20250122.txt"
     *
     * @param context 对账上下文
     * @return 文件相对路径
     */
    protected abstract String getRemoteDataFilePath(ReconciliationContext context);

    /**
     * 子类提供对应的.ok文件在SFTP服务器上的相对路径（相对于SftpConfig中定义的baseDirectory）。
     * 例如："20250122/loan_20250122.txt.ok"
     *
     * @param context 对账上下文
     * @return .ok文件相对路径
     */
    protected abstract String getOkFilePath(ReconciliationContext context);

    /**
     * 子类可选择性地提供多个基础目录列表，用于支持从多个目录同时下载数据。
     * 如果子类实现了此方法并返回非空列表，则会循环处理每个目录中的数据文件。
     * 如果子类未重写此方法或返回空列表，则使用getSftpConfig()中的单一baseDirectory。
     *
     * @param context 对账上下文
     * @return 基础目录列表，如果返回null或空列表则使用单目录模式
     */
    protected List<String> getBaseDirectories(ReconciliationContext context) {
        // 默认实现返回null，表示使用单目录模式（向后兼容）
        return null;
    }

    /**
     * 子类提供CSV/TXT文件的解析格式，使用Apache Commons CSV的CSVFormat。
     *
     * @param context 对账上下文
     * @return CSVFormat 配置
     */
    protected abstract CSVFormat getCsvFormat(ReconciliationContext context);

    /**
     * 子类提供CSV/TXT文件的表头（字段名数组）。
     * <p>
     * 如果CSV文件本身包含表头行，并且CSVFormat配置为{@code CSVFormat.withFirstRecordAsHeader()}，
     * 则此方法可以返回null或空数组，解析器将自动使用文件中的表头。
     * <p>
     * 如果CSV文件没有表头行（例如许多TXT文件），则此方法必须返回一个有效的字段名数组，
     * 用于将每一列数据映射到Map中的键。
     *
     * @param context 对账上下文
     * @return 字段名数组，或在特定情况下为null/空
     */
    protected abstract String[] getCsvHeaders(ReconciliationContext context);

    @Override
    public List<Map<String, Object>> loadData(ReconciliationContext context) throws Exception {
        // 检查是否使用多目录模式
        List<String> baseDirectories = getBaseDirectories(context);
        if (baseDirectories != null && !baseDirectories.isEmpty()) {
            return loadDataFromMultipleDirectories(context, baseDirectories);
        } else {
            // 使用单目录模式（向后兼容）
            SftpConfig sftpConfig = getSftpConfig(context);
            return loadDataFromSingleDirectory(context, sftpConfig, sftpConfig.baseDirectory());
        }
    }

    /**
     * 从多个目录加载数据并合并结果。
     */
    private List<Map<String, Object>> loadDataFromMultipleDirectories(ReconciliationContext context, List<String> baseDirectories) throws Exception {
        List<Map<String, Object>> allRecords = new ArrayList<>();
        SftpConfig sftpConfig = getSftpConfig(context);

        logger.info("开始多目录SFTP数据同步: 渠道={}, 类型={}, 日期={}, 目录数量={}",
                context.getChannelCode(), context.getTransactionType(), context.getProcessingDate(), baseDirectories.size());

        for (String baseDirectory : baseDirectories) {
            try {
                logger.info("处理目录: {}", baseDirectory);
                List<Map<String, Object>> directoryRecords = loadDataFromSingleDirectory(context, sftpConfig, baseDirectory);
                allRecords.addAll(directoryRecords);
                logger.info("目录 {} 成功加载 {} 条记录", baseDirectory, directoryRecords.size());
            } catch (Exception e) {
                logger.error("处理目录 {} 时发生错误: {}", baseDirectory, e.getMessage(), e);
                // 继续处理其他目录，不中断整个流程
            }
        }

        logger.info("多目录SFTP数据同步完成: 渠道={}, 类型={}, 日期={}, 总记录数={}",
                context.getChannelCode(), context.getTransactionType(), context.getProcessingDate(), allRecords.size());

        return allRecords;
    }

    /**
     * 从单个目录加载数据。
     */
    private List<Map<String, Object>> loadDataFromSingleDirectory(ReconciliationContext context, SftpConfig sftpConfig, String baseDirectory) throws Exception {
        String relativeDataFilePath = getRemoteDataFilePath(context);
        String relativeOkFilePath = getOkFilePath(context);

        String fullRemoteDataFilePath = normalizePath(baseDirectory, relativeDataFilePath);
        String fullOkFilePath = normalizePath(baseDirectory, relativeOkFilePath);

        List<Map<String, Object>> records = new ArrayList<>();
        InputStream inputStream = null;

        try {
            logger.info("开始处理SFTP数据: 渠道={}, 类型={}, 日期={}, 文件路径={}",
                    context.getChannelCode(), context.getTransactionType(), context.getProcessingDate(),
                    fullRemoteDataFilePath);

            // 创建临时的SftpConfig，使用指定的baseDirectory
            SftpConfig tempSftpConfig = new SftpConfig(
                    sftpConfig.host(), sftpConfig.port(), sftpConfig.username(),
                    sftpConfig.password(), baseDirectory);

            if (!SFTPUtils.checkFileExists(tempSftpConfig, fullOkFilePath)) {
                logger.info(".ok文件 '{}' 不存在。可能当日无文件或未就绪。返回空列表。", fullOkFilePath);
                return records;
            }
            logger.info(".ok文件 '{}' 存在，继续下载数据文件。", fullOkFilePath);

            byte[] fileBytes = SFTPUtils.downloadFileAsBytes(tempSftpConfig, fullRemoteDataFilePath);
            if (fileBytes == null || fileBytes.length == 0) {
                logger.info("数据文件 '{}' 为空或下载失败。返回空列表。", fullRemoteDataFilePath);
                return records;
            }
            logger.info("数据文件 '{}' 下载成功，大小: {} bytes。开始解析...", fullRemoteDataFilePath, fileBytes.length);
            inputStream = new ByteArrayInputStream(fileBytes);

            String[] headers = getCsvHeaders(context);
            CSVFormat csvFormat = getCsvFormat(context);

            if (!csvFormat.getSkipHeaderRecord() && (headers == null || headers.length == 0)) {
                if (csvFormat.getHeader() == null || csvFormat.getHeader().length == 0) {
                    throw new IllegalStateException(
                            "如果 CSV 文件不包含标题行，或者 CSVFormat 未配置为使用第一条记录作为标题，则必须通过 getCsvHeaders（） 定义 CSV 标题。");
                }
            }

            try (Reader reader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
                    CSVParser csvParser = new CSVParser(reader, csvFormat)) {

                String[] actualHeaders = headers;
                if (csvFormat.getHeader() != null && csvFormat.getHeader().length > 0) {
                    actualHeaders = csvFormat.getHeader();
                }

                if (actualHeaders == null || actualHeaders.length == 0) {
                    throw new IllegalStateException(
                            "Effective CSV headers are missing. Cannot map columns to field names.");
                }

                for (CSVRecord csvRecord : csvParser) {
                    Map<String, Object> recordMap = new LinkedHashMap<>();
                    if (csvRecord.size() != actualHeaders.length && !csvFormat.getIgnoreEmptyLines()) {
                        if (!csvRecord.get(0).isEmpty() || csvRecord.size() > 1) {
                            logger.warn(
                                    "[AbstractSftpCsvDataProvider] 记录 {} 列数 ({}) 与预期表头列数 ({}) 不符。文件: {}. 跳过此行.",
                                    csvRecord.getRecordNumber(), csvRecord.size(), actualHeaders.length,
                                    fullRemoteDataFilePath);
                            continue;
                        }
                    }
                    for (int i = 0; i < actualHeaders.length; i++) {
                        if (i < csvRecord.size()) {
                            recordMap.put(actualHeaders[i], csvRecord.get(i));
                        } else {
                            recordMap.put(actualHeaders[i], null);
                        }
                    }
                    records.add(recordMap);
                }
            }

        } catch (Exception e) {
            logger.error("加载SFTP数据文件 '{}' 时出错: {}", fullRemoteDataFilePath, e.getMessage(), e);
            throw new RuntimeException("加载SFTP数据失败: " + fullRemoteDataFilePath, e);
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (java.io.IOException e) {
                    logger.error("关闭SFTP文件输入流失败: {}", e.getMessage(), e);
                }
            }
        }
        logger.info("成功加载并解析SFTP数据 {} 条。文件: {}", records.size(), fullRemoteDataFilePath);
        return records;
    }

    private String normalizePath(String basePath, String relativePath) {
        String cleanBasePath = basePath.endsWith("/") ? basePath.substring(0, basePath.length() - 1) : basePath;
        String cleanRelativePath = relativePath.startsWith("/") ? relativePath.substring(1) : relativePath;
        return cleanBasePath + "/" + cleanRelativePath;
    }

    /**
     * 定义SFTP连接和认证所需的配置信息。
     * 修改为public static以便外部SFTPUtils可以访问。
     */
    @Data
    public static class SftpConfig {
        private final String host;
        private final int port;
        private final String username;
        private final String password;
        private final String baseDirectory;

        public SftpConfig(String host, int port, String username, String password, String baseDirectory) {
            this.host = host;
            this.port = port;
            this.username = username;
            this.password = password;
            this.baseDirectory = baseDirectory;
        }

        public String host() {
            return host;
        }

        public int port() {
            return port;
        }

        public String username() {
            return username;
        }

        public String password() {
            return password;
        }

        public String baseDirectory() {
            return baseDirectory;
        }
    }
    // getChannelCode(), getTransactionType(), getDataSourceType() 由具体子类实现
}