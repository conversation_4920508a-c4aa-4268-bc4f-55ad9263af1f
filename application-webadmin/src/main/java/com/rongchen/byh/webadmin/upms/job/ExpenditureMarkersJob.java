package com.rongchen.byh.webadmin.upms.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rongchen.byh.webadmin.upms.dao.CapitalProviderMapper;
import com.rongchen.byh.webadmin.upms.model.CapitalProvider;
import com.rongchen.byh.webadmin.upms.vo.cp.UserAndAmountVo;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ExpenditureMarkersJob {

    @Resource
    private CapitalProviderMapper capitalProviderMapper;
    @XxlJob("expenditureMarkersHandler")
    public void expenditureMarkers() {
        // 一次性查询出符合条件的 CapitalProvider 列表，减少数据库查询次数
        LambdaQueryWrapper<CapitalProvider> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CapitalProvider::getStatus, 0).eq(CapitalProvider::getFlag, 0);
        List<CapitalProvider> capitalProviders = capitalProviderMapper.selectList(queryWrapper);

        for (CapitalProvider capitalProvider : capitalProviders) {
            // 查询该 disburse_data、user_data表中在CapitalProvider表中设置的时间段内当天注册，当天支用的用户和授信金额列表
            List<UserAndAmountVo> userAndAmountVos = capitalProviderMapper.selectCreateAndLoan(capitalProvider.getStartTime(), capitalProvider.getEndTime());

            // 提前过滤掉无效数据，避免后续不必要的计算
            userAndAmountVos = userAndAmountVos.stream()
                    .filter(vo -> vo != null && vo.getCreditAmount() != null)
                    .collect(Collectors.toList());

            BigDecimal totalAmount = BigDecimal.ZERO;
            Boolean flag = false;
            for (UserAndAmountVo userAndAmountVo : userAndAmountVos) {
                // 累加授信金额
                BigDecimal nextTotal = totalAmount.add(userAndAmountVo.getCreditAmount());
                if (nextTotal.compareTo(capitalProvider.getFunds()) > 0) {
                    flag = true;
                    break;
                }
                totalAmount = nextTotal;
                if (totalAmount.compareTo(capitalProvider.getFunds()) < 0) {
                    // 累计金额小于资金上限，更新 disburse_data 表的 cp_id 字段
                    capitalProviderMapper.updateDisburseCpid(capitalProvider.getId(), userAndAmountVo.getUserId());
                }
            }
            if (flag){
                // 如果标志变量flag为真，则更新资金提供者的相关信息
                capitalProviderMapper.updateCapitalProvider(capitalProvider.getId(), totalAmount, 1);
            }
        }
    }
}
