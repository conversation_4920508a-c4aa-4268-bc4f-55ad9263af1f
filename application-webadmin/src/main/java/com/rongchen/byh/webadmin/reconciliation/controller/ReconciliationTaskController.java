package com.rongchen.byh.webadmin.reconciliation.controller;

import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.webadmin.reconciliation.job.ReconciliationJob;
import com.rongchen.byh.webadmin.upms.model.DzBatchExecutionLogEntity;
import com.rongchen.byh.webadmin.upms.service.DzBatchExecutionLogService;
import java.time.LocalDate;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 对账任务管理API控制器。
 * <p>
 * 提供手动触发对账任务、查询任务状态等接口。
 */
@RestController
@RequestMapping("/admin/reconciliation/tasks")
public class ReconciliationTaskController {

    private final ReconciliationJob reconciliationJob; // 注入我们之前创建的Job类
    private final DzBatchExecutionLogService dzBatchLogService; // 用于查询批次日志

    @Autowired
    public ReconciliationTaskController(ReconciliationJob reconciliationJob,
            DzBatchExecutionLogService dzBatchLogService) {
        this.reconciliationJob = reconciliationJob;
        this.dzBatchLogService = dzBatchLogService;
    }

    /**
     * 手动触发每日对账任务（通常用于测试或补跑）。
     * 注意：实际的定时调度应由Spring Scheduler或XXL-Job等配置。
     */
    @PostMapping("/trigger/daily")
    public ResponseResult<String> triggerDailyReconciliation() {
        try {
            // 异步执行，避免长时间阻塞HTTP请求
            // 如果reconciliationJob.runDailyReconciliation()是耗时操作，应考虑异步执行
            // 例如: CompletableFuture.runAsync(() ->
            // reconciliationJob.runDailyReconciliation());
            reconciliationJob.runDailyReconciliation();
            return ResponseResult.success("每日对账任务已触发。");
        } catch (Exception e) {
            return ResponseResult.error(ErrorCodeEnum.FAIL,"触发每日对账任务失败: " + e.getMessage());
        }
    }

    /**
     * 根据批次ID查询批次执行日志。
     * 
     * @param batchId 批次ID
     * @return 批次执行日志实体
     */
    @GetMapping("/logs/batch/{batchId}")
    public ResponseResult<DzBatchExecutionLogEntity> getBatchLogById(@PathVariable String batchId) {
        // 假设DzBatchExecutionLogService有类似
        // getOne(Wrappers.<DzBatchExecutionLogEntity>lambdaQuery().eq(DzBatchExecutionLogEntity::getBatchId,
        // batchId)) 的方法
        return ResponseResult.success(dzBatchLogService.getOneByBatchId(batchId)); // 需要您在Service中实现此方法
    }

    /**
     * 根据处理日期查询批次执行日志列表。
     * 
     * @param processingDate 处理日期 (格式 yyyy-MM-dd)
     * @return 批次执行日志实体列表
     */
    @GetMapping("/logs/date/{processingDate}")
    public ResponseResult<List<DzBatchExecutionLogEntity>> getBatchLogsByDate(
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate processingDate) {
        // 假设DzBatchExecutionLogService有类似
        // list(Wrappers.<DzBatchExecutionLogEntity>lambdaQuery().eq(DzBatchExecutionLogEntity::getProcessingDate,
        // processingDate)) 的方法
        return ResponseResult.success(dzBatchLogService.listByProcessingDate(processingDate)); // 需要您在Service中实现此方法
    }

    // TODO: 可能还需要其他任务控制接口，如：
    // - 触发特定渠道、特定交易类型的对账
    // - 查询特定任务的当前状态 (如果任务是长时间运行且有中间状态)
    // - 暂停/恢复/取消任务 (如果调度器支持)
}