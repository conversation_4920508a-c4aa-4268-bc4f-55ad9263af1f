package com.rongchen.byh.webadmin.upms.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**

 * 项目名称：byh_java
 * 文件名称: DzRawPartnerRepaymentDataEntity
 * 创建时间: 2025-05-23 11:30
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.model
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */

/**
 * 资方原始还款数据表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "`dz_raw_partner_repayment_data`")
public class DzRawPartnerRepaymentDataEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * SFTP同步批次ID
     */
    @TableField(value = "`sync_batch_id`")
    private String syncBatchId;

    /**
     * 渠道编码 (如 "XHY")
     */
    @TableField(value = "`channel_code`")
    private String channelCode;

    /**
     * 原始文件对应的业务处理日期 (T日)
     */
    @TableField(value = "`processing_date`")
    private Date processingDate;

    /**
     * 交易类型 (固定为REPAYMENT)
     */
    @TableField(value = "`transaction_type`")
    private String transactionType;

    /**
     * 原始文件名 (可选)
     */
    @TableField(value = "`original_filename`")
    private String originalFilename;

    /**
     * 资方借款单号 (XHY: transSeqno)
     */
    @TableField(value = "`partner_loan_order_no`")
    private String partnerLoanOrderNo;

    /**
     * 资方还款流水号 (XHY: repaySeqNo)
     */
    @TableField(value = "`partner_repayment_id`")
    private String partnerRepaymentId;

    /**
     * 还款成功日期时间 (XHY: repayEndDate, yyyyMMdd HHmmss)
     */
    @TableField(value = "`repayment_effective_date`")
    private Date repaymentEffectiveDate;

    /**
     * 本次实还总金额 (XHY: repayAmt)
     */
    @TableField(value = "`total_repaid_amount`")
    private BigDecimal totalRepaidAmount;

    /**
     * 还款提交日期时间 (XHY: transDate, yyyyMMdd HHmmss)
     */
    @TableField(value = "`submission_datetime`")
    private Date submissionDatetime;

    /**
     * 还款期数 (XHY: installCnt, 可多期，原始字符串)
     */
    @TableField(value = "`installment_numbers_str`")
    private String installmentNumbersStr;

    /**
     * 还款方式代码 (XHY: repayMode)：01线上还款 02线下还款
     */
    @TableField(value = "`repayment_method_code`")
    private String repaymentMethodCode;

    /**
     * 支付通道代码 (XHY: repayChannel)：	TL 通联,BF 宝付,Partner 资方
     */
    @TableField(value = "`payment_channel_code`")
    private String paymentChannelCode;

    /**
     * 债转状态代码 (XHY: CompensatoryStatus)：0债转前扣款，1债转后扣款
     */
    @TableField(value = "`debt_transfer_status_code`")
    private String debtTransferStatusCode;

    /**
     * 还款类型代码 (XHY: repayType)：
     CURRENT：归还当期到期（账期日还款）
     PREPAYMENT-提前还当期
     OVERDUE：归还逾期
     OVER：归还到期（逾期+当期到期）
     CLEAN：全部结清（提前结清）
     OVERCLEAN：逾期结清

     */
    @TableField(value = "`repayment_type_code`")
    private String repaymentTypeCode;

    /**
     * 本次实还本金 (XHY: repayPrincipal)
     */
    @TableField(value = "`repaid_principal_amount`")
    private BigDecimal repaidPrincipalAmount;

    /**
     * 本次实还利息 (XHY: repayInterest)
     */
    @TableField(value = "`repaid_interest_amount`")
    private BigDecimal repaidInterestAmount;

    /**
     * 本次实还费用 (XHY: repayFee)
     */
    @TableField(value = "`repaid_fee_amount`")
    private BigDecimal repaidFeeAmount;

    /**
     * 本次实还罚息 (XHY: repayOverdueInterest)
     */
    @TableField(value = "`repaid_overdue_amount`")
    private BigDecimal repaidOverdueAmount;

    /**
     * 存储所有其他原始字段的JSON对象
     */
    @TableField(value = "`extended_fields_json`")
    private String extendedFieldsJson;

    /**
     * 数据加载到此表的时间
     */
    @TableField(value = "`load_time`")
    private Date loadTime;
}