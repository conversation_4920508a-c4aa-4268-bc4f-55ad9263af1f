package com.rongchen.byh.webadmin.upms.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rongchen.byh.webadmin.upms.dao.DzRawPartnerCreditDataMapper;
import com.rongchen.byh.webadmin.upms.model.DzRawPartnerCreditDataEntity;
import java.util.List;
import org.springframework.stereotype.Service;
/**

 * 项目名称：byh_java
 * 文件名称: DzRawPartnerCreditDataService
 * 创建时间: 2025-05-23 11:51
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.service
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
@Service
public class DzRawPartnerCreditDataService extends ServiceImpl<DzRawPartnerCreditDataMapper, DzRawPartnerCreditDataEntity> {

    
    public int insertSelective(DzRawPartnerCreditDataEntity record) {
        return baseMapper.insertSelective(record);
    }
    
    public int updateByPrimaryKeySelective(DzRawPartnerCreditDataEntity record) {
        return baseMapper.updateByPrimaryKeySelective(record);
    }
    
    public int updateByPrimaryKey(DzRawPartnerCreditDataEntity record) {
        return baseMapper.updateByPrimaryKey(record);
    }
    
    public int updateBatch(List<DzRawPartnerCreditDataEntity> list) {
        return baseMapper.updateBatch(list);
    }
    
    public int updateBatchSelective(List<DzRawPartnerCreditDataEntity> list) {
        return baseMapper.updateBatchSelective(list);
    }
    
    public int batchInsert(List<DzRawPartnerCreditDataEntity> list) {
        return baseMapper.batchInsert(list);
    }
    
    public int batchInsertOrUpdate(List<DzRawPartnerCreditDataEntity> list) {
        return baseMapper.batchInsertOrUpdate(list);
    }
}
