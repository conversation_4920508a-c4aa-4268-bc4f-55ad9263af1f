package com.rongchen.byh.webadmin.upms.controller.headquarters;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.rongchen.byh.common.core.annotation.MyRequestBody;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.MyCommonUtil;
import com.rongchen.byh.common.log.annotation.OperationLog;
import com.rongchen.byh.common.log.model.constant.SysOperationLogType;
import com.rongchen.byh.webadmin.upms.dto.Headquarters.StaffDataDto;
import com.rongchen.byh.webadmin.upms.model.StaffData;
import com.rongchen.byh.webadmin.upms.service.headquarter.StaffDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 销售员工表(StaffData)表控制层
 *
 * <AUTHOR>
 * @since 2025-02-27 14:59:17
 */
@Tag(name = "销售信息相关接口")
@RestController
@RequestMapping("/admin/upms/staff")
public class StaffDataController {

    @Resource
    private StaffDataService staffDataService;

    /**
     * 分页查询销售信息
     *
     * @param dto   筛选条件
     * @return 查询结果
     */
    @Operation(summary = "销售信息-列表-查询")
    @PostMapping("/staffDataList")
    public ResponseResult<MyPageData<StaffData>> staffDataList(@MyRequestBody StaffData dto, @MyRequestBody MyPageParam pageParam) {
        return staffDataService.staffDataList(dto, pageParam);
    }

    /**
     * 新增数据
     *
     * @param dto 实体
     * @return 新增结果
     */
    @Operation(summary = "销售信息-列表-新增")
    @PostMapping("/insertStaffData")
    @SaCheckPermission("staff.insertStaffData")
    @OperationLog(type = SysOperationLogType.ADD)
    public ResponseResult<Boolean> add(@MyRequestBody StaffData dto) {
        Boolean insert = staffDataService.insert(dto);
        if (insert) {
            return ResponseResult.success(true);
        }

        return ResponseResult.error(ErrorCodeEnum.DUPLICATED_UNIQUE_KEY);
    }

    /**
     * 编辑数据
     *
     * @param dto 实体
     * @return 编辑结果
     */
    @Operation(summary = "销售信息-列表-修改")
    @PostMapping("/updateStaffData")
    @SaCheckPermission("staff.updateStaffData")
    @OperationLog(type = SysOperationLogType.UPDATE)
    public ResponseResult<Boolean> updateStaffData(@MyRequestBody StaffData dto) {
        Boolean update = staffDataService.update(dto);
        if (update) {
            return ResponseResult.success(true);
        }
        return ResponseResult.error(ErrorCodeEnum.DUPLICATED_UNIQUE_KEY,"数据修改失败，存在重复数据，请核对");
    }

    /**
     * 修改销售员工状态
     *
     * @param dto 实体
     * @return 编辑结果
     */
    @Operation(summary = "销售信息-销售员工-状态")
    @PostMapping("/freezeStaffStatus")
    @SaCheckPermission("staff.freezeStaffStatus")
    @OperationLog(type = SysOperationLogType.UPDATE)
    public ResponseResult<Boolean> freezeStaffStatus(@MyRequestBody StaffData dto) {
        Boolean update = staffDataService.freezeStaffStatus(dto);
        if (update) {
            return ResponseResult.success(true);
        }
        return ResponseResult.error(ErrorCodeEnum.DUPLICATED_UNIQUE_KEY,"数据修改失败，请核对");
    }

    /**
     * 修改销售员工对应订单号
     *
     * @param dto 实体
     * @return 编辑结果
     */
    @Operation(summary = "销售信息-销售员工-订单号")
    @PostMapping("/ordersStaff")
    @SaCheckPermission("staff.ordersStaff")
    @OperationLog(type = SysOperationLogType.UPDATE)
    public ResponseResult<Boolean> ordersStaff(@MyRequestBody StaffDataDto dto) {
        Integer integer = staffDataService.ordersStaff(dto);
        if (integer == 1) {
            return ResponseResult.success(true);
        } else if (integer == 2) {
            return ResponseResult.error(ErrorCodeEnum.DUPLICATED_UNIQUE_KEY,"数据修改失败，请核对");
        }
        return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST,"改销售不存在对应的订单，请核对");
    }


    /**
     * 删除销售数据
     *
     * @param dto 主键
     * @return 删除是否成功
     */
    @Operation(summary = "销售信息-销售-删除")
    @PostMapping("/deleteStaffData")
    @SaCheckPermission("staff.deleteStaffData")
    @OperationLog(type = SysOperationLogType.DELETE)
    public ResponseResult<Boolean> deleteStaffData(@MyRequestBody StaffData dto) {
        Boolean delete = staffDataService.deleteById(dto.getId());
        if (delete) {
            return ResponseResult.success(true);
        }
        return ResponseResult.error(ErrorCodeEnum.FAIL);
    }

    @Operation(summary = "以字典形式返回全部销售信息数据集合")
    @PostMapping("/staffDictList")
    public ResponseResult<List<Map<String, Object>>> staffList() {
        List<StaffData> list = staffDataService.staffList();
        return ResponseResult.success(
                MyCommonUtil.toDictDataList(list, StaffData::getId, StaffData::getUserName));
    }

}

