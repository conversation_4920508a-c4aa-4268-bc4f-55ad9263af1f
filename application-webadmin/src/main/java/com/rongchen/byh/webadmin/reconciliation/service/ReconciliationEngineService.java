package com.rongchen.byh.webadmin.reconciliation.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rongchen.byh.webadmin.reconciliation.context.ReconciliationContext;
import com.rongchen.byh.webadmin.reconciliation.dataProvider.DataProvider;
import com.rongchen.byh.webadmin.reconciliation.dataProvider.DataProviderFactory;
import com.rongchen.byh.webadmin.reconciliation.exception.ReconciliationProcessException;
import com.rongchen.byh.webadmin.reconciliation.model.DiffResult;
import com.rongchen.byh.webadmin.reconciliation.model.DifferenceType;
import com.rongchen.byh.webadmin.reconciliation.model.FieldDifferenceDetail;
import com.rongchen.byh.webadmin.reconciliation.model.NormalizedTransaction;
import com.rongchen.byh.webadmin.reconciliation.rules.RuleStrategyFactory;
import com.rongchen.byh.webadmin.reconciliation.strategy.ComparisonStrategy;
import com.rongchen.byh.webadmin.upms.model.DzReconciliationRulesEntity;
import com.rongchen.byh.webadmin.upms.service.DzReconciliationRulesService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.beanutils.PropertyUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 对账引擎核心服务。
 * <p>
 * 负责编排整个对账流程，包括：
 * 1. 加载我方和资方数据 (通过 {@link DataProviderFactory} 和 {@link DataProvider})。
 * 2. 将原始数据映射为统一数据模型 (通过 {@link RecordMapperService})。
 * 3. 加载对账规则 (通过 {@link DzReconciliationRulesService})。
 * 4. 根据规则创建比较策略 (通过 {@link RuleStrategyFactory})。
 * 5. 执行记录匹配 (通过 {@link MatcherService})。
 * 6. 对匹配上的记录进行字段比较 (通过 {@link ComparisonStrategy})。
 * 7. 汇总差异结果。
 */
@Service
public class ReconciliationEngineService {

    private static final Logger logger = LoggerFactory.getLogger(ReconciliationEngineService.class);
    private static final ObjectMapper objectMapper = new ObjectMapper(); // 用于解析规则内容中的JSON

    private final RuleStrategyFactory ruleStrategyFactory;
    private final MatcherService matcherService;

    @Autowired
    public ReconciliationEngineService(
            RuleStrategyFactory ruleStrategyFactory,
            MatcherService matcherService) {
        this.ruleStrategyFactory = ruleStrategyFactory;
        this.matcherService = matcherService;
    }

    /**
     * 执行对账核心逻辑：匹配和比较。
     *
     * @param ourNormalizedRecords     我方标准化记录列表。
     * @param partnerNormalizedRecords 资方标准化记录列表。
     * @param activeRule               当前激活的对账规则实体。
     * @param batchId                  当前对账批次ID (用于错误记录和差异结果)。
     * @return 对账产生的差异结果列表。
     */
    public List<DiffResult> reconcile(
            List<? extends NormalizedTransaction> ourNormalizedRecords,
            List<? extends NormalizedTransaction> partnerNormalizedRecords,
            DzReconciliationRulesEntity activeRule,
            String batchId // 传入batchId
    // ReconciliationContext context // 不再需要完整context，如果需要少量信息如processingDate可单独传入
    ) {
        logger.info("对账引擎核心处理开始，批次ID: {}. 我方记录数: {}, 资方记录数: {}. 使用规则ID: {}, 版本: {}",
                batchId,
                ourNormalizedRecords != null ? ourNormalizedRecords.size() : 0,
                partnerNormalizedRecords != null ? partnerNormalizedRecords.size() : 0,
                activeRule.getRuleId(), activeRule.getVersion());

        List<DiffResult> allDiffResults;

        try {
            // 规则有效性已由调用方(ReconciliationJob)保证
            // 数据加载和映射也已由调用方完成

            // 1. 解析规则中的 matchingConfig (之前是步骤4)
            JsonNode ruleContent = objectMapper.readTree(activeRule.getRuleContentJson());
            JsonNode matchingConfigNode = ruleContent.path("matchingConfig");
            if (matchingConfigNode.isMissingNode()) {
                logger.error("规则 {} (版本 {}) 的rule_content_json中缺少matchingConfig节点。批次ID: {}",
                        activeRule.getRuleId(), activeRule.getVersion(), batchId);
                throw new ReconciliationProcessException("规则中缺少matchingConfig", batchId);
            }

            // 提取匹配键字段，用于生成primaryMatchingKeyJson
            List<String> ourKeyFields = extractKeyFields(matchingConfigNode, "ourKeyFields");
            List<String> partnerKeyFields = extractKeyFields(matchingConfigNode, "partnerKeyFields");

            // 2. 执行记录匹配
            MatcherService.MatchingOutcome matchingOutcome = matcherService.matchRecords(
                    ourNormalizedRecords, partnerNormalizedRecords, matchingConfigNode, activeRule.getRuleId(),
                    activeRule.getVersion());
            allDiffResults = new ArrayList<>(matchingOutcome.getUnmatchedDiffs());
            logger.info("批次ID: {}. 匹配完成: {} 对匹配，{} 条未匹配差异初步生成。",
                    batchId, matchingOutcome.getMatchedPairs().size(), matchingOutcome.getUnmatchedDiffs().size());

            // 3. 对匹配上的记录进行字段比较
            if (!CollectionUtils.isEmpty(matchingOutcome.getMatchedPairs())) {
                ComparisonStrategy comparisonStrategy = ruleStrategyFactory.createStrategy(activeRule);
                logger.info("批次ID: {}. 使用比较策略: {}", batchId, comparisonStrategy.getStrategyName());

                // 为compare方法构建一个临时的、简化的context (如果策略实现确实需要)
                // 或者修改ComparisonStrategy接口，使其compare方法不再接收完整ReconciliationContext，
                // 而是只接收必要的参数 (如processingDate, channelCode，如果特定策略需要的话)
                // 为保持当前ComparisonStrategy接口不变，我们创建一个临时的context
                // 但更优的做法是让策略不依赖于可能未完全填充的ReconciliationContext
                ReconciliationContext tempContext = ReconciliationContext.builder()
                        .batchId(batchId)
                        // .processingDate(processingDate) // 如果需要，从调用方传入
                        // .channelCode(activeRule.getChannelCode()) // 如果需要
                        .transactionType(activeRule.getTransactionType()) // 如果需要
                        .build();

                for (MatcherService.MatchedPair<NormalizedTransaction, NormalizedTransaction> pair : matchingOutcome
                        .getMatchedPairs()) {
                    List<FieldDifferenceDetail> fieldDifferences = comparisonStrategy.compare(
                            pair.getOurRecord(), pair.getPartnerRecord(),
                            extractStrategyParameters(ruleContent), tempContext);

                    if (!CollectionUtils.isEmpty(fieldDifferences)) {
                        // 存在字段差异，创建MISMATCHED_FIELDS类型的差异记录
                        DiffResult mismatchDiff = new DiffResult();
                        mismatchDiff.setBatchId(batchId); // 使用传入的batchId
                        mismatchDiff.setRuleIdUsed(activeRule.getRuleId());
                        mismatchDiff.setRuleVersionUsed(activeRule.getVersion());
                        mismatchDiff.setDifferenceType(DifferenceType.MISMATCHED_FIELDS);
                        mismatchDiff.setOurRecordSnapshot(pair.getOurRecord());
                        mismatchDiff.setPartnerRecordSnapshot(pair.getPartnerRecord());
                        mismatchDiff.setFieldDifferences(fieldDifferences);
                        // 为MISMATCHED_FIELDS记录设置primaryMatchingKeyJson
                        mismatchDiff.setPrimaryMatchingKeyJson(buildPrimaryKeyJson(pair.getOurRecord(), ourKeyFields));
                        allDiffResults.add(mismatchDiff);
                    } else {
                        // 无字段差异，创建BALANCED类型的记录
                        DiffResult balancedDiff = new DiffResult();
                        balancedDiff.setBatchId(batchId); // 使用传入的batchId
                        balancedDiff.setRuleIdUsed(activeRule.getRuleId());
                        balancedDiff.setRuleVersionUsed(activeRule.getVersion());
                        balancedDiff.setDifferenceType(DifferenceType.BALANCED);
                        balancedDiff.setOurRecordSnapshot(pair.getOurRecord());
                        balancedDiff.setPartnerRecordSnapshot(pair.getPartnerRecord());
                        // 为BALANCED记录设置primaryMatchingKeyJson（使用我方记录的匹配键）
                        balancedDiff.setPrimaryMatchingKeyJson(buildPrimaryKeyJson(pair.getOurRecord(), ourKeyFields));
                        // BALANCED记录不需要设置fieldDifferences，保持为null或空列表
                        allDiffResults.add(balancedDiff);
                    }
                }
            }
            logger.info("批次ID: {}. 对账引擎核心处理完成，共产生 {} 条对账结果记录（包含差异和已对平记录）。", batchId, allDiffResults.size());
        } catch (Exception e) {
            logger.error("对账引擎核心处理失败，批次ID: {} : {}", batchId, e.getMessage(), e);
            if (e instanceof ReconciliationProcessException)
                throw (ReconciliationProcessException) e;
            throw new ReconciliationProcessException("对账引擎核心处理失败", batchId, e);
        }
        return allDiffResults;
    }

    private Map<String, Object> extractStrategyParameters(JsonNode ruleContentRootNode) {
        JsonNode strategyParamsNode = ruleContentRootNode.path("comparisonStrategy").path("parameters");
        if (strategyParamsNode.isMissingNode() || strategyParamsNode.isNull() || !strategyParamsNode.isObject()) {
            return new HashMap<>();
        }
        try {
            return objectMapper.convertValue(strategyParamsNode, Map.class);
        } catch (IllegalArgumentException e) {
            logger.warn("无法将 comparisonStrategy.parameters 转换为Map: {}", strategyParamsNode, e);
            return new HashMap<>();
        }
    }

    /**
     * 从匹配配置中提取指定侧的键字段列表。
     */
    private List<String> extractKeyFields(JsonNode matchingConfigNode, String sideKeyFieldsName) {
        JsonNode keyFieldsNode = matchingConfigNode.path(sideKeyFieldsName);
        List<String> keyFields = new ArrayList<>();
        if (keyFieldsNode.isArray()) {
            for (JsonNode fieldNameNode : keyFieldsNode) {
                String fieldName = fieldNameNode.asText();
                if (fieldName != null && !fieldName.trim().isEmpty()) {
                    keyFields.add(fieldName.trim());
                }
            }
        }
        return keyFields;
    }

    /**
     * 构建主键JSON字符串。
     */
    private String buildPrimaryKeyJson(Object record, List<String> keyFields) {
        if (record == null || keyFields == null || keyFields.isEmpty()) {
            return "{}";
        }
        Map<String, Object> keyMap = new HashMap<>();
        for (String fieldName : keyFields) {
            try {
                Object value = PropertyUtils.getProperty(record, fieldName);
                keyMap.put(fieldName, value);
            } catch (Exception e) {
                logger.warn("构建主键JSON时获取字段 '{}' 失败: {}", fieldName, e.getMessage());
                keyMap.put(fieldName, "ERROR_FETCHING_VALUE");
            }
        }
        try {
            return objectMapper.writeValueAsString(keyMap);
        } catch (Exception e) {
            logger.error("序列化主键Map为JSON失败: {}", keyMap, e);
            return "{\"error\":\"Serialization failed\"}";
        }
    }
}