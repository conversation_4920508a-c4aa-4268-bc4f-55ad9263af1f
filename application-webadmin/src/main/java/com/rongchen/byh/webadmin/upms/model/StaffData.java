package com.rongchen.byh.webadmin.upms.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 销售员工表(StaffData)实体类
 *
 * <AUTHOR>
 * @since 2025-02-27 11:58:14
 */
@Data
@TableName(value = "staff_data")
public class StaffData implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 邀请码
     */
    private String inviteCode;
    /**
     * 邀请码使用标识 0-允许使用 1-禁止使用（禁止注册）
     */
    private Integer inviteFlag;
    /**
     * 状态 0-正常 1-禁用
     */
    private Integer staffStatus;
    /**
     *门店管理员
     */
    private String sysUserId;

    /**
     *门店管理员名称
     */
    @TableField(exist = false)
    private String showName;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 749893891669284756L;


}

