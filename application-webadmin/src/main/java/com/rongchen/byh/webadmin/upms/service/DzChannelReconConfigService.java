package com.rongchen.byh.webadmin.upms.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rongchen.byh.webadmin.upms.dao.DzChannelReconConfigMapper;
import com.rongchen.byh.webadmin.upms.model.DzChannelReconConfigEntity;
import java.util.List;
import org.springframework.stereotype.Service;
/**

 * 项目名称：byh_java
 * 文件名称: DzChannelReconConfigService
 * 创建时间: 2025-05-22 15:04
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.service
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
@Service
public class DzChannelReconConfigService extends ServiceImpl<DzChannelReconConfigMapper, DzChannelReconConfigEntity> {

    
    public int insertSelective(DzChannelReconConfigEntity record) {
        return baseMapper.insertSelective(record);
    }
    
    public int updateByPrimaryKeySelective(DzChannelReconConfigEntity record) {
        return baseMapper.updateByPrimaryKeySelective(record);
    }
    
    public int updateByPrimaryKey(DzChannelReconConfigEntity record) {
        return baseMapper.updateByPrimaryKey(record);
    }
    
    public int updateBatch(List<DzChannelReconConfigEntity> list) {
        return baseMapper.updateBatch(list);
    }
    
    public int updateBatchSelective(List<DzChannelReconConfigEntity> list) {
        return baseMapper.updateBatchSelective(list);
    }
    
    public int batchInsert(List<DzChannelReconConfigEntity> list) {
        return baseMapper.batchInsert(list);
    }
    
    public int batchInsertOrUpdate(List<DzChannelReconConfigEntity> list) {
        return baseMapper.batchInsertOrUpdate(list);
    }
}
