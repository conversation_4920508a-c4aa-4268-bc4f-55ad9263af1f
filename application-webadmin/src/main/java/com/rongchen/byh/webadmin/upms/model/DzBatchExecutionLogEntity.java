package com.rongchen.byh.webadmin.upms.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**

 * 项目名称：byh_java
 * 文件名称: DzBatchExecutionLogEntity
 * 创建时间: 2025-05-27 15:00
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.model
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */

/**
 * 批次执行日志表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "`dz_batch_execution_log`")
public class DzBatchExecutionLogEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 批次ID
     */
    @TableField(value = "`batch_id`")
    private String batchId;

    /**
     * 父级月度批次ID (用于关联月度对账任务下的每日批次)
     */
    @TableField(value = "`parent_monthly_batch_id`")
    private String parentMonthlyBatchId;

    /**
     * 处理的业务日期T
     */
    @TableField(value = "`processing_date`")
    private Date processingDate;

    /**
     * 渠道编码 (若为特定渠道任务)
     */
    @TableField(value = "`channel_code`")
    private String channelCode;

    /**
     * 交易类型 (若为特定类型任务)
     */
    @TableField(value = "`transaction_type`")
    private String transactionType;

    /**
     * 批次开始时间
     */
    @TableField(value = "`start_time`")
    private Date startTime;

    /**
     * 批次结束时间
     */
    @TableField(value = "`end_time`")
    private Date endTime;

    /**
     * 状态 (PROCESSING, COMPLETED, FAILED)
     */
    @TableField(value = "`status`")
    private String status;

    /**
     * 我方记录总数
     */
    @TableField(value = "`total_our_records`")
    private Integer totalOurRecords;

    /**
     * 资方记录总数
     */
    @TableField(value = "`total_partner_records`")
    private Integer totalPartnerRecords;

    /**
     * 差异总数
     */
    @TableField(value = "`total_diff_count`")
    private Integer totalDiffCount;

    /**
     * 错误信息 (若失败)
     */
    @TableField(value = "`error_message`")
    private String errorMessage;

    /**
     * 记录创建时间
     */
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 记录更新时间
     */
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}