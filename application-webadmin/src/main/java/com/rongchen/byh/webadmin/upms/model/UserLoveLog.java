package com.rongchen.byh.webadmin.upms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 爱签签署记录表
 * user_love_log
 */
@Data
@TableName(value ="user_love_log")
public class UserLoveLog implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 合同编号
     */
    private String contractNo;

    /**
     * 合同预览地址
     */
    private String contractUrl;

    /**
     * 签署状态
     */
    private Integer contractStatus;

    /**
     * 签署时间
     */
    private Date createTime;

    private static final long serialVersionUID = 1L;
}