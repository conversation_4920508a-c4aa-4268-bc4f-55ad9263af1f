package com.rongchen.byh.webadmin.upms.service.impl.report;

import com.github.pagehelper.page.PageMethod;
import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.MyPageUtil;
import com.rongchen.byh.common.redis.util.RedissonUtils;
import com.rongchen.byh.webadmin.upms.dao.StatisticalReportMapper;
import com.rongchen.byh.webadmin.upms.dto.export.StatisticalReportDto;
import com.rongchen.byh.webadmin.upms.service.report.StatisticalReportService;
import com.rongchen.byh.webadmin.upms.vo.export.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

@Slf4j
@Service
public class StatisticalReportServiceImpl implements StatisticalReportService {
    @Resource
    private StatisticalReportMapper statisticalReportMapper;

    @Resource
    private RedissonUtils redissonUtils;

    @Override
    public ResponseResult<MyPageData<StatisticalReportListVo.Root>> statisticalReportList(StatisticalReportDto dto, MyPageParam pageParam) {
        // 构造缓存键
        String cacheKey = "statisticalReportList:" + dto.toString() + ":" + pageParam.getPageNum() + ":" + pageParam.getPageSize();
        // 尝试从缓存中获取数据
        MyPageData<StatisticalReportListVo.Root> cachedData = redissonUtils.getObject(cacheKey);
        if (cachedData != null) {
            // 缓存命中，直接返回
            return ResponseResult.success(cachedData);
        }
        List<StatisticalReportListVo> list = new ArrayList<>();
        list.addAll(statisticalReportMapper.statisticalReportCreditList(dto));
        list.addAll(statisticalReportMapper.statisticalReportRepaymentList(dto));

        List<StatisticalReportListVo.Root> rootList = this.convert(list);

        rootList.sort((r1, r2) -> LocalDate.parse(r2.getTime()).compareTo(LocalDate.parse(r1.getTime())));

        List<StatisticalReportListVo.Root> pagedRootList = new ArrayList<>();
        long totalCount = rootList.size();
        if (pageParam != null) {
            int startIndex = (pageParam.getPageNum() - 1) * pageParam.getPageSize();
            int endIndex = Math.min(startIndex + pageParam.getPageSize(), rootList.size());
            if (startIndex < endIndex) {
                pagedRootList = new ArrayList<>(rootList.subList(startIndex, endIndex));
            }
        } else {
            pagedRootList = rootList;
        }

        MyPageData<StatisticalReportListVo.Root> pageData = MyPageUtil.makeResponseData(pagedRootList, totalCount);
        // 将结果存入缓存，设置过期时间为1小时
        redissonUtils.setObject(cacheKey, pageData, 1, TimeUnit.HOURS);
        return ResponseResult.success(pageData);
    }

    /**
     * 获取逾期报告列表
     * @return 包含逾期报告数据的响应结果
     */
    @Override
    public ResponseResult<MyPageData<ReportDataVo>> lateReportList(StatisticalReportDto dto, MyPageParam pageParam) {
        List<OverdueProcessVo> overdueProcessList = statisticalReportMapper.getOverdueProcessList(dto, null);

        Map<LocalDate, Map<Long, List<OverdueProcessVo>>> dateUserGroup = overdueProcessList.stream()
                .collect(Collectors.groupingBy(
                        OverdueProcessVo::getCreateTime,
                        Collectors.groupingBy(OverdueProcessVo::getUserId)
                ));
        LocalDate currentDate = LocalDate.now(); // 获取当前日期
        List<ReportDataVo> reportList = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        for (Map.Entry<LocalDate, Map<Long, List<OverdueProcessVo>>> dateEntry : dateUserGroup.entrySet()) {
            LocalDate createDate = dateEntry.getKey();
            // 获取当前日期对应区间的所有逾期记录
            List<OverdueProcessVo> processList = statisticalReportMapper.getOverdueProcessList(dto, createDate.minusDays(1));

            // 按渠道分组(渠道→逾期用户)
            Map<String, Map<Long, List<OverdueProcessVo>>> channelUserGroup = processList.stream()
                    .collect(Collectors.groupingBy(
                            OverdueProcessVo::getChannelName,  // 按渠道分组
                            Collectors.groupingBy(OverdueProcessVo::getUserId)  // 渠道内按用户分组
                    ));

            // 遍历每个渠道
            for (Map.Entry<String, Map<Long, List<OverdueProcessVo>>> channelEntry : channelUserGroup.entrySet()) {
                String channelName = channelEntry.getKey();
                Map<Long, List<OverdueProcessVo>> userGroup = channelEntry.getValue();

                long fpd1 = 0, fpd4 = 0, fpd7 = 0, fpd15 = 0, fpd30 = 0;
                double fpd1Amount = 0.0, fpd4Amount = 0.0, fpd7Amount = 0.0, fpd15Amount = 0.0, fpd30Amount = 0.0;
                ReportDataVo report = new ReportDataVo();
                // 统计当前渠道下所有用户的逾期情况
                for (List<OverdueProcessVo> userList : userGroup.values()) {
                    boolean hasOverdue1 = false, hasOverdue4 = false, hasOverdue7 = false,
                            hasOverdue15 = false, hasOverdue30 = false;
                    double userTotalRetPrin = 0.0;
                    boolean amountCollected = false; // 新增标记，确保仅取一次金额
                    for (OverdueProcessVo op : userList) {
                        int overdueDays = op.getOverdueDays();
                        Double totalRetPrin = op.getTotalRetPrin() != null ? op.getTotalRetPrin() : 0.0;  // 处理null值
                        // 仅取第一条记录的金额 因为只看本金所以可以这样做，只取第一条记录
                        if (!amountCollected) {
                            userTotalRetPrin = totalRetPrin;
                            amountCollected = true;
                        }

                        hasOverdue1 = hasOverdue1 || (overdueDays >= 1);
                        hasOverdue4 = hasOverdue4 || (overdueDays >= 4);
                        hasOverdue7 = hasOverdue7 || (overdueDays >= 7);
                        hasOverdue15 = hasOverdue15 || (overdueDays >= 15);
                        hasOverdue30 = hasOverdue30 || (overdueDays >= 30);
                    }
                    if (hasOverdue1) {
                        fpd1++;
                        fpd1Amount += userTotalRetPrin;
                    }
                    if (hasOverdue4) {
                        if (createDate.isBefore(currentDate.minusDays(2))) {
                            fpd4++;
                            fpd4Amount += userTotalRetPrin;
                        }
                    }
                    if (hasOverdue7) {
                        if (createDate.isBefore(currentDate.minusDays(5))) {
                            fpd7++;
                            fpd7Amount += userTotalRetPrin;
                        }
                    }
                    if (hasOverdue15) {
                        if (createDate.isBefore(currentDate.minusDays(13))) {
                            fpd15++;
                            fpd15Amount += userTotalRetPrin;
                        }
                    }
                    if (hasOverdue30) {
                        if (createDate.isBefore(currentDate.minusDays(28))) {
                            fpd30++;
                            fpd30Amount += userTotalRetPrin;
                        }
                    }
                }

                // 填充渠道信息和统计结果
                report.setChannelName(channelName);  // 新增渠道字段
                report.setDayTime(createDate.format(formatter));
                report.setDay1Count(fpd1);
                report.setDay4Count(fpd4);
                report.setDay7Count(fpd7);
                report.setDay15Count(fpd15);
                report.setDay30Count(fpd30);
                // 计算应还人数，如：今天是5月7号，查询5.1-5.6一期应还的人数
                Map<String, Object> userAndAmountMap = statisticalReportMapper.selectRepayScheduleCountUserAndAmount(createDate.minusDays(1),channelName);
                report.setLastMonthLoanCount(Long.valueOf(userAndAmountMap.get("countUser").toString()));
                // 计算人数逾比例：分母为：区间内的应还人数，分子为：逾期人数
                calculateOverdueDaysRate(report, Long.parseLong(userAndAmountMap.get("countUser").toString()));

                // 金额统计（保留两位小数，避免浮点误差）
                report.setDay1Amount(roundToTwoDecimalPlaces(fpd1Amount));
                report.setDay4Amount(roundToTwoDecimalPlaces(fpd4Amount));
                report.setDay7Amount(roundToTwoDecimalPlaces(fpd7Amount));
                report.setDay15Amount(roundToTwoDecimalPlaces(fpd15Amount));
                report.setDay30Amount(roundToTwoDecimalPlaces(fpd30Amount));

                //计算金额比例: 第一期 逾期总金额/区间内用户贷款总金额
                Object totalAmountObj = userAndAmountMap.get("totalAmount");
                BigDecimal totalAmount = null;
                if (totalAmountObj instanceof BigDecimal) {
                    totalAmount = (BigDecimal) totalAmountObj;
                }
                report.setLastMonthLoanAmount(totalAmount);
                calculateOverdueDaysAmountRate(report,totalAmount.doubleValue());
                reportList.add(report);
            }
        }

        // 按日期倒序排序
        reportList.sort((a, b) -> b.getDayTime().compareTo(a.getDayTime()));

        // 分页处理
        List<ReportDataVo> pageList = new ArrayList<>();
        long totalCount = reportList.size();
        if (pageParam != null) {
            int start = (pageParam.getPageNum() - 1) * pageParam.getPageSize();
            int end = Math.min(start + pageParam.getPageSize(), reportList.size());
            if (start < end) {
                pageList = new ArrayList<>(reportList.subList(start, end));
            }
        } else {
            pageList = reportList;
        }
        MyPageData<ReportDataVo> pageData = MyPageUtil.makeResponseData(pageList, totalCount);
        return ResponseResult.success(pageData);
    }
    @Override
    public ResponseResult<MyPageData<MobUserRateVO>> lateMOBUserRate(StatisticalReportDto dto, MyPageParam pageParam) {
        // 获取放款数据（包含年月和放款总人数）
        List<LoanDataVo> disburseDataList = statisticalReportMapper.getDisburseDataList(dto);
        List<MobUserRateVO> reportList = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        LocalDate currentDate = LocalDate.now(); // 当前日期 2025-05-09（示例）

        for (LoanDataVo disburseData : disburseDataList) {
            String loanYearMonth = disburseData.getYearMonth(); // 放款月份，如 "2025-01"
            int totalLoans = disburseData.getUserCount(); // 放款总人数，如 5
            String channelName = disburseData.getChannelName(); // 渠道名称
            YearMonth yearMonth = YearMonth.parse(loanYearMonth, formatter);
            LocalDate loanMonth = yearMonth.atDay(1); // 放款月份的第一天（如 2025-01-01）

            MobUserRateVO report = new MobUserRateVO();
            report.setYearMonth(loanYearMonth);
            report.setChannelName(channelName); //设置渠道名称
            report.setTotalLoans(totalLoans); // 设置放款总人数

            Set<Long> overdueUserIds = new HashSet<>(); // 用于记录已经逾期的用户ID

            // 计算 MOB1 到 MOB12 的逾期比例
            for (int n = 1; n <= 12; n++) {
                LocalDate mobMonth = loanMonth.plusMonths(n); // MOBn 对应的月份（如 MOB1 为 2025-02，MOB12 为 2026-01）
                long overdueCount;
                // 仅当 MOB 月份在当前日期之前或当月（未结束但需统计到当前日期）时查询数据
                if (mobMonth.isBefore(currentDate.plusMonths(1)) && !mobMonth.isAfter(currentDate)) {
                    String mobYearMonth = mobMonth.format(formatter); // 格式化为 "yyyy-MM"
                    // 传递放款月份和MOB月份到SQL
                    Map<String, Object> params = new HashMap<>();
                    params.put("loanYearMonth", loanYearMonth);  // 放款月份（如2025-01）
                    params.put("mobYearMonth", mobYearMonth);    // MOB月份（如2025-03）
                    params.put("channelName", channelName); // 传递渠道参数（如需渠道过滤）
                    List<Long> currentOverdueUserIds = statisticalReportMapper.getOverdueUserIds(params);
                    overdueUserIds.addAll(currentOverdueUserIds); // 将当前月份逾期的用户加入集合
                    overdueCount = overdueUserIds.size();
                } else {
                    overdueCount = 0; // 未来月份或已过但无数据时设为 0
                }

                String ratio;
                if (totalLoans == 0) {
                    ratio = "0.00%"; // 避免分母为 0
                } else {
                    double ratioValue = (double) overdueCount / totalLoans * 100;
                    // 保留两位小数，格式化为百分比
                    ratio = String.format("%.2f%%", ratioValue);
                }

                // 将比例设置到对应的 MOB 字段（通过反射或直接赋值，此处使用直接赋值）
                setOverdueField(report, n, ratio);
            }

            reportList.add(report);
        }

        // 分页处理
        List<MobUserRateVO> pageList = new ArrayList<>();
        long totalCount = reportList.size();
        if (pageParam != null) {
            int start = (pageParam.getPageNum() - 1) * pageParam.getPageSize();
            int end = Math.min(start + pageParam.getPageSize(), reportList.size());
            if (start < end) {
                pageList = reportList.subList(start, end);
            }
        } else {
            pageList = reportList;
        }

        MyPageData<MobUserRateVO> pageData = MyPageUtil.makeResponseData(pageList, totalCount);
        return ResponseResult.success(pageData);
    }


    @Override
    public ResponseResult<MyPageData<MobAmountRateVO>> lateMOBAmountRate(StatisticalReportDto dto, MyPageParam pageParam) {
        // 获取放款数据（包含年月和放款总金额）
        List<LoanDataVo> disburseDataList = statisticalReportMapper.getDisburseDataAmountList(dto);
        List<MobAmountRateVO> reportList = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        LocalDate currentDate = LocalDate.now(); // 当前日期 2025-05-12（示例）

        for (LoanDataVo disburseData : disburseDataList) {
            String loanYearMonth = disburseData.getYearMonth(); // 放款月份，如 "2025-02"
            BigDecimal totalLoanAmount = disburseData.getCreditAmount(); // 放款总金额，如 613000.00
            YearMonth yearMonth = YearMonth.parse(loanYearMonth, formatter);
            LocalDate loanMonth = yearMonth.atDay(1); // 放款月份的第一天（如 2025-02-01）
            String channelName = disburseData.getChannelName(); // 渠道名称

            MobAmountRateVO report = new MobAmountRateVO();
            report.setYearMonth(loanYearMonth);
            report.setTotalAmount(totalLoanAmount); // 设置放款总金额
            report.setChannelName(channelName); //设置渠道名称

            // 计算 MOB1-MOB12 的逾期金额（以 MOB1 为例，对应 2025-03，MOBn 对应放款月+1 到放款月+n）
            for (int n = 1; n <= 12; n++) {
                LocalDate startMobMonth = loanMonth.plusMonths(1); // MOB1 开始月份
                LocalDate endMobMonth = loanMonth.plusMonths(n); // MOBn 结束月份
                String startMobMonthStr = startMobMonth.format(formatter);
                String endMobMonthStr = endMobMonth.format(formatter);

                // 仅统计已过或当前 MOB 月份范围（截止到当前日期）
                if (endMobMonth.isBefore(currentDate)) {
                    Map<String, Object> params = new HashMap<>();
                    params.put("loanYearMonth", loanYearMonth);
                    params.put("startMobMonth", startMobMonthStr);
                    params.put("endMobMonth", endMobMonthStr);
                    params.put("channelName", channelName); // 传递渠道参数（如需渠道过滤）
                    double overdueAmount = statisticalReportMapper.sumOverdueTermRetPrin(params);
                    String ratio = calculateOverdueRatio(overdueAmount, totalLoanAmount);
                    setOverdueAmountField(report, n, ratio);
                } else {
                    setOverdueAmountField(report, n, "0.00%"); // 未来月份无数据
                }
            }

            reportList.add(report);
        }

        List<MobAmountRateVO> pageList = new ArrayList<>();
        long totalCount = reportList.size();
        if (pageParam != null) {
            int start = (pageParam.getPageNum() - 1) * pageParam.getPageSize();
            int end = Math.min(start + pageParam.getPageSize(), reportList.size());
            if (start < end) {
                pageList = reportList.subList(start, end);
            }
        } else {
            pageList = reportList;
        }
        // 分页处理
        MyPageData<MobAmountRateVO> pageData = MyPageUtil.makeResponseData(pageList, totalCount);
        return ResponseResult.success(pageData);
    }

    private String calculateOverdueRatio(double overdueAmount, BigDecimal totalLoanAmount) {
        if (totalLoanAmount == null || totalLoanAmount.doubleValue() == 0 ) {
            return "0.00%";
        }
        double ratio = (overdueAmount / totalLoanAmount.doubleValue()) * 100;
        return new BigDecimal(ratio)
                .setScale(2, RoundingMode.HALF_UP)
                .toString() + "%";
    }

    /**
     * 设置 MOBn 对应的逾期金额比例字段
     * @param report 月度报告对象
     * @param month MOB 月份（1-12）
     * @param ratio 逾期比例字符串（如 "20.00%" 或空字符串）
     */
    private void setOverdueAmountField(MobAmountRateVO report, int month, String ratio) {
        switch (month) {
            case 1:
                report.setOverdue1MonthAmountRatio(ratio);
                break;
            case 2:
                report.setOverdue2MonthAmountRatio(ratio);
                break;
            case 3:
                report.setOverdue3MonthAmountRatio(ratio);
                break;
            case 4:
                report.setOverdue4MonthAmountRatio(ratio);
                break;
            case 5:
                report.setOverdue5MonthAmountRatio(ratio);
                break;
            case 6:
                report.setOverdue6MonthAmountRatio(ratio);
                break;
            case 7:
                report.setOverdue7MonthAmountRatio(ratio);
                break;
            case 8:
                report.setOverdue8MonthAmountRatio(ratio);
                break;
            case 9:
                report.setOverdue9MonthAmountRatio(ratio);
                break;
            case 10:
                report.setOverdue10MonthAmountRatio(ratio);
                break;
            case 11:
                report.setOverdue11MonthAmountRatio(ratio);
                break;
            case 12:
                report.setOverdue12MonthAmountRatio(ratio);
                break;
            default:
                log.warn("无效的 MOB 月份: {}", month);
        }
    }

    /**
     * 设置 MOBn 对应的逾期比例字段
     * @param report 月度报告对象
     * @param month MOB 月份（1-12）
     * @param ratio 逾期比例字符串（如 "20.00%" 或空字符串）
     */
    private void setOverdueField(MobUserRateVO report, int month, String ratio) {
        switch (month) {
            case 1:
                report.setOverdue1MonthRatio(ratio);
                break;
            case 2:
                report.setOverdue2MonthRatio(ratio);
                break;
            case 3:
                report.setOverdue3MonthRatio(ratio);
                break;
            case 4:
                report.setOverdue4MonthRatio(ratio);
                break;
            case 5:
                report.setOverdue5MonthRatio(ratio);
                break;
            case 6:
                report.setOverdue6MonthRatio(ratio);
                break;
            case 7:
                report.setOverdue7MonthRatio(ratio);
                break;
            case 8:
                report.setOverdue8MonthRatio(ratio);
                break;
            case 9:
                report.setOverdue9MonthRatio(ratio);
                break;
            case 10:
                report.setOverdue10MonthRatio(ratio);
                break;
            case 11:
                report.setOverdue11MonthRatio(ratio);
                break;
            case 12:
                report.setOverdue12MonthRatio(ratio);
                break;
            default:
                log.warn("无效的 MOB 月份: {}", month);
        }
    }

    @Override
    public ResponseResult<MyPageData<DailyWithdrawalVo>> dailyWithdrawalList(StatisticalReportDto dto, MyPageParam pageParam) {
        // 构造缓存键
        String cacheKey = "dailyWithdrawalList:" + dto.toString() + ":" + pageParam.getPageNum() + ":" + pageParam.getPageSize();
        // 尝试从缓存中获取数据
        MyPageData<DailyWithdrawalVo> cachedData = redissonUtils.getObject(cacheKey);
        if (cachedData != null) {
            // 缓存命中，直接返回
            return ResponseResult.success(cachedData);
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        }
        List<DailyWithdrawalVo> dailyWithdrawalVos = statisticalReportMapper.dailyWithdrawalList(dto);
        for (DailyWithdrawalVo vo : dailyWithdrawalVos) {
            // 计算提现率
            vo.setDay3WithdrawalRate(calculateRate(vo.getDay3WithdrawalCount(), vo.getAuditsStatus()));
            vo.setDay7WithdrawalRate(calculateRate(vo.getDay7WithdrawalCount(), vo.getAuditsStatus()));
            vo.setDay15WithdrawalRate(calculateRate(vo.getDay15WithdrawalCount(), vo.getAuditsStatus()));
            vo.setDay30WithdrawalRate(calculateRate(vo.getDay30WithdrawalCount(), vo.getAuditsStatus()));
        }
        MyPageData<DailyWithdrawalVo> pageData = MyPageUtil.makeResponseData(dailyWithdrawalVos);
        // 将结果存入缓存，设置过期时间为1小时
        redissonUtils.setObject(cacheKey, pageData, 1, TimeUnit.HOURS);
        return ResponseResult.success(pageData);
    }

    @Override
    public ResponseResult<MyPageData<DailyCreditLendingVo>> dailyCreditLendinglList(StatisticalReportDto dto, MyPageParam pageParam) {
        // 构造缓存键，加入分页参数
        String cacheKey = "dailyCreditLendinglList:" + dto.toString() + ":" + pageParam.getPageNum() + ":" + pageParam.getPageSize();
        // 尝试从缓存中获取数据
        MyPageData<DailyCreditLendingVo> cachedData = redissonUtils.getObject(cacheKey);
        if (cachedData != null) {
            // 缓存命中，直接返回
            return ResponseResult.success(cachedData);
        }

        // 缓存未命中，执行原有逻辑
        // 获取各维度数据
        List<DailyCreditVo> registrationUsers = statisticalReportMapper.selectCountZC();
        List<DailyCreditVo> applicationCustomers = statisticalReportMapper.selectCountJJ();
        List<DailyCreditVo> approvedCustomers = statisticalReportMapper.selectCountSXTG();
        List<DailyCreditVo> failedCustomers = statisticalReportMapper.selectCountSXJJ();
        List<DailyCreditVo> payoutData = statisticalReportMapper.selectCountPay();

        // 合并数据到Map，键为：日期_渠道名称_来源模式（统一处理空值/空字符串）
        Map<String, DailyCreditLendingVo> mergedDataMap = new HashMap<>();
        mergeDataToMap(registrationUsers, mergedDataMap, DailyCreditLendingVo::setRegistrationUser);
        mergeDataToMap(applicationCustomers, mergedDataMap, DailyCreditLendingVo::setApplicationCustomer);
        mergeDataToMap(approvedCustomers, mergedDataMap, DailyCreditLendingVo::setApprovedCustomer);
        mergeDataToMap(failedCustomers, mergedDataMap, DailyCreditLendingVo::setFailedCustomer);
        mergePayoutDataToMap(payoutData, mergedDataMap);

        // 转换Map为List，并补充计算字段
        List<DailyCreditLendingVo> resultList = convertMapToList(mergedDataMap);

        // 根据筛选条件进行筛选
        resultList = filterListByDto(resultList, dto);

        // 按日期倒序排序
        // 按日期倒序排序
        resultList.sort((a, b) -> b.getCreateTime().compareTo(a.getCreateTime()));


        List<DailyCreditLendingVo> pageList = new ArrayList<>();
        long totalCount = resultList.size();
        if (pageParam != null) {
            int startIndex = (pageParam.getPageNum() - 1) * pageParam.getPageSize();
            int endIndex = Math.min(startIndex + pageParam.getPageSize(), resultList.size());
            if (startIndex < endIndex) {
                pageList = new ArrayList<>(resultList.subList(startIndex, endIndex));
            }
        } else {
            pageList = resultList;
        }

        MyPageData<DailyCreditLendingVo> pageData = MyPageUtil.makeResponseData(pageList, (long) resultList.size());
        // 将结果存入缓存，设置过期时间为1小时
        redissonUtils.setObject(cacheKey, pageData, 1, TimeUnit.HOURS);
        return ResponseResult.success(pageData);
    }

    public static double calculatePercentage(Number numerator, Number denominator) {
        if (denominator == null || numerator == null) {
            return 0.0; // 分母为null时视为0
        }
        double denominatorValue = denominator.doubleValue();
        if (denominatorValue == 0) {
            return 0.0; // 避免除以0异常
        }
        double ratio = numerator.doubleValue() / denominatorValue * 100;
        // 使用BigDecimal进行精确四舍五入
        return new BigDecimal(ratio)
                .setScale(2, RoundingMode.HALF_UP)
                .doubleValue();
    }

    private List<DailyCreditLendingVo> filterListByDto(List<DailyCreditLendingVo> list, StatisticalReportDto dto) {
        return list.stream()
                .filter(item -> {
                    boolean timeCondition = true;
                    // 时间条件：当dto同时有开始和结束时间时，才进行时间范围校验
                    if (StringUtils.isNotEmpty(dto.getTimeStart()) && StringUtils.isNotEmpty(dto.getTimeEnd())) {
                        timeCondition = item.getCreateTime() != null
                                && item.getCreateTime().compareTo(dto.getTimeStart()) >= 0
                                && item.getCreateTime().compareTo(dto.getTimeEnd()) <= 0;
                    }

                    boolean channelCondition = true;
                    // 渠道条件：当dto有渠道ID时，才进行渠道匹配校验（避免NPE，先检查item的channelId是否非空）
                    if (StringUtils.isNotEmpty(dto.getChannelId())) {
                        channelCondition = item.getChannelId() != null
                                && item.getChannelId().equals(dto.getChannelId());
                    }

                    boolean sourceModeCondition = true;
                    // 来源模式条件：当dto有来源模式时，才进行来源模式匹配校验
                    if (StringUtils.isNotEmpty(dto.getSourceMode())) {
                        sourceModeCondition = item.getSourceMode() != null
                                && item.getSourceMode().equals(dto.getSourceMode());
                    }

                    return timeCondition && channelCondition && sourceModeCondition;
                })
                .collect(Collectors.toList());
    }

    private void mergeDataToMap(List<DailyCreditVo> dataList, Map<String, DailyCreditLendingVo> mergedDataMap, BiConsumer<DailyCreditLendingVo, Integer> setter) {
        dataList.forEach(vo -> {
            String createTime = vo.getCreateTime() != null ? vo.getCreateTime() : "";
            String channelName = vo.getName() != null ? vo.getName() : ""; // 处理 name 为 null 或空字符串
            String sourceMode = vo.getSourceMode() != null ? vo.getSourceMode() : ""; // 处理 sourceMode 为 null 或空字符串
            String channelId = vo.getChannelId() != null ? vo.getChannelId() : ""; // 处理 channelId 为 null 或空字符串

            String key = createTime + "_" + channelName + "_" + sourceMode + "_" + channelId;
            mergedDataMap.computeIfAbsent(key, k -> new DailyCreditLendingVo());
            Integer count = vo.getCount() != null ? vo.getCount() : 0;
            setter.accept(mergedDataMap.get(key), count);
        });
    }

    private void mergePayoutDataToMap(List<DailyCreditVo> payoutData, Map<String, DailyCreditLendingVo> mergedDataMap) {
        payoutData.forEach(vo -> {
            String createTime = vo.getCreateTime() != null ? vo.getCreateTime() : "";
            String channelName = vo.getName() != null ? vo.getName() : "";
            String sourceMode = vo.getSourceMode() != null ? vo.getSourceMode() : "";
            String channelId = vo.getChannelId() != null ? vo.getChannelId() : ""; // 处理 channelId 为 null 或空字符串
            String key = createTime + "_" + channelName + "_" + sourceMode + "_" + channelId;
            DailyCreditLendingVo dataVo = mergedDataMap.computeIfAbsent(key, k -> new DailyCreditLendingVo());
            dataVo.setPayoutUser(vo.getPayoutsUser() != null ? vo.getPayoutsUser() : 0);
            dataVo.setSuccessUser(vo.getSuccessUser() != null ? vo.getSuccessUser() : 0);
            dataVo.setErrorUser(vo.getErrorUser() != null ? vo.getErrorUser() : 0);
            dataVo.setSuccessAmount(vo.getSuccessAmount() != null ? vo.getSuccessAmount() : 0.0);
        });
    }

    private List<DailyCreditLendingVo> convertMapToList(Map<String, DailyCreditLendingVo> mergedDataMap) {
        List<DailyCreditLendingVo> resultList = new ArrayList<>();
        for (Map.Entry<String, DailyCreditLendingVo> entry : mergedDataMap.entrySet()) {
            DailyCreditLendingVo vo = entry.getValue();
            // 解析 key 时与构建逻辑一致，确保空字符串处理统一
            String[] keys = entry.getKey().split("_");
            // 检查数组长度，确保安全取值
            vo.setCreateTime(keys.length > 0 ? keys[0] : "");
            vo.setChannelName(keys.length > 1 ? keys[1] : "");
            vo.setSourceMode(keys.length > 2 ? keys[2] : "");
            vo.setChannelId(keys.length > 3 ? keys[3] : "");

            // 计算授信通过率：授信通过客户 / 进件客户
            vo.setApprovalRate(calculateRate(vo.getApprovedCustomer(), vo.getApplicationCustomer()));

            // 计算件均：放款成功金额 / 放款成功用户（注意处理除数为 0）
            vo.setAverageAmount(calculateAverage(vo.getSuccessAmount(), vo.getSuccessUser()));

            // 计算放款成功率：放款成功用户 / 提现用户(放款成功，放款失败)
            vo.setPayoutSuccessRate(calculateRate(vo.getSuccessUser(), vo.getPayoutUser()));

            resultList.add(vo);
        }
        return resultList;
    }

    /**
     * 计算件均金额（保留两位小数）
     */
    private String calculateAverage(Double amount, Integer count) {
        double amt = amount != null ? amount : 0.0;
        int cnt = count != null ? count : 0;
        if (cnt == 0) {
            return "0.00";
        }
        double average = amt / cnt;
        return BigDecimal.valueOf(average)
                .setScale(2, RoundingMode.HALF_UP)
                .toString();
    }


    private String calculateRate(Integer numerator, Integer denominator) {
        int num = numerator != null ? numerator : 0;
        int den = denominator != null ? denominator : 0;
        if (den == 0) {
            return "0.00%";
        }
        double rate = (double) num / den * 100;
        return String.format("%.2f%%", rate);
    }

    /**
     * 将数值四舍五入到小数点后两位
     * @param value 要处理的数值
     * @return 四舍五入后的数值
     */
    private double roundToTwoDecimalPlaces(double value) {
        if (Double.isNaN(value) || Double.isInfinite(value)) {
            return 0.0; // 或者根据业务需求返回默认值
        }
        return BigDecimal.valueOf(value)
                .setScale(2, RoundingMode.HALF_UP)
                .doubleValue();
    }

    /**
     * 计算逾期天数比例
     */
    private void calculateOverdueDaysRate(ReportDataVo report, double number) {
        // 将比率值格式化为带 % 的字符串
        report.setDay1Rate(String.format("%.2f%%", number == 0 ? 0.00 : roundToTwoDecimalPlaces(report.getDay1Count() / number * 100)));
        report.setDay4Rate(String.format("%.2f%%", number == 0 ? 0.00 : roundToTwoDecimalPlaces(report.getDay4Count() / number * 100)));
        report.setDay7Rate(String.format("%.2f%%", number == 0 ? 0.00 : roundToTwoDecimalPlaces(report.getDay7Count() / number * 100)));
        report.setDay15Rate(String.format("%.2f%%", number == 0 ? 0.00 : roundToTwoDecimalPlaces(report.getDay15Count() / number * 100)));
        report.setDay30Rate(String.format("%.2f%%", number == 0 ? 0.00 : roundToTwoDecimalPlaces(report.getDay30Count() / number * 100)));
    }

    /**
     * 计算逾期天数金额比例
     *
     * @param report 报告数据对象
     */
    private void calculateOverdueDaysAmountRate(ReportDataVo report, Double amount) {
        report.setDay1AmountRate(String.format("%.2f%%", amount == null ? 0.00 : roundToTwoDecimalPlaces(report.getDay1Amount() / amount * 100)));
        report.setDay4AmountRate(String.format("%.2f%%", amount == null ? 0.00 : roundToTwoDecimalPlaces(report.getDay4Amount() / amount * 100)));
        report.setDay7AmountRate(String.format("%.2f%%", amount == null ? 0.00 : roundToTwoDecimalPlaces(report.getDay7Amount() / amount * 100)));
        report.setDay15AmountRate(String.format("%.2f%%", amount == null ? 0.00 : roundToTwoDecimalPlaces(report.getDay15Amount() / amount * 100)));
        report.setDay30AmountRate(String.format("%.2f%%", amount == null ? 0.00 : roundToTwoDecimalPlaces(report.getDay30Amount() / amount * 100)));
    }



    private static final Map<String, String> STATUS_MAPPING = new HashMap<>();
    static {
        STATUS_MAPPING.put("待审核", "error");
        STATUS_MAPPING.put("转人工", "error");
        STATUS_MAPPING.put("无", "error");
        STATUS_MAPPING.put("", "error");
        STATUS_MAPPING.put("审核通过", "approved");
        STATUS_MAPPING.put("审核不通过", "rejected");
        STATUS_MAPPING.put("授信中", "creditProcessing");
        STATUS_MAPPING.put("授信失败", "creditFailed");
        STATUS_MAPPING.put("放款中", "disbursing");
        STATUS_MAPPING.put("放款失败", "disburseFailed");
        STATUS_MAPPING.put("还款中", "repaying");
        STATUS_MAPPING.put("已结清", "success");
    }

    public List<StatisticalReportListVo.Root> convert(List<StatisticalReportListVo> list) {
        if (list == null) {
            return new ArrayList<>();
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        Map<String, Map<String, Map<String, Map<String, Map<String, Long>>>>> dateStoreGroupChannelStatus = list.stream()
                .collect(Collectors.groupingBy(
                        vo -> {
                            if (StringUtils.isBlank(vo.getCreateTime())) {
                                return "unknown_date";
                            }
                            try {
                                return LocalDate.parse(vo.getCreateTime(), formatter).toString();
                            } catch (Exception e) {
                                return "unknown_date";
                            }
                        },
                        Collectors.groupingBy(
                                StatisticalReportListVo::getStoreName,
                                Collectors.groupingBy(
                                        StatisticalReportListVo::getGroupName,
                                        Collectors.groupingBy(
                                                vo -> {
                                                    String channelKey = StringUtils.isBlank(vo.getChannelName()) ? "unknown_channel" : vo.getChannelName();
                                                    String sourceModeKey = StringUtils.isBlank(vo.getSourceMode()) ? "unknown_sourceMode" : vo.getSourceMode();
                                                    return channelKey + "_" + sourceModeKey;
                                                },
                                                Collectors.groupingBy(vo -> STATUS_MAPPING.getOrDefault(vo.getAuditsStatus(), "unknown"), Collectors.counting())
                                        )
                                )
                        )
                ));

        List<StatisticalReportListVo.Root> dateRoots = new ArrayList<>();
        int rootIndex = 1;

        for (Map.Entry<String, Map<String, Map<String, Map<String, Map<String, Long>>>>> dateEntry : dateStoreGroupChannelStatus.entrySet()) {
            String date = dateEntry.getKey();
            Map<String, Map<String, Map<String, Map<String, Long>>>> storeGroupChannelMap = dateEntry.getValue();

            StatisticalReportListVo.Root root = new StatisticalReportListVo.Root();
            root.setId(String.valueOf(rootIndex++));
            root.setTime(date);
            root.setChildren(buildStores(storeGroupChannelMap, root.getId()));

            Map<String, Long> globalStatusCounts = new HashMap<>();
            for (StatisticalReportListVo.Store store : root.getChildren()) {
                globalStatusCounts = mergeStatusCounts(globalStatusCounts, store.getStatusCounts());
            }

            long grandTotal = globalStatusCounts.values().stream().mapToLong(Long::longValue).sum();
            Map<String, Double> globalStatusRates = calculateStatusRates(globalStatusCounts, grandTotal);

            root.setTotal(grandTotal);
            root.setStatusCounts(globalStatusCounts);
            root.setStatusRates(globalStatusRates);

            root.setStatusCountsApproved(root.getStatusCounts().getOrDefault("approved", 0L));
            root.setStatusCountsRejected(root.getStatusCounts().getOrDefault("rejected", 0L));
            root.setStatusCountsCreditProcessing(root.getStatusCounts().getOrDefault("creditProcessing", 0L));
            root.setStatusCountsCreditFailed(root.getStatusCounts().getOrDefault("creditFailed", 0L));
            root.setStatusCountsDisbursing(root.getStatusCounts().getOrDefault("disbursing", 0L));
            root.setStatusCountsDisburseFailed(root.getStatusCounts().getOrDefault("disburseFailed", 0L));
            root.setStatusCountsRepaying(root.getStatusCounts().getOrDefault("repaying", 0L));
            root.setStatusCountsSuccess(root.getStatusCounts().getOrDefault("success", 0L));
            root.setStatusCountsError(root.getStatusCounts().getOrDefault("error", 0L));

            root.setStatusRatesApproved(root.getStatusRates().getOrDefault("approved", 0.0));
            root.setStatusRatesRejected(root.getStatusRates().getOrDefault("rejected", 0.0));
            root.setStatusRatesCreditProcessing(root.getStatusRates().getOrDefault("creditProcessing", 0.0));
            root.setStatusRatesCreditFailed(root.getStatusRates().getOrDefault("creditFailed", 0.0));
            root.setStatusRatesDisbursing(root.getStatusRates().getOrDefault("disbursing", 0.0));
            root.setStatusRatesDisburseFailed(root.getStatusRates().getOrDefault("disburseFailed", 0.0));
            root.setStatusRatesRepaying(root.getStatusRates().getOrDefault("repaying", 0.0));
            root.setStatusRatesSuccess(root.getStatusRates().getOrDefault("success", 0.0));
            root.setStatusRatesError(root.getStatusRates().getOrDefault("error", 0.0));

            dateRoots.add(root);
        }

        return dateRoots;
    }

    private List<StatisticalReportListVo.Store> buildStores(
            Map<String, Map<String, Map<String, Map<String, Long>>>> storeGroupChannelMap,
            String parentId
    ) {
        List<StatisticalReportListVo.Store> stores = new ArrayList<>();
        int storeIndex = 1;

        for (Map.Entry<String, Map<String, Map<String, Map<String, Long>>>> storeEntry : storeGroupChannelMap.entrySet()) {
            StatisticalReportListVo.Store store = new StatisticalReportListVo.Store();
            store.setId(parentId + "." + storeIndex++);
            store.setStoreName(storeEntry.getKey());
            store.setChildren(buildGroups(storeEntry.getValue(), store.getId()));

            Map<String, Long> storeStatusCounts = new HashMap<>();
            for (StatisticalReportListVo.Group group : store.getChildren()) {
                storeStatusCounts = mergeStatusCounts(storeStatusCounts, group.getStatusCounts());
            }

            long storeTotal = storeStatusCounts.values().stream().mapToLong(Long::longValue).sum();
            Map<String, Double> storeStatusRates = calculateStatusRates(storeStatusCounts, storeTotal);

            store.setTotal(storeTotal);
            store.setStatusCounts(storeStatusCounts);
            store.setStatusRates(storeStatusRates);

            store.setStatusCountsApproved(store.getStatusCounts().getOrDefault("approved", 0L));
            store.setStatusCountsRejected(store.getStatusCounts().getOrDefault("rejected", 0L));
            store.setStatusCountsCreditProcessing(store.getStatusCounts().getOrDefault("creditProcessing", 0L));
            store.setStatusCountsCreditFailed(store.getStatusCounts().getOrDefault("creditFailed", 0L));
            store.setStatusCountsDisbursing(store.getStatusCounts().getOrDefault("disbursing", 0L));
            store.setStatusCountsDisburseFailed(store.getStatusCounts().getOrDefault("disburseFailed", 0L));
            store.setStatusCountsRepaying(store.getStatusCounts().getOrDefault("repaying", 0L));
            store.setStatusCountsSuccess(store.getStatusCounts().getOrDefault("success", 0L));
            store.setStatusCountsError(store.getStatusCounts().getOrDefault("error", 0L));

            store.setStatusRatesApproved(store.getStatusRates().getOrDefault("approved", 0.0));
            store.setStatusRatesRejected(store.getStatusRates().getOrDefault("rejected", 0.0));
            store.setStatusRatesCreditProcessing(store.getStatusRates().getOrDefault("creditProcessing", 0.0));
            store.setStatusRatesCreditFailed(store.getStatusRates().getOrDefault("creditFailed", 0.0));
            store.setStatusRatesDisbursing(store.getStatusRates().getOrDefault("disbursing", 0.0));
            store.setStatusRatesDisburseFailed(store.getStatusRates().getOrDefault("disburseFailed", 0.0));
            store.setStatusRatesRepaying(store.getStatusRates().getOrDefault("repaying", 0.0));
            store.setStatusRatesSuccess(store.getStatusRates().getOrDefault("success", 0.0));
            store.setStatusRatesError(store.getStatusRates().getOrDefault("error", 0.0));

            stores.add(store);
        }

        return stores;
    }

    private List<StatisticalReportListVo.Group> buildGroups(
            Map<String, Map<String, Map<String, Long>>> groupMap,
            String parentId
    ) {
        List<StatisticalReportListVo.Group> groups = new ArrayList<>();
        int groupIndex = 1;

        for (Map.Entry<String, Map<String, Map<String, Long>>> groupEntry : groupMap.entrySet()) {
            StatisticalReportListVo.Group group = new StatisticalReportListVo.Group();
            group.setId(parentId + "." + groupIndex++);
            group.setGroupName(groupEntry.getKey());
            group.setChildren(buildChannels(groupEntry.getValue(), group.getId()));

            Map<String, Long> groupStatusCounts = new HashMap<>();
            for (StatisticalReportListVo.Channel channel : group.getChildren()) {
                groupStatusCounts = mergeStatusCounts(groupStatusCounts, channel.getStatusCounts());
            }

            long groupTotal = groupStatusCounts.values().stream().mapToLong(Long::longValue).sum();
            Map<String, Double> groupStatusRates = calculateStatusRates(groupStatusCounts, groupTotal);

            group.setTotal(groupTotal);
            group.setStatusCounts(groupStatusCounts);
            group.setStatusRates(groupStatusRates);

            group.setStatusCountsApproved(group.getStatusCounts().getOrDefault("approved", 0L));
            group.setStatusCountsRejected(group.getStatusCounts().getOrDefault("rejected", 0L));
            group.setStatusCountsCreditProcessing(group.getStatusCounts().getOrDefault("creditProcessing", 0L));
            group.setStatusCountsCreditFailed(group.getStatusCounts().getOrDefault("creditFailed", 0L));
            group.setStatusCountsDisbursing(group.getStatusCounts().getOrDefault("disbursing", 0L));
            group.setStatusCountsDisburseFailed(group.getStatusCounts().getOrDefault("disburseFailed", 0L));
            group.setStatusCountsRepaying(group.getStatusCounts().getOrDefault("repaying", 0L));
            group.setStatusCountsSuccess(group.getStatusCounts().getOrDefault("success", 0L));
            group.setStatusCountsError(group.getStatusCounts().getOrDefault("error", 0L));

            group.setStatusRatesApproved(group.getStatusRates().getOrDefault("approved", 0.0));
            group.setStatusRatesRejected(group.getStatusRates().getOrDefault("rejected", 0.0));
            group.setStatusRatesCreditProcessing(group.getStatusRates().getOrDefault("creditProcessing", 0.0));
            group.setStatusRatesCreditFailed(group.getStatusRates().getOrDefault("creditFailed", 0.0));
            group.setStatusRatesDisbursing(group.getStatusRates().getOrDefault("disbursing", 0.0));
            group.setStatusRatesDisburseFailed(group.getStatusRates().getOrDefault("disburseFailed", 0.0));
            group.setStatusRatesRepaying(group.getStatusRates().getOrDefault("repaying", 0.0));
            group.setStatusRatesSuccess(group.getStatusRates().getOrDefault("success", 0.0));
            group.setStatusRatesError(group.getStatusRates().getOrDefault("error", 0.0));

            groups.add(group);
        }

        return groups;
    }

    private List<StatisticalReportListVo.Channel> buildChannels(
            Map<String, Map<String, Long>> channelMap,
            String parentId
    ) {
        List<StatisticalReportListVo.Channel> channels = new ArrayList<>();
        int channelIndex = 1;

        for (Map.Entry<String, Map<String, Long>> channelEntry : channelMap.entrySet()) {
            StatisticalReportListVo.Channel channel = new StatisticalReportListVo.Channel();
            channel.setId(parentId + "." + channelIndex++);
            // 解析渠道名称和sourceMode（假设键为"channelName_sourceMode"）
            String[] keys = channelEntry.getKey().split("_");
            String channelName = keys[0];
            String sourceMode = keys.length > 1 ? keys[1] : ""; // 提取sourceMode
            channel.setChannelName(channelName);
            channel.setSourceMode(sourceMode); // 设置新增的sourceMode

            Map<String, Long> statusCounts = channelEntry.getValue();
            long total = statusCounts.values().stream().mapToLong(Long::longValue).sum();
            Map<String, Double> statusRates = calculateStatusRates(statusCounts, total);

            channel.setTotal(total);
            channel.setStatusCounts(statusCounts);
            channel.setStatusRates(statusRates);

            channel.setStatusCountsApproved(channel.getStatusCounts().getOrDefault("approved", 0L));
            channel.setStatusCountsRejected(channel.getStatusCounts().getOrDefault("rejected", 0L));
            channel.setStatusCountsCreditProcessing(channel.getStatusCounts().getOrDefault("creditProcessing", 0L));
            channel.setStatusCountsCreditFailed(channel.getStatusCounts().getOrDefault("creditFailed", 0L));
            channel.setStatusCountsDisbursing(channel.getStatusCounts().getOrDefault("disbursing", 0L));
            channel.setStatusCountsDisburseFailed(channel.getStatusCounts().getOrDefault("disburseFailed", 0L));
            channel.setStatusCountsRepaying(channel.getStatusCounts().getOrDefault("repaying", 0L));
            channel.setStatusCountsSuccess(channel.getStatusCounts().getOrDefault("success", 0L));
            channel.setStatusCountsError(channel.getStatusCounts().getOrDefault("error", 0L));

            channel.setStatusRatesApproved(channel.getStatusRates().getOrDefault("approved", 0.0));
            channel.setStatusRatesRejected(channel.getStatusRates().getOrDefault("rejected", 0.0));
            channel.setStatusRatesCreditProcessing(channel.getStatusRates().getOrDefault("creditProcessing", 0.0));
            channel.setStatusRatesCreditFailed(channel.getStatusRates().getOrDefault("creditFailed", 0.0));
            channel.setStatusRatesDisbursing(channel.getStatusRates().getOrDefault("disbursing", 0.0));
            channel.setStatusRatesDisburseFailed(channel.getStatusRates().getOrDefault("disburseFailed", 0.0));
            channel.setStatusRatesRepaying(channel.getStatusRates().getOrDefault("repaying", 0.0));
            channel.setStatusRatesSuccess(channel.getStatusRates().getOrDefault("success", 0.0));
            channel.setStatusRatesError(channel.getStatusRates().getOrDefault("error", 0.0));

            channels.add(channel);
        }

        return channels;
    }

    private Map<String, Long> mergeStatusCounts(Map<String, Long> target, Map<String, Long> source) {
        source.forEach((status, count) -> target.merge(status, count, Long::sum));
        return target;
    }

    private Map<String, Double> calculateStatusRates(Map<String, Long> statusCounts, long total) {
        return statusCounts.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> calculateRate(entry.getValue(), total)
                ));
    }

    private double calculateRate(long count, long total) {
        double rate = total > 0 ? (double) count / total * 100 : 0;
        return BigDecimal.valueOf(rate)
                .setScale(2, RoundingMode.HALF_UP)
                .doubleValue();
    }
}