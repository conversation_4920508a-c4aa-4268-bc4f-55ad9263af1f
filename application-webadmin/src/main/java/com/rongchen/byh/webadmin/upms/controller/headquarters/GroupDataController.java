package com.rongchen.byh.webadmin.upms.controller.headquarters;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.rongchen.byh.common.core.annotation.MyRequestBody;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.MyCommonUtil;
import com.rongchen.byh.common.log.annotation.OperationLog;
import com.rongchen.byh.common.log.model.constant.SysOperationLogType;
import com.rongchen.byh.webadmin.upms.model.GroupData;
import com.rongchen.byh.webadmin.upms.service.headquarter.GroupDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 小组表(GroupData)表控制层
 *
 * <AUTHOR>
 * @since 2025-02-27 15:47:06
 */
@Tag(name = "小组信息相关接口")
@RestController
@RequestMapping("/admin/upms/group")
public class GroupDataController {

    @Resource
    private GroupDataService groupDataService;

    /**
     * 分页查询小组信息
     *
     * @param dto   筛选条件
     * @return 查询结果
     */
    @Operation(summary = "小组信息-列表-查询")
    @PostMapping("/groupDataList")
    public ResponseResult<MyPageData<GroupData>> groupDataList(@MyRequestBody GroupData dto, @MyRequestBody MyPageParam pageParam) {
        return groupDataService.groupDataList(dto, pageParam);
    }

    /**
     * 新增数据
     *
     * @param dto 实体
     * @return 新增结果
     */
    @Operation(summary = "小组信息-列表-新增数据")
    @PostMapping("/insertGroupData")
    @SaCheckPermission("group.insertGroupData")
    @OperationLog(type = SysOperationLogType.ADD)
    public ResponseResult<Boolean> insertGroupData(@MyRequestBody GroupData dto) {
        Boolean insert = groupDataService.insert(dto);
        if (insert) {
            return ResponseResult.success(true);
        }
        return ResponseResult.error(ErrorCodeEnum.DUPLICATED_UNIQUE_KEY);
    }

    /**
     * 编辑数据
     *
     * @param dto 实体
     * @return 编辑结果
     */
    @Operation(summary = "小组信息-列表-编辑数据")
    @PostMapping("/updateGroupData")
    @SaCheckPermission("group.updateGroupData")
    @OperationLog(type = SysOperationLogType.UPDATE)
    public ResponseResult<Boolean> updateGroupData(@MyRequestBody GroupData dto) {
        Boolean update = groupDataService.update(dto);
        if (update) {
            return ResponseResult.success(true);
        }
        return ResponseResult.error(ErrorCodeEnum.DATA_SAVE_FAILED);
    }

    /**
     * 删除数据
     *
     * @param dto 主键
     * @return 删除是否成功
     */
    @Operation(summary = "小组信息-列表-删除")
    @PostMapping("/deleteGroupData")
    @SaCheckPermission("group.deleteGroupData")
    @OperationLog(type = SysOperationLogType.DELETE)
    public ResponseResult<Boolean> deleteGroupData(@MyRequestBody GroupData dto) {
        Boolean delete = groupDataService.deleteById(dto.getId());
        if (delete) {
            return ResponseResult.success(true);
        }
        return ResponseResult.error(ErrorCodeEnum.DATA_SAVE_FAILED);
    }

    @Operation(summary = "以字典形式返回全部小组信息数据集合")
    @PostMapping("/groupDictList")
    public ResponseResult<List<Map<String, Object>>> groupList() {
        List<GroupData> list = groupDataService.groupList();
        return ResponseResult.success(
                MyCommonUtil.toDictDataList(list, GroupData::getId, GroupData::getGroupName));
    }

}

