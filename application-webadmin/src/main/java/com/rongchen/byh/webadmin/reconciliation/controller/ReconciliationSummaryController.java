package com.rongchen.byh.webadmin.reconciliation.controller;

import com.github.pagehelper.page.PageMethod;
import com.rongchen.byh.common.core.annotation.MyRequestBody;
import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.MyPageUtil;
import com.rongchen.byh.webadmin.reconciliation.dto.ReconciliationSummaryDetailDto;
import com.rongchen.byh.webadmin.reconciliation.dto.ReconciliationSummeryListDto;
import com.rongchen.byh.webadmin.reconciliation.vo.ReconciliationSummaryDetailVo;
import com.rongchen.byh.webadmin.reconciliation.vo.ReconciliationSummaryListVo;
import com.rongchen.byh.webadmin.upms.service.DzReconciliationSummaryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 对账汇总接口
 * @date 2025/5/27 10:52:06
 */
@Tag(name = "对账汇总接口")
@RestController
@RequestMapping("/admin/upms/reconciliation/summary")
public class ReconciliationSummaryController {
    @Resource
    private DzReconciliationSummaryService reconciliationSummaryService;
    /**
     * 根据查询条件查询对账汇总表
     */
    @Operation(summary = "根据查询条件查询对账汇总表")
    @PostMapping("/list")
    public ResponseResult<MyPageData<ReconciliationSummaryListVo>> getAll(@MyRequestBody ReconciliationSummeryListDto dto,
                                                                          @MyRequestBody MyPageParam pageParam

    ) {
        PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        List<ReconciliationSummaryListVo> list = reconciliationSummaryService.selectAll(dto);
        MyPageData<ReconciliationSummaryListVo> pageData = MyPageUtil.makeResponseData(list);
        return ResponseResult.success(pageData);
    }

    /**
     * 根据查询条件查询对账汇总表详情表
     */
    @Operation(summary = "根据查询条件查询对账汇总表详情表")
    @PostMapping("/detail")
    public ResponseResult<ReconciliationSummaryDetailVo> getAllDetail(@MyRequestBody ReconciliationSummaryDetailDto dto) {
        ReconciliationSummaryDetailVo data = reconciliationSummaryService.getAllDetail(dto);
        return ResponseResult.success(data);
    }
}
