package com.rongchen.byh.webadmin.upms.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rongchen.byh.webadmin.upms.dao.DzRawPartnerLoanDataMapper;
import com.rongchen.byh.webadmin.upms.model.DzRawPartnerLoanDataEntity;
import java.util.List;
import org.springframework.stereotype.Service;
/**

 * 项目名称：byh_java
 * 文件名称: DzRawPartnerLoanDataService
 * 创建时间: 2025-05-21 17:26
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.service
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
@Service
public class DzRawPartnerLoanDataService extends ServiceImpl<DzRawPartnerLoanDataMapper, DzRawPartnerLoanDataEntity> {

    
    public int insertSelective(DzRawPartnerLoanDataEntity record) {
        return baseMapper.insertSelective(record);
    }
    
    public int updateByPrimaryKeySelective(DzRawPartnerLoanDataEntity record) {
        return baseMapper.updateByPrimaryKeySelective(record);
    }
    
    public int updateByPrimaryKey(DzRawPartnerLoanDataEntity record) {
        return baseMapper.updateByPrimaryKey(record);
    }
    
    public int updateBatch(List<DzRawPartnerLoanDataEntity> list) {
        return baseMapper.updateBatch(list);
    }
    
    public int updateBatchSelective(List<DzRawPartnerLoanDataEntity> list) {
        return baseMapper.updateBatchSelective(list);
    }
    
    public int batchInsert(List<DzRawPartnerLoanDataEntity> list) {
        return baseMapper.batchInsert(list);
    }
    
    public int batchInsertOrUpdate(List<DzRawPartnerLoanDataEntity> list) {
        return baseMapper.batchInsertOrUpdate(list);
    }
}
