package com.rongchen.byh.webadmin.reconciliation.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import lombok.Data;

/**
 * 统一还款记录模型。
 * <p>
 * 用于标准化来自不同渠道的还款数据，以便进行统一的对账处理。
 */
@Data
public class NormalizedRepaymentRecord implements NormalizedTransaction {

    /**
     * 资方借款单号 (对应原始字段: transSeqno)
     */
    private String partnerLoanOrderNo;

    /**
     * 资方还款流水号 (对应原始字段: repaySeqNo)
     */
    private String partnerRepaymentId;

    /**
     * 还款提交日期时间 (对应原始字段: transDate, 格式 yyyyMMdd HHmmss)
     */
    private LocalDateTime submissionDatetime;

    /**
     * 还款成功日期时间 (对应原始字段: repayEndDate, 格式 yyyyMMdd HHmmss)
     */
    private LocalDateTime repaymentEffectiveDate;

    /**
     * 还款期数 (对应原始字段: installCnt, 可能为多期，如 "1,2,3")
     */
    private String installmentNumbersStr;

    /**
     * 还款方式 (对应原始字段: repayMode, 例如 "01"线上还款, "02"线下还款)
     */
    private String repaymentMethodCode;

    /**
     * 支付通道 (对应原始字段: repayChannel, 例如 "TL"通联, "BF"宝付, "Partner"资方)
     */
    private String paymentChannelCode;

    /**
     * 债转状态 (对应原始字段: CompensatoryStatus, 例如 "0"债转前扣款, "1"债转后扣款)
     */
    private String debtTransferStatusCode;

    /**
       * 还款类型代码 (XHY: repayType)：
     CURRENT：归还当期到期（账期日还款）
     PREPAYMENT-提前还当期
     OVERDUE：归还逾期
     OVER：归还到期（逾期+当期到期）
     CLEAN：全部结清（提前结清）
     OVERCLEAN：逾期结清
     */
    private String repaymentTypeCode;

    /**
     * 本次实还总金额 (对应原始字段: repayAmt)
     */
    private BigDecimal totalRepaidAmount;

    /**
     * 本次实还本金 (对应原始字段: repayPrincipal)
     */
    private BigDecimal repaidPrincipalAmount;

    /**
     * 本次实还利息 (对应原始字段: repayInterest)
     */
    private BigDecimal repaidInterestAmount;

    /**
     * 本次实还费用 (对应原始字段: repayFee)
     */
    private BigDecimal repaidFeeAmount;

    /**
     * 本次实还罚息 (对应原始字段: repayOverdueInterest)
     */
    private BigDecimal repaidOverdueAmount;



    // --- 实现 NormalizedTransaction 接口 ---
    private Map<String, Object> additionalData = new HashMap<>();
    private String sourceChannel;
    private String originalRecordId;
    /**
     * 记录的通用状态 (例如，用于对账过程中的内部状态标记)
     */
    private String status;

    @Override
    public BigDecimal getReconciliationAmount() {
        // 还款记录通常使用本次实还总金额作为对账和汇总的关键金额
        return this.totalRepaidAmount;
    }
}