package com.rongchen.byh.webadmin.upms.service.headquarter;

import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.webadmin.upms.model.GroupData;

import java.util.List;

/**
 * 小组表(GroupData)表服务接口
 *
 * <AUTHOR>
 * @since 2025-02-27 15:47:06
 */
public interface GroupDataService {


    ResponseResult<MyPageData<GroupData>> groupDataList(GroupData groupData, MyPageParam pageParam);

    /**
     * 新增数据
     *
     * @param groupData 实例对象
     * @return 实例对象
     */
    Boolean insert(GroupData groupData);

    /**
     * 修改数据
     *
     * @param groupData 实例对象
     * @return 实例对象
     */
    Boolean update(GroupData groupData);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    Boolean deleteById(Long id);

    List<GroupData> groupList();
}
