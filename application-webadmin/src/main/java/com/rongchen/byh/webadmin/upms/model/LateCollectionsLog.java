package com.rongchen.byh.webadmin.upms.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * 逾期催收日志表(LateCollectionsLog)实体类
 *
 * <AUTHOR>
 * @since 2025-03-20 11:32:40
 */
@Data
@TableName(value = "late_collections_log")
public class LateCollectionsLog implements Serializable {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 账单号
     */
    private Long billId;
    /**
     * 催收内容
     */
    private String content;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 创建者id
     */
    private Long createUserId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}

