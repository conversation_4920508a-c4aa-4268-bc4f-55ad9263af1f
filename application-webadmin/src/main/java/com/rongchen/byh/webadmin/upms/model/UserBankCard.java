package com.rongchen.byh.webadmin.upms.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户绑定银行卡
 * @TableName user_bank_card
 */
@TableName(value ="user_bank_card")
@Data
public class UserBankCard implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 客户身份证号
     */
    private String idCard;

    /**
     * 开户行
     */
    private String bankName;

    /**
     * 银行卡号
     */
    private String bankAccount;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 服务合同号
     */
    private String contractNum;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 合同录入时服务费收取方式
     * 0:百分比收取
     * 1:固定金额收取
     */
    private String consultingMode;

    /**
     * 合同录入时的服务费收取比例/金额
     */
    private String consultingRate;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}