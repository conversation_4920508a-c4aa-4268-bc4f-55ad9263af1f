package com.rongchen.byh.webadmin.upms.service;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rongchen.byh.webadmin.upms.dao.DzBatchExecutionLogMapper;
import com.rongchen.byh.webadmin.upms.model.DzBatchExecutionLogEntity;
import java.time.LocalDate;
import java.util.List;
import org.springframework.stereotype.Service;
/**

 * 项目名称：byh_java
 * 文件名称: DzBatchExecutionLogService
 * 创建时间: 2025-05-21 11:50
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.service
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
@Service
public class DzBatchExecutionLogService extends ServiceImpl<DzBatchExecutionLogMapper, DzBatchExecutionLogEntity> {

    
    public int insertSelective(DzBatchExecutionLogEntity record) {
        return baseMapper.insertSelective(record);
    }
    
    public int updateByPrimaryKeySelective(DzBatchExecutionLogEntity record) {
        return baseMapper.updateByPrimaryKeySelective(record);
    }
    
    public int updateByPrimaryKey(DzBatchExecutionLogEntity record) {
        return baseMapper.updateByPrimaryKey(record);
    }
    
    public int updateBatch(List<DzBatchExecutionLogEntity> list) {
        return baseMapper.updateBatch(list);
    }
    
    public int updateBatchSelective(List<DzBatchExecutionLogEntity> list) {
        return baseMapper.updateBatchSelective(list);
    }
    
    public int batchInsert(List<DzBatchExecutionLogEntity> list) {
        return baseMapper.batchInsert(list);
    }
    
    public int batchInsertOrUpdate(List<DzBatchExecutionLogEntity> list) {
        return baseMapper.batchInsertOrUpdate(list);
    }

    public List<DzBatchExecutionLogEntity> listByProcessingDate(LocalDate processingDate) {
        return ListUtil.empty();
    }

    public DzBatchExecutionLogEntity getOneByBatchId(String batchId) {
        return null;
    }
}
