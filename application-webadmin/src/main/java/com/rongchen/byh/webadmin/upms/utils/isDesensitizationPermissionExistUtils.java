package com.rongchen.byh.webadmin.upms.utils;

import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.webadmin.upms.bo.SysMenuExtraData;
import com.rongchen.byh.webadmin.upms.model.SysMenu;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;

public class isDesensitizationPermissionExistUtils {
    public static boolean isDesensitizationPermissionExist(Collection<SysMenu> allMenuList, String formRouterName) {
        Long targetMenuId = null;
        for (SysMenu menu : allMenuList) {
            if (formRouterName.equals(menu.getFormRouterName())) {
                targetMenuId = menu.getMenuId();
                break; // 找到即可，无需继续遍历
            }
        }
        if (targetMenuId == null) {
            return false;
        }
        for (SysMenu menu : allMenuList) {
            if (targetMenuId.equals(menu.getParentId())
                    && menu.getMenuName() != null
                    && menu.getMenuName().contains("脱敏")) {
                return true; // 存在脱敏功能
            }
        }
        return false; // 未找到相关脱敏功能
    }
}
