package com.rongchen.byh.webadmin.upms.service.impl.headquarter;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.page.PageMethod;
import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.MyPageUtil;
import com.rongchen.byh.webadmin.upms.dao.GroupDataMapper;
import com.rongchen.byh.webadmin.upms.model.GroupData;
import com.rongchen.byh.webadmin.upms.service.headquarter.GroupDataService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 小组表(GroupData)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-27 15:56:44
 */
@Service
public class GroupDataServiceImpl implements GroupDataService {
    @Resource
    private GroupDataMapper groupDataMapper;

    @Override
    public ResponseResult<MyPageData<GroupData>> groupDataList(GroupData groupData, MyPageParam pageParam) {
        if (pageParam != null){
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        }
        List<GroupData> list = groupDataMapper.groupDataList(groupData);
        return ResponseResult.success(MyPageUtil.makeResponseData(list, GroupData.class));
    }


    /**
     * 新增数据
     *
     * @param groupData 实例对象
     * @return 实例对象
     */
    @Override
    public Boolean insert(GroupData groupData) {
        LambdaQueryWrapper<GroupData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GroupData::getGroupName, groupData.getGroupName());
        Long l = groupDataMapper.selectCount(queryWrapper);
        if (l > 0) {return false;}
        return groupDataMapper.insert(groupData) > 0;
    }

    /**
     * 修改数据
     *
     * @param groupData 实例对象
     * @return 实例对象
     */
    @Override
    public Boolean update(GroupData groupData) {
        return groupDataMapper.updateById(groupData) > 0;
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public Boolean deleteById(Long id) {
        return groupDataMapper.deleteById(id) > 0;
    }

    @Override
    public List<GroupData> groupList() {
        return groupDataMapper.selectList(null);
    }
}
