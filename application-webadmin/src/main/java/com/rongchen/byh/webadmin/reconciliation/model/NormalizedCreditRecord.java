package com.rongchen.byh.webadmin.reconciliation.model;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;
import lombok.Data;
// 导入其他需要的类型，例如BigDecimal, LocalDate等，如果授信有这类标准字段

/**
 * 统一授信记录模型
 */
@Data
public class NormalizedCreditRecord implements NormalizedTransaction {

    /**
     * 资方授信申请/记录ID (对应原始字段: creditNo)
     */
    private String partnerCreditNo; // 标准化命名，对应原始 creditNo

    /**
     * 授信申请/审批状态 (对应原始字段: status, 例如 "1"通过, "2"失败)
     * 使用更具体的名称以区别于 NormalizedTransaction 中的通用 status 字段
     */
    private String creditStatus;

    /**
     * 授信日期 (对应原始字段: creditDate, 格式 yyyyMMdd)
     */
    private LocalDate creditDate; // 改为LocalDate类型

    /**
     * 授信额度 (假设这是授信业务中的核心金额)
     */
    private BigDecimal creditAmount;


    // 实现 NormalizedTransaction 接口要求的字段
    private Map<String, Object> additionalData = new HashMap<>();
    private String sourceChannel;
    private String originalRecordId;
       /**
     * 记录的通用状态 (例如，用于对账过程中的内部状态标记)
     */
    private String status;
    @Override
    public BigDecimal getReconciliationAmount() {
        // 授信记录使用授信额度作为对账金额
        // 如果授信对账不关注金额，或者此字段可能不存在，可以返回 BigDecimal.ZERO 或 null
        return this.creditAmount;
    }


}