package com.rongchen.byh.webadmin.upms.dao;

import com.rongchen.byh.webadmin.upms.dto.Headquarters.HeadquartersDto;
import com.rongchen.byh.webadmin.upms.vo.headquarters.HeadquartersVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface HeadquartersMapper {

    List<HeadquartersVo> headquartersList(HeadquartersDto dto);

    int countStoreData( @Param("storeName") String storeName,
                        @Param("groupName") String groupName);

    void deleteStoreAndGroup(HeadquartersDto dto);

    void deleteGroupAndStaff(HeadquartersDto dto);

    void insertStoreAndGroup(HeadquartersDto dto);

    void insertGroupAndStaff(HeadquartersDto dto);

    int updateStoreAndGroup(HeadquartersDto dto);

    int updateGroupAndStaff(HeadquartersDto dto);

    int selectStaffAndGroup(String staffId,String name,String oldGroupId);

    int selectGroupAndStore(String groupId,String name,String oldStoreId);
}
