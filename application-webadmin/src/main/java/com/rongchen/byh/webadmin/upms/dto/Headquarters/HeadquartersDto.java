package com.rongchen.byh.webadmin.upms.dto.Headquarters;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class HeadquartersDto {
    @Schema(description = "门店ID")
    private String storeId;

    private String oldStoreId;

    @Schema(description = "门店名称")
    private String storeName;

    @Schema(description = "小组ID")
    private String groupId;

    private String oldGroupId;

    @Schema(description = "小组名称")
    private String groupName;

    @Schema(description = "销售员工ID")
    private String staffId;

    private String oldStaffId;

    @Schema(description = "销售员工名称")
    private String userName;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "邀请码")
    private String inviteCode;
}
