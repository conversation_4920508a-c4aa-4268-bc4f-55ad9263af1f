package com.rongchen.byh.webadmin.reconciliation.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 对账汇总列表vo
 * @date 2025/5/23 09:55:10
 */
@Data
public class ReconciliationSummaryDetailDifferentVo {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "我方参与对账的总笔数")
    private Integer ourRecordsCount;

    @Schema(description = "资方参与对账的总笔数")
    private Integer partnerRecordsCount;

    @Schema(description = "我方对账总金额")
    private BigDecimal ourTotalAmount;

    @Schema(description = "资方对账总金额")
    private BigDecimal partnerTotalAmount;

    @Schema(description = "对账结果状态")
    private String reconStatus;

    @Schema(description = "对账结果状态名")
    private String reconStatusName;

}
