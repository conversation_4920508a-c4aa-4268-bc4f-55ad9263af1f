package com.rongchen.byh.webadmin.upms.service.report;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.webadmin.upms.dto.export.StatisticalReportDto;
import com.rongchen.byh.webadmin.upms.vo.export.*;
import org.apache.ibatis.annotations.Param;

public interface StatisticalReportService {
    /**
     * 授信信息和还款信息统计
     *
     * @param dto 筛选条件
     * */
    ResponseResult<MyPageData<StatisticalReportListVo.Root>> statisticalReportList(StatisticalReportDto dto, MyPageParam pageParam) throws JsonProcessingException;

    ResponseResult<MyPageData<ReportDataVo>> lateReportList(StatisticalReportDto dto, MyPageParam pageParam) throws JsonProcessingException;

    ResponseResult<MyPageData<MobUserRateVO>> lateMOBUserRate(StatisticalReportDto dto, MyPageParam pageParam);

    ResponseResult<MyPageData<MobAmountRateVO>> lateMOBAmountRate(StatisticalReportDto dto, MyPageParam pageParam);

    ResponseResult<MyPageData<DailyWithdrawalVo>> dailyWithdrawalList(StatisticalReportDto dto, MyPageParam pageParam);

    ResponseResult<MyPageData<DailyCreditLendingVo>> dailyCreditLendinglList(StatisticalReportDto dto, MyPageParam pageParam);


}
