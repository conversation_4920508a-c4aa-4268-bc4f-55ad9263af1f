package com.rongchen.byh.webadmin.upms.service.impl.capitalprovider;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.DesensitizedUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.page.PageMethod;
import com.rongchen.byh.common.core.object.TokenData;
import com.rongchen.byh.common.core.util.MyPageUtil;
import com.rongchen.byh.webadmin.upms.dao.CapitalProviderMapper;
import com.rongchen.byh.webadmin.upms.dao.SysRoleMapper;
import com.rongchen.byh.webadmin.upms.dto.cp.CPBillDetailDto;
import com.rongchen.byh.webadmin.upms.dto.cp.CPBillListDto;
import com.rongchen.byh.webadmin.upms.dto.cp.DisburseDataRequestListDto;
import com.rongchen.byh.webadmin.upms.model.CapitalProvider;
import com.rongchen.byh.webadmin.upms.model.SysRole;
import com.rongchen.byh.webadmin.upms.service.capitalprovider.CapitalProviderService;
import com.rongchen.byh.webadmin.upms.vo.cp.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 资方供应商表(CapitalProvider)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-16 18:48:00
 */
@Service
public class CapitalProviderServiceImpl implements CapitalProviderService {

    @Resource
    private CapitalProviderMapper capitalProviderMapper;

    @Resource
    private SysRoleMapper sysRoleMapper;

    @Override
    public ResponseResult<MyPageData<CapitalProviderVo>> capitalProviderList(CapitalProvider dto, MyPageParam pageParam) {
        TokenData tokenData = TokenData.takeFromRequest();
        if (pageParam != null){
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        }
        if (tokenData != null && tokenData.getRoleIds() != null) {
            LambdaQueryWrapper<SysRole> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(SysRole::getRoleId, tokenData.getRoleIds().split(","));
            List<SysRole> sysRoles = sysRoleMapper.selectList(queryWrapper);
            if (sysRoles != null){
                for (SysRole sysRole : sysRoles) {
                    if ("供应商".equals(sysRole.getRoleName())) {
                        dto.setSysUserId(String.valueOf(tokenData.getUserId()));
                    }
                }
            }
        }
        List<CapitalProviderVo> list = capitalProviderMapper.selectCapitalProviderList(dto);
        return ResponseResult.success(MyPageUtil.makeResponseData(list, CapitalProviderVo.class));
    }

    /**
     * 新增数据
     *
     * @param capitalProvider 实例对象
     * @return 实例对象
     */
    @Override
    public boolean insert(CapitalProvider capitalProvider) {
        LambdaQueryWrapper<CapitalProvider> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CapitalProvider::getProviderName, capitalProvider.getProviderName());
        Long l = capitalProviderMapper.selectCount(queryWrapper);
        if (l > 0) {return false;}
        capitalProvider.setFlag("0");
        capitalProvider.setCreatedTime(new Date());
        capitalProvider.setUpdatedTime(new Date());
        return capitalProviderMapper.insert(capitalProvider) > 0;
    }

    @Override
    public void expenditureMarkers(CapitalProvider dto) {
        // 一次性查询出符合条件的 CapitalProvider 列表，减少数据库查询次数
        LambdaQueryWrapper<CapitalProvider> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CapitalProvider::getStatus, 0).eq(CapitalProvider::getFlag, 0);
        List<CapitalProvider> capitalProviders = capitalProviderMapper.selectList(queryWrapper);

        for (CapitalProvider capitalProvider : capitalProviders) {
            // 查询该 disburse_data、user_data表中在CapitalProvider表中设置的时间段内当天注册，当天支用的用户和授信金额列表
            List<UserAndAmountVo> userAndAmountVos = capitalProviderMapper.selectCreateAndLoan(capitalProvider.getStartTime(), capitalProvider.getEndTime());

            // 提前过滤掉无效数据，避免后续不必要的计算
            userAndAmountVos = userAndAmountVos.stream()
                    .filter(vo -> vo != null && vo.getCreditAmount() != null)
                    .collect(Collectors.toList());

            BigDecimal totalAmount = BigDecimal.ZERO;
            Boolean flag = false;
            for (UserAndAmountVo userAndAmountVo : userAndAmountVos) {
                // 累加授信金额
                BigDecimal nextTotal = totalAmount.add(userAndAmountVo.getCreditAmount());
                if (nextTotal.compareTo(capitalProvider.getFunds()) > 0) {
                    flag = true;
                    break;
                }
                totalAmount = nextTotal;
                if (totalAmount.compareTo(capitalProvider.getFunds()) < 0) {
                    // 累计金额小于资金上限，更新 disburse_data 表的 cp_id 字段
                    capitalProviderMapper.updateDisburseCpid(capitalProvider.getId(), userAndAmountVo.getUserId());
                }
            }
            if (flag){
                // 如果标志变量flag为真，则更新资金提供者的相关信息
                capitalProviderMapper.updateCapitalProvider(capitalProvider.getId(), totalAmount, 1);
            }
        }
    }


    @Override
    public ResponseResult<MyPageData<CPDisburseDataResultListVo>> disburseOrderList(DisburseDataRequestListDto dto, MyPageParam pageParam) {
        TokenData tokenData = TokenData.takeFromRequest();
        if (tokenData != null && tokenData.getRoleIds() != null) {
            LambdaQueryWrapper<SysRole> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(SysRole::getRoleId, tokenData.getRoleIds().split(","));
            List<SysRole> sysRoles = sysRoleMapper.selectList(queryWrapper);
            if (sysRoles != null){
                for (SysRole sysRole : sysRoles) {
                    if ("供应商".equals(sysRole.getRoleName())) {
                        dto.setSysUserId(tokenData.getUserId());
                    }
                }
            }
        }
        if (pageParam != null){
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        }
        if (dto != null && StringUtils.isNotEmpty(dto.getTimeEnd())){
            dto.setTimeEnd(dto.getTimeEnd() + " 23:59:59");
        }
        List<CPDisburseDataResultListVo> list = capitalProviderMapper.disburseOrderList(dto);
        for (CPDisburseDataResultListVo cpDisburseDataResultListVo : list) {
            cpDisburseDataResultListVo.setMobile(DesensitizedUtil.mobilePhone(cpDisburseDataResultListVo.getMobile()));
            cpDisburseDataResultListVo.setIdNumber(DesensitizedUtil.idCardNum(cpDisburseDataResultListVo.getIdNumber(), 6, 4));
            cpDisburseDataResultListVo.setUserName(DesensitizedUtil.chineseName(cpDisburseDataResultListVo.getUserName()));
        }
        return ResponseResult.success(MyPageUtil.makeResponseData(list, CPDisburseDataResultListVo.class));
    }

    @Override
    public ResponseResult<MyPageData<CPBillListVo>> billList(CPBillListDto dto, MyPageParam pageParam) {
        TokenData tokenData = TokenData.takeFromRequest();
        if (tokenData != null && tokenData.getRoleIds() != null) {
            LambdaQueryWrapper<SysRole> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(SysRole::getRoleId, tokenData.getRoleIds().split(","));
            List<SysRole> sysRoles = sysRoleMapper.selectList(queryWrapper);
            if (sysRoles != null){
                for (SysRole sysRole : sysRoles) {
                    if ("供应商".equals(sysRole.getRoleName())) {
                        dto.setSysUserId(tokenData.getUserId());
                    }
                }
            }
        }

        LocalDate currentDate = LocalDate.now();
        Date date = Date.from(currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        String formattedDate = DateUtil.format(date, "yyyy-MM-dd");
        List<CPBillListVo> list = capitalProviderMapper.billList(dto);

        if (StringUtils.isNotBlank(dto.getRepayStartTime()) && StringUtils.isNotBlank(dto.getRepayEndTime())) {
            // 解析开始和结束日期
            Date startDate = DateUtil.parse(dto.getRepayStartTime(), "yyyy-MM-dd");
            Date endDate = DateUtil.parse(dto.getRepayEndTime(), "yyyy-MM-dd");

            // 过滤列表
            list = list.stream()
                    .filter(billListVo -> {
                        if (billListVo == null || billListVo.getRepaymentDate() == null) {
                            return false;
                        }
                        Date repaymentDate = billListVo.getRepaymentDate();
                        return repaymentDate.compareTo(startDate) >= 0 && repaymentDate.compareTo(endDate) < 0;
                    })
                    .collect(Collectors.toList());
        }
        for (CPBillListVo billListVo : list) {
            if (billListVo != null && billListVo.getCreditStatusName() != null) {
                String totalTerm = billListVo.getTotalTerm();
                Date repaymentDate = billListVo.getRepaymentDate();

                if ("已结清".equals(billListVo.getCreditStatusName())) {
                    billListVo.setTotalTerm("12/12");
                } else if (StringUtils.isNotBlank(totalTerm) && "0".equals(totalTerm)) {
                    billListVo.setTotalTerm("1/12");
                } else if (StringUtils.isNotBlank(totalTerm) && repaymentDate != null &&
                        DateUtil.format(repaymentDate, "yyyy-MM-dd").equals(formattedDate)) {
                    billListVo.setTotalTerm(Integer.valueOf(totalTerm) - 1 + "/12");
                } else if (StringUtils.isNotBlank(totalTerm)) {
                    billListVo.setTotalTerm(totalTerm + "/12");
                }
            }
            if (billListVo != null) {
                billListVo.setMobile(DesensitizedUtil.mobilePhone(billListVo.getMobile()));
                billListVo.setUserName(DesensitizedUtil.chineseName(billListVo.getUserName()));
            }
        }

        List<CPBillListVo> pageList = new ArrayList<>();
        if (pageParam != null) {
            int startIndex = (pageParam.getPageNum() - 1) * pageParam.getPageSize();
            int endIndex = Math.min(startIndex + pageParam.getPageSize(), list.size());
            if (startIndex < endIndex) {
                pageList = new ArrayList<>(list.subList(startIndex, endIndex));
            }
        } else {
            pageList = list;
        }

        MyPageData<CPBillListVo> pageData = MyPageUtil.makeResponseData(pageList, (long) list.size());
        return ResponseResult.success(pageData);

    }

    @Override
    public ResponseResult<List<CPBillPeriodDetailVo>> billDetail(CPBillDetailDto dto) {
        List<CPBillPeriodDetailVo> periodDetail = capitalProviderMapper.billPeriodDetail(dto.getBillId());
        return ResponseResult.success(periodDetail);
    }

    @Override
    public Boolean freezeCpStatus(CapitalProvider dto) {
        LambdaQueryWrapper<CapitalProvider> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CapitalProvider::getId, dto.getId());
        CapitalProvider capitalProvider = capitalProviderMapper.selectOne(queryWrapper);
        if (capitalProvider != null) {
            CapitalProvider data = new CapitalProvider();
            data.setStatus(capitalProvider.getStatus() > 0 ? 0 : 1);
            data.setUpdatedTime(new Date());
            return capitalProviderMapper.update(data, queryWrapper) > 0;
        }
        return false;
    }
}
