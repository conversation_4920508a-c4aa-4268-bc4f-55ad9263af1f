package com.rongchen.byh.webadmin.upms.controller.channel;


import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.MyCommonUtil;
import com.rongchen.byh.webadmin.upms.dao.ChannelDataMapper;
import com.rongchen.byh.webadmin.upms.model.ChannelData;
import com.rongchen.byh.webadmin.upms.model.SysUser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Tag(name = "授信订单管理相关接口")
@RestController
@RequestMapping("/admin/upms/channel")
public class ChannelController {

    @Resource
    ChannelDataMapper channelDataMapper;


    @Operation(summary = "以字典形式返回全部用户管理数据集合。")
    @GetMapping("/listDict")
    public ResponseResult<List<Map<String, Object>>> listDict() {
        List<ChannelData> list = channelDataMapper.selectList(null);
        return ResponseResult.success(
                MyCommonUtil.toDictDataList(list, ChannelData::getId, ChannelData::getName, ChannelData::getSecret));
    }
}
