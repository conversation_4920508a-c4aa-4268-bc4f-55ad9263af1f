package com.rongchen.byh.webadmin.reconciliation.enums;

/**
 * 项目名称：byh_java
 * 文件名称: TransactionTypeEnum
 * 创建时间: 2025-06-04 10:45
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.reconciliation.enums
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
public enum TransactionTypeEnum {
    //交易类型 (LOAN, REPAYMENT, CREDIT)
    LOAN,
    REPAYMENT,
    CREDIT,
    ;

    public static TransactionTypeEnum getEnum(String name) {
        for (TransactionTypeEnum e : TransactionTypeEnum.values()) {
            if (e.name().equals(name)) {
                return e;
            }
        }
        return null;
    }
    
     
        

}
