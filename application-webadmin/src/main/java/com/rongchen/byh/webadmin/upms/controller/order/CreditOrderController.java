package com.rongchen.byh.webadmin.upms.controller.order;

import com.rongchen.byh.common.core.annotation.MyRequestBody;
import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.webadmin.upms.dto.order.CreditOrderListDetailDto;
import com.rongchen.byh.webadmin.upms.dto.order.CreditOrderListDto;
import com.rongchen.byh.webadmin.upms.dto.order.DisburseOrderListDetailDto;
import com.rongchen.byh.webadmin.upms.dto.order.DisburseOrderRequestListDto;
import com.rongchen.byh.webadmin.upms.dto.user.UserListDto;
import com.rongchen.byh.webadmin.upms.service.order.CreditOrderService;
import com.rongchen.byh.webadmin.upms.vo.order.CreditOrderListVo;
import com.rongchen.byh.webadmin.upms.vo.order.DisburseOrderListDetailVo;
import com.rongchen.byh.webadmin.upms.vo.order.DisburseOrderResultListVo;
import com.rongchen.byh.webadmin.upms.vo.order.UserDetailVo;
import com.rongchen.byh.webadmin.upms.vo.user.UserListDetailVo;
import com.rongchen.byh.webadmin.upms.vo.user.UserListVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @ClassName CreditOrderController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/6 15:26
 * @Version 1.0
 **/
@Tag(name = "授信订单管理相关接口")
@RestController
@RequestMapping("/admin/upms/order")
public class CreditOrderController {

    @Resource
    CreditOrderService creditOrderService;


    @Operation(summary = "授信订单管理-列表")
    @PostMapping("/list")
    public ResponseResult<MyPageData<CreditOrderListVo>> list(
            @MyRequestBody CreditOrderListDto dto,
            @MyRequestBody MyPageParam pageParam) {
        return creditOrderService.list(dto, pageParam);
    }


    @Operation(summary = "授信订单管理-列表-详情")
    @PostMapping("/listDetail")
    public ResponseResult<UserDetailVo> listDetail(
            @MyRequestBody CreditOrderListDetailDto dto) {
        return creditOrderService.listDetail(dto);
    }


    @Operation(summary = "支用订单管理-列表-查询")
    @PostMapping("/disburseList")
    public ResponseResult<MyPageData<DisburseOrderResultListVo>> disburseOrderList(
            @MyRequestBody DisburseOrderRequestListDto dto,
            @MyRequestBody MyPageParam pageParam) {
        return creditOrderService.disburseOrderList(dto ,pageParam);
    }


    @Operation(summary = "支用订单管理-列表-详情")
    @PostMapping("/disburseListDetail")
    public ResponseResult<DisburseOrderListDetailVo> disburseListDetail(
            @MyRequestBody DisburseOrderListDetailDto dto) {
        return creditOrderService.disburseListDetail(dto);
    }


    @Operation(summary = "客户信息关系-列表-查询")
    @PostMapping("/userList")
    public ResponseResult<MyPageData<UserListVo>> userList(
            @MyRequestBody UserListDto dto,
            @MyRequestBody MyPageParam pageParam) {
        return creditOrderService.userList(dto ,pageParam);
    }


    @Operation(summary = "客户信息关系-详情-查询")
    @PostMapping("/userDetail")
    public ResponseResult<UserListDetailVo> userDetail(@MyRequestBody CreditOrderListDetailDto dto) {
        return creditOrderService.userDetail(Long.parseLong(dto.getUserId()));
    }

    @Operation(summary = "客户信息关系-注销")
    @PostMapping("/deleteUser")
    public ResponseResult<Void> deleteUser(@MyRequestBody CreditOrderListDetailDto dto) {
        return creditOrderService.deleteUser(Long.parseLong(dto.getUserId()));
    }
}
