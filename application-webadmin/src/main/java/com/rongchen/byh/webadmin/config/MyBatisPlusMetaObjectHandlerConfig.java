package com.rongchen.byh.webadmin.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

/**
 * 项目名称：byh_java
 * 文件名称：MyBatisPlusMetaObjectHandlerConfig
 * 创建时间：2025-03-12
 * 创建人：XuYu
 * 所属包名：com.rongchen.byh.app.config
 * 文件描述：MyBatis-Plus自动填充处理器
 * Copyright (c) 2025 byh_java All rights reserved.
 */
@Slf4j
@Component
public class MyBatisPlusMetaObjectHandlerConfig implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        log.debug("开始插入填充字段");
        // 检查是否存在该字段，存在才进行填充
        if (metaObject.hasSetter("createTime")) {
            this.strictInsertFill(metaObject, "createTime", Date.class, new Date());
        }

        if (metaObject.hasSetter("updateTime")) {
            this.strictInsertFill(metaObject, "updateTime", Date.class, new Date());
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        log.debug("开始更新填充字段");
        // 检查是否存在该字段，存在才进行填充
        if (metaObject.hasSetter("updateTime")) {
            this.strictUpdateFill(metaObject, "updateTime", Date.class, new Date());
        }
    }
}