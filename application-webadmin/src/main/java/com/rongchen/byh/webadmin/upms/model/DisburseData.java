package com.rongchen.byh.webadmin.upms.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 支用记录
 * @TableName disburse_data
 */
@TableName(value ="disburse_data")
@Data
public class DisburseData implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 授信流水号
     */
    private String creditNo;

    /**
     * 放款编号
     */
    private String loanNo;

    /**
     * 赊销编号
     */
    private String saleNo;

    /**
     * 放款金额
     */
    private BigDecimal creditAmount;

    /**
     * 总利息
     */
    private BigDecimal grossInterest;

    /**
     * 授信状态  100 授信中  200 授信失败   300 放款中  400  放款失败   500 还款中   600 已结清
     */
    private Integer creditStatus;

    /**
     * 授信通过时间
     */
    private Date creditTime;

    /**
     * 放款通过时间
     */
    private Date loanTime;

    /**
     * 结清时间
     */
    private Date repaymentTime;

    /**
     * 放款期数 月
     */
    private Integer periods;

    /**
     * 借款用途
     */
    private String purposeLoan;

    /**
     * 年利率
     */
    private String yearRete;

    /**
     * 还款方式   等额本息
     */
    private String repaymentMethod;

    /**
     * 赊销金额
     */
    private BigDecimal saleRepayAmount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 资方id
     */
    private String capitalId;

    /**
     * 资金方编码 放款成功后写入
     */
    private String fundCode;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}