package com.rongchen.byh.webadmin.upms.service.headquarter;

import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.webadmin.upms.dto.Headquarters.StaffDataDto;
import com.rongchen.byh.webadmin.upms.model.StaffData;

import java.util.List;

/**
 * 销售员工表(StaffData)表服务接口
 *
 * <AUTHOR>
 * @since 2025-02-27 14:59:17
 */
public interface StaffDataService {

    ResponseResult<MyPageData<StaffData>> staffDataList(StaffData staffData, MyPageParam myPageParam);
    /**
     * 新增数据
     *
     * @param staffData 实例对象
     * @return 实例对象
     */
    Boolean insert(StaffData staffData);

    /**
     * 修改数据
     *
     * @param staffData 实例对象
     * @return 实例对象
     */
    Boolean update(StaffData staffData);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    Boolean deleteById(Long id);

    /**
     * 修改销售状态
     * @param dto
     * @return
     */
    Boolean freezeStaffStatus(StaffData dto);

    /**
     * 修改销售员工对应订单号
     * @param dto
     * @return
     */
    Integer ordersStaff(StaffDataDto dto);

    List<StaffData> staffList();
}
