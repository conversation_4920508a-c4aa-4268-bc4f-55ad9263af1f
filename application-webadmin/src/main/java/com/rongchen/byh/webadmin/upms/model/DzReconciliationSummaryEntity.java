package com.rongchen.byh.webadmin.upms.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**

 * 项目名称：byh_java
 * 文件名称: DzReconciliationSummaryEntity
 * 创建时间: 2025-05-26 10:50
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.model
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */

/**
 * 对账结果摘要表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "`dz_reconciliation_summary`")
public class DzReconciliationSummaryEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 对账批次ID (关联dz_batch_execution_log.batch_id)
     */
    @TableField(value = "`recon_batch_id`")
    private String reconBatchId;

    /**
     * 对账的业务处理日期 (T日)
     */
    @TableField(value = "`processing_date`")
    private Date processingDate;

    /**
     * 渠道编码
     */
    @TableField(value = "`channel_code`")
    private String channelCode;

    /**
     * 业务交易类型 (LOAN, REPAYMENT, CREDIT, etc.)
     */
    @TableField(value = "`transaction_type`")
    private String transactionType;

    /**
     * 我方参与对账的总笔数
     */
    @TableField(value = "`our_records_count`")
    private Integer ourRecordsCount;

    /**
     * 资方参与对账的总笔数
     */
    @TableField(value = "`partner_records_count`")
    private Integer partnerRecordsCount;

    /**
     * 我方对账总金额
     */
    @TableField(value = "`our_total_amount`")
    private BigDecimal ourTotalAmount;

    /**
     * 资方对账总金额
     */
    @TableField(value = "`partner_total_amount`")
    private BigDecimal partnerTotalAmount;

    /**
     * 差异总笔数
     */
    @TableField(value = "`diff_records_count`")
    private Integer diffRecordsCount;

    /**
     * 对账结果状态 (BALANCED, UNBALANCED, ERROR)
     */
    @TableField(value = "`recon_status`")
    private String reconStatus;

    /**
     * 此摘要记录的生成/更新时间
     */
    @TableField(value = "`summary_time`")
    private Date summaryTime;

    /**
     * 备注
     */
    @TableField(value = "`remarks`")
    private String remarks;

    /**
     * 创建时间
     */
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}