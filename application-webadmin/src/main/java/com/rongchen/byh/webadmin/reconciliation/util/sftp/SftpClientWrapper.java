package com.rongchen.byh.webadmin.reconciliation.util.sftp;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.Session;
import lombok.Getter;

/**
 * 封装 JSch Session 和 ChannelSftp，用于连接池管理。
 */
@Getter
public class SftpClientWrapper {
    private Session session;
    private ChannelSftp channelSftp;

    public SftpClientWrapper(Session session, ChannelSftp channelSftp) {
        this.session = session;
        this.channelSftp = channelSftp;
    }

    /**
     * 检查SFTP连接是否仍然有效。
     * 
     * @return true 如果连接有效，否则 false。
     */
    public boolean isValid() {
        return session != null && session.isConnected() &&
                channelSftp != null && channelSftp.isConnected() && !channelSftp.isClosed();
    }

    /**
     * 断开连接并关闭通道和会话。
     */
    public void disconnect() {
        if (channelSftp != null) {
            if (channelSftp.isConnected()) {
                channelSftp.disconnect();
            }
            if (!channelSftp.isClosed()) {
                // channelSftp.close(); // JSch ChannelSftp 没有 close() 方法，disconnect() 已足够
            }
        }
        if (session != null && session.isConnected()) {
            session.disconnect();
        }
    }
}