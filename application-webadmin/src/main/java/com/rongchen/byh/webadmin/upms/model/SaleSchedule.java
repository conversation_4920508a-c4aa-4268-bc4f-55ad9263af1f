package com.rongchen.byh.webadmin.upms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 赊销账单表(SaleSchedule)实体类
 *
 * <AUTHOR>
 * @since 2025-04-07 10:31:55
 */
@TableName(value ="sale_schedule")
@Data
public class SaleSchedule implements Serializable {
    private static final long serialVersionUID = -63908649131080232L;
    /**
     * id
     */
    private Long id;
    /**
     * 支用表id
     */
    private Long disburseId;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 还款申请流水
     */
    private String repayApplyNo;
    /**
     * 还款期数
     */
    private String repayTerm;
    /**
     * 还款起日 自动扣款开始日期
     */
    private String repayOwnbDate;
    /**
     * 还款止日 自动扣款结束日期
     */
    private String repayOwneDate;
    /**
     * 本期起日 结息周期开始日期
     */
    private String repayIntbDate;
    /**
     * 本期止日 结息周期截止日期
     */
    private String repayInteDate;
    /**
     * 本期应还总金额
     */
    private Double totalAmt;
    /**
     * 本期应还本金
     */
    private Double termRetPrin;
    /**
     * 本期应还利息
     */
    private Double termRetInt;
    /**
     * 本期应还罚息
     */
    private Double termRetFint;
    /**
     * 状态 N - 正常，G - 宽限期，O - 逾期
     */
    private String termStatus;
    /**
     * 结清状态 RUNNING - 未结 ，CLOSE - 已结
     */
    private String settleFlag;
    /**
     * 还款发起时间
     */
    private String payTime;
    /**
     * 实际还款日
     */
    private String datePay;
    /**
     * 时间还款日期
     */
    private String datePayTime;
    /**
     * 自动扣款标识 1-自动扣款 0-不扣 （api渠道不需要自动发起）
     */
    private Integer autoRepay;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;
}

