package com.rongchen.byh.webadmin.reconciliation.model;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;
import lombok.Data;

/**
 * 统一借款记录模型
 */
@Data
public class NormalizedLoanRecord implements NormalizedTransaction {

    /**
     * 资方借款订单号 (例如，来自XHY渠道的 transSeqno)
     */
    private String partnerLoanOrderNo;

    /**
     * 借款提交日期 (例如，来自XHY渠道的 transDate, yyyyMMdd)
     */
    private LocalDate submissionDate;

    /**
     * 放款成功日期 (例如，来自XHY渠道的 payDate, yyyyMMdd)
     */
    private LocalDate fundingDate;

    /**
     * 借款到期日期 (例如，来自XHY渠道的 endDate, yyyyMMdd)
     */
    private LocalDate maturityDate;

    /**
     * 借款本金金额 (例如，来自XHY渠道的 transAmt)
     */
    private BigDecimal principalAmount;

    /**
     * 借款期数 (例如，来自XHY渠道的 totalCnt)
     */
    private Integer termCount;

    /**
     * 年化利率 (例如，来自XHY渠道的 rate, "0.2400")
     */
    private String interestRateStr;

    /**
    * 还款方式代码 (XHY: repayType)：
     0-等本等息
     1-等额本⾦
     2-等额本息
     3-次性还本付息
     */
    private String repaymentTypeCode;

    /**
     * 贷款机构代码 (例如，来自XHY渠道的 capCode)
     */
    private String fundingProviderCode;


    // --- 元数据字段，用于追踪和溯源 ---
    /**
     * 附加数据，用于存储未在标准字段中定义的其他源数据或中间处理数据
     */
    private Map<String, Object> additionalData = new HashMap<>();
    /**
     * 数据来源渠道 (例如, "XHY", "MY_SYSTEM")
     */
    private String sourceChannel;
    /**
     * 原始记录在源系统/文件中的ID
     * 对于文件型数据源，这可能是行号或者某个唯一业务键的组合
     */
    private String originalRecordId;

       /**
     * 记录的通用状态 (例如，用于对账过程中的内部状态标记)
     */
    private String status;
    
    @Override
    public BigDecimal getReconciliationAmount() {
        return this.principalAmount; // 借款记录使用借款本金作为对账金额
    }

}