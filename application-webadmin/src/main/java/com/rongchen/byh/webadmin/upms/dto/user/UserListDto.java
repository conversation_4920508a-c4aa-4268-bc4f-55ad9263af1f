package com.rongchen.byh.webadmin.upms.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName UserListDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/1/23 17:46
 * @Version 1.0
 **/
@Data
public class UserListDto {

    @Schema(description = "客户名称")
    private String userName;

    @Schema(description = "客户手机号")
    private String mobile;

    @Schema(description = "渠道来源")
    private String channelName;

    @Schema(description = "客户状态 0-禁用 1-启用")
    private Integer statusFlag;

    @Schema(description = "授信状态 1-已授信 2-未授信")
    private Integer creditsStatus;

    @Schema(description = "开始时间")
    private String timeStart;

    @Schema(description = "结束时间")
    private String timeEnd;

    @Schema(description = "渠道id")
    private String channelId;
}
