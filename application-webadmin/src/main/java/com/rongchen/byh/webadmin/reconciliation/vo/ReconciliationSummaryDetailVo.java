package com.rongchen.byh.webadmin.reconciliation.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025/5/27 10:22:43
 */
@Data
public class ReconciliationSummaryDetailVo {
    @Schema(description = "差异摘要")
    String differenceSummary;

    @Schema(description = "差异列表")
    List<ReconciliationSummaryDetailDifferentVo> differenceList;

}
