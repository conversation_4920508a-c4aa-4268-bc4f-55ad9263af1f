package com.rongchen.byh.webadmin.upms.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rongchen.byh.webadmin.upms.dto.cp.CPBillListDto;
import com.rongchen.byh.webadmin.upms.dto.cp.DisburseDataRequestListDto;
import com.rongchen.byh.webadmin.upms.model.CapitalProvider;
import com.rongchen.byh.webadmin.upms.vo.cp.*;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 资方供应商表(CapitalProvider)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-16 18:48:00
 */
public interface CapitalProviderMapper extends BaseMapper<CapitalProvider> {

    List<CapitalProviderVo>  selectCapitalProviderList(CapitalProvider dto);

    List<UserAndAmountVo> selectCreateAndLoan(@Param("startTime")String startTime, @Param("endTime")String endTime);

    void updateDisburseCpid(@Param("cpId")Integer cpId,@Param("userId")String userId);

    List<CPDisburseDataResultListVo> disburseOrderList(DisburseDataRequestListDto dto);

    void updateCapitalProvider(@Param("id")Integer id, @Param("totalAmount") BigDecimal totalAmount, @Param("flag")Integer flag);

    List<CPBillListVo> billList(CPBillListDto dto);

    List<CPBillPeriodDetailVo> billPeriodDetail(Long billId);
}

