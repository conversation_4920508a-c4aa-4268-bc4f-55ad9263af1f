package com.rongchen.byh.webadmin.upms.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**

 * 项目名称：byh_java
 * 文件名称: UserData
 * 创建时间: 2025-04-24 19:04
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.app.entity
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */

/**
 * 用户基础信息表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "`user_data`")
public class UserData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 手机号
     */
    @TableField(value = "`mobile`")
    private String mobile;

    /**
     * 用户状态 1 正常  0 不正常
     */
    @TableField(value = "`status_flag`")
    private Integer statusFlag;

    /**
     * 审核状态 0 未通过  1 通过
     */
    @TableField(value = "`audit_flag`")
    private Integer auditFlag;

    /**
     * 用户审核状态
     1-待提交初审（默认）:没有提交h5风控
     5-初审拒绝：h5风控拒绝
     10-待复审：h5风控通过，待员工审核
     15-复审拒绝：员工审核拒绝

     20-待App授信：待进入我方app授信
     25-已授信：我方app授信完成
     30-授信拒绝：我方app授信拒绝
     */
    @TableField(value = "`audit_status`")
    private Integer auditStatus;

    /**
     * 手机号md
     */
    @TableField(value = "`mobile_md`")
    private String mobileMd;

    /**
     * 姓名MD5
     */
    @TableField(value = "`name_md5`")
    private String nameMd5;

    /**
     * 身份证号MD5
     */
    @TableField(value = "`id_card_md5`")
    private String idCardMd5;

    /**
     * 平台id
     */
    @TableField(value = "`channel_id`")
    private Long channelId;

    /**
     * 线上线下类型 0 线上 1 线下 2空中 5空中仅注册 6线上仅注册 7线下仅注册
     */
    @TableField(value = "`source_mode`")
    private Integer sourceMode;

    /**
     * 注册ip
     */
    @TableField(value = "`ip`")
    private String ip;

    /**
     * 注册时间
     */
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private Date createTime;
}