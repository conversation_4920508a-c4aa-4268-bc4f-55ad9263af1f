package com.rongchen.byh.webadmin.upms.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**

 * 项目名称：byh_java
 * 文件名称: DzFieldMappingRulesEntity
 * 创建时间: 2025-05-21 11:50
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.model
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
/**
 * 字段映射规则表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "`dz_field_mapping_rules`")
public class DzFieldMappingRulesEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 渠道编码
     */
    @TableField(value = "`channel_code`")
    private String channelCode;

    /**
     * 交易类型
     */
    @TableField(value = "`transaction_type`")
    private String transactionType;

    /**
     * 源字段名
     */
    @TableField(value = "`source_field_name`")
    private String sourceFieldName;

    /**
     * 标准化后的字段名
     */
    @TableField(value = "`normalized_field_name`")
    private String normalizedFieldName;

    /**
     * 转换函数名
     */
    @TableField(value = "`transform_function_name`")
    private String transformFunctionName;

    /**
     * 默认值表达式
     */
    @TableField(value = "`default_value_expr`")
    private String defaultValueExpr;

    /**
     * 是否为匹配键
     */
    @TableField(value = "`is_matching_key`")
    private Boolean isMatchingKey;

    /**
     * 描述
     */
    @TableField(value = "`description`")
    private String description;

    /**
     * 创建时间
     */
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}