package com.rongchen.byh.webadmin.reconciliation.dataProvider;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import javax.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * DataProvider工厂类。
 * <p>
 * 负责根据渠道、交易类型和数据源类型动态获取相应的{@link DataProvider}实例。
 * 利用Spring的依赖注入自动发现并注册所有DataProvider实现。
 */
@Component
public class DataProviderFactory {

    private static final Logger logger = LoggerFactory.getLogger(DataProviderFactory.class);

    private final List<DataProvider> allProviders;
    private final Map<String, DataProvider> providerCache = new ConcurrentHashMap<>();
    // private final ApplicationContext applicationContext; // Removed

    /**
     * 构造函数，通过Spring注入所有实现了DataProvider接口的Bean。
     * 
     * @param allProviders 所有自动发现的DataProvider实例列表。
     */
    @Autowired
    public DataProviderFactory(List<DataProvider> allProviders /* , ApplicationContext applicationContext */) { // Removed
                                                                                                                // ApplicationContext
        this.allProviders = allProviders;
        // this.applicationContext = applicationContext; // Removed
    }

    /**
     * 初始化方法，在Spring Bean实例化并注入依赖后执行。
     * 将所有注入的DataProvider实例缓存起来，以便快速查找。
     */
    @PostConstruct
    public void init() {
        if (allProviders != null) {
            logger.info("初始化DataProviderFactory，发现 {} 个DataProvider实现。", allProviders.size());
            for (DataProvider provider : allProviders) {
                // All DataProviders are now cached uniformly
                // if (!(provider instanceof DatabaseRawPartnerDataProvider)) { ... } // This
                // check removed
                String key = generateKey(provider.getChannelCode(), provider.getTransactionType(),
                        provider.getDataSourceType());
                if (providerCache.containsKey(key)) {
                    logger.warn("DataProvider注册冲突，键 '{}' 已被Provider '{} ({})' 占用，新的Provider '{} ({})' 将覆盖旧的。",
                            key, providerCache.get(key).getClass().getSimpleName(),
                            providerCache.get(key).getChannelCode(),
                            provider.getClass().getSimpleName(), provider.getChannelCode());
                }
                providerCache.put(key, provider);
                logger.info("缓存 DataProvider: Key='{}', ProviderClass='{}'", key, provider.getClass().getName());
            }
        } else {
            logger.warn("DataProviderFactory初始化时未发现任何DataProvider实现。");
        }
    }

    /**
     * 根据指定的渠道编码、交易类型和数据来源类型获取相应的DataProvider实例。
     *
     * @param channelCode     渠道编码 (例如 "XHY", "QYC_SYSTEM")
     * @param transactionType 交易类型 (例如 "LOAN", "REPAYMENT")
     * @param dataSourceType  数据来源类型 (例如 "PARTNER_SIDE", "OUR_SIDE")
     * @return 匹配的DataProvider实例
     * @throws IllegalArgumentException 如果未找到匹配的DataProvider
     */
    public DataProvider getDataProvider(String channelCode, String transactionType, String dataSourceType) {
        String key = generateKey(channelCode, transactionType, dataSourceType);

        // Removed special handling for "SYNCED_DB_PARTNER_SIDE"
        // if ("SYNCED_DB_PARTNER_SIDE".equalsIgnoreCase(dataSourceType)) { ... }

        DataProvider provider = providerCache.get(key);
        if (provider == null) {
            logger.error("未找到匹配的 DataProvider: Key='{}' (channel={}, transactionType={}, dataSourceType={})",
                    key, channelCode, transactionType, dataSourceType);
            throw new IllegalArgumentException("未找到匹配的 DataProvider，Key: " + key);
        }
        logger.debug("从缓存获取DataProvider: Key='{}', ProviderClass='{}'", key, provider.getClass().getName());
        return provider;
    }

    /**
     * 生成用于缓存DataProvider的唯一键。
     * 键的格式为: CHANNELCODE_TRANSACTIONTYPE_DATASOURCETYPE (全大写)
     *
     * @param channelCode     渠道编码
     * @param transactionType 交易类型
     * @param dataSourceType  数据来源类型
     * @return 生成的缓存键
     */
    private String generateKey(String channelCode, String transactionType, String dataSourceType) {
        if (channelCode == null || channelCode.trim().isEmpty()) {
            throw new IllegalArgumentException("DataProvider的channelCode不能为空");
        }
        if (transactionType == null || transactionType.trim().isEmpty()) {
            throw new IllegalArgumentException("DataProvider的transactionType不能为空");
        }
        if (dataSourceType == null || dataSourceType.trim().isEmpty()) {
            throw new IllegalArgumentException("DataProvider的dataSourceType不能为空");
        }
        return (channelCode.trim() + "_" +
                transactionType.trim() + "_" +
                dataSourceType.trim()).toUpperCase();
    }
}