package com.rongchen.byh.webadmin.reconciliation.exception;

/**
 * 对账处理过程中发生的特定运行时异常。
 */
public class ReconciliationProcessException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    private String batchId;

    public ReconciliationProcessException(String message) {
        super(message);
    }

    public ReconciliationProcessException(String message, Throwable cause) {
        super(message, cause);
    }

    public ReconciliationProcessException(String message, String batchId, Throwable cause) {
        super(message, cause);
        this.batchId = batchId;
    }

    public ReconciliationProcessException(String message, String batchId) {
        super(message);
        this.batchId = batchId;
    }

    public String getBatchId() {
        return batchId;
    }
}