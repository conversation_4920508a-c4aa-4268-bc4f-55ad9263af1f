package com.rongchen.byh.webadmin.upms.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

/**
 * 资方供应商表(CapitalProvider)实体类
 *
 * <AUTHOR>
 * @since 2025-05-16 18:48:00
 */
@Data
public class CapitalProvider implements Serializable {
    private static final long serialVersionUID = 334262154160191730L;
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 供应商名称
     */
    private String providerName;
    /**
     * 资金
     */
    private BigDecimal funds;
    /**
     * 供应商状态：0-禁用，1-启用
     */
    private Integer status;
    /**
     * 资产范围开始时间
     */
    private String startTime;
    /**
     * 资产范围结束时间
     */
    private String endTime;
    /**
     * 创建时间
     */
    private Date createdTime;
    /**
     * 修改时间
     */
    private Date updatedTime;
    /**
     * 用户哦对
     */
    private String sysUserId;
    /**
     * 资产匹配是否完成 0 未完成，1 完成
     */
    private String flag;
    /**
     * 累计本金金额
     */
    private Double totalAmount;


}

