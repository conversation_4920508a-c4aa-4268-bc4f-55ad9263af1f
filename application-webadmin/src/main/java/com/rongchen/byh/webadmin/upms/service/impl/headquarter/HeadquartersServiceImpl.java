package com.rongchen.byh.webadmin.upms.service.impl.headquarter;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.page.PageMethod;
import com.rongchen.byh.common.core.exception.MyRuntimeException;
import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.MyPageUtil;
import com.rongchen.byh.webadmin.upms.dao.HeadquartersMapper;
import com.rongchen.byh.webadmin.upms.dao.StaffDataMapper;
import com.rongchen.byh.webadmin.upms.dto.Headquarters.HeadquartersDto;
import com.rongchen.byh.webadmin.upms.model.StaffData;
import com.rongchen.byh.webadmin.upms.service.headquarter.HeadquartersService;
import com.rongchen.byh.webadmin.upms.vo.headquarters.HeadquartersVo;
import jodd.util.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
public class HeadquartersServiceImpl implements HeadquartersService {
    @Resource
    private HeadquartersMapper headquartersMapper;

    @Resource
    private StaffDataMapper staffDataMapper;

    @Override
    public ResponseResult<MyPageData<HeadquartersVo>> headquartersList(HeadquartersDto dto, MyPageParam pageParam) {
        if (pageParam != null){
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        }
        List<HeadquartersVo> list = headquartersMapper.headquartersList(dto);
        return ResponseResult.success(MyPageUtil.makeResponseData(list, HeadquartersVo.class));
    }

    @Transactional
    @Override
    public void insertHeadquarters(HeadquartersDto dto) {
        //判断该销售是否在其他的门店存点，如果存在不允许添加
        //销售是否在其他小组下，并且销售状态为0
        LambdaQueryWrapper<StaffData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StaffData::getId, dto.getStaffId());
        StaffData staffData = staffDataMapper.selectOne(queryWrapper);
        if (staffData != null && staffData.getStaffStatus() == 0) {
            //判断销售是否在其他小组存在
            int staffAndGroup = headquartersMapper.selectStaffAndGroup(dto.getStaffId(),"add",dto.getOldGroupId());
            //判断小组是否在其他门店存在，但是门店相同则忽略
            int groupAndStore = headquartersMapper.selectGroupAndStore(dto.getGroupId(),"add",dto.getOldStoreId());
            if (staffAndGroup <= 0 && groupAndStore <= 0) {
                headquartersMapper.deleteGroupAndStaff(dto);
                headquartersMapper.deleteStoreAndGroup(dto);
                //新增门店和小组
                headquartersMapper.insertStoreAndGroup(dto);
                // 新增小组和销售
                headquartersMapper.insertGroupAndStaff(dto);
            } else {
                throw new MyRuntimeException("销售员工已在其他门店或小组");
            }
        } else {
            throw new MyRuntimeException("该销售员工已禁用");
        }
    }

    @Transactional
    @Override
    public Boolean updateHeadquarters(HeadquartersDto dto) {
        //销售是否存在且销售状态为0
        LambdaQueryWrapper<StaffData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StaffData::getId, dto.getStaffId());
        StaffData staffData = staffDataMapper.selectOne(queryWrapper);
        if (staffData != null && staffData.getStaffStatus() == 0) {
            //判断销售是否在其他小组存在，小组相同则忽略
            int staffAndGroup = headquartersMapper.selectStaffAndGroup(dto.getStaffId(),"update", dto.getOldGroupId());
            //判断小组是否在其他门店存在，门店相同则忽略
            int groupAndStore = headquartersMapper.selectGroupAndStore(dto.getGroupId(),"update", dto.getOldStoreId());
            if (staffAndGroup <= 0 && groupAndStore <= 0) {
                if (StringUtil.isBlank(dto.getOldGroupId()) || StringUtil.isBlank(dto.getOldStaffId())) {
                    headquartersMapper.deleteGroupAndStaff(dto);
                    headquartersMapper.deleteStoreAndGroup(dto);
                    //新增门店和小组
                    headquartersMapper.insertStoreAndGroup(dto);
                    // 新增小组和销售
                    headquartersMapper.insertGroupAndStaff(dto);
                    return true;
                } else {
                    // 修改门店和小组
                    int storeAndGroup = headquartersMapper.updateStoreAndGroup(dto);
                    // 修改小组和销售
                    int groupAndStaff = headquartersMapper.updateGroupAndStaff(dto);
                    if (storeAndGroup > 0 || groupAndStaff > 0) {
                        return true;
                    }

                }
            } else {
                throw new MyRuntimeException("销售员工已在其他门店或小组");
            }
        } else {
            throw new MyRuntimeException("该销售员工已禁用");
        }
        return false;
    }
}
