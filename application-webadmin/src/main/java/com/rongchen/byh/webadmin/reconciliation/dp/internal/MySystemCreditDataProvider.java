package com.rongchen.byh.webadmin.reconciliation.dp.internal;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.rongchen.byh.webadmin.reconciliation.context.ReconciliationContext;
import com.rongchen.byh.webadmin.reconciliation.dataProvider.DataProvider;
import com.rongchen.byh.webadmin.reconciliation.model.NormalizedCreditRecord;
import com.rongchen.byh.webadmin.upms.dao.ApiCreditRecordMapper;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 我方系统授信数据提供者。
 */
@Component
@Slf4j
public class MySystemCreditDataProvider implements DataProvider {
    private static final Logger logger = LoggerFactory.getLogger(MySystemCreditDataProvider.class);
    private static final DateTimeFormatter OUR_SYSTEM_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private final ApiCreditRecordMapper apiCreditRecordMapper; // 使用正确的Mapper类型
    private final ObjectMapper objectMapper; // Jackson ObjectMapper，用于POJO到Map的转换

    @Autowired
    public MySystemCreditDataProvider(ApiCreditRecordMapper apiCreditRecordMapper, ObjectMapper objectMapper) {
        this.apiCreditRecordMapper = apiCreditRecordMapper;
        // ObjectMapper通常是Spring Boot自动配置的，可以直接注入。
        // 如果没有自动配置或需要特定配置，您可能需要在配置类中提供一个@Bean
        this.objectMapper = objectMapper;
        logger.warn("MySystemCreditDataProvider 依赖的 ApiCreditRecordMapper 尚未配置!"); // 临时日志
    }

    @Override
    public List<Map<String, Object>> loadData(ReconciliationContext context) throws Exception {
        throw new UnsupportedOperationException("请使用 loadNormalizedData() 方法获取我方系统的标准化授信数据。");
    }

    @Override
    public List<NormalizedCreditRecord> loadNormalizedData(ReconciliationContext context) throws Exception {
        logger.info("开始从我方系统加载并转换授信数据，处理日期: {}", context.getProcessingDate());

        // TODO: 实现从数据库查询我方授信数据的逻辑
        // LambdaQueryWrapper<OurCreditEntity> queryWrapper = new
        // LambdaQueryWrapper<OurCreditEntity>()
        // .eq(OurCreditEntity::getApplyDate, context.getProcessingDate());
        // List<OurCreditEntity> creditEntities =
        // ourCreditMapper.selectList(queryWrapper);
        List<?> creditEntities = new ArrayList<>(); // 临时空列表

        if (CollectionUtils.isEmpty(creditEntities)) {
            logger.info("在我方系统未找到处理日期 {} 的授信数据。", context.getProcessingDate());
            return new ArrayList<>();
        }
        logger.info("从我方系统加载了 {} 条授信数据。", creditEntities.size());

        return creditEntities.stream()
                .map(entity -> convertToNormalized(entity, context)) // entity 类型需要明确
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private NormalizedCreditRecord convertToNormalized(Object entity, ReconciliationContext context) {
        // TODO: 强转为您的实际我方授信实体类型 OurCreditEntity
        // OurCreditEntity ourCreditEntity = (OurCreditEntity) entity;
        try {
            NormalizedCreditRecord norm = new NormalizedCreditRecord();

            // --- 手动映射关键字段 ---
            // norm.setPartnerCreditId(ourCreditEntity.getOurCreditApplicationId()); // 示例
            // norm.setCreditApprovalStatus(ourCreditEntity.getApprovalStatus());
            // norm.setCreditDate(parseOurDate(ourCreditEntity.getApplicationDateStr()));
            // norm.setCreditLimitAmount(ourCreditEntity.getApprovedAmount());
            // norm.setStatus(ourCreditEntity.getRecordStatus());

            logger.warn("MySystemCreditDataProvider.convertToNormalized() 需要您根据 'OurCreditEntity' 实现具体的字段映射逻辑。");
            // 占位符，直接返回一个空的，避免NPE，但需要您填充
            norm.setPartnerCreditNo("TEMP_CREDIT_ID_" + System.nanoTime());

            norm.setSourceChannel(getChannelCode()); // MYSYSTEM
            // norm.setOriginalRecordId(String.valueOf(ourCreditEntity.getId()));
            norm.setAdditionalData(new HashMap<>());
            // TODO: 将OurCreditEntity中其他未直接映射的有用字段放入additionalData

            return norm;
        } catch (Exception e) {
            // logger.error("将OurCreditEntity (ID: {}) 转换为NormalizedCreditRecord失败: {}",
            // ourCreditEntity.getId(), e.getMessage(), e);
            logger.error("将我方授信实体转换为NormalizedCreditRecord失败: {}", e.getMessage(), e);
            return null;
        }
    }

    private LocalDate parseOurDate(String dateStr) {
        if (!StringUtils.hasText(dateStr))
            return null;
        try {
            return LocalDate.parse(dateStr, OUR_SYSTEM_DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            logger.warn("解析我方日期字符串 '{}' (格式: yyyy-MM-dd) 失败: {}", dateStr, e.getMessage());
            return null;
        }
    }

    @Override
    public String getChannelCode() {
        return "MYSYSTEM";
    }

    @Override
    public String getTransactionType() {
        return "CREDIT";
    }

    @Override
    public String getDataSourceType() {
        return "OUR_SIDE";
    }
}