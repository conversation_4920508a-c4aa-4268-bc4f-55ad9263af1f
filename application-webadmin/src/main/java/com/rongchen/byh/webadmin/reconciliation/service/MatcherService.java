package com.rongchen.byh.webadmin.reconciliation.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rongchen.byh.webadmin.reconciliation.model.DiffResult;
import com.rongchen.byh.webadmin.reconciliation.model.DifferenceType;
import com.rongchen.byh.webadmin.reconciliation.model.NormalizedTransaction;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Getter;
import org.apache.commons.beanutils.PropertyUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 对账匹配服务。
 * <p>
 * 负责根据对账规则中定义的匹配键 (matchingConfig)，在我方记录列表和资方记录列表之间
 * 进行匹配，找出双方都存在的记录对、我方独有的记录以及资方独有的记录。
 */
@Service
public class MatcherService {

    private static final Logger logger = LoggerFactory.getLogger(MatcherService.class);
    private static final ObjectMapper objectMapper = new ObjectMapper(); // 用于将匹配键对象序列化为JSON字符串

    /**
     * 执行匹配逻辑。
     *
     * @param ourRecords         我方标准化记录列表。
     * @param partnerRecords     资方标准化记录列表。
     * @param matchingConfigNode 从规则中解析出的 'matchingConfig' JSON节点。
     * @param ruleId             当前使用的规则ID (用于差异记录)。
     * @param ruleVersion        当前使用的规则版本 (用于差异记录)。
     * @return {@link MatchingOutcome} 包含匹配上的记录对和未匹配的差异。
     */
    public MatchingOutcome matchRecords(
            List<? extends NormalizedTransaction> ourRecords,
            List<? extends NormalizedTransaction> partnerRecords,
            JsonNode matchingConfigNode,
            String ruleId,
            Integer ruleVersion) {

        MatchingOutcome outcome = new MatchingOutcome();

        if (matchingConfigNode == null || matchingConfigNode.isMissingNode()) {
            logger.error("匹配配置(matchingConfig)缺失，无法执行匹配。RuleId: {}", ruleId);
            ourRecords.forEach(r -> outcome.addUnmatchedOurRecord(r, ruleId, ruleVersion, "CONFIG_ERROR"));
            partnerRecords.forEach(r -> outcome.addUnmatchedPartnerRecord(r, ruleId, ruleVersion, "CONFIG_ERROR"));
            return outcome;
        }

        List<String> ourKeyFields = extractKeyFields(matchingConfigNode, "ourSideKeyFields");
        List<String> partnerKeyFields = extractKeyFields(matchingConfigNode, "partnerSideKeyFields");
        // boolean allowOneToMany =
        // matchingConfigNode.path("allowOneToMany").asBoolean(false); // 暂不处理一对多

        if (CollectionUtils.isEmpty(ourKeyFields) || CollectionUtils.isEmpty(partnerKeyFields)) {
            logger.error("匹配键字段(ourSideKeyFields 或 partnerSideKeyFields)未在matchingConfig中定义。RuleId: {}", ruleId);
            ourRecords.forEach(r -> outcome.addUnmatchedOurRecord(r, ruleId, ruleVersion, "CONFIG_ERROR_NO_KEYS"));
            partnerRecords
                    .forEach(r -> outcome.addUnmatchedPartnerRecord(r, ruleId, ruleVersion, "CONFIG_ERROR_NO_KEYS"));
            return outcome;
        }

        // 构建资方记录的索引，以便快速查找
        // Key: 由partnerKeyFields构成的组合键字符串, Value: 资方记录
        // TODO: 如果允许一对多/多对多，这里的Value应该是 List<NormalizedLoanRecord>，暂时不处理
        // 注意：这里的索引构建过程会忽略掉重复的匹配键，因此如果存在重复的匹配键，后面的匹配逻辑可能无法正确处理
        // 但如果存在重复的匹配键，也会在差异中标记为"资方重复"
        Map<String, NormalizedTransaction> partnerRecordsMap = new HashMap<>();
        Set<String> partnerKeysProcessedForDuplicates = new HashSet<>(); // 用于检测资方重复键

        for (NormalizedTransaction partnerRecord : partnerRecords) {
            String partnerKey = buildCompositeKey(partnerRecord, partnerKeyFields);
            if (partnerKey == null) {
                logger.warn("资方记录 {} 生成匹配键失败 (可能缺少关键字段)，将作为未匹配处理。RuleId: {}", partnerRecord, ruleId);
                outcome.addUnmatchedPartnerRecord(partnerRecord, ruleId, ruleVersion, "KEY_GENERATION_FAILED");
                continue;
            }
            if (partnerRecordsMap.containsKey(partnerKey)) {
                // 检测到资方数据中存在重复的匹配键
                logger.warn("资方数据中检测到重复匹配键: '{}'。当前记录 {} 将被忽略，请检查数据质量。RuleId: {}", partnerKey, partnerRecord, ruleId);
                // 可以将重复的记录也加入到差异中，标记为资方重复
                 outcome.addDuplicatePartnerRecord(partnerRecord, ruleId, ruleVersion, partnerKey);
            } else {
                partnerRecordsMap.put(partnerKey, partnerRecord);
            }
            partnerKeysProcessedForDuplicates.add(partnerKey); // 标记此键已被处理过
        }

        Set<NormalizedTransaction> matchedPartnerRecords = new HashSet<>(); // 存储已匹配上的资方记录，用于后续找出资方独有的记录

        // 遍历我方记录，尝试在资方记录中查找匹配项
        for (NormalizedTransaction ourRecord : ourRecords) {
            String ourKey = buildCompositeKey(ourRecord, ourKeyFields);
            if (ourKey == null) {
                logger.warn("我方记录 {} 生成匹配键失败，将作为未匹配处理。RuleId: {}", ourRecord, ruleId);
                outcome.addUnmatchedOurRecord(ourRecord, ruleId, ruleVersion, "KEY_GENERATION_FAILED");
                continue;
            }

            NormalizedTransaction matchedPartnerRecord = partnerRecordsMap.get(ourKey);

            if (matchedPartnerRecord != null) {
                // 找到了匹配项
                logger.debug("记录匹配成功: 我方键 '{}' -> 资方键 '{}'. RuleId: {}", ourKey, ourKey, ruleId);
                outcome.addMatchedPair(ourRecord, matchedPartnerRecord);
                matchedPartnerRecords.add(matchedPartnerRecord);
            } else {
                // 我方记录在资方中未找到匹配
                logger.debug("我方记录未匹配: 我方键 '{}'. RuleId: {}", ourKey, ruleId);
                outcome.addUnmatchedOurRecord(ourRecord, ruleId, ruleVersion,
                        buildPrimaryKeyJson(ourRecord, ourKeyFields));
            }
        }

        // 找出资方独有的记录 (在partnerRecords中但不在matchedPartnerRecords中)
        for (NormalizedTransaction partnerRecord : partnerRecords) {
            if (!matchedPartnerRecords.contains(partnerRecord)) {
                String partnerKey = buildCompositeKey(partnerRecord, partnerKeyFields);
                // 确保这条未匹配的资方记录不是因为前面重复键被忽略而导致的"伪"未匹配
                // (实际上，如果因重复键被忽略，它一开始就不会进入partnerRecordsMap，也就不会在matchedPartnerRecords里)
                // 但如果partnerRecords本身就包含重复键的记录，这里会正确地将它们都识别为资方多
                logger.debug("资方记录未匹配: 资方键 '{}'. RuleId: {}", partnerKey, ruleId);
                outcome.addUnmatchedPartnerRecord(partnerRecord, ruleId, ruleVersion,
                        buildPrimaryKeyJson(partnerRecord, partnerKeyFields));
            }
        }

        return outcome;
    }

    private List<String> extractKeyFields(JsonNode matchingConfigNode, String sideKeyFieldsName) {
        JsonNode keyFieldsNode = matchingConfigNode.path(sideKeyFieldsName);
        List<String> keyFields = new ArrayList<>();
        if (keyFieldsNode.isArray()) {
            for (JsonNode fieldNameNode : keyFieldsNode) {
                String fieldName = fieldNameNode.asText();
                if (fieldName != null && !fieldName.trim().isEmpty()) {
                    keyFields.add(fieldName.trim());
                }
            }
        }
        return keyFields;
    }

    private String buildCompositeKey(Object record, List<String> keyFields) {
        if (record == null || CollectionUtils.isEmpty(keyFields)) {
            return null;
        }
        return keyFields.stream()
                .map(fieldName -> {
                    try {
                        Object value = PropertyUtils.getProperty(record, fieldName);
                        return value == null ? "NULL" : value.toString(); // 对null值使用特殊标记
                    } catch (Exception e) {
                        logger.warn("构建组合键时获取字段 '{}' 失败: {}", fieldName, e.getMessage());
                        return "ERROR_FIELD"; // 字段获取失败的特殊标记
                    }
                })
                .collect(Collectors.joining("|")); // 使用分隔符构建组合键
    }

    private String buildPrimaryKeyJson(Object record, List<String> keyFields) {
        if (record == null || CollectionUtils.isEmpty(keyFields)) {
            return "{}";
        }
        Map<String, Object> keyMap = new HashMap<>();
        for (String fieldName : keyFields) {
            try {
                Object value = PropertyUtils.getProperty(record, fieldName);
                keyMap.put(fieldName, value);
            } catch (Exception e) {
                logger.warn("构建主键JSON时获取字段 '{}' 失败: {}", fieldName, e.getMessage());
                keyMap.put(fieldName, "ERROR_FETCHING_VALUE");
            }
        }
        try {
            return objectMapper.writeValueAsString(keyMap);
        } catch (Exception e) {
            logger.error("序列化主键Map为JSON失败: {}", keyMap, e);
            return "{\"error\":\"Serialization failed\"}";
        }
    }

    /**
     * 匹配结果的封装类。
     */
    @Getter
    public static class MatchingOutcome {
        private final List<MatchedPair<NormalizedTransaction, NormalizedTransaction>> matchedPairs = new ArrayList<>();
        private final List<DiffResult> unmatchedDiffs = new ArrayList<>();

        public void addMatchedPair(NormalizedTransaction ourRecord, NormalizedTransaction partnerRecord) {
            this.matchedPairs.add(new MatchedPair<>(ourRecord, partnerRecord));
        }

        public void addUnmatchedOurRecord(NormalizedTransaction ourRecord, String ruleId, Integer ruleVersion,
                String primaryKeyJson) {
            DiffResult diff = new DiffResult();
            diff.setRuleIdUsed(ruleId);
            diff.setRuleVersionUsed(ruleVersion);
            diff.setDifferenceType(DifferenceType.MISSING_PARTNER_SIDE);
            diff.setOurRecordSnapshot(ourRecord);
            diff.setPrimaryMatchingKeyJson(primaryKeyJson);
            this.unmatchedDiffs.add(diff);
        }

        public void addUnmatchedPartnerRecord(NormalizedTransaction partnerRecord, String ruleId, Integer ruleVersion,
                String primaryKeyJson) {
            DiffResult diff = new DiffResult();
            diff.setRuleIdUsed(ruleId);
            diff.setRuleVersionUsed(ruleVersion);
            diff.setDifferenceType(DifferenceType.MISSING_OUR_SIDE);
            diff.setPartnerRecordSnapshot(partnerRecord);
            diff.setPrimaryMatchingKeyJson(primaryKeyJson);
            this.unmatchedDiffs.add(diff);
        }

        public void addDuplicatePartnerRecord(NormalizedTransaction partnerRecord, String ruleId, Integer ruleVersion,
            String partnerKey) {
            DiffResult diff = new DiffResult();
            diff.setRuleIdUsed(ruleId);
            diff.setRuleVersionUsed(ruleVersion);
            diff.setDifferenceType(DifferenceType.DUPLICATE_PARTNER_SIDE);
            diff.setPartnerRecordSnapshot(partnerRecord);
            diff.setPrimaryMatchingKeyJson(partnerKey);
            this.unmatchedDiffs.add(diff);
        }
    }

    /**
     * 匹配上的记录对。
     */
    @Getter
    public static class MatchedPair<O extends NormalizedTransaction, P extends NormalizedTransaction> {
        private final O ourRecord;
        private final P partnerRecord;

        public MatchedPair(O ourRecord, P partnerRecord) {
            this.ourRecord = ourRecord;
            this.partnerRecord = partnerRecord;
        }

    }
}