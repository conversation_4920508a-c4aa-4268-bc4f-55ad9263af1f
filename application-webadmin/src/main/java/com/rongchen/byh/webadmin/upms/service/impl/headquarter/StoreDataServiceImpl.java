package com.rongchen.byh.webadmin.upms.service.impl.headquarter;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.page.PageMethod;
import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.MyPageUtil;
import com.rongchen.byh.webadmin.upms.dao.StoreDataMapper;
import com.rongchen.byh.webadmin.upms.model.StoreData;
import com.rongchen.byh.webadmin.upms.service.headquarter.StoreDataService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 门店小组表(StoreData)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-27 14:20:25
 */
@Service
public class StoreDataServiceImpl  implements StoreDataService {

    @Resource
    private StoreDataMapper storeDataMapper;

    @Override
    public ResponseResult<MyPageData<StoreData>> storeDataList(StoreData dto, MyPageParam pageParam) {
        if (pageParam != null){
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        }
        List<StoreData> list = storeDataMapper.storeDataList(dto);
        return ResponseResult.success(MyPageUtil.makeResponseData(list, StoreData.class));
    }

    @Override
    public Boolean insertStoreData(StoreData storeData) {
        LambdaQueryWrapper<StoreData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreData::getStoreName, storeData.getStoreName());
        Long l = storeDataMapper.selectCount(queryWrapper);
        if (l > 0) {return false;}
        return storeDataMapper.insert(storeData) > 0;
    }

    @Override
    public Boolean updateStoreData(StoreData storeData) {
        return storeDataMapper.updateById(storeData) > 0;
    }

    @Override
    public Boolean deleteStoreData(StoreData storeData) {
        return storeDataMapper.deleteById(storeData) > 0;
    }

    @Override
    public List<StoreData> storeList() {
        return storeDataMapper.selectList(null);
    }


}

