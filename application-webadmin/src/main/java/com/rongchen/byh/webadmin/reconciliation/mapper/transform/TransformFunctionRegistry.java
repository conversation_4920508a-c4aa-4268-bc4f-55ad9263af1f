package com.rongchen.byh.webadmin.reconciliation.mapper.transform;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 转换函数注册表。
 * <p>
 * 负责注册和提供各种 {@link TransformFunction} 实现。
 * RecordMapperService将使用此注册表来查找和应用字段映射规则中指定的转换函数。
 */
@Component
public class TransformFunctionRegistry {

    private static final Logger logger = LoggerFactory.getLogger(TransformFunctionRegistry.class);

    private final Map<String, TransformFunction> functions = new HashMap<>();

    /**
     * 注册一个转换函数。
     *
     * @param name     函数的唯一名称，在字段映射规则中引用。
     * @param function {@link TransformFunction} 的实例。
     */
    public void register(String name, TransformFunction function) {
        if (name == null || name.trim().isEmpty() || function == null) {
            logger.warn("注册转换函数失败：名称或函数实例不能为空。");
            return;
        }
        if (functions.containsKey(name)) {
            logger.warn("转换函数名称 '{}' 已存在，旧的将被覆盖。", name);
        }
        functions.put(name, function);
        logger.info("已注册转换函数: {}", name);
    }

    /**
     * 根据名称获取一个已注册的转换函数。
     *
     * @param name 函数的名称。
     * @return {@link TransformFunction} 的实例，如果未找到则返回null。
     */
    public TransformFunction get(String name) {
        if (name == null) {
            return null;
        }
        TransformFunction function = functions.get(name);
        if (function == null) {
            logger.warn("未找到名称为 '{}' 的转换函数。", name);
        }
        return function;
    }

    /**
     * 初始化方法，预注册一些通用的转换函数。
     * 实际应用中，可以通过Spring的Bean发现机制自动注册所有TransformFunction类型的Bean。
     */
    @PostConstruct
    public void init() {
        // 预注册通用函数示例
        register("TO_STRING", (value, params) -> value != null ? value.toString() : null);
        register("TRIM_TO_NULL", (value, params) -> value != null ? StringUtils.trimToNull(value.toString()) : null);
        register("TO_BIGDECIMAL", (value, params) -> {
            if (value == null)
                return null;
            String strValue = StringUtils.trimToNull(value.toString());
            if (strValue == null)
                return null;
            try {
                return new BigDecimal(strValue);
            } catch (NumberFormatException e) {
                logger.error("TO_BIGDECIMAL转换失败: 值='{}', 错误='{}", value, e.getMessage());
                throw e; // 或者返回null，取决于错误处理策略
            }
        });

        register("PARSE_LOCALDATE", (value, params) -> {
            if (value == null)
                return null;
            String strValue = StringUtils.trimToNull(value.toString());
            if (strValue == null)
                return null;
            String formatPattern = params != null ? params.getOrDefault("format", "yyyyMMdd") : "yyyyMMdd";
            try {
                return LocalDate.parse(strValue, DateTimeFormatter.ofPattern(formatPattern));
            } catch (DateTimeParseException e) {
                logger.error("PARSE_LOCALDATE转换失败: 值='{}', 格式='{}', 错误='{}", value, formatPattern, e.getMessage());
                throw e;
            }
        });

        register("PARSE_LOCALDATETIME", (value, params) -> {
            if (value == null)
                return null;
            String strValue = StringUtils.trimToNull(value.toString());
            if (strValue == null)
                return null;
            String formatPattern = params != null ? params.getOrDefault("format", "yyyyMMddHHmmss") : "yyyyMMddHHmmss";
            try {
                return LocalDateTime.parse(strValue, DateTimeFormatter.ofPattern(formatPattern));
            } catch (DateTimeParseException e) {
                logger.error("PARSE_LOCALDATETIME转换失败: 值='{}', 格式='{}', 错误='{}", value, formatPattern, e.getMessage());
                throw e;
            }
        });

        // TODO: 添加更多您需要的通用转换函数
        // 例如：分转元、特定编码映射、布尔值转换等。

        logger.info("TransformFunctionRegistry 初始化完成，共注册 {} 个函数。", functions.size());
    }

    /**
     * 获取所有已注册函数的名称和实现类（主要用于调试或管理界面显示）。
     * 
     * @return 函数名称到函数实现的映射
     */
    public Map<String, TransformFunction> getAllFunctions() {
        return new HashMap<>(functions); // 返回副本以防外部修改
    }
}