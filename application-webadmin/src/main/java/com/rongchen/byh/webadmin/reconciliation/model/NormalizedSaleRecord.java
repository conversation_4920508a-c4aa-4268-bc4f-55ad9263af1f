package com.rongchen.byh.webadmin.reconciliation.model;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import lombok.Data;
// 导入其他需要的类型，例如BigDecimal, String (产品ID, 订单号), Integer (数量)等

/**
 * 统一权益类交易记录模型 (骨架)
 * (例如：赊销实物、虚拟产品)
 */
@Data
public class NormalizedSaleRecord implements NormalizedTransaction {

    // TODO: 根据实际权益类/赊销业务，添加标准化的字段
    // 例如:
    // private String partnerSalesOrderNo; // 资方销售订单号/权益订单号
    // private String internalOrderNo; // 我方内部订单号
    // private String productId; // 产品ID/SKU
    // private String productName; // 产品名称
    // private Integer quantity; // 数量
    // private BigDecimal unitPrice; // 单价
    // private BigDecimal totalAmount; // 总金额
    // private String equityType; // 权益类型 (PHYSICAL_GOODS, VIRTUAL_PRODUCT)
    // private java.time.LocalDateTime transactionTime; // 交易发生时间

    // 通用状态字段
    private String status;

    // 实现 NormalizedTransaction 接口要求的字段
    private Map<String, Object> additionalData = new HashMap<>();
    private String sourceChannel;
    private String originalRecordId;

    /**
     * 获取用于对账金额汇总的 核心金额字段。 每个具体的交易类型实现类应返回其用于金额累加的核心金额字段。 例如，借款返回借款本金，还款返回实还总额。
     *
     * @return 对账相关的金额，如果此交易类型不适用金额汇总或金额为空，则可以返回 BigDecimal.ZERO 或 null。
     */
    @Override
    public BigDecimal getReconciliationAmount() {
        return BigDecimal.ZERO;
    }
}