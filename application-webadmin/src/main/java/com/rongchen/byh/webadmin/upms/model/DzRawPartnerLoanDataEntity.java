package com.rongchen.byh.webadmin.upms.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**

 * 项目名称：byh_java
 * 文件名称: DzRawPartnerLoanDataEntity
 * 创建时间: 2025-05-23 11:57
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.model
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */

/**
 * 资方原始借款数据表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "`dz_raw_partner_loan_data`")
public class DzRawPartnerLoanDataEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * SFTP同步批次ID
     */
    @TableField(value = "`sync_batch_id`")
    private String syncBatchId;

    /**
     * 渠道编码 (如 "XHY")
     */
    @TableField(value = "`channel_code`")
    private String channelCode;

    /**
     * 原始文件对应的业务处理日期 (T日)
     */
    @TableField(value = "`processing_date`")
    private Date processingDate;

    /**
     * 交易类型 (固定为LOAN)
     */
    @TableField(value = "`transaction_type`")
    private String transactionType;

    /**
     * 原始文件名 (可选)
     */
    @TableField(value = "`original_filename`")
    private String originalFilename;

    /**
     * 资方借款单号 (XHY: transSeqno)
     */
    @TableField(value = "`partner_loan_order_no`")
    private String partnerLoanOrderNo;

    /**
     * 放款成功日期 (XHY: payDate, yyyyMMdd)
     */
    @TableField(value = "`funding_date`")
    private Date fundingDate;

    /**
     * 借款本金金额 (XHY: transAmt)
     */
    @TableField(value = "`principal_amount`")
    private BigDecimal principalAmount;

    /**
     * 借款提交日期 (XHY: transDate, yyyyMMdd)
     */
    @TableField(value = "`submission_date`")
    private Date submissionDate;

    /**
     * 借款到期日 (XHY: endDate, yyyyMMdd)
     */
    @TableField(value = "`maturity_date`")
    private Date maturityDate;

    /**
     * 期数 (XHY: totalCnt)
     */
    @TableField(value = "`term_count`")
    private Integer termCount;

    /**
     * 费率字符串 (XHY: rate, 例如0.2400)
     */
    @TableField(value = "`interest_rate_str`")
    private String interestRateStr;

    /**
     * 还款方式代码 (XHY: repayType)：
     0-等本等息
     1-等额本⾦
     2-等额本息
     3-次性还本付息
     */
    @TableField(value = "`repayment_type_code`")
    private String repaymentTypeCode;

    /**
     * 贷款机构代码 (XHY: capCode)
     */
    @TableField(value = "`funding_provider_code`")
    private String fundingProviderCode;

    /**
     * 存储所有其他原始字段的JSON对象
     */
    @TableField(value = "`extended_fields_json`")
    private String extendedFieldsJson;

    /**
     * 数据加载到此表的时间
     */
    @TableField(value = "`load_time`")
    private Date loadTime;
}