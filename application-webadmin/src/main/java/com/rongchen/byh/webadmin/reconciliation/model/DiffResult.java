package com.rongchen.byh.webadmin.reconciliation.model;

import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * 对账差异结果模型
 */
@Data
public class DiffResult {

    private String batchId;
    private String ruleIdUsed;
    private Integer ruleVersionUsed; // 技术方案中是int，保持一致
    private DifferenceType differenceType;
    private String primaryMatchingKeyJson; // 主要匹配键的JSON表示

    // 注意：技术方案中的 NormalizedTransaction ourRecordSnapshot 和 partnerRecordSnapshot
    // 这里我们暂时使用 NormalizedLoanRecord，如果后续有多种类型的 NormalizedTransaction，
    // 可能需要一个通用的基类或接口 NormalizedTransaction。
    private NormalizedTransaction ourRecordSnapshot; // 改为通用接口类型
    private NormalizedTransaction partnerRecordSnapshot; // 改为通用接口类型

    private List<FieldDifferenceDetail> fieldDifferences;

    private String confirmationStatus;
    private String confirmedBy;
    private LocalDateTime confirmationTimestamp;
    private String comments;
    private LocalDateTime createdTimestamp;


}