package com.rongchen.byh.webadmin.upms.controller;

import com.rongchen.byh.common.core.annotation.MyRequestBody;
import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.MyCommonUtil;
import com.rongchen.byh.webadmin.upms.model.CapitalData;
import com.rongchen.byh.webadmin.upms.service.CapitalDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 资方信息表(CapitalData)表控制层
 *
 * <AUTHOR>
 * @since 2025-04-03 17:29:59
 */
@Tag(name = "资方信息表接口")
@Slf4j
@RestController
@RequestMapping("/admin/upms/capital")
public class CapitalDataController {
    /**
     * 服务对象
     */
    @Resource
    private CapitalDataService capitalDataService;

    @Operation(summary = "资方信息表列表查询")
    @PostMapping("/list")
    public ResponseResult<MyPageData<CapitalData>> capitalList(@MyRequestBody CapitalData capitalData, @MyRequestBody MyPageParam pageParam) {
        return capitalDataService.capitalList(capitalData, pageParam);
    }


    /**
     * 新增数据
     *
     * @param capitalData 实体
     * @return 新增结果
     */
    @Operation(summary = "新增数据")
    @PostMapping("/add")
    public ResponseResult<Boolean> add(@MyRequestBody CapitalData capitalData) {
        return ResponseResult.success(capitalDataService.insert(capitalData));
    }

    /**
     * 编辑数据
     *
     * @param capitalData 实体
     * @return 编辑结果
     */
    @Operation(summary = "编辑数据")
    @PostMapping("/edit")
    public ResponseResult<Boolean> edit(@MyRequestBody CapitalData capitalData) {
        return ResponseResult.success(capitalDataService.update(capitalData));
    }

    @Operation(summary = "以字典形式返回全部资金方信息数据集合")
    @PostMapping("/capitalDictList")
    public ResponseResult<List<Map<String, Object>>> capitalDictList() {
        List<CapitalData> list = capitalDataService.capitalDictList();
        return ResponseResult.success(
                MyCommonUtil.toDictDataList(list, CapitalData::getId, CapitalData::getName));
    }

}

