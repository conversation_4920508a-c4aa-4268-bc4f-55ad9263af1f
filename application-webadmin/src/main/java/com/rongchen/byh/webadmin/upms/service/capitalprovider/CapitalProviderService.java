package com.rongchen.byh.webadmin.upms.service.capitalprovider;

import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.webadmin.upms.dto.cp.CPBillDetailDto;
import com.rongchen.byh.webadmin.upms.dto.cp.CPBillListDto;
import com.rongchen.byh.webadmin.upms.dto.cp.DisburseDataRequestListDto;
import com.rongchen.byh.webadmin.upms.model.CapitalProvider;
import com.rongchen.byh.webadmin.upms.vo.cp.CPBillListVo;
import com.rongchen.byh.webadmin.upms.vo.cp.CPBillPeriodDetailVo;
import com.rongchen.byh.webadmin.upms.vo.cp.CPDisburseDataResultListVo;
import com.rongchen.byh.webadmin.upms.vo.cp.CapitalProviderVo;

import java.util.List;

/**
 * 资方供应商表(CapitalProvider)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-16 18:48:00
 */
public interface CapitalProviderService {


    /**
     * 查询数据
     */
    ResponseResult<MyPageData<CapitalProviderVo>>  capitalProviderList(CapitalProvider dto, MyPageParam pageParam);

    /**
     * 新增数据
     *
     * @param capitalProvider 实例对象
     * @return 实例对象
     */
    boolean insert(CapitalProvider capitalProvider);

    void expenditureMarkers(CapitalProvider dto);

    ResponseResult<MyPageData<CPDisburseDataResultListVo>> disburseOrderList(DisburseDataRequestListDto dto, MyPageParam pageParam);

    ResponseResult<MyPageData<CPBillListVo>> billList(CPBillListDto dto, MyPageParam pageParam);

    ResponseResult<List<CPBillPeriodDetailVo>> billDetail(CPBillDetailDto dto);

    Boolean freezeCpStatus(CapitalProvider dto);
}
