package com.rongchen.byh.webadmin.upms.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.DesensitizedUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.page.PageMethod;
import com.rongchen.byh.common.api.zifang.dto.*;
import com.rongchen.byh.common.api.zifang.service.ContractApi;
import com.rongchen.byh.common.api.zifang.service.OtherApi;
import com.rongchen.byh.common.api.zifang.service.RepaymentApi;
import com.rongchen.byh.common.api.zifang.vo.*;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.constant.SettleFlagConstant;
import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.object.TokenData;
import com.rongchen.byh.common.core.util.MyPageUtil;
import com.rongchen.byh.webadmin.config.ZifangFactory;
import com.rongchen.byh.webadmin.upms.dao.*;
import com.rongchen.byh.webadmin.upms.dto.bill.*;
import com.rongchen.byh.webadmin.upms.dto.order.DisburseOrderDetailDto;
import com.rongchen.byh.webadmin.upms.dto.order.DisburseOrderListDto;
import com.rongchen.byh.webadmin.upms.loveSign.LoveSignProperties;
import com.rongchen.byh.webadmin.upms.model.*;
import com.rongchen.byh.webadmin.upms.service.DisburseDataService;
import com.rongchen.byh.webadmin.upms.service.SysMenuService;
import com.rongchen.byh.webadmin.upms.utils.CreditLoanNoUtils;
import com.rongchen.byh.webadmin.upms.utils.LoveSignUtil;
import com.rongchen.byh.webadmin.upms.utils.isDesensitizationPermissionExistUtils;
import com.rongchen.byh.webadmin.upms.vo.bill.*;
import com.rongchen.byh.webadmin.upms.vo.order.DisburseOrderDetailVo;
import com.rongchen.byh.webadmin.upms.vo.order.DisburseOrderListVo;
import com.rongchen.byh.webadmin.upms.vo.order.LateCollectionsLogVo;
import com.rongchen.byh.webadmin.upms.vo.order.OnLoanVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【disburse_data(支用记录)】的数据库操作Service实现
 * @createDate 2025-01-23 10:21:02
 */
@Service
@Slf4j
public class DisburseDataServiceImpl extends ServiceImpl<DisburseDataMapper, DisburseData>
        implements DisburseDataService{
    @Resource
    private DisburseDataMapper disburseDataMapper;
    @Resource
    private SysRoleMapper sysRoleMapper;
    @Resource
    private UserLoveLogMapper userLoveLogMapper;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private LateCollectionsLogMapper lateCollectionsLogMapper;
    @Resource
    private RepayScheduleMapper repayScheduleMapper;
    @Resource
    private LoveSignProperties loveSignProperties;
    @Resource
    private CapitalDataMapper capitalDataMapper;
    @Resource
    private ZifangFactory zifangFactory;
    @Resource
    private UserBankCardMapper userBankCardMapper;
    @Resource
    private SaleScheduleMapper saleScheduleMapper;
    @Resource
    private RepaySaleApplyMapper repaySaleApplyMapper;
    @Resource
    private RepayScheduleApplyMapper repayScheduleApplyMapper;
    @Autowired
    private SysMenuService sysMenuService;
    @Resource
    private UserDetailMapper userDetailMapper;

    @Override
    public ResponseResult<MyPageData<DisburseOrderListVo>> list(DisburseOrderListDto dto, MyPageParam pageParam) {
        TokenData tokenData = TokenData.takeFromRequest();
        if (tokenData != null && tokenData.getRoleIds() != null) {
            LambdaQueryWrapper<SysRole> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(SysRole::getRoleId, tokenData.getRoleIds().split(","));
            List<SysRole> sysRoles = sysRoleMapper.selectList(queryWrapper);
            // 定义需要检查的角色名称关键字列表
            List<String> roleKeywords = Arrays.asList("门店主管", "门店组长", "门店销售");
            for (SysRole sysRole : sysRoles) {
                String roleName = sysRole.getRoleName();
                if (StringUtils.isNotEmpty(roleName)) {
                    for (String keyword : roleKeywords) {
                        if (roleName.contains(keyword) && !tokenData.getIsAdmin()) {
                            dto.setSysUserId(tokenData.getUserId());
                            dto.setSysRoleName(roleName);
                        }
                    }
                }
            }
        }
        if (pageParam != null){
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        }
        List<DisburseOrderListVo> list = disburseDataMapper.list(dto);

        Collection<SysMenu> allMenuList = sysMenuService.getMenuListByRoleIds(tokenData.getRoleIds());

        if (isDesensitizationPermissionExistUtils.isDesensitizationPermissionExist(allMenuList,"fullOrder")) {
            for (DisburseOrderListVo disburseOrderListVo : list) {
                disburseOrderListVo.setMobile(DesensitizedUtil.mobilePhone(disburseOrderListVo.getMobile()));
            }
        }
        return ResponseResult.success(MyPageUtil.makeResponseData(list));
    }

    @Override
    public ResponseResult<DisburseOrderDetailVo> detail(DisburseOrderDetailDto dto) {
        DisburseOrderDetailVo detail = disburseDataMapper.detail(dto.getDisburseOrderId());
        if (detail != null && StringUtils.isNotBlank(detail.getUserId())){
            List<UserLoveLog> userLoveLogList = userLoveLogMapper.getByUserId(detail.getUserId());
            detail.setUserLoveLogList(userLoveLogList);
        }
        return ResponseResult.success(detail);
    }

    @Override
    public ResponseResult<MyPageData<BillListVo>> billList(BillListDto dto, MyPageParam pageParam) {
        TokenData tokenData = TokenData.takeFromRequest();
        if (tokenData != null && tokenData.getRoleIds() != null) {
            LambdaQueryWrapper<SysRole> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(SysRole::getRoleId, tokenData.getRoleIds().split(","));
            List<SysRole> sysRoles = sysRoleMapper.selectList(queryWrapper);
            // 定义需要检查的角色名称关键字列表
            List<String> roleKeywords = Arrays.asList("门店主管", "门店组长", "门店销售","城市代理","线下催收");
            for (SysRole sysRole : sysRoles) {
                String roleName = sysRole.getRoleName();
                if (StringUtils.isNotEmpty(roleName)) {
                    for (String keyword : roleKeywords) {
                        if (roleName.contains(keyword) && !tokenData.getIsAdmin()) {
                            dto.setSysUserId(tokenData.getUserId());
                            dto.setSysRoleName(roleName);
                        }
                    }
                }
            }
        }
        LocalDate currentDate = LocalDate.now();
        Date date = Date.from(currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        String formattedDate = DateUtil.format(date, "yyyy-MM-dd");
        List<BillListVo> list = disburseDataMapper.billList(dto);
        // 处理时间过滤
        if (StringUtils.isNotBlank(dto.getRepayStartTime()) && StringUtils.isNotBlank(dto.getRepayEndTime())) {
            // 解析开始和结束日期
            Date startDate = DateUtil.parse(dto.getRepayStartTime(), "yyyy-MM-dd");
            Date endDate = DateUtil.parse(dto.getRepayEndTime(), "yyyy-MM-dd");

            // 过滤列表
            list = list.stream()
                    .filter(billListVo -> {
                        if (billListVo == null || billListVo.getRepaymentDate() == null) {
                            return false;
                        }
                        Date repaymentDate = billListVo.getRepaymentDate();
                        return repaymentDate.compareTo(startDate) >= 0 && repaymentDate.compareTo(endDate) < 0;
                    })
                    .collect(Collectors.toList());
        }

        Collection<SysMenu> allMenuList = sysMenuService.getMenuListByRoleIds(tokenData.getRoleIds());

        if (isDesensitizationPermissionExistUtils.isDesensitizationPermissionExist(allMenuList,"repaymentBill")) {
            for (BillListVo billListVo : list) {
                billListVo.setMobile(DesensitizedUtil.mobilePhone(billListVo.getMobile()));
            }
        }
        for (BillListVo billListVo : list) {
            if (billListVo != null && billListVo.getCreditStatusName() != null) {
                String totalTerm = billListVo.getTotalTerm();
                Date repaymentDate = billListVo.getRepaymentDate();

                if ("已结清".equals(billListVo.getCreditStatusName())) {
                    billListVo.setTotalTerm("12/12");
                } else if (StringUtils.isNotBlank(totalTerm) && "0".equals(totalTerm)) {
                    billListVo.setTotalTerm("1/12");
                } else if (StringUtils.isNotBlank(totalTerm) && repaymentDate != null &&
                        DateUtil.format(repaymentDate, "yyyy-MM-dd").equals(formattedDate)) {
                    billListVo.setTotalTerm(Integer.valueOf(totalTerm) - 1 + "/12");
                } else if (StringUtils.isNotBlank(totalTerm)) {
                    billListVo.setTotalTerm(totalTerm + "/12");
                }
            }
        }

        List<BillListVo> pageList = new ArrayList<>();
        if (pageParam != null) {
            int startIndex = (pageParam.getPageNum() - 1) * pageParam.getPageSize();
            int endIndex = Math.min(startIndex + pageParam.getPageSize(), list.size());
            if (startIndex < endIndex) {
                pageList = new ArrayList<>(list.subList(startIndex, endIndex));
            }
        } else {
            pageList = list;
        }

        MyPageData<BillListVo> pageData = MyPageUtil.makeResponseData(pageList, (long) list.size());
        return ResponseResult.success(pageData);
    }

    @Override
    public ResponseResult<BillDetailVo> billDetail(BillDetailDto dto) {
        BillDetailVo billDetailVo = disburseDataMapper.billDetail(dto.getBillId());
        List<BillPeriodDetailVo> periodDetail = disburseDataMapper.billPeriodDetail(dto.getBillId());
        billDetailVo.setBillPeriodDetailVoList(periodDetail);
        return ResponseResult.success(billDetailVo);
    }

    @Override
    public ResponseResult<List<BillRepayRecordVo>> billRepayRecordList(BillRepayRecordDto dto) {
        Integer repayType = dto.getRepayType();
        if (repayType == 1) {
            List<BillRepayRecordVo> list = disburseDataMapper.billScheduleRepayRecord(dto.getId());
            return ResponseResult.success(list);
        } else  {
            List<BillRepayRecordVo> list = disburseDataMapper.billSaleRepayRecord(dto.getId());
            return ResponseResult.success(list);
        }
    }

    @Override
    public ResponseResult<Void> repay(BillRepayDto dto) {
        try {
            // 检查输入参数是否合法
            if (dto == null || dto.getRepayType() == null) {
                return ResponseResult.error(ErrorCodeEnum.FAIL, "调用 application-app 服务失败，参数不合法");
            }

            // 根据类型获取对应的 URL
            String appServiceUrl = getAppServiceUrl(dto.getRepayType());
            if (appServiceUrl.isEmpty()) {
                return ResponseResult.error(ErrorCodeEnum.FAIL, "调用 application-app 服务失败，未知的类型");
            }
            if (dto.getRepayMethod() == null) {
                dto.setRepayMethod(1);
            }

            // 构建请求头和请求实体
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<BillRepayDto> requestEntity = new HttpEntity<>(dto, headers);

            // 调用远程服务
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(
                    appServiceUrl,
                    requestEntity,
                    String.class
            );

            // 检查响应状态码
            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                return ResponseResult.success();
            } else {
                return ResponseResult.error(ErrorCodeEnum.FAIL, "调用 application-app 服务失败，响应状态码异常");
            }
        } catch (RestClientException e) {
            // 捕获 RestClientException 并返回对应错误信息
            String errorMessage = getErrorMessage(dto.getRepayType());
            log.error("调用 application-app 服务失败，类型: {}, 异常信息: {}", dto.getRepayType(), e.getMessage(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, errorMessage);
        } catch (Exception e) {
            // 捕获其他异常并返回通用错误信息
            log.error("调用 application-app 服务失败，异常信息: {}", e.getMessage(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, "调用 application-app 服务失败");
        }
    }


    @Override
    public ResponseResult<MyPageData<OverdueListVo>> overdueList(OverdueListDto dto, MyPageParam pageParam) {
        TokenData tokenData = TokenData.takeFromRequest();
        if (tokenData != null && tokenData.getRoleIds() != null) {
            LambdaQueryWrapper<SysRole> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(SysRole::getRoleId, tokenData.getRoleIds().split(","));
            List<SysRole> sysRoles = sysRoleMapper.selectList(queryWrapper);
            // 定义需要检查的角色名称关键字列表
            List<String> roleKeywords = Arrays.asList("门店主管", "门店组长", "门店销售", "线下催收");
            for (SysRole sysRole : sysRoles) {
                String roleName = sysRole.getRoleName();
                if (StringUtils.isNotEmpty(roleName)) {
                    for (String keyword : roleKeywords) {
                        if (roleName.contains(keyword) && !tokenData.getIsAdmin()) {
                            dto.setSysUserId(tokenData.getUserId());
                            dto.setSysRoleName(roleName);
                        }
                    }
                }
            }
        }

        if (pageParam != null){
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        }
        List<OverdueListVo> list = disburseDataMapper.overdueList(dto);

        Collection<SysMenu> allMenuList = sysMenuService.getMenuListByRoleIds(tokenData.getRoleIds());

        if (isDesensitizationPermissionExistUtils.isDesensitizationPermissionExist(allMenuList,"overdueBill")) {
            for (OverdueListVo overdueListVo : list) {
                overdueListVo.setMobile(DesensitizedUtil.mobilePhone(overdueListVo.getMobile()));
            }
        }
        Date today = DateUtil.date(); // 当前日期

        for (OverdueListVo overdueListVo : list) {
            SaleSchedule term1 = null, term2 = null, term3 = null;
            List<SaleSchedule> saleSchedules = disburseDataMapper.saleScheduleSettleFlag(overdueListVo.getId());
            // 分离各期还款计划（假设 repayTerm 是字符串，如 "1", "2", "3"）
            for (SaleSchedule schedule : saleSchedules) {
                String term = schedule.getRepayTerm();
                if ("1".equals(term)) term1 = schedule;
                else if ("2".equals(term)) term2 = schedule;
                else if ("3".equals(term)) term3 = schedule;
            }

            // ----------------------- 处理第一期 -----------------------
            if (term1 != null) { // 存在第一期
                Date term1Date = DateUtil.parse(term1.getRepayOwnbDate(), "yyyy-MM-dd");
                if (term1Date.after(today)) {
                    // 第一期未到：直接未还，终止处理
                    overdueListVo.setSaleScheduleStatus("未还");
                } else {
                    // 第一期已到
                    if (!"CLOSE".equals(term1.getSettleFlag())) {
                        // 第一期未结清：直接逾期，终止处理
                        overdueListVo.setSaleScheduleStatus("逾期");
                    } else {
                        // 第一期已结清，处理第二期（仅当第二期存在且已到还款日时继续）
                        if (term2 != null) { // 存在第二期
                            Date term2Date = DateUtil.parse(term2.getRepayOwnbDate(), "yyyy-MM-dd");
                            if (term2Date.after(today)) {
                                // 第二期未到：保持第一期结清状态（已还），终止处理
                                overdueListVo.setSaleScheduleStatus("已还");
                            } else {
                                // 第二期已到，继续处理第二期
                                if (!"CLOSE".equals(term2.getSettleFlag())) {
                                    // 第二期未结清：逾期，终止处理
                                    overdueListVo.setSaleScheduleStatus("逾期");
                                } else {
                                    // 第二期已结清，处理第三期（仅当第三期存在且已到还款日时继续）
                                    if (term3 != null) {
                                        Date term3Date = DateUtil.parse(term3.getRepayOwnbDate(), "yyyy-MM-dd");
                                        if (term3Date.after(today)) {
                                            // 第三期未到：保持前两期结清状态（已还），终止处理
                                            overdueListVo.setSaleScheduleStatus("已还");
                                        } else {
                                            // 第三期已到，按状态标记
                                            overdueListVo.setSaleScheduleStatus(
                                                    term3.getSettleFlag().equals("CLOSE") ? "已还" : "逾期"
                                            );
                                        }
                                    } else {
                                        // 无第三期，前两期已结清：已还
                                        overdueListVo.setSaleScheduleStatus("已还");
                                    }
                                }
                            }
                        } else {
                            // 无第二期，第一期已结清：已还
                            overdueListVo.setSaleScheduleStatus("已还");
                        }
                    }
                }
            } else {
                // 无第一期，从第二期开始处理（逻辑类似第一期，仅处理存在的期数）
                if (term2 != null) {
                    Date term2Date = DateUtil.parse(term2.getRepayOwnbDate(), "yyyy-MM-dd");
                    if (term2Date.after(today)) {
                        // 第二期未到：未还（无第一期，默认未还）
                        overdueListVo.setSaleScheduleStatus("未还");
                    } else {
                        if (!"CLOSE".equals(term2.getSettleFlag())) {
                            // 第二期未结清：逾期
                            overdueListVo.setSaleScheduleStatus("逾期");
                        } else {
                            // 第二期已结清，处理第三期
                            if (term3 != null) {
                                Date term3Date = DateUtil.parse(term3.getRepayOwnbDate(), "yyyy-MM-dd");
                                if (term3Date.after(today)) {
                                    // 第三期未到：已还（仅第二期结清）
                                    overdueListVo.setSaleScheduleStatus("已还");
                                } else {
                                    overdueListVo.setSaleScheduleStatus(
                                            term3.getSettleFlag().equals("CLOSE") ? "已还" : "逾期"
                                    );
                                }
                            } else {
                                // 无第三期，第二期已结清：已还
                                overdueListVo.setSaleScheduleStatus("已还");
                            }
                        }
                    }
                } else {
                    // 无第一、二期，处理第三期
                    if (term3 != null) {
                        Date term3Date = DateUtil.parse(term3.getRepayOwnbDate(), "yyyy-MM-dd");
                        overdueListVo.setSaleScheduleStatus(
                                term3Date.after(today) ? "未还" :
                                        term3.getSettleFlag().equals("CLOSE") ? "已还" : "逾期"
                        );
                    } else {
                        // 无任何期数：默认未还
                        overdueListVo.setSaleScheduleStatus("未还");
                    }
                }
            }
        }

        return ResponseResult.success(MyPageUtil.makeResponseData(list));
    }

    @Override
    public ResponseResult<BillDetailVo> overdueDetailList(BillDetailDto dto) {
        BillDetailVo billDetailVo = disburseDataMapper.billDetail(dto.getBillId());
        List<BillPeriodDetailVo> periodDetail = disburseDataMapper.billPeriodDetail(dto.getBillId());
        billDetailVo.setBillPeriodDetailVoList(periodDetail);
        return ResponseResult.success(billDetailVo);
    }

    @Override
    public ResponseResult<List<BillRepayRecordVo>> overdueRepayRecordList(BillRepayRecordDto dto) {
        Integer repayType = dto.getRepayType();
        if (repayType == 1) {
            List<BillRepayRecordVo> list = disburseDataMapper.billScheduleRepayRecord(dto.getId());
            return ResponseResult.success(list);
        } else  {
            List<BillRepayRecordVo> list = disburseDataMapper.billSaleRepayRecord(dto.getId());
            return ResponseResult.success(list);
        }
    }


    @Override
    public ResponseResult<Void> onlineRepay(BillRepayDto dto) {
        try {
            // 检查输入参数是否合法
            if (dto == null || dto.getRepayType() == null) {
                return ResponseResult.error(ErrorCodeEnum.FAIL, "调用 application-app 服务失败，参数不合法");
            }

            // 根据类型获取对应的 URL
            String appServiceUrl = getAppServiceUrl(dto.getRepayType());
            if (appServiceUrl.isEmpty()) {
                return ResponseResult.error(ErrorCodeEnum.FAIL, "调用 application-app 服务失败，未知的类型");
            }
            if (dto.getRepayMethod() == null) {
                dto.setRepayMethod(2);
            }
            // 构建请求头和请求实体
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<BillRepayDto> requestEntity = new HttpEntity<>(dto, headers);

            // 调用远程服务
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(
                    appServiceUrl,
                    requestEntity,
                    String.class
            );

            // 检查响应状态码
            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                return ResponseResult.success();
            } else {
                return ResponseResult.error(ErrorCodeEnum.FAIL, "调用 application-app 服务失败，响应状态码异常");
            }
        } catch (RestClientException e) {
            // 捕获 RestClientException 并返回对应错误信息
            String errorMessage = getErrorMessage(dto.getRepayType());
            log.error("调用 application-app 服务失败，类型: {}, 异常信息: {}", dto.getRepayType(), e.getMessage(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, errorMessage);
        } catch (Exception e) {
            // 捕获其他异常并返回通用错误信息
            log.error("调用 application-app 服务失败，异常信息: {}", e.getMessage(), e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, "调用 application-app 服务失败");
        }
    }

    @Override
    public ResponseResult<MyPageData<LateCollectionsLogVo>> LateCollectionsLogList(BillDetailDto dto, MyPageParam pageParam) {
        if (pageParam != null){
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        }
        List<LateCollectionsLogVo> lateCollectionsLogs = lateCollectionsLogMapper.selectList(dto);
        return ResponseResult.success(MyPageUtil.makeResponseData(lateCollectionsLogs));
    }

    @Override
    public ResponseResult<Void> insertLateCollectionsLog(LateCollectionsLog dto) {
        dto.setCreateUserId(TokenData.takeFromRequest().getUserId());
        lateCollectionsLogMapper.insert(dto);
        return ResponseResult.success();
    }


    @Override
    public Void download(DisburseOrderDetailDto dto, HttpServletResponse response) {
        try {
            String contractNo = dto.getContractNo();
            if (StringUtils.isNotBlank(contractNo)) {
                String host = loveSignProperties.getBaseUrl() + "/contract/downloadContract" ;
                String appId = loveSignProperties.getAppId();
                String privateKey = loveSignProperties.getRsaPriKey();

                // 构造请求参数
                String dataString = "{\"contractNo\":\"" + contractNo + "\"}";
                dataString = JSONObject.toJSONString(JSONObject.parseObject(dataString),
                        SerializerFeature.MapSortField, SerializerFeature.DisableCircularReferenceDetect);

                String timestamp = String.valueOf(LocalDateTime.now().plusMinutes(10)
                        .toInstant(ZoneOffset.of("+8")).toEpochMilli());
                String sign = LoveSignUtil.getSign(appId, privateKey, dataString, timestamp);
                String boundary = UUID.randomUUID().toString();

                // 建立HTTP连接
                HttpURLConnection connection = (HttpURLConnection) new URL(host).openConnection();
                connection.setRequestMethod("POST");
                connection.setRequestProperty("sign", sign);
                connection.setRequestProperty("timestamp", timestamp);
                connection.setRequestProperty("Content-Type",
                        "multipart/form-data;boundary=" + boundary);
                connection.setDoOutput(true);

                // 写入请求体
                DataOutputStream dos = new DataOutputStream(connection.getOutputStream());
                String textValues = LoveSignUtil.addTextValue("appId", appId, boundary) +
                        LoveSignUtil.addTextValue("timestamp", timestamp, boundary) +
                        LoveSignUtil.addTextValue("bizData", dataString, boundary);
                dos.write(textValues.getBytes(StandardCharsets.UTF_8));
                dos.write(("--" + boundary + "--\r\n").getBytes(StandardCharsets.UTF_8));
                dos.flush();
                dos.close();

                // 获取响应状态码
                int responseCode = connection.getResponseCode();
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    // 解析响应JSON
                    String jsonResponse = readResponse(connection);
                    JSONObject result = JSONObject.parseObject(jsonResponse);

                    // 处理成功响应
                    if (result.getIntValue("code") == 100000) {
                        JSONObject data = result.getJSONObject("data");
                        String fileName = data.getString("fileName");
                        // 检查文件名是否有 .pdf 后缀，如果没有则添加
                        if (!fileName.toLowerCase().endsWith(".pdf")) {
                            fileName = fileName + ".pdf";
                        }
                        String fileData = data.getString("data");

                        // 解码Base64数据
                        byte[] pdfBytes = Base64.getDecoder().decode(fileData);

                        // 设置响应头
                        response.setContentType("application/pdf");
                        response.setHeader("Content-Disposition",
                                "attachment; filename=\"" + URLEncoder.encode(fileName, "UTF-8") + "\"");
                        response.setContentLength(pdfBytes.length);

                        // 写入输出流
                        try (OutputStream outputStream = response.getOutputStream()) {
                            outputStream.write(pdfBytes);
                            outputStream.flush();
                        }
                        return null;
                    } else {
                        // 处理业务错误
                        response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                        try (PrintWriter out = response.getWriter()) {
                            out.println("文件下载失败：" + result.getString("msg"));
                            out.flush();
                        }
                        return null;
                    }
                } else {
                    // 处理HTTP错误
                    response.setStatus(responseCode);
                    try (PrintWriter out = response.getWriter()) {
                        out.println("请求失败，响应码：" + responseCode);
                        out.flush();
                    }
                    return null;
                }
            }
            return null;
        } catch (Exception e) {
            // 处理异常
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            try (PrintWriter out = response.getWriter()) {
                out.println("下载失败：" + e.getMessage());
                e.printStackTrace(out);
                out.flush();
            } catch (IOException ex) {
                throw new RuntimeException(ex);
            }
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<BillPreRepayApplyVo> settlement(SettleRepayApplyDto dto) {
        try {
            //获取支用订单
            DisburseData disburseData = disburseDataMapper.selectById(dto.getId());
            if (disburseData.getCreditStatus() != 500) {
                return ResponseResult.error(ErrorCodeEnum.FAIL, "该授信状态【非还款中】状态,无法操作");
            }

            // 获取本息还款计划为还款中的数据
            List<RepaySchedule> repaymentList =  repayScheduleMapper.selectList(new LambdaQueryWrapper<RepaySchedule>().eq(RepaySchedule::getDisburseId, dto.getId())
                    .in(RepaySchedule::getSettleFlag, "REPAYING"));
            if (CollectionUtils.isNotEmpty(repaymentList)) {
                return ResponseResult.error(ErrorCodeEnum.FAIL, "12期账单存在还款中状态，无法进行试算 !");
            }

            String sequence = CreditLoanNoUtils.getSequence("ln-rp");
            //获取本息还款计划为未还的数据
            LambdaQueryWrapper<RepaySchedule> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(RepaySchedule::getDisburseId, dto.getId())
                    .eq(RepaySchedule::getSettleFlag, "RUNNING");
            List<RepaySchedule> repaySchedules = repayScheduleMapper.selectList(queryWrapper);
            BigDecimal repayTotalAmt = new BigDecimal("0.00");
            Integer minTerm = null;
            for (RepaySchedule repaySchedule : repaySchedules) {
                repaySchedule.setRepayApplyNo(sequence);
                String repayTerm = repaySchedule.getRepayTerm();
                // 获取当前还款计划中的最小期数
                Integer currentTerm = Integer.parseInt(repayTerm);
                if (minTerm == null || currentTerm < minTerm) {
                    minTerm = currentTerm;
                }
                repayTotalAmt = repayTotalAmt.add(repaySchedule.getTermRetPrin());
                repayScheduleMapper.updateById(repaySchedule);
            }
            BillPreRepayApplyVo billPreRepayApplyVo = new BillPreRepayApplyVo();

            // 获取权益部分数据
            List<SaleSchedule> saleSchedules = disburseDataMapper.saleScheduleSettleFlag(disburseData.getId());
            LocalDate currentDate = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String goodsRightsAmt = "0";

            Double totalPerAmt = saleSchedules.get(0).getTotalAmt(); // 假设每期金额相同
            int repaidTerms = (int) saleSchedules.stream()
                    .filter(s -> "CLOSE".equals(s.getSettleFlag()))
                    .count();

            if (repaidTerms >= 3) {
                goodsRightsAmt = "0";
            } else {
                SaleSchedule first = saleSchedules.stream()
                        .filter(s -> "1".equals(s.getRepayTerm()))
                        .findFirst()
                        .orElse(null);
                SaleSchedule second = saleSchedules.stream()
                        .filter(s -> "2".equals(s.getRepayTerm()))
                        .findFirst()
                        .orElse(null);
                SaleSchedule third = saleSchedules.stream()
                        .filter(s -> "3".equals(s.getRepayTerm()))
                        .findFirst()
                        .orElse(null);
                // 未还任何期，按未还规则
                if (repaidTerms == 0) {
                    if (currentDate.isBefore(LocalDate.parse(first.getRepayOwnbDate(), formatter))) {
                        goodsRightsAmt = "0";
                    } else if (currentDate.isBefore(LocalDate.parse(second.getRepayOwnbDate(), formatter))) {
                        goodsRightsAmt = String.valueOf(totalPerAmt);
                    } else if (currentDate.isBefore(LocalDate.parse(third.getRepayOwnbDate(), formatter))) {
                        goodsRightsAmt = String.valueOf(totalPerAmt * 2);
                    } else {
                        goodsRightsAmt = String.valueOf(totalPerAmt * 3);
                    }
                } else if (repaidTerms == 1) { // 已还1期，检查未还的2、3期
                    boolean isSecondUnsettled = "RUNNING".equals(second.getSettleFlag());
                    // 已还1期，第二期未到期，不收取剩余2期
                    if (isSecondUnsettled && currentDate.isBefore(LocalDate.parse(second.getRepayOwnbDate(), formatter))) {
                        goodsRightsAmt = "0";
                    } else {
                        // 按未还规则处理未还部分
                        if (currentDate.isBefore(LocalDate.parse(third.getRepayOwnbDate(), formatter))) {
                            // 已还1期，未还2期已到期（若），按未还规则在第三期前收2期 - 已还1期（实际未还1期已到期，1期amt）
                            goodsRightsAmt = String.valueOf(totalPerAmt * 2 - totalPerAmt);
                        } else {
                            // 第三期后收3期 - 已还1期（未还2期）
                            goodsRightsAmt = String.valueOf(totalPerAmt * 3 - totalPerAmt);
                        }
                    }
                } else if (repaidTerms == 2) { // 已还2期，检查未还的3期
                    boolean isThirdUnsettled = "RUNNING".equals(third.getSettleFlag());
                    // 已还2期，第三期未到期，不收取剩余1期
                    if (isThirdUnsettled && currentDate.isBefore(LocalDate.parse(third.getRepayOwnbDate(), formatter))) {
                        goodsRightsAmt = "0";
                    } else {
                        // 未还第三期已到期，收取该期
                        goodsRightsAmt = String.valueOf(totalPerAmt);
                    }
                }
            }
            //调用还款试算
            PreRepayApplyDto applyDto = new PreRepayApplyDto();
            applyDto.setMerserno(cn.hutool.core.lang.UUID.randomUUID().toString().replace("-", ""));
            applyDto.setLoanNo(disburseData.getLoanNo());
            applyDto.setPaytotalamt(repayTotalAmt.toString());
            //判断还款类型 提前全部结清
            applyDto.setPrePayType("3");
            applyDto.setTerm(String.valueOf(minTerm));

            CapitalData capitalData = capitalDataMapper.selectById(disburseData.getCapitalId());
            RepaymentApi repaymentApi = zifangFactory.getApi(capitalData.getBeanName(), RepaymentApi.class);
            ResponseResult<PreRepayApplyVo> preRepayApply = repaymentApi.getPreRepayApply(applyDto);
            if (preRepayApply.getData().getResponseCode().equals("0000")) {
                PreRepayApplyVo data = preRepayApply.getData();
                billPreRepayApplyVo.setSaleRepayAmount(String.valueOf(goodsRightsAmt));
                billPreRepayApplyVo.setSaleRepayInterest("0.00");
                BeanUtils.copyProperties(billPreRepayApplyVo , data);
                return ResponseResult.success(billPreRepayApplyVo);
            }
            return ResponseResult.error(ErrorCodeEnum.FAIL, preRepayApply.getData().getResponseMsg());
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        } catch (InvocationTargetException e) {
            throw new RuntimeException(e);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Void> settle(SettleRepayApplyDto dto) {

        DisburseData disburseData = disburseDataMapper.selectById(dto.getId());
        if (disburseData == null) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "未找到支用记录");
        }
        if (disburseData.getCreditStatus() != 500) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "该账单授信状态【非还款中】状态,无法操作 !");
        }

        // 获取本息还款计划
        List<RepaySchedule> repaymentList =  repayScheduleMapper.selectList(new LambdaQueryWrapper<RepaySchedule>().eq(RepaySchedule::getDisburseId, dto.getId())
                .in(RepaySchedule::getSettleFlag, "REPAYING"));
        if (CollectionUtils.isNotEmpty(repaymentList)) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "12期账单存在还款中状态，无法操作 !");
        }

        // 本息划扣
        RepaymentApplyDto repaymentApplyDto = new RepaymentApplyDto();
        repaymentApplyDto.setFundCode(disburseData.getFundCode());

        // 获取本息还款计划
        LambdaQueryWrapper<RepaySchedule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RepaySchedule::getDisburseId, dto.getId())
                .eq(RepaySchedule::getSettleFlag, "RUNNING");
        List<RepaySchedule> repaySchedules = repayScheduleMapper.selectList(queryWrapper);
        BigDecimal repayTotalAmt = new BigDecimal("0.00");
        String sequence = "";
        Integer minTerm = null;

        for (RepaySchedule repaySchedule : repaySchedules) {
            String repayTerm = repaySchedule.getRepayTerm();
            Integer currentTerm = Integer.parseInt(repayTerm);
            if (minTerm == null || currentTerm < minTerm) {
                minTerm = currentTerm;
            }
            sequence = repaySchedule.getRepayApplyNo();
            repayTotalAmt = repayTotalAmt.add(repaySchedule.getTotalAmt());
        }

        repaymentApplyDto.setRepayApplyNo(sequence);
        repaymentApplyDto.setUserId(disburseData.getUserId().toString());
        repaymentApplyDto.setRepayMethod(("0").equals(dto.getRepayMethod()) ? "0" : "1");
        repaymentApplyDto.setRepayType("CLEAN");

        // 获取银行卡信息
        BackVo backVo = userBankCardMapper.queryBackByUserId(disburseData.getUserId());
        if (backVo == null) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "无用户对应银行卡信息");
        }

        repaymentApplyDto.setBankPhoneNo(backVo.getMobile());
        repaymentApplyDto.setAmount(repayTotalAmt.toString());
        repaymentApplyDto.setBankCardNo(backVo.getBankAccount());
        repaymentApplyDto.setAccountCardType("1");
        repaymentApplyDto.setIdNo(backVo.getIdCard());
        repaymentApplyDto.setCustomerName(backVo.getName());
        repaymentApplyDto.setBranchName(backVo.getBankName());
        repaymentApplyDto.setBankAccountType("1");

        List<RepaymentApplyRepayDto> repaymentApplyRepayList = new ArrayList<>();
        List<RepaymentApplyDetailDto> repaymentApplyDetailList = new ArrayList<>();
        RepaymentApplyRepayDto repaymentApplyRepayDto = new RepaymentApplyRepayDto();
        repaymentApplyRepayDto.setLoanNo(disburseData.getLoanNo());
        BigDecimal add = new BigDecimal(dto.getGuarantorAmt())
                .add(new BigDecimal(dto.getPayNormAmt()))
                .add(new BigDecimal(dto.getPayInteAmt()))
                .add(new BigDecimal(dto.getPayEnteAmt()))
                .add(new BigDecimal(dto.getServiceAmt()))
                .add(new BigDecimal(dto.getFee()));
        repaymentApplyRepayDto.setRepayAmt(add.toString());
        repaymentApplyRepayDto.setRepayTerm(String.valueOf(minTerm));
        repaymentApplyRepayDto.setGuarantorFee(dto.getGuarantorAmt());
        repaymentApplyRepayDto.setSplitType("1");
        repaymentApplyRepayDto.setPrinAmt(dto.getPayNormAmt());
        repaymentApplyRepayDto.setIntAmt(dto.getPayInteAmt());
        repaymentApplyRepayDto.setForfeitAmt(dto.getPayEnteAmt());
        repaymentApplyRepayDto.setServiceAmt(dto.getServiceAmt());
        repaymentApplyRepayDto.setBreachFee(dto.getFee());
        repaymentApplyRepayList.add(repaymentApplyRepayDto);
        repaymentApplyDto.setRepayList(repaymentApplyRepayList);

        RepaymentApplyDetailDto repaymentApplyDetailDto = new RepaymentApplyDetailDto();
        repaymentApplyDetailDto.setRepayterm(String.valueOf(minTerm));
        repaymentApplyDetailDto.setPrintAmt(dto.getPayNormAmt());
        repaymentApplyDetailDto.setIntAmt(dto.getPayInteAmt());
        repaymentApplyDetailDto.setForfeitAmt(dto.getPayEnteAmt());
        repaymentApplyDetailDto.setGuarantorFee(dto.getGuarantorAmt());
        repaymentApplyDetailDto.setServiceAmt(dto.getServiceAmt());
        repaymentApplyDetailDto.setBreachFee(dto.getFee());
        repaymentApplyDetailList.add(repaymentApplyDetailDto);

        repaymentApplyRepayDto.setRepayDetailList(repaymentApplyDetailList);

        try {
            CapitalData capitalData = getCapitalData(disburseData);
            RepaymentApi repaymentApi = getRepaymentApi(capitalData);
            ResponseResult<RepaymentApplyVo> repaymentApply = repaymentApi.getRepaymentApply(repaymentApplyDto);
            if (repaymentApply.getData() == null || !repaymentApply.getData().getResponseCode().equals("0000")) {
                for (RepaySchedule repayment : repaySchedules) {
                    RepayScheduleApply repayScheduleApply = new RepayScheduleApply();
                    repayScheduleApply.setRepayScheduleId(repayment.getId());
                    repayScheduleApply.setRepayApplyNo(repayment.getRepayApplyNo());
                    repayScheduleApply.setUserId(disburseData.getUserId());
                    repayScheduleApply.setRepayType(8);
                    repayScheduleApply.setRepayStatus(2);
                    repayScheduleApply.setReason(repaymentApply.getData().getResponseMsg());
                    repayScheduleApply.setResponseTime(DateUtil.date());
                    repayScheduleApplyMapper.insert(repayScheduleApply);
                }
                return ResponseResult.error(ErrorCodeEnum.FAIL, repaymentApply.getData() != null ? repaymentApply.getData().getResponseMsg() : "提请结清还款申请失败");
            } else {
                RepaySchedule repayScheduleUp = new RepaySchedule();
                repayScheduleUp.setSettleFlag(SettleFlagConstant.REPAYING);
                repayScheduleUp.setDisburseId(Long.valueOf(dto.getId()));
                repayScheduleMapper.updateRepayScheduleSettleFlag(repayScheduleUp);
                for (RepaySchedule repayment : repaySchedules) {
                    RepayScheduleApply repayScheduleApply = new RepayScheduleApply();
                    repayScheduleApply.setRepayScheduleId(repayment.getId());
                    repayScheduleApply.setRepayApplyNo(repayment.getRepayApplyNo());
                    repayScheduleApply.setUserId(disburseData.getUserId());
                    repayScheduleApply.setRepayType(8);
                    repayScheduleApply.setRepayStatus(0);
                    repayScheduleApply.setResponseTime(DateUtil.date());
                    repayScheduleApplyMapper.insert(repayScheduleApply);
                }
            }

            // 权益划扣
            sequence = CreditLoanNoUtils.getSequence("sa-rp");
            List<SaleSchedule> saleSchedules = disburseDataMapper.saleScheduleList(disburseData.getId());
            BigDecimal saleRepayTotalAmt = new BigDecimal("0.00");
            for (SaleSchedule saleSchedule : saleSchedules) {
                saleSchedule.setRepayApplyNo(sequence);
                saleScheduleMapper.updateById(saleSchedule);
                String repayTerm = saleSchedule.getRepayTerm();
                Integer currentTerm = Integer.parseInt(repayTerm);
                if (minTerm == null || currentTerm < minTerm) {
                    minTerm = currentTerm;
                }
                saleRepayTotalAmt = saleRepayTotalAmt.add(BigDecimal.valueOf(saleSchedule.getTotalAmt()));
            }

            SaleRepayApplyDto saleRepayApplyDto = new SaleRepayApplyDto();
            saleRepayApplyDto.setRepayApplyNo(sequence);
            saleRepayApplyDto.setUserId(String.valueOf(disburseData.getUserId()));
            saleRepayApplyDto.setRepayMethod(("0").equals(dto.getRepayMethod()) ? "0" : "1");
            saleRepayApplyDto.setRepayType("CLEAN");
            saleRepayApplyDto.setBankPhoneNo(backVo.getMobile());
            saleRepayApplyDto.setAmount(String.valueOf(saleRepayTotalAmt));
            saleRepayApplyDto.setBankCardNo(backVo.getBankAccount());
            saleRepayApplyDto.setAccountCardType("1");
            saleRepayApplyDto.setIdNo(backVo.getIdCard());
            saleRepayApplyDto.setCustomerName(backVo.getName());
            saleRepayApplyDto.setBranchName(backVo.getBankName());
            saleRepayApplyDto.setBankAccountType("1");

            List<SaleRepayApplyRepayDto> repayList = new ArrayList<>();
            SaleRepayApplyRepayDto saleRepayApplyRepayDto = new SaleRepayApplyRepayDto();
            saleRepayApplyRepayDto.setSaleNo(disburseData.getSaleNo());
            saleRepayApplyRepayDto.setRepayAmt(String.valueOf(saleRepayTotalAmt));
            saleRepayApplyRepayDto.setRepayTerm(String.valueOf(minTerm));
            saleRepayApplyRepayDto.setSplitType("1");
            saleRepayApplyRepayDto.setPrinAmt(dto.getSaleRepayAmount());
            saleRepayApplyRepayDto.setIntAmt("0");
            saleRepayApplyRepayDto.setForfeitAmt("0");
            //减免
            if (dto.getSaleRepayAmount().equals("0")) {
                // 减免所有权益费用
                saleRepayApplyDto.setAmount("0");
                saleRepayApplyRepayDto.setRepayAmt("0");
                saleRepayApplyRepayDto.setPrinAmt("0");
                saleRepayApplyRepayDto.setDeratePrin(String.valueOf(saleRepayTotalAmt));
            } else if (new BigDecimal(dto.getSaleRepayAmount()).compareTo(saleRepayTotalAmt) != 0){
                // 减免部分权益费用
                BigDecimal disburseAmount = new BigDecimal(String.valueOf(saleRepayTotalAmt));
                BigDecimal dtoAmount = new BigDecimal(dto.getSaleRepayAmount());
                saleRepayApplyRepayDto.setDeratePrin(String.valueOf(dtoAmount));
                saleRepayApplyRepayDto.setDerateInt("0");

                saleRepayApplyDto.setAmount(disburseAmount.subtract(dtoAmount).toString());
                saleRepayApplyRepayDto.setRepayAmt(disburseAmount.subtract(dtoAmount).toString());
                saleRepayApplyRepayDto.setPrinAmt(disburseAmount.subtract(dtoAmount).toString());
            } else {
                // 不减免
                saleRepayApplyDto.setAmount(String.valueOf(saleRepayTotalAmt));
                saleRepayApplyRepayDto.setRepayAmt(String.valueOf(saleRepayTotalAmt));
                saleRepayApplyRepayDto.setPrinAmt(String.valueOf(saleRepayTotalAmt));
            }


            repayList.add(saleRepayApplyRepayDto);
            List<SaleRepayApplyDetailDto> repayDetailList = new ArrayList<>();

            SaleRepayApplyDetailDto saleRepayApplyDetailDto = new SaleRepayApplyDetailDto();
            saleRepayApplyDetailDto.setRepayterm(String.valueOf(minTerm));
            saleRepayApplyDetailDto.setPrintAmt(String.valueOf(saleRepayTotalAmt));
            saleRepayApplyDetailDto.setIntAmt("0.00");
            saleRepayApplyDetailDto.setForfeitAmt("0.00");
            //减免
            if (dto.getSaleRepayAmount().equals("0")) {
                // 减免所有权益费用
                saleRepayApplyDetailDto.setPrintAmt("0");
                saleRepayApplyDetailDto.setDeratePrin(String.valueOf(disburseData.getSaleRepayAmount()));
            } else if (new BigDecimal(dto.getSaleRepayAmount()).compareTo(saleRepayTotalAmt) != 0){
                // 减免部分权益费用
                BigDecimal disburseAmount = new BigDecimal(String.valueOf(disburseData.getSaleRepayAmount()));
                BigDecimal dtoAmount = new BigDecimal(dto.getSaleRepayAmount());
                saleRepayApplyDetailDto.setDeratePrin(String.valueOf(dtoAmount));
                saleRepayApplyDetailDto.setDerateInt("0");
                saleRepayApplyDetailDto.setPrintAmt(disburseAmount.subtract(dtoAmount).toString());
            } else {
                // 不减免
                saleRepayApplyDto.setAmount(String.valueOf(saleRepayTotalAmt));
                saleRepayApplyRepayDto.setRepayAmt(String.valueOf(saleRepayTotalAmt));
                saleRepayApplyRepayDto.setPrinAmt(String.valueOf(saleRepayTotalAmt));
            }

            repayDetailList.add(saleRepayApplyDetailDto);
            saleRepayApplyDto.setRepayList(repayList);
            saleRepayApplyRepayDto.setRepayDetailList(repayDetailList);

            capitalData = getCapitalData(disburseData);
            OtherApi otherApi = zifangFactory.getApi(capitalData.getBeanName(), OtherApi.class);
            ResponseResult<SaleRepayApplyVo> saleRepayApply = otherApi.getSaleRepayApply(saleRepayApplyDto);
            if (saleRepayApply.getData() == null || !saleRepayApply.getData().getResponseCode().equals("0000")) {
                for (SaleSchedule sale : saleSchedules) {
                    RepaySaleApply repaySaleApply = new RepaySaleApply();
                    repaySaleApply.setSaleScheduleId(sale.getId());
                    repaySaleApply.setRepayApplyNo(sale.getRepayApplyNo());
                    repaySaleApply.setUserId(disburseData.getUserId());
                    repaySaleApply.setRepayType(8);
                    repaySaleApply.setRepayStatus(2);
                    repaySaleApply.setReason(saleRepayApply.getData().getResponseMsg());
                    repaySaleApply.setResponseTime(DateUtil.date());
                    repaySaleApplyMapper.insert(repaySaleApply);
                }
                return ResponseResult.error(ErrorCodeEnum.FAIL, saleRepayApply.getData() != null ? "权益: " + saleRepayApply.getData().getResponseMsg() : "Sale repay apply failed");
            } else {
                SaleSchedule saleSchedule = new SaleSchedule();
                saleSchedule.setSettleFlag(SettleFlagConstant.REPAYING);
                saleSchedule.setDisburseId(Long.valueOf(dto.getId()));
                saleScheduleMapper.updateSaleScheduleSettleFlag(saleSchedule);

                for (SaleSchedule sale : saleSchedules) {
                    RepaySaleApply repaySaleApply = new RepaySaleApply();
                    repaySaleApply.setSaleScheduleId(sale.getId());
                    repaySaleApply.setRepayApplyNo(sale.getRepayApplyNo());
                    repaySaleApply.setUserId(disburseData.getUserId());
                    repaySaleApply.setRepayType(8);
                    repaySaleApply.setRepayStatus(0);
                    repaySaleApply.setResponseTime(DateUtil.date());
                    repaySaleApplyMapper.insert(repaySaleApply);
                }
            }
            return ResponseResult.success();
        } catch (Exception e) {
            log.error("Settle error: ", e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, e.getMessage());
        }
    }

    private CapitalData getCapitalData(DisburseData disburseData) {
        return capitalDataMapper.selectById(disburseData.getCapitalId());
    }

    private RepaymentApi getRepaymentApi(CapitalData capitalData) {
        return zifangFactory.getApi(capitalData.getBeanName(), RepaymentApi.class);
    }

    @Override
    public ResponseResult<RepaymentResultVo> repaymentResult(SettleRepayApplyDto dto) {
        //获取订单
        DisburseData disburseData = disburseDataMapper.selectById(dto.getId());
        RepaymentResultDto repaymentResultDto = new RepaymentResultDto();
        repaymentResultDto.setUserId(disburseData.getUserId().toString());
        //获取本息还款计划
        LambdaQueryWrapper<RepaySchedule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RepaySchedule::getDisburseId, dto.getId())
                .eq(RepaySchedule::getSettleFlag,SettleFlagConstant.REPAYING) ;
        List<RepaySchedule> repaySchedules = repayScheduleMapper.selectList(queryWrapper);
        String repayApplyNo ="";
        for (RepaySchedule repaySchedule : repaySchedules) {
            repayApplyNo = repaySchedule.getRepayApplyNo();
            break;
        }
        repaymentResultDto.setRepayApplyNo(repayApplyNo);
        CapitalData capitalData = capitalDataMapper.selectById(disburseData.getCapitalId());
        RepaymentApi repaymentApi = zifangFactory.getApi(capitalData.getBeanName(), RepaymentApi.class);
        return repaymentApi.getRepaymentResult(repaymentResultDto);

    }
    @Override
    public ResponseResult<SaleRepayResultVo> saleResult(SettleRepayApplyDto dto) {
        //获取订单
        DisburseData disburseData = disburseDataMapper.selectById(dto.getId());
        RepaymentResultDto repaymentResultDto = new RepaymentResultDto();
        repaymentResultDto.setUserId(disburseData.getUserId().toString());
        //获取权益还款计划
        LambdaQueryWrapper<SaleSchedule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SaleSchedule::getDisburseId, dto.getId())
        .eq(SaleSchedule::getSettleFlag,SettleFlagConstant.REPAYING) ;
        List<SaleSchedule> repaySchedules = saleScheduleMapper.selectList(queryWrapper);
        String repayApplyNo ="";
        for (SaleSchedule repaySchedule : repaySchedules) {
            repayApplyNo = repaySchedule.getRepayApplyNo();
            break;
        }
        repaymentResultDto.setRepayApplyNo(repayApplyNo);
        CapitalData capitalData = capitalDataMapper.selectById(disburseData.getCapitalId());
        OtherApi otherApi = zifangFactory.getApi(capitalData.getBeanName(), OtherApi.class);

        SaleRepayResultDto saleRepayResultDto = new SaleRepayResultDto();
        saleRepayResultDto.setSaleNo(disburseData.getSaleNo());
        saleRepayResultDto.setRepayApplyNo(repayApplyNo);
        return otherApi.getSaleRepayResult(saleRepayResultDto);
    }

    @Override
    public ResponseResult<ContractSignedQueryVo> downloadMayi(DisburseOrderDetailDto dto, HttpServletResponse response) {

        DisburseData disburseData = disburseDataMapper.selectById(dto.getDisburseOrderId());
        ContractSignedQueryDto contractSignedQueryDto = new ContractSignedQueryDto();
        contractSignedQueryDto.setUserId(disburseData.getUserId().toString());
        contractSignedQueryDto.setLoanNo(disburseData.getLoanNo());
        contractSignedQueryDto.setScene("02");

        CapitalData capitalData = capitalDataMapper.selectById(disburseData.getCapitalId());
        ContractApi contractApi = zifangFactory.getApi(capitalData.getBeanName(), ContractApi.class);
        return contractApi.contractSignedQuery(contractSignedQueryDto);
    }

    @Override
    public ResponseResult<SaleSignedQueryVo> downloadMayiQY(DisburseOrderDetailDto dto, HttpServletResponse response) {
        DisburseData disburseData = disburseDataMapper.selectById(dto.getDisburseOrderId());
        SaleSignedQueryDto saleSignedQueryDto = new SaleSignedQueryDto();
        saleSignedQueryDto.setSaleNo(disburseData.getSaleNo());
        saleSignedQueryDto.setScene("02");
        CapitalData capitalData = capitalDataMapper.selectById(disburseData.getCapitalId());
        OtherApi otherApi = zifangFactory.getApi(capitalData.getBeanName(), OtherApi.class);
        return otherApi.getSaleSignedQuery(saleSignedQueryDto);
    }

    @Override
    public ResponseResult<OnLoanVo> onLoan() {
        return ResponseResult.success(repayScheduleMapper.selectOnLoan());
    }

    private String readResponse(HttpURLConnection connection) throws IOException {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(connection.getInputStream()))) {
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            return response.toString();
        }
    }

    // 根据类型获取对应的 URL
    private String getAppServiceUrl(Integer type) {
        switch (type) {
            case 1:
                return "http://localhost:8088/inner/repay";
            case 2:
                return "http://localhost:8088/inner/repaySale";
            default:
                return "";
        }
    }

    // 根据类型获取对应的错误信息
    private String getErrorMessage(Integer type) {
        switch (type) {
            case 1:
                return "调用 application-app 服务 线下发起本息罚扣款部分,失败";
            case 2:
                return "调用 application-app 服务 线下发起赊销扣款部分,失败";
            default:
                return "调用 application-app 服务 失败";
        }
    }
}




