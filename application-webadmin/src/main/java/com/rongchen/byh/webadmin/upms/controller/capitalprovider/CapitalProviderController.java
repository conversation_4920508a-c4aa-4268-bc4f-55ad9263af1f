package com.rongchen.byh.webadmin.upms.controller.capitalprovider;

import com.rongchen.byh.common.core.annotation.MyRequestBody;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.log.annotation.OperationLog;
import com.rongchen.byh.common.log.model.constant.SysOperationLogType;
import com.rongchen.byh.webadmin.upms.dto.cp.CPBillDetailDto;
import com.rongchen.byh.webadmin.upms.dto.cp.CPBillListDto;
import com.rongchen.byh.webadmin.upms.dto.cp.DisburseDataRequestListDto;
import com.rongchen.byh.webadmin.upms.model.CapitalProvider;
import com.rongchen.byh.webadmin.upms.service.capitalprovider.CapitalProviderService;
import com.rongchen.byh.webadmin.upms.vo.cp.CPBillListVo;
import com.rongchen.byh.webadmin.upms.vo.cp.CPBillPeriodDetailVo;
import com.rongchen.byh.webadmin.upms.vo.cp.CPDisburseDataResultListVo;
import com.rongchen.byh.webadmin.upms.vo.cp.CapitalProviderVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 供应商表(CapitalProvider)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-17 09:40:17
 */
@Tag(name = "供应商信息相关接口")
@RestController
@RequestMapping("/admin/upms/capitalProvider")
public class CapitalProviderController {

    @Resource
    private CapitalProviderService capitalProviderService;

    /**
     * 分页查询供应商信息
     *
     * @param dto   筛选条件
     * @return 查询结果
     */
    @Operation(summary = "供应商-列表-查询")
    @PostMapping("/cpList")
    public ResponseResult<MyPageData<CapitalProviderVo>> cpList(@MyRequestBody CapitalProvider dto, @MyRequestBody MyPageParam pageParam) {
        return capitalProviderService.capitalProviderList(dto, pageParam);
    }

    /**
     * 新增数据
     *
     * @param dto 实体
     * @return 新增结果
     */
    @Operation(summary = "供应商-列表-新增")
    @PostMapping("/insertCp")
    @OperationLog(type = SysOperationLogType.ADD)
    public ResponseResult<Boolean> insertCp(@MyRequestBody CapitalProvider dto) {
        Boolean insert = capitalProviderService.insert(dto);
        if (insert) {
            return ResponseResult.success(true);
        }
        return ResponseResult.error(ErrorCodeEnum.DUPLICATED_UNIQUE_KEY);
    }

    /**
     * 修改供应商状态
     *
     * @param dto 实体
     * @return 编辑结果
     */
    @Operation(summary = "供应商-供应商状态")
    @PostMapping("/freezeCpStatus")
    @OperationLog(type = SysOperationLogType.UPDATE)
    public ResponseResult<Boolean> freezeCpStatus(@MyRequestBody CapitalProvider dto) {
        Boolean update = capitalProviderService.freezeCpStatus(dto);
        if (update) {
            return ResponseResult.success(true);
        }
        return ResponseResult.error(ErrorCodeEnum.DUPLICATED_UNIQUE_KEY,"数据修改失败，请核对");
    }

    @Operation(summary = "供应商-支用标记")
    @PostMapping("/expenditureMarkers")
    @OperationLog(type = SysOperationLogType.UPDATE)
    public ResponseResult<Void> expenditureMarkers(@MyRequestBody CapitalProvider dto) {
        capitalProviderService.expenditureMarkers(dto);
        return ResponseResult.success();
    }

    @Operation(summary = "供应商-支用订单管理-列表-查询")
    @PostMapping("/disburseList")
    public ResponseResult<MyPageData<CPDisburseDataResultListVo>> disburseOrderList(
            @MyRequestBody DisburseDataRequestListDto dto,
            @MyRequestBody MyPageParam pageParam) {
        return capitalProviderService.disburseOrderList(dto ,pageParam);
    }


    @Operation(summary = "供应商- 还款列表")
    @PostMapping("/list")
    public ResponseResult<MyPageData<CPBillListVo>> list(
            @MyRequestBody CPBillListDto dto,
            @MyRequestBody MyPageParam pageParam) {
        return capitalProviderService.billList(dto, pageParam);
    }


    @Operation(summary = "供应商 - 还款列表- 详情")
    @PostMapping("/detail")
    public ResponseResult<List<CPBillPeriodDetailVo>> list(@MyRequestBody CPBillDetailDto dto) {
        return capitalProviderService.billDetail(dto);
    }

}

