package com.rongchen.byh.webadmin.upms.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;
import java.io.Serializable;

/**
 * 逾期过程明细表(OverdueProcess)实体类
 *
 * <AUTHOR>
 * @since 2025-04-16 18:30:00
 */
@Data
@TableName(value = "overdue_process")
public class OverdueProcess implements Serializable {
    private static final long serialVersionUID = 768701310650533133L;

    private Long id;
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 支用表ID
     */
    private Long disburseId;

    /**
     * 期数
     */
    private String repayTerm;

    /**
     * 应还日期
     */
    private LocalDate repayOwnbDate;

    /**
     * 当日逾期天数
     */
    private Integer overdueDays;

    /**
     * 当日状态（O/L/B）
     */
    private String overdueStatus;

    /**
     * 剩余本金
     */
    private Double totalRetPrin;

    /**
     * 总本金
     */
    private Double totalAmt;

    /**
     * 当日结清标志（RUNNING/CLOSE）
     */
    private String settleFlag;

    /**
     * 1 本息，2 赊销
     */
    private String repaySale;

    private LocalDate createTime;

}

