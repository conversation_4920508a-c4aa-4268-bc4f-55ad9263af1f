package com.rongchen.byh.webadmin.reconciliation.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 对账汇总列表dto
 * @date 2025/5/23 10:03:06
 */
@Data
public class ReconciliationSummeryListDto {
    @Schema(description = "渠道code")
    private String channelCode;

    @Schema(description = "交易类型 LOAN 借款, REPAYMENT 还款, CREDIT 授信")
    private String transactionType;

    @Schema(description = "对账批次id")
    private String reconBatchId;

    @Schema(description = "交易开始日期")
    private String processingBeginDate;

    @Schema(description = "交易结束日期")
    private String processingEndDate;

    @Schema(description = "对账结果状态")
    private String reconStatus;
}
