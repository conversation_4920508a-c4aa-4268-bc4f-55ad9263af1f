package com.rongchen.byh.webadmin.reconciliation.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rongchen.byh.webadmin.reconciliation.context.ReconciliationContext;
import com.rongchen.byh.webadmin.reconciliation.dataProvider.DataProvider;
import com.rongchen.byh.webadmin.reconciliation.dataProvider.DataProviderFactory;
import com.rongchen.byh.webadmin.reconciliation.exception.ReconciliationProcessException;
import com.rongchen.byh.webadmin.reconciliation.mapper.EntityToNormalizedModelConverter;
import com.rongchen.byh.webadmin.reconciliation.model.DiffResult;
import com.rongchen.byh.webadmin.reconciliation.model.NormalizedCreditRecord;
import com.rongchen.byh.webadmin.reconciliation.model.NormalizedLoanRecord;
import com.rongchen.byh.webadmin.reconciliation.model.NormalizedRepaymentRecord;
import com.rongchen.byh.webadmin.reconciliation.model.NormalizedTransaction;
import com.rongchen.byh.webadmin.reconciliation.service.DiffStorageService;
import com.rongchen.byh.webadmin.reconciliation.service.ReconciliationEngineService;
import com.rongchen.byh.webadmin.reconciliation.util.BatchIdGenerator;
import com.rongchen.byh.webadmin.reconciliation.util.ReconciliationStatisticsUtil;
import com.rongchen.byh.webadmin.upms.model.DzBatchExecutionLogEntity;
import com.rongchen.byh.webadmin.upms.model.DzChannelReconConfigEntity;
import com.rongchen.byh.webadmin.upms.model.DzRawPartnerCreditDataEntity;
import com.rongchen.byh.webadmin.upms.model.DzRawPartnerLoanDataEntity;
import com.rongchen.byh.webadmin.upms.model.DzRawPartnerRepaymentDataEntity;
import com.rongchen.byh.webadmin.upms.model.DzReconciliationRulesEntity;
import com.rongchen.byh.webadmin.upms.model.DzReconciliationSummaryEntity;
import com.rongchen.byh.webadmin.upms.service.DzBatchExecutionLogService;
import com.rongchen.byh.webadmin.upms.service.DzChannelReconConfigService;
import com.rongchen.byh.webadmin.upms.service.DzRawPartnerCreditDataService;
import com.rongchen.byh.webadmin.upms.service.DzRawPartnerLoanDataService;
import com.rongchen.byh.webadmin.upms.service.DzRawPartnerRepaymentDataService;
import com.rongchen.byh.webadmin.upms.service.DzReconciliationRulesService;
import com.rongchen.byh.webadmin.upms.service.DzReconciliationSummaryService;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 对账任务调度器。
 * <p>
 * 负责定时触发对账流程。可以使用Spring Scheduler或XXL-Job等。
 * 这里提供一个基于Spring Bean的结构，具体调度触发机制需另外配置。
 */
@Component
public class ReconciliationJob {

    private static final Logger logger = LoggerFactory.getLogger(ReconciliationJob.class);

    private final DataProviderFactory dataProviderFactory;
    private final DzReconciliationRulesService dzReconciliationRulesService;
    private final ReconciliationEngineService reconciliationEngineService;
    private final DiffStorageService diffStorageService;
    private final BatchIdGenerator batchIdGenerator;
    private final DzBatchExecutionLogService dzBatchLogService;
    private final DzChannelReconConfigService dzChannelReconConfigService;
    private final DzReconciliationSummaryService dzReconciliationSummaryService;
    private final EntityToNormalizedModelConverter entityConverter;
    private final DzRawPartnerLoanDataService dzRawPartnerLoanDataService;
    private final DzRawPartnerRepaymentDataService dzRawPartnerRepaymentDataService;
    private final DzRawPartnerCreditDataService dzRawPartnerCreditDataService;

    private Map<String, Class<? extends NormalizedTransaction>> transactionTypeToModelClassMap;

    @Autowired
    public ReconciliationJob(DataProviderFactory dataProviderFactory,
        DzReconciliationRulesService dzReconciliationRulesService,
        ReconciliationEngineService reconciliationEngineService,
        DiffStorageService diffStorageService,
        BatchIdGenerator batchIdGenerator,
        DzBatchExecutionLogService dzBatchLogService,
        DzChannelReconConfigService dzChannelReconConfigService,
        DzReconciliationSummaryService dzReconciliationSummaryService,
        EntityToNormalizedModelConverter entityConverter,
        DzRawPartnerLoanDataService dzRawPartnerLoanDataService,
        DzRawPartnerRepaymentDataService dzRawPartnerRepaymentDataService,
        DzRawPartnerCreditDataService dzRawPartnerCreditDataService) {
        this.dataProviderFactory = dataProviderFactory;
        this.dzReconciliationRulesService = dzReconciliationRulesService;
        this.reconciliationEngineService = reconciliationEngineService;
        this.diffStorageService = diffStorageService;
        this.batchIdGenerator = batchIdGenerator;
        this.dzBatchLogService = dzBatchLogService;
        this.dzChannelReconConfigService = dzChannelReconConfigService;
        this.dzReconciliationSummaryService = dzReconciliationSummaryService;
        this.entityConverter = entityConverter;
        this.dzRawPartnerLoanDataService = dzRawPartnerLoanDataService;
        this.dzRawPartnerRepaymentDataService = dzRawPartnerRepaymentDataService;
        this.dzRawPartnerCreditDataService = dzRawPartnerCreditDataService;
    }

    @PostConstruct
    public void init() {
        transactionTypeToModelClassMap = new HashMap<>();
        transactionTypeToModelClassMap.put("LOAN", NormalizedLoanRecord.class);
        transactionTypeToModelClassMap.put("REPAYMENT", NormalizedRepaymentRecord.class);
        transactionTypeToModelClassMap.put("CREDIT", NormalizedCreditRecord.class);
        logger.info("ReconciliationJob: transactionTypeToModelClassMap initialized: {}",
            transactionTypeToModelClassMap.keySet());
    }

    /**
     * 执行每日对账的核心方法。 (XXL-Job 入口)
     * 由XXL-Job等外部调度器调用。
     */
    @XxlJob("reconciliationJobHandler")
    public void runDailyReconciliation() {
        logger.info("开始执行每日对账任务 (XXL-JOB)...");
        LocalDate processingDate = LocalDate.now().minusDays(1); // 通常对账前一天的数据

        List<DzChannelReconConfigEntity> reconConfigs = dzChannelReconConfigService.list(
            new LambdaQueryWrapper<DzChannelReconConfigEntity>()
                .eq(DzChannelReconConfigEntity::getReconEnabled, true));

        if (CollectionUtils.isEmpty(reconConfigs)) {
            logger.info("日期 {} 没有找到需要执行对账的已启用渠道配置。", processingDate);
            return;
        }
        logger.info("日期 {} 发现 {} 条已启用的对账配置项。", processingDate, reconConfigs.size());
        processDailyReconciliationForConfigs(processingDate, reconConfigs, null);
        logger.info("每日对账任务 (XXL-JOB) 执行完毕，日期: {}", processingDate);
    }

    /**
     * 为指定的配置列表执行每日对账的核心逻辑。
     *
     * @param processingDate       对账日期
     * @param configsToProcess     需要处理的对账配置列表
     * @param parentMonthlyBatchId 父级月度批次ID (可选, 用于关联)
     */
    public void processDailyReconciliationForConfigs(LocalDate processingDate,
        List<DzChannelReconConfigEntity> configsToProcess,
        String parentMonthlyBatchId) {

        if (processingDate == null) {
            logger.error("processDailyReconciliationForConfigs 调用失败: processingDate 不能为空。");
            return;
        }
        if (CollectionUtils.isEmpty(configsToProcess)) {
            logger.info("日期 {} 没有提供需要处理的对账配置，跳过执行。", processingDate);
            return;
        }


        logger.info("开始为日期 {} 处理 {} 条对账配置，父批次ID: {}",
            processingDate, configsToProcess.size(), parentMonthlyBatchId == null ? "N/A" : parentMonthlyBatchId);

        for (DzChannelReconConfigEntity config : configsToProcess) {
            String channelCode = config.getChannelCode();
            String transactionType = config.getTransactionType();
            // 使用传入的后缀生成批次ID
            String dailyBatchId = batchIdGenerator.generateBatchId(processingDate, channelCode,
                transactionType );
            logger.info("为渠道[{}], 类型[{}], 日期[{}] 生成当日对账批次ID: {} (父批次: {})",
                channelCode, transactionType, processingDate, dailyBatchId,
                parentMonthlyBatchId == null ? "N/A" : parentMonthlyBatchId);

            // 传入 parentMonthlyBatchId 到 startBatchLog
            DzBatchExecutionLogEntity batchLog = startBatchLog(dailyBatchId, processingDate, channelCode,
                transactionType , parentMonthlyBatchId);

            List<? extends NormalizedTransaction> ourNormalizedRecords = Collections.emptyList();
            List<? extends NormalizedTransaction> partnerNormalizedRecords = Collections.emptyList();
            BigDecimal ourTotalAmount = BigDecimal.ZERO;
            BigDecimal partnerTotalAmount = BigDecimal.ZERO;
            int ourCount = 0;
            int partnerCount = 0;

            try {
                // 1. 加载对账规则
                DzReconciliationRulesEntity activeRule = dzReconciliationRulesService.findActiveRuleByChannelAndType(
                    channelCode, transactionType);
                if (activeRule == null) {
                    logger.warn("渠道 '{}' 类型 '{}' 未找到有效对账规则，跳过此对账批次: {}", channelCode,
                        transactionType, dailyBatchId);
                    completeBatchLog(batchLog, "SKIPPED_NO_RULE", 0, "未找到有效对账规则");
                    continue;
                }

                // 2. 加载我方标准化数据
                logger.info("批次 {}: 加载我方数据 (MYSYSTEM/{})", dailyBatchId, transactionType);
                DataProvider ourDataProvider = dataProviderFactory.getDataProvider("MYSYSTEM", transactionType,
                    "OUR_SIDE");
                ourNormalizedRecords = ourDataProvider.loadNormalizedData(
                    buildContextForDataLoad(processingDate, "MYSYSTEM", transactionType,
                        dailyBatchId + "_OUR_LOAD"));
                ourCount = ourNormalizedRecords.size();
                ourTotalAmount = calculateTotalAmount(ourNormalizedRecords);
                logger.info("批次 {}: 我方数据加载并转换完成 {} 条，总金额: {}", dailyBatchId, ourCount, ourTotalAmount);

                // 3. 加载并转换资方数据
                logger.info("批次 {}: 加载资方数据 ({}/{}) 从已同步数据库表", dailyBatchId, channelCode,
                    transactionType);
                if ("LOAN".equalsIgnoreCase(transactionType)) {
                    List<DzRawPartnerLoanDataEntity> rawPartnerEntities = dzRawPartnerLoanDataService.list(
                        new LambdaQueryWrapper<DzRawPartnerLoanDataEntity>()
                            .eq(DzRawPartnerLoanDataEntity::getChannelCode, channelCode)
                            .eq(DzRawPartnerLoanDataEntity::getProcessingDate, processingDate)); // 使用
                    // processingDate
                    partnerNormalizedRecords = entityConverter.convertLoanEntities(rawPartnerEntities, channelCode);
                } else if ("REPAYMENT".equalsIgnoreCase(transactionType)) {
                    List<DzRawPartnerRepaymentDataEntity> rawPartnerEntities = dzRawPartnerRepaymentDataService.list(
                        new LambdaQueryWrapper<DzRawPartnerRepaymentDataEntity>()
                            .eq(DzRawPartnerRepaymentDataEntity::getChannelCode, channelCode)
                            .eq(DzRawPartnerRepaymentDataEntity::getProcessingDate, processingDate)); // 使用
                    // processingDate
                    partnerNormalizedRecords = entityConverter.convertRepaymentEntities(rawPartnerEntities,
                        channelCode);
                } else if ("CREDIT".equalsIgnoreCase(transactionType)) {
                    List<DzRawPartnerCreditDataEntity> rawPartnerEntities = dzRawPartnerCreditDataService.list(
                        new LambdaQueryWrapper<DzRawPartnerCreditDataEntity>()
                            .eq(DzRawPartnerCreditDataEntity::getChannelCode, channelCode)
                            .eq(DzRawPartnerCreditDataEntity::getProcessingDate, processingDate)); // 使用
                    // processingDate
                    partnerNormalizedRecords = entityConverter.convertCreditEntities(rawPartnerEntities, channelCode);
                } else {
                    logger.warn("批次 {}: 未知的交易类型 {}，无法加载资方数据", dailyBatchId, transactionType);
                }

                partnerCount = partnerNormalizedRecords.size();
                partnerTotalAmount = calculateTotalAmount(partnerNormalizedRecords);
                logger.info("批次 {}: 资方数据从数据库加载并转换完成 {} 条，总金额: {}", dailyBatchId, partnerCount,
                    partnerTotalAmount);

                // 4. 执行对账引擎
                List<DiffResult> diffResults = reconciliationEngineService.reconcile(
                    ourNormalizedRecords, partnerNormalizedRecords, activeRule, dailyBatchId);

                // 5. 保存差异结果
                diffStorageService.saveDiffs(diffResults, dailyBatchId);

                // 6. 分析对账结果并输出详细统计
                ReconciliationStatisticsUtil.ReconciliationStatistics statistics =
                    ReconciliationStatisticsUtil.analyzeReconciliationResults(diffResults);
                logger.info(statistics.formatOutput(dailyBatchId));

                // 7. 保存对账摘要
                saveReconciliationSummary(dailyBatchId, processingDate, channelCode, transactionType,
                    ourCount, partnerCount, ourTotalAmount, partnerTotalAmount,
                    statistics.getTotalDifferences(), batchLog);

                logger.info("渠道[{}], 类型[{}], 日期[{}] 对账完成。批次ID: {}, 总记录: {}, 差异数: {}",
                    channelCode, transactionType, processingDate, dailyBatchId, statistics.getTotalRecords(),
                    statistics.getTotalDifferences());

            } catch (ReconciliationProcessException rpe) {
                logger.error("渠道[{}], 类型[{}], 日期[{}] 对账处理失败。批次ID: {} : {}",
                    channelCode, transactionType, processingDate,
                    rpe.getBatchId() != null ? rpe.getBatchId() : dailyBatchId,
                    rpe.getMessage(), rpe);
                if (batchLog != null) {
                    completeBatchLog(batchLog, "FAILED", -1, rpe.getMessage());
                }
                saveReconciliationSummary(dailyBatchId, processingDate, channelCode, transactionType, ourCount,
                    partnerCount, ourTotalAmount, partnerTotalAmount, -1, batchLog, "ERROR", rpe.getMessage());
            } catch (Exception e) {
                logger.error("渠道[{}], 类型[{}], 日期[{}] 对账时发生意外错误。批次ID: {} : {}",
                    channelCode, transactionType, processingDate, dailyBatchId, e.getMessage(), e);
                if (batchLog != null) {
                    completeBatchLog(batchLog, "FAILED", -1, "发生意外错误: " + e.getMessage());
                }
                saveReconciliationSummary(dailyBatchId, processingDate, channelCode, transactionType, ourCount,
                    partnerCount,
                    ourTotalAmount, partnerTotalAmount, -1, batchLog, "ERROR", "发生意外错误: " + e.getMessage());
            }
        }
        logger.info("日期 {} 的所有指定对账配置处理完毕。", processingDate);
    }


    private ReconciliationContext buildContextForDataLoad(LocalDate processingDate, String channelCode,
        String transactionType, String batchId) {
        return ReconciliationContext.builder()
            .processingDate(processingDate)
            .channelCode(channelCode)
            .transactionType(transactionType)
            .batchId(batchId)
            .build();
    }

    private BigDecimal calculateTotalAmount(List<? extends NormalizedTransaction> records) {
        if (CollectionUtils.isEmpty(records)) {
            return BigDecimal.ZERO;
        }
        BigDecimal total = BigDecimal.ZERO;
        for (NormalizedTransaction record : records) {
            if (record != null && record.getReconciliationAmount() != null) {
                total = total.add(record.getReconciliationAmount());
            }
        }
        return total;
    }

    private void saveReconciliationSummary(String batchId, LocalDate processingDate, String channelCode,
        String transactionType,
        int ourCount, int partnerCount, BigDecimal ourTotalAmount, BigDecimal partnerTotalAmount,
        int diffCount, DzBatchExecutionLogEntity batchLog) {
        saveReconciliationSummary(batchId, processingDate, channelCode, transactionType, ourCount, partnerCount,
            ourTotalAmount, partnerTotalAmount, diffCount, batchLog, null, null);
    }

    private void saveReconciliationSummary(String batchId, LocalDate processingDate, String channelCode,
        String transactionType, int ourCount, int partnerCount, BigDecimal ourTotalAmount, BigDecimal partnerTotalAmount,
        int diffCount, DzBatchExecutionLogEntity batchLog, String errorStatus, String errorMessage) {
        try {
            DzReconciliationSummaryEntity summary = new DzReconciliationSummaryEntity();
            summary.setReconBatchId(batchId);
            if (processingDate != null) {
                summary.setProcessingDate(java.sql.Date.valueOf(processingDate));
            }
            summary.setChannelCode(channelCode);
            summary.setTransactionType(transactionType);
            summary.setOurRecordsCount(ourCount);
            summary.setPartnerRecordsCount(partnerCount);
            summary.setOurTotalAmount(ourTotalAmount);
            summary.setPartnerTotalAmount(partnerTotalAmount);
            summary.setDiffRecordsCount(diffCount);

            String reconStatus;
            String logStatus;

            if (errorStatus != null) { // 如果是引擎或步骤中途错误
                reconStatus = "ERROR";
                logStatus = "FAILED";
            } else if (diffCount == 0) {
                reconStatus = "BALANCED"; // 已对平
                logStatus = "COMPLETED";
            } else {
                reconStatus = "UNBALANCED"; // 差错账
                logStatus = "COMPLETED"; // Job本身是完成的，但有业务差异
            }
            summary.setReconStatus(reconStatus);
            if (errorMessage != null) {
                summary.setRemarks(errorMessage.substring(0, Math.min(errorMessage.length(), 250)));
            }
            summary.setSummaryTime(java.sql.Timestamp.valueOf(LocalDateTime.now()));

            dzReconciliationSummaryService.save(summary); // 假设Service有save方法
            logger.info("批次ID: {} 的对账摘要已保存，状态: {}", batchId, reconStatus);

            if (batchLog != null) { // 更新总批次日志
                completeBatchLog(batchLog, logStatus, diffCount, errorMessage);
            }

        } catch (Exception e) {
            logger.error("保存对账摘要失败，批次ID: {} : {}", batchId, e.getMessage(), e);
            // 如果摘要保存失败，主批次日志可能已经是FAILED或需要更新为包含此新错误
            if (batchLog != null && !"FAILED".equals(batchLog.getStatus())) {
                completeBatchLog(batchLog, "FAILED", diffCount, "保存对账摘要失败: " + e.getMessage());
            }
        }
    }

    private DzBatchExecutionLogEntity startBatchLog(String batchId, LocalDate processingDate, String channelCode,
        String taskType, String parentMonthlyBatchId) {
        try {
            DzBatchExecutionLogEntity logEntry = new DzBatchExecutionLogEntity();
            logEntry.setBatchId(batchId);
            if (processingDate != null) {
                logEntry.setProcessingDate(java.sql.Date.valueOf(processingDate));
            }
            logEntry.setChannelCode(channelCode);
            logEntry.setTransactionType(taskType);
            logEntry.setStartTime(java.sql.Timestamp.valueOf(LocalDateTime.now()));
            logEntry.setStatus("PROCESSING");
            logEntry.setParentMonthlyBatchId(parentMonthlyBatchId);
            boolean saved = dzBatchLogService.save(logEntry);
            if (!saved) {
                logger.error("保存对账初始批次日志失败, BatchID: {}", batchId);
                return null;
            }
            return logEntry;
        } catch (Exception e) {
            logger.error("记录对账批次开始日志失败, BatchID: {} : {}", batchId, e.getMessage(), e);
            return null;
        }
    }

    private void completeBatchLog(DzBatchExecutionLogEntity batchLog, String status, int diffCount,
        String errorMessage) {
        if (batchLog == null || batchLog.getId() == null) {
            logger.warn("无法更新对账批次日志：传入的batchLog对象为null或没有ID。");
            return;
        }
        try {
            batchLog.setEndTime(java.sql.Timestamp.valueOf(LocalDateTime.now()));
            batchLog.setStatus(status);
            // total_diff_count在summary表中，batch_log中可以不重复记录，或根据需要保留
            if (diffCount >= 0) {
                // batchLog.setTotalDiffCount(diffCount); // 如需保留
            }
            if (errorMessage != null) {
                batchLog.setErrorMessage(errorMessage.substring(0, Math.min(errorMessage.length(), 2000)));
            }
            boolean updated = dzBatchLogService.updateById(batchLog);
            if (!updated) {
                logger.error("更新对账最终批次日志失败, BatchID: {}", batchLog.getBatchId());
            }
        } catch (Exception e) {
            logger.error("记录对账批次结束日志失败, BatchID: {} : {}", batchLog.getBatchId(), e.getMessage(), e);
        }
    }

    @XxlJob("monthlyReconciliationJobHandler")
    public void runMonthlyReconciliation() {
        YearMonth targetMonth = YearMonth.now().minusMonths(1); // 对账上一个月
        logger.info("开始执行月度对账任务 (XXL-JOB) - 目标月份: {}", targetMonth);

        List<DzChannelReconConfigEntity> allEnabledConfigs = dzChannelReconConfigService.list(
            new LambdaQueryWrapper<DzChannelReconConfigEntity>()
                .eq(DzChannelReconConfigEntity::getReconEnabled, true));

        if (CollectionUtils.isEmpty(allEnabledConfigs)) {
            logger.info("目标月份 {} 没有找到需要执行对账的已启用渠道配置。", targetMonth);
            return;
        }
        logger.info("目标月份 {} 发现 {} 条已启用的对账配置项，将为每项配置执行全月对账。", targetMonth,
            allEnabledConfigs.size());

        // 生成一个主月度批次ID，用于关联此月度任务下的所有日批次
        // "ALL" 代表此月度任务涵盖了所有传入的配置（在此场景下是所有启用配置）
        // "AUTO" 表示这是自动月度任务触发的
        String monthlyMasterBatchId = batchIdGenerator.generateMonthlyBatchId(targetMonth, "ALL_ENABLED", "ALL_TYPES",
            "AUTO");
        logger.info("月度对账主批次ID: {} (用于目标月份: {})", monthlyMasterBatchId, targetMonth);

        // TODO: 未来可以引入月度主批次日志表 (DzMonthlyBatchExecutionLogEntity) 在此处记录开始

        int daysInMonth = targetMonth.lengthOfMonth();
        for (int day = 1; day <= daysInMonth; day++) {
            LocalDate currentProcessingDate = targetMonth.atDay(day);
            logger.info("月度对账 (主批次ID: {}): 开始处理日期: {}", monthlyMasterBatchId, currentProcessingDate);

            // 1. 清理当天的历史数据 (针对即将处理的配置)
            // TODO: 实现并调用
            // ReconciliationRetryService.cleanupDataForDateAndConfigs(currentProcessingDate,
            // allEnabledConfigs);
            // 暂时先跳过实际清理调用，因为清理方法尚未创建。在日志中记录此步骤。
            logger.warn(
                "月度对账 (主批次ID: {}): 日期 {} 的历史数据清理步骤待实现并调用 (ReconciliationRetryService.cleanupDataForDateAndConfigs)",
                monthlyMasterBatchId, currentProcessingDate);

            // 2. 执行当天的对账，传入主月度批次ID和特定后缀
            try {
                processDailyReconciliationForConfigs(currentProcessingDate, allEnabledConfigs, monthlyMasterBatchId);
                logger.info("月度对账 (主批次ID: {}): 日期 {} 处理完成。", monthlyMasterBatchId, currentProcessingDate);
            } catch (Exception e) {
                logger.error("月度对账 (主批次ID: {}): 日期 {} 处理失败。错误: {}", monthlyMasterBatchId,
                    currentProcessingDate,
                    e.getMessage(), e);
                // 根据业务需求，决定是否因为一天失败而中止整个月度任务，或继续处理其他天
                // 当前实现为继续处理其他天，错误已在 processDailyReconciliationForConfigs 内部记录到日批次日志
            }
        }

        // TODO: 未来可以引入月度主批次日志表，在此处记录结束和整体状态
        logger.info("月度对账任务 (XXL-JOB) 执行完毕 - 目标月份: {}, 主批次ID: {}", targetMonth, monthlyMasterBatchId);
    }
}