package com.rongchen.byh.webadmin.upms.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rongchen.byh.webadmin.reconciliation.dto.ReReconciliationDto;
import com.rongchen.byh.webadmin.reconciliation.vo.ReconciliationSummaryDetailDifferentVo;
import com.rongchen.byh.webadmin.reconciliation.vo.ReconciliationVo;
import com.rongchen.byh.webadmin.reconciliation.dto.ReconciliationDto;
import com.rongchen.byh.webadmin.upms.model.DzReconciliationDiffResultsEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 项目名称：byh_java
 * 文件名称: DzReconciliationDiffResultsMapper
 * 创建时间: 2025-05-26 12:13
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.dao
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
public interface DzReconciliationDiffResultsMapper extends BaseMapper<DzReconciliationDiffResultsEntity> {

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(DzReconciliationDiffResultsEntity record);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(DzReconciliationDiffResultsEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(DzReconciliationDiffResultsEntity record);

    int updateBatch(@Param("list") List<DzReconciliationDiffResultsEntity> list);

    int updateBatchSelective(@Param("list") List<DzReconciliationDiffResultsEntity> list);

    int batchInsert(@Param("list") List<DzReconciliationDiffResultsEntity> list);

    int batchInsertOrUpdate(@Param("list") List<DzReconciliationDiffResultsEntity> list);

    List<ReconciliationVo> selectLoansList(ReconciliationDto vo);

    List<ReconciliationVo> selectCreditsList(ReconciliationDto vo);

    List<ReconciliationVo> selectRepaysList(ReconciliationDto vo);

    List<ReconciliationSummaryDetailDifferentVo> getSummeryDetailDifferent(@Param("reconBatchId") String reconBatchId,
                                                                           @Param("transactionType") String transactionType);
    ReconciliationVo selectByTypeAndNo(ReReconciliationDto dto);

    ReconciliationVo selectById(Long id);
}