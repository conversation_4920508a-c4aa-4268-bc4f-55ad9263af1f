package com.rongchen.byh.webadmin.reconciliation.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 字段级别差异详情模型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FieldDifferenceDetail {

    /**
     * 字段名 (标准化的字段名)
     */
    private String fieldName;

    /**
     * 我方记录中的值
     */
    private Object ourValue;

    /**
     * 对方/资方记录中的值
     */
    private Object partnerValue;

    /**
     * 差异描述信息，例如："金额不匹配：预期 100.00，实际 100.05"
     */
    private String message;

}