package com.rongchen.byh.webadmin.reconciliation.dataProvider;

import com.rongchen.byh.webadmin.reconciliation.context.ReconciliationContext;
import com.rongchen.byh.webadmin.reconciliation.model.NormalizedTransaction;
import java.util.List;
import java.util.Map;

/**
 * 数据提供者接口
 * <p>
 * 负责从特定数据源（外部资方、我方系统）加载原始对账数据。
 */
public interface DataProvider {

    /**
     * 加载原始数据，通常返回 List<Map<String, Object>>，其中键是原始字段名。
     * 主要用于需要通过 RecordMapperService 进行通用映射的数据源 (例如SFTP文件)。
     */
    List<Map<String, Object>> loadData(ReconciliationContext context) throws Exception;

    /**
     * 直接加载并转换为标准化模型数据。
     * 主要用于我方系统等数据源，其字段到标准化模型的映射可以在代码中直接完成。
     * 
     * @return 返回一个 NormalizedTransaction 或其具体子类型的列表。
     * @throws UnsupportedOperationException 如果此DataProvider不支持直接加载标准化模型。
     */
    default List<? extends NormalizedTransaction> loadNormalizedData(ReconciliationContext context) throws Exception {
        throw new UnsupportedOperationException("此DataProvider不支持直接加载标准化模型数据。");
    }

    /**
     * 获取此数据提供者对应的渠道编码。
     *
     * @return 渠道编码字符串。
     */
    String getChannelCode();

    /**
     * 获取此数据提供者对应的交易类型。
     * 例如 "LOAN", "REPAYMENT"。
     *
     * @return 交易类型字符串。
     */
    String getTransactionType();

    /**
     * 获取此数据提供者是代表我方数据还是资方数据。
     * 这有助于在主流程中区分和记录。
     * 
     * @return 数据来源标识 (例如 "OUR_SIDE", "PARTNER_SIDE")
     */
    String getDataSourceType(); // 新增一个方法用于区分数据源是我方还是合作方

}