package com.rongchen.byh.webadmin.upms.dto.export;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class StatisticalReportDto {

    @Schema(description = "开始时间 yyyy-mm-dd")
    private String timeStart;

    @Schema(description = "结束时间 yyyy-mm-dd")
    private String timeEnd;

    @Schema(description = "开始时间 yyyy-mm")
    private String yearMonthStart;

    @Schema(description = "开始结束 yyyy-mm")
    private String yearMonthEnd;

    @Schema(description = "门店名称")
    private String storeName;

    @Schema(description = "小组名称")
    private String groupName;

    @Schema(description = "渠道")
    private String channelId;

    @Schema(description = "来源模式")
    private String sourceMode;

}
