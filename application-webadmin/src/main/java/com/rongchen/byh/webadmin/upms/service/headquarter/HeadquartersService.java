package com.rongchen.byh.webadmin.upms.service.headquarter;

import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.webadmin.upms.dto.Headquarters.HeadquartersDto;
import com.rongchen.byh.webadmin.upms.vo.headquarters.HeadquartersVo;

public interface HeadquartersService {
    /**
     * 总部信息列表
     * @date 2024/12/6 15:37
     *
     * @param dto
     * @param pageParam
     * @return
     */
    ResponseResult<MyPageData<HeadquartersVo>> headquartersList(HeadquartersDto dto, MyPageParam pageParam);

    Boolean updateHeadquarters(HeadquartersDto dto);

    void insertHeadquarters(HeadquartersDto dto);
}
