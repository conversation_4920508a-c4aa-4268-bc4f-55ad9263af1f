package com.rongchen.byh.webadmin.upms.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**

 * 项目名称：byh_java
 * 文件名称: DzReconciliationRulesEntity
 * 创建时间: 2025-05-21 11:50
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.model
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
/**
 * 对账规则定义表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "`dz_reconciliation_rules`")
public class DzReconciliationRulesEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 规则业务ID
     */
    @TableField(value = "`rule_id`")
    private String ruleId;

    /**
     * 规则版本号
     */
    @TableField(value = "`version`")
    private Integer version;

    /**
     * 适用的渠道编码, '*'表示通用
     */
    @TableField(value = "`channel_code`")
    private String channelCode;

    /**
     * 适用的交易类型 (如 LOAN, REPAYMENT)
     */
    @TableField(value = "`transaction_type`")
    private String transactionType;

    /**
     * 规则类型: JSON_DSL 或 JAVA_CLASS
     */
    @TableField(value = "`rule_type`")
    private String ruleType;

    /**
     * 规则内容JSON
     */
    @TableField(value = "`rule_content_json`")
    private String ruleContentJson;

    /**
     * 状态: ACTIVE, INACTIVE
     */
    @TableField(value = "`status`")
    private String status;

    /**
     * 备注
     */
    @TableField(value = "`description`")
    private String description;

    /**
     * 创建时间
     */
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}