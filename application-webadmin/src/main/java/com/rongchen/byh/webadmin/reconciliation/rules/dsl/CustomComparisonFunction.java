package com.rongchen.byh.webadmin.reconciliation.rules.dsl;

import com.rongchen.byh.webadmin.reconciliation.context.ReconciliationContext;
import java.util.Map;

/**
 * JSON DSL中使用的自定义字段比较函数接口。
 * <p>
 * 当标准的比较类型（如EQUALS, DECIMAL_TOLERANCE）不足以满足特定字段的比较需求时，
 * 可以在JSON DSL规则中引用一个实现了此接口的自定义函数。
 */
@FunctionalInterface
public interface CustomComparisonFunction {

    /**
     * 执行自定义比较逻辑。
     *
     * @param ourValue     我方记录中待比较字段的值。
     * @param partnerValue 资方记录中待比较字段的值。
     * @param params       在JSON DSL规则中为此自定义函数配置的参数。
     *                     例如：{"tolerance": "0.01", "ignoreCase": "true"}
     * @param context      当前的对账上下文信息。
     * @return 如果字段值根据自定义逻辑被认为是匹配的，则返回 true；否则返回 false。
     * @throws Exception 如果比较过程中发生错误。
     */
    boolean compare(Object ourValue,
            Object partnerValue,
            Map<String, Object> params,
            ReconciliationContext context) throws Exception;

    // 默认方法可以提供函数名称等，但对于函数式接口不是必需的
    default String getName() { return this.getClass().getSimpleName(); }
}