package com.rongchen.byh.webadmin.upms.dto.bill;

import lombok.Data;

/**
 * @ClassName PreRepayApplyVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/9 14:44
 * @Version 1.0
 **/
@Data
public class SettleRepayApplyDto {

    /**
     * 支用id
     */
    private String id;

    /**
     * 本次还款本金
     */
    private String payNormAmt;

    /**
     * 本次还款利息
     */
    private String payInteAmt;

    /**
     * 本次还款罚息
     */
    private String payEnteAmt;

    /**
     * 手续费/违约金
     */
    private String fee;

    /**
     * 还款总额
     */
    private String payTotalAmt;

    /**
     * 还款日期
     */
    private String payDate;

    /**
     * 融担费
     */
    private String guarantorAmt;

    /**
     * 服务费
     */
    private String serviceAmt;

    /**
     * 还款方式
     */
    private String repayMethod;

    /**
     * 商品权益本金
     */
    private String saleRepayAmount;

    /**
     * 商品权益利息
     */
    private String saleRepayInterest;
}
