package com.rongchen.byh.webadmin.upms.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rongchen.byh.webadmin.upms.model.DzReconciliationRulesEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**

 * 项目名称：byh_java
 * 文件名称: DzReconciliationRulesMapper
 * 创建时间: 2025-05-21 11:50
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.dao
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
public interface DzReconciliationRulesMapper extends BaseMapper<DzReconciliationRulesEntity> {
    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(DzReconciliationRulesEntity record);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(DzReconciliationRulesEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(DzReconciliationRulesEntity record);

    int updateBatch(@Param("list") List<DzReconciliationRulesEntity> list);

    int updateBatchSelective(@Param("list") List<DzReconciliationRulesEntity> list);

    int batchInsert(@Param("list") List<DzReconciliationRulesEntity> list);

    int batchInsertOrUpdate(@Param("list") List<DzReconciliationRulesEntity> list);
}