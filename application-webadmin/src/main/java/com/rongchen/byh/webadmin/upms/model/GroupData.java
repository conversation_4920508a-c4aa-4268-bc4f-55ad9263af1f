package com.rongchen.byh.webadmin.upms.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 小组表(GroupData)实体类
 *
 * <AUTHOR>
 * @since 2025-02-27 15:47:06
 */
@Data
@TableName(value = "group_data")
public class GroupData implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 小组名称
     */
    private String groupName;

    /**
     *销售管理员
     */
    private String sysUserId;

    /**
     *销售管理员名称
     */
    @TableField(exist = false)
    private String showName;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}

