package com.rongchen.byh.webadmin.reconciliation.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.page.PageMethod;
import com.rongchen.byh.common.core.annotation.MyRequestBody;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.MyPageUtil;
import com.rongchen.byh.webadmin.reconciliation.dto.ReReconciliationDto;
import com.rongchen.byh.webadmin.reconciliation.vo.ReReconciliationVo;
import com.rongchen.byh.webadmin.reconciliation.vo.ReconciliationVo;
import com.rongchen.byh.webadmin.reconciliation.dto.ReconciliationDto;
import com.rongchen.byh.webadmin.upms.model.DzReconciliationDiffResultsEntity;
import com.rongchen.byh.webadmin.upms.service.DzReconciliationDiffResultsService;

import java.time.LocalDate;
import java.util.List;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
// 如果需要分页，可能需要导入Page类，例如MyBatisPlus的IPage
// import com.baomidou.mybatisplus.core.metadata.IPage;
// import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 对账差异结果查询与处理API控制器。
 */
@Tag(name = "对账差异结果相关接口")
@RestController
@RequestMapping("/admin/upms/reconciliation/diffs")
public class DiffResultController {

    private final DzReconciliationDiffResultsService dzDiffResultsService;


    @Autowired
    public DiffResultController(DzReconciliationDiffResultsService dzDiffResultsService) {
        this.dzDiffResultsService = dzDiffResultsService;
    }

    /**
     * 根据查询条件查询授信的差异结果详情表
     */
    @Operation(summary = "根据查询条件查询授信的差异结果详情表")
    @PostMapping("/batch/credit/list")
    public ResponseResult<MyPageData<ReconciliationVo>> getDiffCredits(@MyRequestBody ReconciliationDto vo,
                                                                       @MyRequestBody MyPageParam pageParam

    ) {
        PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        List<ReconciliationVo> list = dzDiffResultsService.selectCreditsList(vo);
        MyPageData<ReconciliationVo> pageData = MyPageUtil.makeResponseData(list);
        return ResponseResult.success(pageData);
    }

    /**
     * 根据查询条件查询借款的差异结果详情表
     */
    @Operation(summary = "根据查询条件查询借款的差异结果详情表")
    @PostMapping("/batch/loan/list")
    public ResponseResult<MyPageData<ReconciliationVo>> getDiffLoans(@MyRequestBody ReconciliationDto vo,
                                                                     @MyRequestBody MyPageParam pageParam

    ) {
        PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        List<ReconciliationVo> list = dzDiffResultsService.selectLoansList(vo);
        MyPageData<ReconciliationVo> pageData = MyPageUtil.makeResponseData(list);
        return ResponseResult.success(pageData);
    }

    /**
     * 根据查询条件查询还款的差异结果详情表
     */
    @Operation(summary = "根据查询条件查询还款的差异结果详情表")
    @PostMapping("/batch/repay/list")
    public ResponseResult<MyPageData<ReconciliationVo>> getDiffRepays(@MyRequestBody ReconciliationDto vo,
                                                                      @MyRequestBody MyPageParam pageParam

    ) {
        PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        List<ReconciliationVo> list = dzDiffResultsService.selectRepaysList(vo);
        MyPageData<ReconciliationVo> pageData = MyPageUtil.makeResponseData(list);
        return ResponseResult.success(pageData);
    }

    @Operation(summary = "重新对账")
    @PostMapping("/batch/reReconciliation")
    public ResponseResult<ReReconciliationVo> reReconciliation(@MyRequestBody ReReconciliationDto dto) {
        return ResponseResult.success(dzDiffResultsService.reReconciliation(dto));
    }


    /**
     * 根据批次ID查询差异结果列表。
     *
     * @param batchId 批次ID
     * @return 差异结果实体列表
     */
    @GetMapping("/batch/{batchId}")
    public ResponseResult<List<DzReconciliationDiffResultsEntity>> getDiffsByBatchId(@PathVariable String batchId) {
        // 假设Service有此查询方法
        return ResponseResult.success(dzDiffResultsService.list(new LambdaQueryWrapper<DzReconciliationDiffResultsEntity>()
                .eq(DzReconciliationDiffResultsEntity::getBatchId, batchId)));
    }

    /**
     * 根据多种条件分页查询差异结果。
     *
     * @param page               当前页码 (从1开始)
     * @param size               每页大小
     * @param batchId            批次ID (可选)
     * @param ruleIdUsed         使用的规则ID (可选)
     * @param differenceType     差异类型 (可选)
     * @param confirmationStatus 确认状态 (可选)
     * @param startDate          创建开始日期 (可选, yyyy-MM-dd)
     * @param endDate            创建结束日期 (可选, yyyy-MM-dd)
     * @return 分页的差异结果 (具体返回类型取决于您是否使用IPage等)
     */
    @GetMapping("")
    public ResponseResult<List<DzReconciliationDiffResultsEntity>> queryDiffs(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String batchId,
            @RequestParam(required = false) String ruleIdUsed,
            @RequestParam(required = false) String differenceType,
            @RequestParam(required = false) String confirmationStatus,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {

        // Page<DzReconciliationDiffResultsEntity> pageRequest = new Page<>(page, size);
        // QueryWrapper<DzReconciliationDiffResultsEntity> queryWrapper = new
        // QueryWrapper<>();
        // queryWrapper.eq(StringUtils.isNotBlank(batchId), "batch_id", batchId)
        // .eq(StringUtils.isNotBlank(ruleIdUsed), "rule_id_used", ruleIdUsed)
        // .eq(StringUtils.isNotBlank(differenceType), "difference_type",
        // differenceType)
        // .eq(StringUtils.isNotBlank(confirmationStatus), "confirmation_status",
        // confirmationStatus)
        // .ge(startDate != null, "created_timestamp", startDate)
        // .le(endDate != null, "created_timestamp", endDate != null ?
        // endDate.plusDays(1) : null) // endDate通常包含当天
        // .orderByDesc("created_timestamp");
        // return dzDiffResultsService.page(pageRequest, queryWrapper);

        // 简化版：返回List，需要您在Service中实现此多条件查询逻辑
        return ResponseResult.success(dzDiffResultsService.selectList(new LambdaQueryWrapper<DzReconciliationDiffResultsEntity>()
                .eq(StringUtils.isNotBlank(batchId), DzReconciliationDiffResultsEntity::getBatchId, batchId)
                .eq(StringUtils.isNotBlank(ruleIdUsed), DzReconciliationDiffResultsEntity::getRuleIdUsed, ruleIdUsed)
                .eq(StringUtils.isNotBlank(differenceType), DzReconciliationDiffResultsEntity::getDifferenceType,
                        differenceType)
                .eq(StringUtils.isNotBlank(confirmationStatus), DzReconciliationDiffResultsEntity::getConfirmationStatus,
                        confirmationStatus)
                .ge(startDate != null, DzReconciliationDiffResultsEntity::getCreatedTimestamp, startDate)
                .le(endDate != null, DzReconciliationDiffResultsEntity::getCreatedTimestamp,
                        endDate != null ? endDate.plusDays(1) : null) // endDate通常包含当天
                .orderByDesc(DzReconciliationDiffResultsEntity::getCreatedTimestamp)));
    }

    /**
     * 更新差异记录的确认状态和备注。
     *
     * @param diffId                    差异记录的ID
     * @param updateConfirmationRequest 包含新状态和备注的请求体
     * @return 更新后的差异记录实体
     */
    @PatchMapping("/{diffId}/confirm")
    public ResponseResult<DzReconciliationDiffResultsEntity> confirmDiff(
            @PathVariable Long diffId,
            @RequestBody DiffConfirmationRequest updateConfirmationRequest) {
        // TODO: 校验diffId是否存在
        DzReconciliationDiffResultsEntity diffEntity = dzDiffResultsService.getById(diffId);
        if (diffEntity == null) {
            // throw new RecordNotFoundException("差异记录未找到: " + diffId);
            return ResponseResult.error(ErrorCodeEnum.FAIL, "差异记录未找到: " + diffId); // 或抛出异常
        }
        if (updateConfirmationRequest.getConfirmationStatus() != null) {
            diffEntity.setConfirmationStatus(updateConfirmationRequest.getConfirmationStatus());
        }
        if (updateConfirmationRequest.getComments() != null) {
            diffEntity.setComments(updateConfirmationRequest.getComments());
        }
        // diffEntity.setConfirmedBy(getCurrentUser()); // 设置当前确认用户
        // diffEntity.setConfirmationTimestamp(LocalDateTime.now()); // 设置确认时间, 注意类型转换
        dzDiffResultsService.updateById(diffEntity);
        return ResponseResult.success(diffEntity);
    }

    @GetMapping("/{diffId}")
    public ResponseResult<DzReconciliationDiffResultsEntity> getDiffById(@PathVariable Long diffId) {
        return ResponseResult.success(dzDiffResultsService.getById(diffId));
    }

    // 用于confirmDiff的请求体
    @Getter
    public static class DiffConfirmationRequest {
        // getters and setters
        private String confirmationStatus;
        private String comments;

        public void setConfirmationStatus(String confirmationStatus) {
            this.confirmationStatus = confirmationStatus;
        }

        public void setComments(String comments) {
            this.comments = comments;
        }
    }


}