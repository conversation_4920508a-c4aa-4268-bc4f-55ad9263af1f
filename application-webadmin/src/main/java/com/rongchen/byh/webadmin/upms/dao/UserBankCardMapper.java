package com.rongchen.byh.webadmin.upms.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rongchen.byh.webadmin.upms.model.UserBankCard;
import com.rongchen.byh.webadmin.upms.vo.bill.BackVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【user_bank_card(用户绑定银行卡)】的数据库操作Mapper
* @createDate 2024-12-11 20:00:35
* @Entity com.rongchen.byh.app.entity.UserBankCard
*/
@Mapper
public interface UserBankCardMapper extends BaseMapper<UserBankCard> {
    List<BackVo> queryBackListByUserId(Long userId);

    BackVo queryBackByUserId(Long userId);

    UserBankCard queryByUserId(Long userId);
}




