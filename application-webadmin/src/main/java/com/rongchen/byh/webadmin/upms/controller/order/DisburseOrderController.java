package com.rongchen.byh.webadmin.upms.controller.order;

import com.rongchen.byh.common.api.zifang.vo.ContractSignedQueryVo;
import com.rongchen.byh.common.api.zifang.vo.SaleSignedQueryVo;
import com.rongchen.byh.common.core.annotation.MyRequestBody;
import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.webadmin.upms.dto.order.DisburseOrderDetailDto;
import com.rongchen.byh.webadmin.upms.dto.order.DisburseOrderListDto;
import com.rongchen.byh.webadmin.upms.service.DisburseDataService;
import com.rongchen.byh.webadmin.upms.vo.order.DisburseOrderDetailVo;
import com.rongchen.byh.webadmin.upms.vo.order.DisburseOrderListVo;
import com.rongchen.byh.webadmin.upms.vo.order.OnLoanVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 支用订单
 * @date 2025/1/23 10:21:32
 */
@Tag(name = "支用订单管理相关接口")
@RestController
@RequestMapping("/admin/upms/disburseOrder")
public class DisburseOrderController {
    @Resource
    private DisburseDataService disburseDataService;
    @Operation(summary = "支用订单管理-列表")
    @PostMapping("/list")
    public ResponseResult<MyPageData<DisburseOrderListVo>> list(
            @MyRequestBody DisburseOrderListDto dto,
            @MyRequestBody MyPageParam pageParam) {
        return disburseDataService.list(dto, pageParam);
    }


    @Operation(summary = "支用订单管理-详情")
    @PostMapping("/detail")
    public ResponseResult<DisburseOrderDetailVo> detail(@MyRequestBody DisburseOrderDetailDto dto) {
        return disburseDataService.detail(dto);
    }

    @Operation(summary = "支用订单管理-爱签合同下载")
    @PostMapping("/download")
    @ResponseStatus(HttpStatus.OK)
    public Void download(@MyRequestBody DisburseOrderDetailDto dto, HttpServletResponse response) {
        return disburseDataService.download(dto,response);
    }

    @Operation(summary = "支用订单管理-本息合同下载")
    @PostMapping("/downloadMayi")
    @ResponseStatus(HttpStatus.OK)
    public ResponseResult<ContractSignedQueryVo> downloadMayi(@MyRequestBody DisburseOrderDetailDto dto, HttpServletResponse response) {
        return disburseDataService.downloadMayi(dto,response);
    }

    @Operation(summary = "支用订单管理-权益合同下载")
    @PostMapping("/downloadMayiQY")
    @ResponseStatus(HttpStatus.OK)
    public ResponseResult<SaleSignedQueryVo> downloadMayiQY(@MyRequestBody DisburseOrderDetailDto dto, HttpServletResponse response) {
        return disburseDataService.downloadMayiQY(dto,response);
    }


    @Operation(summary = "支用订单管理-在贷数据")
    @PostMapping("/onLoan")
    @ResponseStatus(HttpStatus.OK)
    public ResponseResult<OnLoanVo> onLoan() {
        return disburseDataService.onLoan();
    }
}
