package com.rongchen.byh.webadmin.upms.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rongchen.byh.webadmin.upms.model.DzRawPartnerCreditDataEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**

 * 项目名称：byh_java
 * 文件名称: DzRawPartnerCreditDataMapper
 * 创建时间: 2025-05-23 11:51
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.dao
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
public interface DzRawPartnerCreditDataMapper extends BaseMapper<DzRawPartnerCreditDataEntity> {
    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(DzRawPartnerCreditDataEntity record);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(DzRawPartnerCreditDataEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(DzRawPartnerCreditDataEntity record);

    int updateBatch(@Param("list") List<DzRawPartnerCreditDataEntity> list);

    int updateBatchSelective(@Param("list") List<DzRawPartnerCreditDataEntity> list);

    int batchInsert(@Param("list") List<DzRawPartnerCreditDataEntity> list);

    int batchInsertOrUpdate(@Param("list") List<DzRawPartnerCreditDataEntity> list);
}