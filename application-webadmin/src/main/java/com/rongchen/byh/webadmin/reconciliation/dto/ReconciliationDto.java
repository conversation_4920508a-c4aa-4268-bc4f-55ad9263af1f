package com.rongchen.byh.webadmin.reconciliation.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 后台对账列表的查询条件vo
 * @date 2025/5/22 15:17
 */
@Data
public class ReconciliationDto {
    /**
     * 批次ID (可选)
     */
    @Schema(description = "批次ID(可选)")
    private String batchId;

    /**
     * 合作方订单号
     */
    @Schema(description = "合作方订单号(可选)")
    private String outOrderNo;
    /**
     * 我司订单号
     */
    @Schema(description = "我司订单号(可选)")
    private String myOrderNo;

    @Schema(description = "我司授信单号(可选)")
    private String myCreditNo;

    @Schema(description = "合作方授信单号(可选)")
    private String outCreditNo;
    /**
     * 交易渠道 (可选)
     */
    @Schema(description = "交易渠道code(可选)")
    private String channelCode;

    /**
     * 交易类型(必填)
     */
    @Schema(description = "交易类型(必填) LOAN=放款, REPAYMENT=还款, CREDIT=授信")
    private String transactionType;
    /**
     * 对账结果 (可选)
     */
    @Schema(description = "对账结果(可选) 0 已对平，1 未对平")
    private Integer reconciliationResult;
    /**
     * 差异类型 (可选)
     */
    @Schema(description = "差异类型(可选) MISSING_OUR_SIDE=渠道单边，MISSING_PARTNER_SIDE=我司单边 MISMATCHED_FIELDS=金额不等")
    private String differenceType;
    /**
     * 创建开始日期 (可选, yyyy-MM-dd)
     */
    @Schema(description = "创建开始日期(可选) yyyyMMdd HHmmss")
    private LocalDateTime startDate;
    /**
     * 创建结束日期 (可选, yyyy-MM-dd)
     */
    @Schema(description = "创建结束日期(可选) yyyyMMdd HHmmss")
    private LocalDateTime endDate;


}