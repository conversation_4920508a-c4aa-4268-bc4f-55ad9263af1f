package com.rongchen.byh.webadmin.reconciliation.mapper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rongchen.byh.webadmin.reconciliation.model.NormalizedCreditRecord;
import com.rongchen.byh.webadmin.reconciliation.model.NormalizedLoanRecord;
import com.rongchen.byh.webadmin.reconciliation.model.NormalizedRepaymentRecord;
import com.rongchen.byh.webadmin.upms.model.DzRawPartnerCreditDataEntity;
import com.rongchen.byh.webadmin.upms.model.DzRawPartnerLoanDataEntity;
import com.rongchen.byh.webadmin.upms.model.DzRawPartnerRepaymentDataEntity;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.beanutils.BeanUtilsBean;
import org.apache.commons.beanutils.ConvertUtilsBean;
import org.apache.commons.beanutils.Converter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 将从数据库原始数据表查询出的实体 (DzRawPartner...Entity) 转换为标准化的对账模型 (NormalizedTransaction)。
 */
@Component
public class EntityToNormalizedModelConverter {

    private static final Logger logger = LoggerFactory.getLogger(EntityToNormalizedModelConverter.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private final BeanUtilsBean beanUtilsBean;

    public EntityToNormalizedModelConverter() {
        this.beanUtilsBean = new BeanUtilsBean(new ConvertUtilsBean() {
            @Override
            public Converter lookup(Class<?> clazz) {
                if (clazz.equals(LocalDate.class) || clazz.equals(LocalDateTime.class)
                        || clazz.equals(BigDecimal.class)) {
                    return null;
                }
                return super.lookup(clazz);
            }
        });
    }

    public List<NormalizedLoanRecord> convertLoanEntities(List<DzRawPartnerLoanDataEntity> rawEntities,
            String channelCode) {
        if (CollectionUtils.isEmpty(rawEntities))
            return Collections.emptyList();
        List<NormalizedLoanRecord> normalizedList = new ArrayList<>();

        for (DzRawPartnerLoanDataEntity rawEntity : rawEntities) {
            NormalizedLoanRecord norm = new NormalizedLoanRecord();
            try {
                norm.setPartnerLoanOrderNo(rawEntity.getPartnerLoanOrderNo());
                norm.setSubmissionDate(toLocalDate(rawEntity.getSubmissionDate()));
                norm.setFundingDate(toLocalDate(rawEntity.getFundingDate()));
                norm.setMaturityDate(toLocalDate(rawEntity.getMaturityDate()));
                norm.setPrincipalAmount(rawEntity.getPrincipalAmount());
                norm.setTermCount(rawEntity.getTermCount());
                norm.setInterestRateStr(rawEntity.getInterestRateStr());
                norm.setRepaymentTypeCode(rawEntity.getRepaymentTypeCode());
                norm.setFundingProviderCode(rawEntity.getFundingProviderCode());

                norm.setSourceChannel(channelCode);
                norm.setOriginalRecordId(rawEntity.getId() != null ? String.valueOf(rawEntity.getId()) : null);

                Map<String, Object> additionalDataMap = new HashMap<>();
                if (StringUtils.isNotBlank(rawEntity.getExtendedFieldsJson())) {
                    try {
                        additionalDataMap.putAll(objectMapper.readValue(rawEntity.getExtendedFieldsJson(),
                                new TypeReference<Map<String, Object>>() {
                                }));
                    } catch (JsonProcessingException e) {
                        logger.warn("批次ID {} 渠道 {}：解析贷款记录 (ID:{}) 的extended_fields_json失败: {}",
                                rawEntity.getSyncBatchId(), channelCode, rawEntity.getId(), e.getMessage());
                    }
                }
                norm.setAdditionalData(additionalDataMap);

                normalizedList.add(norm);
            } catch (Exception e) {
                logger.error("转换DzRawPartnerLoanDataEntity (ID: {}) 到 NormalizedLoanRecord 失败: {}",
                        rawEntity.getId(), e.getMessage(), e);
            }
        }
        return normalizedList;
    }

    public List<NormalizedRepaymentRecord> convertRepaymentEntities(List<DzRawPartnerRepaymentDataEntity> rawEntities,
            String channelCode) {
        if (CollectionUtils.isEmpty(rawEntities))
            return Collections.emptyList();
        List<NormalizedRepaymentRecord> normalizedList = new ArrayList<>();
        for (DzRawPartnerRepaymentDataEntity rawEntity : rawEntities) {
            NormalizedRepaymentRecord norm = new NormalizedRepaymentRecord();
            try {
                norm.setPartnerLoanOrderNo(rawEntity.getPartnerLoanOrderNo());
                norm.setPartnerRepaymentId(rawEntity.getPartnerRepaymentId());
                norm.setSubmissionDatetime(toLocalDateTime(rawEntity.getSubmissionDatetime()));
                norm.setRepaymentEffectiveDate(toLocalDateTime(rawEntity.getRepaymentEffectiveDate()));
                norm.setInstallmentNumbersStr(rawEntity.getInstallmentNumbersStr());
                norm.setRepaymentMethodCode(rawEntity.getRepaymentMethodCode());
                norm.setPaymentChannelCode(rawEntity.getPaymentChannelCode());
                norm.setDebtTransferStatusCode(rawEntity.getDebtTransferStatusCode());
                norm.setRepaymentTypeCode(rawEntity.getRepaymentTypeCode());
                norm.setTotalRepaidAmount(rawEntity.getTotalRepaidAmount());
                norm.setRepaidPrincipalAmount(rawEntity.getRepaidPrincipalAmount());
                norm.setRepaidInterestAmount(rawEntity.getRepaidInterestAmount());
                norm.setRepaidFeeAmount(rawEntity.getRepaidFeeAmount());
                norm.setRepaidOverdueAmount(rawEntity.getRepaidOverdueAmount());

                norm.setSourceChannel(channelCode);
                norm.setOriginalRecordId(rawEntity.getId() != null ? String.valueOf(rawEntity.getId()) : null);
                Map<String, Object> additionalDataMap = new HashMap<>();
                if (StringUtils.isNotBlank(rawEntity.getExtendedFieldsJson())) {
                    try {
                        additionalDataMap.putAll(objectMapper.readValue(rawEntity.getExtendedFieldsJson(),
                                new TypeReference<Map<String, Object>>() {
                                }));
                    } catch (JsonProcessingException e) {
                        logger.warn("批次ID {} 渠道 {}：解析还款记录 (ID:{}) 的extended_fields_json失败: {}",
                                rawEntity.getSyncBatchId(), channelCode, rawEntity.getId(), e.getMessage());
                    }
                }
                norm.setAdditionalData(additionalDataMap);
                normalizedList.add(norm);
            } catch (Exception e) {
                logger.error("转换DzRawPartnerRepaymentDataEntity (ID: {}) 到 NormalizedRepaymentRecord 失败: {}",
                        rawEntity.getId(), e.getMessage(), e);
            }
        }
        return normalizedList;
    }

    public List<NormalizedCreditRecord> convertCreditEntities(List<DzRawPartnerCreditDataEntity> rawEntities,
            String channelCode) {
        if (CollectionUtils.isEmpty(rawEntities))
            return Collections.emptyList();
        List<NormalizedCreditRecord> normalizedList = new ArrayList<>();
        for (DzRawPartnerCreditDataEntity rawEntity : rawEntities) {
            NormalizedCreditRecord norm = new NormalizedCreditRecord();
            try {
                norm.setPartnerCreditNo(rawEntity.getPartnerCreditNo());
                norm.setCreditStatus(rawEntity.getCreditStatus());
                norm.setCreditDate(toLocalDate(rawEntity.getCreditDate()));
                norm.setCreditAmount(rawEntity.getCreditAmount());

                norm.setSourceChannel(channelCode);
                norm.setOriginalRecordId(rawEntity.getId() != null ? String.valueOf(rawEntity.getId()) : null);
                Map<String, Object> additionalDataMap = new HashMap<>();
                if (StringUtils.isNotBlank(rawEntity.getExtendedFieldsJson())) {
                    try {
                        additionalDataMap.putAll(objectMapper.readValue(rawEntity.getExtendedFieldsJson(),
                                new TypeReference<Map<String, Object>>() {
                                }));
                    } catch (JsonProcessingException e) {
                        logger.warn("批次ID {} 渠道 {}：解析授信记录 (ID:{}) 的extended_fields_json失败: {}",
                                rawEntity.getSyncBatchId(), channelCode, rawEntity.getId(), e.getMessage());
                    }
                }
                norm.setAdditionalData(additionalDataMap);
                normalizedList.add(norm);
            } catch (Exception e) {
                logger.error("转换DzRawPartnerCreditDataEntity (ID: {}) 到 NormalizedCreditRecord 失败: {}",
                        rawEntity.getId(), e.getMessage(), e);
            }
        }
        return normalizedList;
    }

    private LocalDate toLocalDate(Date date) {
        return date == null ? null : date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    private LocalDateTime toLocalDateTime(Date date) {
        return date == null ? null : date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }
}