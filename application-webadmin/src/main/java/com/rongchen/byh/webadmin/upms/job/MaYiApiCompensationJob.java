package com.rongchen.byh.webadmin.upms.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.common.core.util.MDCUtil;
import com.rongchen.byh.webadmin.upms.dao.RepayScheduleMapper;
import com.rongchen.byh.webadmin.upms.dto.CompensationDto;
import com.rongchen.byh.webadmin.upms.model.RepaySchedule;
import com.rongchen.byh.webadmin.upms.model.RepayScheduleApply;
import com.rongchen.byh.webadmin.upms.service.RepayScheduleApplyService;
import com.rongchen.byh.webadmin.upms.service.RepayScheduleService;
import com.rongchen.byh.webadmin.upms.vo.RepayScheduleCompensationVo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 蚂蚁全流程api代偿标识处理
 * @date 2025/5/13 17:47:16
 */
@Component
@Slf4j
public class MaYiApiCompensationJob {
    @Resource
    RepayScheduleMapper repayScheduleMapper;
    @Resource
    RepayScheduleService repayScheduleService;
    @Resource
    RepayScheduleApplyService repayScheduleApplyService;
    // 定义字段名称
    private static final String[] FIELD_NAMES = {
            "transSeqno", "repayType", "repayAmt", "repayPrincipal", "repayInterest",
            "repayFee", "repayOverdueInterest", "repayDate", "repayTerm"
    };

    private static final String FILENAME = "/home/<USER>/files/trans/{}/needRepayComp_{}.txt";

    @XxlJob("maYiApiCompensationJobHandler")
    public void maYiApiCompensationJobHandler() {
        MDCUtil.setTraceId(IdUtil.fastSimpleUUID());
        log.info("=====开始执行蚂蚁全流程api代偿标识处理=====");
        String param = XxlJobHelper.getJobParam();
        String date = DateUtil.format(DateUtil.yesterday(), DatePattern.PURE_DATE_PATTERN);
        if (StrUtil.isNotEmpty(param)) {
            date = DateUtil.format(DateUtil.parseDate(param), DatePattern.PURE_DATE_PATTERN);
        }
        String fileName = StrUtil.format(FILENAME, date, date);
        List<String> strList = readFileLines(fileName);
        if (CollUtil.isEmpty(strList)) {
            log.info("没有需要处理的文件");
            return;
        }
        List<CompensationDto> dataList = parseData(strList);
        List<RepayScheduleCompensationVo> dealList = repayScheduleMapper.selectByOrderNoAndTerm(dataList);
        List<RepayScheduleApply> repayScheduleApplyList = new ArrayList<>();
        if (CollUtil.isEmpty(dealList)) {
            log.info("没有需要处理的数据");
            return;
        }
        Map<String, RepayScheduleCompensationVo> loanCompensationVoMap = dealList.stream().collect(Collectors.toMap(item -> item.getLoanNo() + StrUtil.COLON + item.getRepayTerm(), item -> item));
        Map<String, RepayScheduleCompensationVo> contractCompensationVoMap = dealList.stream().collect(Collectors.toMap(item -> item.getContractId() + StrUtil.COLON + item.getRepayTerm(), item -> item));
        List<RepaySchedule> upList = new ArrayList<>();
        dataList.forEach(compensationDto -> {
            RepayScheduleCompensationVo repayScheduleCompensationVo;
            String key = compensationDto.getTransSeqno() + StrUtil.COLON + compensationDto.getRepayTerm();
            RepayScheduleCompensationVo loanCompensationVo = loanCompensationVoMap.get(key);
            if (ObjectUtil.isEmpty(loanCompensationVo)) {
                repayScheduleCompensationVo = contractCompensationVoMap.get(key);
            } else {
                repayScheduleCompensationVo = loanCompensationVo;
            }
            if (ObjectUtil.isEmpty(repayScheduleCompensationVo)) {
                log.info("没有找到对应的数据，借款单号：{}，期数：{}", compensationDto.getTransSeqno(), compensationDto.getRepayTerm());
            } else {
                RepaySchedule repaySchedule = new RepaySchedule();
                repaySchedule.setId(repayScheduleCompensationVo.getId());
                repaySchedule.setCompensationStatus(Integer.valueOf(compensationDto.getRepayType()));
                upList.add(repaySchedule);
                RepayScheduleApply repayScheduleApply = new RepayScheduleApply();
                repayScheduleApply.setRepayApplyNo(IdUtil.fastSimpleUUID());
                repayScheduleApply.setUserId(repayScheduleCompensationVo.getUserId());
                repayScheduleApply.setRepayScheduleId(repayScheduleCompensationVo.getId());
                repayScheduleApply.setRepayStatus(1);
                repayScheduleApply.setPayMethod(1);
                repayScheduleApply.setCreateTime(new Date());
                repayScheduleApply.setUpdateTime(new Date());
                repayScheduleApply.setResponseTime(DateUtil.parse(compensationDto.getRepayDate() + " 00:00:00"));
                repayScheduleApplyList.add(repayScheduleApply);
            }
        });
        if (CollUtil.isEmpty(upList)) {
            log.info("没有需要更新的数据");
            return;
        }
        // 批量更新还款计划
        repayScheduleService.updateBatchById(upList);
        // 批量插入还款申请记录
        repayScheduleApplyService.saveBatch(repayScheduleApplyList);
        log.info("=====结束执行蚂蚁全流程api代偿标识处理=====");
    }


    /**
     * 读取文件行内容
     */
    private static List<String> readFileLines(String filePath) {
        try {
            try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
                List<String> lines = new ArrayList<>();
                String line;
                while ((line = reader.readLine()) != null) {
                    lines.add(line);
                }
                return lines;
            }
        } catch (IOException e) {
            log.error("读取文件失败", e);
        }
        return new ArrayList<>();
    }

    /**
     * 解析数据行
     */
    private static List<CompensationDto> parseData(List<String> lines) {
        List<CompensationDto> result = new ArrayList<>();
        for (String line : lines) {
            // 使用竖线(|)分割数据
            String[] values = line.split("\\|");

            // 确保字段数量匹配
            if (values.length != FIELD_NAMES.length) {
                log.error("警告: 数据行格式不匹配 - :{}", line);
                continue;
            }

            // 创建数据映射
            JSONObject data = new JSONObject();
            for (int i = 0; i < values.length; i++) {
                data.put(FIELD_NAMES[i], values[i]);
            }
            CompensationDto compensationDto = data.toJavaObject(CompensationDto.class);
            result.add(compensationDto);
        }
        return result;
    }
}
