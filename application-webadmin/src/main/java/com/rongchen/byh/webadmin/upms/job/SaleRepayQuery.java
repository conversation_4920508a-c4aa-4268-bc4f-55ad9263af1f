package com.rongchen.byh.webadmin.upms.job;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rongchen.byh.webadmin.config.ZifangFactory;
import com.rongchen.byh.webadmin.upms.dao.CapitalDataMapper;
import com.rongchen.byh.webadmin.upms.dao.DisburseDataMapper;
import com.rongchen.byh.webadmin.upms.dao.RepaySaleApplyMapper;
import com.rongchen.byh.webadmin.upms.dao.SaleScheduleMapper;
import com.rongchen.byh.webadmin.upms.model.*;
import com.rongchen.byh.common.api.zifang.dto.SaleRepayResultDto;
import com.rongchen.byh.common.api.zifang.service.OtherApi;
import com.rongchen.byh.common.api.zifang.vo.SaleRepayResultVo;
import com.rongchen.byh.common.core.constant.SettleFlagConstant;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


/**
 * 赊销账单提前结清结果查询
 */
@Component
@Slf4j
public class SaleRepayQuery {


    @Resource
    SaleScheduleMapper saleScheduleMapper;
    @Resource
    CapitalDataMapper capitalDataMapper;
    @Resource
    ZifangFactory zifangFactory;
    @Resource
    DisburseDataMapper disburseDataMapper;
    @Resource
    private RepaySaleApplyMapper repaySaleApplyMapper;

    @XxlJob("saleRepayQueryHandler")
    public void saleRepayQueryHandler() {
        LambdaQueryWrapper<RepaySaleApply> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RepaySaleApply::getRepayType, "8")
                .eq(RepaySaleApply::getRepayStatus, 0);
        List<RepaySaleApply> repayScheduleApplies = repaySaleApplyMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(repayScheduleApplies)) {
            log.info("权益赊销提前结清还款中查询 没有提前结清还款申请记录");
            return;
        }
        List<String> list = new ArrayList<>();
        for (RepaySaleApply repaySaleApply : repayScheduleApplies) {
            list.add(repaySaleApply.getRepayApplyNo());
        }
        LambdaQueryWrapper<SaleSchedule> queryWrapperWrapper =  new LambdaQueryWrapper<>();
        queryWrapperWrapper.in(SaleSchedule::getRepayApplyNo, list)
                .eq(SaleSchedule::getSettleFlag, SettleFlagConstant.REPAYING);
        List<SaleSchedule> saleSchedules = saleScheduleMapper.selectList(queryWrapperWrapper);
        if (CollectionUtil.isEmpty(saleSchedules)) {
            log.info("赊销赊销提前结清还款中查询 没有还款中订单");
            return;
        }
        saleSchedules.forEach(this::queryProcess);
    }

    private void queryProcess(SaleSchedule salePayVo) {
        try {
            Long disburseId = salePayVo.getDisburseId();
            DisburseData disburseData = disburseDataMapper.selectById(disburseId);
            CapitalData capitalData = capitalDataMapper.selectById(disburseData.getCapitalId());
            if (capitalData == null) {
                log.error("赊销还款中查询 - 未找到资方信息: salePayVo={}", salePayVo);
                return;
            }
            OtherApi otherApi = zifangFactory.getApi(capitalData.getBeanName(),OtherApi.class);
            SaleRepayResultDto applyDto = new SaleRepayResultDto();
            applyDto.setSaleNo(disburseData.getSaleNo());
            applyDto.setRepayApplyNo(salePayVo.getRepayApplyNo());
            ResponseResult<SaleRepayResultVo> saleRepayResult = otherApi.getSaleRepayResult(applyDto);
            if (saleRepayResult.isSuccess()) {

                SaleRepayResultVo data = saleRepayResult.getData();
                int repayStatus = 0;
                String reason = "";
                if ("0000".equals(data.getResponseCode())) {
                    SaleSchedule saleSchedule = new SaleSchedule();
                    saleSchedule.setId(salePayVo.getId());
                    if ("SUCCESS".equals(data.getStatus())) {
                        //成功
                        saleSchedule.setSettleFlag(SettleFlagConstant.CLOSE);
                        saleSchedule.setDatePay(DateUtil.today());
                        saleSchedule.setDatePayTime(data.getRepayTime());
                        repayStatus = 1;
                    } else if ("FAIL".equals(data.getStatus())) {
                        // 失败
                        saleSchedule.setSettleFlag(SettleFlagConstant.RUNNING);
                        repayStatus = 2;
                        reason = data.getResult();
                    } else if ("CLOSE".equals(data.getStatus())) {
                        // 关闭
                        saleSchedule.setSettleFlag(SettleFlagConstant.CLOSE);
                        saleSchedule.setDatePay(DateUtil.today());
                        saleSchedule.setDatePayTime(data.getRepayTime());
                        repayStatus = 1;
                    } else {
                        log.info("赊销还款中查询 - 订单号: {}, 状态: {}", salePayVo.getRepayApplyNo(), data.getStatus());
                        return;
                    }
                    saleScheduleMapper.updateById(saleSchedule);
                    List<RepaySaleApply> repaySaleApplies = repaySaleApplyMapper.selectByRepayApplyNo(salePayVo.getRepayApplyNo());
                    for (RepaySaleApply repaySaleApply : repaySaleApplies) {
                        if (repaySaleApply != null) {
                            repaySaleApply.setRepayStatus(repayStatus);
                            repaySaleApply.setReason(reason);
                            repaySaleApply.setResponseTime(DateUtil.date());
                            repaySaleApplyMapper.updateById(repaySaleApply);
                        } else {
                            log.info("赊销还款中查询 - 订单号: {}, 资方查询响应失败：{}", salePayVo.getRepayApplyNo(), data);
                            return;
                        }
                    }
                    if ("SUCCESS".equals(data.getStatus())) {
                        log.info("权益还款中查询 - 订单号: {}, 资方查询成功：{}", salePayVo.getRepayApplyNo(), data);
                        disburseDataMapper.updateCreditStatus(disburseId);
                    }

                }
                log.info("赊销还款中查询 - 订单号: {}, 资方查询失败：{}", salePayVo.getRepayApplyNo(), saleRepayResult);
                return;
            }
        } catch (Exception e) {
            log.info("赊销还款中查询 - 订单号: {}, 异常: {}", salePayVo.getRepayApplyNo(),e);
            return;
        }
    }
}
