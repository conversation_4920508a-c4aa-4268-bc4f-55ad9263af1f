package com.rongchen.byh.webadmin.reconciliation.util.sftp;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import com.rongchen.byh.webadmin.reconciliation.dataProvider.base.AbstractSftpCsvDataProvider.SftpConfig;
import java.util.Properties;
import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * SftpClientWrapper对象的池化工厂。
 * 负责创建、激活、验证和销毁SftpClientWrapper实例。
 */
public class SftpClientFactory extends BasePooledObjectFactory<SftpClientWrapper> {

    private static final Logger logger = LoggerFactory.getLogger(SftpClientFactory.class);

    private final SftpConfig sftpConfig;

    public SftpClientFactory(SftpConfig sftpConfig) {
        this.sftpConfig = sftpConfig;
    }

    @Override
    public SftpClientWrapper create() throws Exception {
        logger.debug("连接池工厂: 创建新的SFTP连接，目标: {}@{}:{}",
            sftpConfig.username(), sftpConfig.host(), sftpConfig.port());
        JSch jsch = new JSch();
        Session session = null;
        ChannelSftp channelSftp = null;
        try {
            session = jsch.getSession(sftpConfig.username(), sftpConfig.host(), sftpConfig.port());
            session.setPassword(sftpConfig.password());

            Properties configProps = new Properties();
            configProps.put("StrictHostKeyChecking", "no"); // 生产环境应使用known_hosts或更严格的检查
            // configProps.put("PreferredAuthentications", "publickey,password"); //
            // 可以指定认证方式
            // TODO: 根据需要添加更多JSch会话配置，如超时时间等
            // session.setConfig("ConnectTimeout", "10000"); // 连接超时10秒
            session.setConfig(configProps);
            session.connect(30000); // 连接超时30秒

            channelSftp = (ChannelSftp) session.openChannel("sftp");
            channelSftp.connect(20000); // 通道连接超时20秒

            logger.info("成功创建并连接SFTP: {}@{}:{}",
                sftpConfig.username(), sftpConfig.host(), sftpConfig.port());
            return new SftpClientWrapper(session, channelSftp);
        } catch (Exception e) {
            logger.error("创建SFTP连接失败: {}@{}:{} - {}",
                sftpConfig.username(), sftpConfig.host(), sftpConfig.port(), e.getMessage(), e);
            // 创建失败时，确保部分打开的资源被关闭
            if (channelSftp != null && channelSftp.isConnected()) {
                channelSftp.disconnect();
            }
            if (session != null && session.isConnected()) {
                session.disconnect();
            }
            throw e; // 将异常抛出，以便连接池知道创建失败
        }
    }

    @Override
    public PooledObject<SftpClientWrapper> wrap(SftpClientWrapper sftpClientWrapper) {
        return new DefaultPooledObject<>(sftpClientWrapper);
    }

    @Override
    public void destroyObject(PooledObject<SftpClientWrapper> p) throws Exception {
        logger.debug("连接池工厂: 销毁SFTP连接对象，目标: {}@{}", sftpConfig.username(), sftpConfig.host());
        SftpClientWrapper clientWrapper = p.getObject();
        if (clientWrapper != null) {
            clientWrapper.disconnect();
        }
        super.destroyObject(p);
    }

    @Override
    public boolean validateObject(PooledObject<SftpClientWrapper> p) {
        SftpClientWrapper clientWrapper = p.getObject();
        if (clientWrapper == null || !clientWrapper.isValid()) {
            logger.warn("连接池工厂: SFTP连接验证失败 (null或无效)，目标: {}@{}",
                sftpConfig.username(), sftpConfig.host());
            return false;
        }
        // 可选：发送一个简单的测试命令，如 channelSftp.pwd() 来进一步验证连接
        try {
            clientWrapper.getChannelSftp().pwd();
            return true;
        } catch (Exception e) {
            logger.warn("SFTP连接验证失败 (pwd命令执行异常)，目标: {}@{}: {}",
                sftpConfig.username(), sftpConfig.host(), e.getMessage());
            return false;
        }

    }

    // activateObject 和 passivateObject 可以根据需要覆盖，例如在借用/归还对象时进行一些清理或设置
}