package com.rongchen.byh.webadmin.reconciliation.util.sftp;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.SftpException;
import com.rongchen.byh.webadmin.reconciliation.dataProvider.base.AbstractSftpCsvDataProvider.SftpConfig;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * SFTP操作工具类，包含连接池管理。
 */
public class SFTPUtils {
    private static final Logger logger = LoggerFactory.getLogger(SFTPUtils.class);

    // 每个SFTP服务器配置对应一个连接池
    private static final Map<String, GenericObjectPool<SftpClientWrapper>> poolMap = new ConcurrentHashMap<>();

    private static GenericObjectPool<SftpClientWrapper> getPool(SftpConfig sftpConfig) {
        String poolKey = sftpConfig.host() + ":" + sftpConfig.port() + "@" + sftpConfig.username();
        return poolMap.computeIfAbsent(poolKey, key -> {
            logger.info("为 {} 初始化SFTP连接池...", key);
            GenericObjectPoolConfig<SftpClientWrapper> poolConfig = new GenericObjectPoolConfig<>();
            // --- 连接池配置 ---
            poolConfig.setMaxTotal(10); // 池中最大连接数
            poolConfig.setMaxIdle(5); // 池中最大空闲连接数
            poolConfig.setMinIdle(2); // 池中最小空闲连接数
            poolConfig.setTestOnBorrow(true); // 从池中获取连接时进行验证
            poolConfig.setTestOnReturn(false); // 归还连接到池中时不进行验证
            poolConfig.setTestWhileIdle(true); // 后台空闲对象检测线程运行时是否验证
            poolConfig.setTimeBetweenEvictionRunsMillis(60000); // 空闲对象检测线程运行周期 (1分钟)
            poolConfig.setMinEvictableIdleTimeMillis(300000); // 连接在池中保持空闲而不被回收的最小时间 (5分钟)
            poolConfig.setBlockWhenExhausted(true); // 当池耗尽时，borrowObject方法阻塞
            poolConfig.setMaxWaitMillis(5000); // 当池耗尽时，borrowObject方法最大阻塞时间 (5秒)
            // --- 连接池配置结束 ---
            return new GenericObjectPool<>(new SftpClientFactory(sftpConfig), poolConfig);
        });
    }

    /**
     * 从连接池获取一个SFTP客户端。
     * 使用完毕后必须调用 {@link #returnClient(SftpConfig, SftpClientWrapper)} 归还。
     */
    private static SftpClientWrapper borrowClient(SftpConfig sftpConfig) throws Exception {
        logger.debug("从连接池借用SFTP客户端: {}@{}", sftpConfig.username(), sftpConfig.host());
        return getPool(sftpConfig).borrowObject();
    }

    /**
     * 将SFTP客户端归还到连接池。
     */
    private static void returnClient(SftpConfig sftpConfig, SftpClientWrapper clientWrapper) {
        if (clientWrapper != null) {
            logger.debug("归还SFTP客户端到连接池: {}@{}", sftpConfig.username(), sftpConfig.host());
            try {
                getPool(sftpConfig).returnObject(clientWrapper);
            } catch (Exception e) {
                logger.error("归还SFTP客户端到连接池失败: {}@{} - {}",
                        sftpConfig.username(), sftpConfig.host(), e.getMessage(), e);
                // 如果归还失败，尝试销毁该对象以避免污染连接池
                destroyClient(sftpConfig, clientWrapper);
            }
        }
    }

    /**
     * 显式销毁一个SFTP客户端（例如当归还失败时）。
     */
    private static void destroyClient(SftpConfig sftpConfig, SftpClientWrapper clientWrapper) {
        if (clientWrapper != null) {
            try {
                getPool(sftpConfig).invalidateObject(clientWrapper);
                logger.info("已销毁SFTP客户端: {}@{}", sftpConfig.username(), sftpConfig.host());
            } catch (Exception e) {
                logger.error("销毁SFTP客户端失败: {}@{} - {}",
                        sftpConfig.username(), sftpConfig.host(), e.getMessage(), e);
            }
        }
    }

    /**
     * 检查远程文件是否存在。
     */
    public static boolean checkFileExists(SftpConfig sftpConfig, String remotePath) throws Exception {
        SftpClientWrapper clientWrapper = null;
        try {
            clientWrapper = borrowClient(sftpConfig);
            try {
                clientWrapper.getChannelSftp().lstat(remotePath);
                return true;
            } catch (SftpException e) {
                if (e.id == ChannelSftp.SSH_FX_NO_SUCH_FILE) {
                    return false;
                }
                throw e;
            }
        } finally {
            returnClient(sftpConfig, clientWrapper);
        }
    }

    /**
     * 下载远程文件并返回其InputStream。
     * 调用者负责关闭返回的InputStream。
     * 推荐使用 {@link #downloadFileAsBytes(SftpConfig, String)} 或 {@link #downloadFileAsStream(SftpConfig, String)} 方法。
     */
    public static InputStream downloadFileAsStream(SftpConfig sftpConfig, String remoteFilePath) throws Exception {
        SftpClientWrapper clientWrapper = null;
        // 注意：这里直接返回InputStream，意味着连接在流被完全读取并关闭之前必须保持。
        // 这对于连接池模式可能不是最优的，因为连接会被长时间占用。
        // 更好的做法可能是下载到临时文件或字节数组，然后从那里创建流，并立即归还连接。
        // 或者，提供一个回调机制来处理流。
        // 为简单起见，当前实现直接返回从ChannelSftp获取的流。
        try {
            clientWrapper = borrowClient(sftpConfig);
            logger.info("准备从 {} 下载文件: {}", sftpConfig.host(), remoteFilePath);
            return clientWrapper.getChannelSftp().get(remoteFilePath);
        } catch (Exception e) {
            // 如果在获取流时发生异常，需要确保归还（或销毁）clientWrapper
            returnClient(sftpConfig, clientWrapper); // 尝试归还，如果是因为连接问题，验证时会被销毁
            logger.error("下载文件 {} 失败: {}", remoteFilePath, e.getMessage(), e);
            throw e;
        }
        // 注意：连接不会在这里归还，因为InputStream还在使用它。
        // AbstractSftpCsvDataProvider.loadData() 方法中将在InputStream使用完毕后负责归还连接。
        // 这就要求 SftpClientWrapper 需要传递到 loadData 的 finally 块中进行归还。
        // 或者 loadData 方法在获取到inputStream后，应立即将clientWrapper归还。
        // --- 为了连接池的正确性，修改这里的逻辑：下载到字节数组，然后返回 ByteArrayInputStream ---
        // 上述注释保留作为设计考量说明。实际采用更健壮的方式。
    }

    /**
     * 下载远程文件内容为字节数组。这种方式更适合连接池，因为它在下载完成后立即释放连接。
     */
    public static byte[] downloadFileAsBytes(SftpConfig sftpConfig, String remoteFilePath) throws Exception {
        SftpClientWrapper clientWrapper = null;
        try {
            clientWrapper = borrowClient(sftpConfig);
            logger.info("准备从 {} 下载文件: {} 到字节数组", sftpConfig.host(), remoteFilePath);
            try (InputStream is = clientWrapper.getChannelSftp().get(remoteFilePath)) {
                // 使用Java 9+的 is.readAllBytes() 或 Apache Commons IOUtils.toByteArray(is)
                // 为保持兼容性，这里使用传统方式读取
                java.io.ByteArrayOutputStream baos = new java.io.ByteArrayOutputStream();
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = is.read(buffer)) != -1) {
                    baos.write(buffer, 0, bytesRead);
                }
                logger.info("文件 {} 下载完成，大小: {} bytes", remoteFilePath, baos.size());
                return baos.toByteArray();
            }
        } finally {
            returnClient(sftpConfig, clientWrapper);
        }
    }

    /**
     * 关闭指定SFTP服务器配置的所有连接池。
     * 通常在应用程序关闭时调用。
     */
    public static void shutdownPool(SftpConfig sftpConfig) {
        String poolKey = sftpConfig.host() + ":" + sftpConfig.port() + "@" + sftpConfig.username();
        GenericObjectPool<SftpClientWrapper> pool = poolMap.get(poolKey);
        if (pool != null) {
            logger.info("关闭SFTP连接池: {}", poolKey);
            pool.close();
            poolMap.remove(poolKey);
        }
    }

    /**
     * 关闭所有已创建的SFTP连接池。
     */
    public static void shutdownAllPools() {
        logger.info("准备关闭所有SFTP连接池...");
        for (String poolKey : new ArrayList<>(poolMap.keySet())) { // 复制keySet以避免并发修改异常
            GenericObjectPool<SftpClientWrapper> pool = poolMap.get(poolKey);
            if (pool != null) {
                logger.info("关闭SFTP连接池: {}", poolKey);
                pool.close();
            }
        }
        poolMap.clear();
        logger.info("所有SFTP连接池已关闭。");
    }
}