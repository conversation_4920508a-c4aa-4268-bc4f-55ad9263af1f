package com.rongchen.byh.webadmin.upms.controller.headquarters;


import cn.dev33.satoken.annotation.SaCheckPermission;
import com.rongchen.byh.common.core.annotation.MyRequestBody;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.MyCommonUtil;
import com.rongchen.byh.common.log.annotation.OperationLog;
import com.rongchen.byh.common.log.model.constant.SysOperationLogType;
import com.rongchen.byh.webadmin.upms.model.StoreData;
import com.rongchen.byh.webadmin.upms.service.headquarter.StoreDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 门店小组表(StoreData)表控制层
 *
 * <AUTHOR>
 * @since 2025-02-27 14:20:22
 */
@Tag(name = "门店信息相关接口")
@RestController
@RequestMapping("/admin/upms/store")
public class StoreDataController {
    /**
     * 服务对象
     */
    @Resource
    private StoreDataService storeDataService;

    /**
     * 分页查询门店所有数据
     *
     * @param dto 查询实体
     * @param pageParam      分页对象
     * @return 所有数据
     */
    @Operation(summary = "门店信息-列表-查询")
    @PostMapping("/storeDataList")
    public ResponseResult<MyPageData<StoreData>> storeDataList(@MyRequestBody StoreData dto,@MyRequestBody MyPageParam pageParam) {
        return storeDataService.storeDataList(dto, pageParam);
    }

    /**
     * 新增数据
     *
     * @param dto 实体对象
     * @return 新增结果
     */
    @Operation(summary = "门店信息-列表-新增")
    @PostMapping("/insertStoreData")
    @SaCheckPermission("store.insertStoreData")
    @OperationLog(type = SysOperationLogType.ADD)
    public ResponseResult<Boolean> insertStoreData(@MyRequestBody StoreData dto) {
        Boolean data = storeDataService.insertStoreData(dto);
        if (data) {
            return ResponseResult.success(true);
        }
        return ResponseResult.error(ErrorCodeEnum.DUPLICATED_UNIQUE_KEY);
    }

    /**
     * 修改数据
     *
     * @param dto 实体对象
     * @return 修改结果
     */
    @Operation(summary = "门店信息-列表-修改")
    @PostMapping("/updateStoreData")
    @SaCheckPermission("store.updateStoreData")
    @OperationLog(type = SysOperationLogType.UPDATE)
    public ResponseResult<Boolean> updateStoreData(@MyRequestBody StoreData dto) {
        Boolean data = storeDataService.updateStoreData(dto);
        if (data) {
            return ResponseResult.success(true);
        }
        return ResponseResult.error(ErrorCodeEnum.DATA_SAVE_FAILED);
    }

    /**
     * 删除数据
     *
     * @param dto
     * @return 删除结果
     */
    @Operation(summary = "门店信息-列表-删除")
    @PostMapping("/deleteStoreData")
    @SaCheckPermission("store.deleteStoreData")
    @OperationLog(type = SysOperationLogType.DELETE)
    public ResponseResult<Boolean> deleteStoreData(@MyRequestBody StoreData dto) {
        Boolean data = storeDataService.deleteStoreData(dto);
        if (data) {
            return ResponseResult.success(true);
        }
        return ResponseResult.error(ErrorCodeEnum.FAIL);
    }

    @Operation(summary = "以字典形式返回全部门店信息数据集合")
    @PostMapping("/storeDictList")
    public ResponseResult<List<Map<String, Object>>> storeList() {
        List<StoreData> list = storeDataService.storeList();
        return ResponseResult.success(
                MyCommonUtil.toDictDataList(list, StoreData::getId, StoreData::getStoreName));
    }
}

