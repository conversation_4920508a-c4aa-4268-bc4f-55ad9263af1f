package com.rongchen.byh.webadmin.upms.job;

import com.rongchen.byh.webadmin.upms.dao.OverdueProcessMapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class OverdueProcessJob {

    @Resource
    OverdueProcessMapper overdueProcessMapper;

    @XxlJob("overdueProcessHandler")
    public void overdueProcessHandler() {
        log.info("开始：插入本息(自动代扣渠道)逾期记录");
            overdueProcessMapper.insertRepayScheduleAutoRepay1();
        log.info("插入本息(自动代扣渠道)逾期记录 结束");

        log.info("开始：插入本息(不用代扣渠道)逾期记录");
            overdueProcessMapper.insertRepayScheduleAutoRepay0();
        log.info("插入本息(不用代扣渠道)逾期记录 结束");
    }
}
