package com.rongchen.byh.webadmin.upms.service.impl.order;


import cn.hutool.core.util.DesensitizedUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.page.PageMethod;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.object.TokenData;
import com.rongchen.byh.common.core.util.MyPageUtil;
import com.rongchen.byh.webadmin.upms.dao.CreditOrderMapper;
import com.rongchen.byh.webadmin.upms.dao.SysRoleMapper;
import com.rongchen.byh.webadmin.upms.dao.UserDataMapper;
import com.rongchen.byh.webadmin.upms.dto.order.CreditOrderListDetailDto;
import com.rongchen.byh.webadmin.upms.dto.order.CreditOrderListDto;
import com.rongchen.byh.webadmin.upms.dto.order.DisburseOrderListDetailDto;
import com.rongchen.byh.webadmin.upms.dto.order.DisburseOrderRequestListDto;
import com.rongchen.byh.webadmin.upms.dto.user.UserListDto;
import com.rongchen.byh.webadmin.upms.model.SysMenu;
import com.rongchen.byh.webadmin.upms.model.SysRole;
import com.rongchen.byh.webadmin.upms.service.SysMenuService;
import com.rongchen.byh.webadmin.upms.service.order.CreditOrderService;
import com.rongchen.byh.webadmin.upms.utils.isDesensitizationPermissionExistUtils;
import com.rongchen.byh.webadmin.upms.vo.order.*;
import com.rongchen.byh.webadmin.upms.vo.user.UserListDetailVo;
import com.rongchen.byh.webadmin.upms.vo.user.UserListVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * @ClassName CreditOrderServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/6 15:29
 * @Version 1.0
 **/
@Service
public class CreditOrderServiceImpl implements CreditOrderService {

    @Resource
    private CreditOrderMapper creditOrderMapper;
    @Resource
    private UserDataMapper userDataMapper;
    @Resource
    private SysRoleMapper sysRoleMapper;
    @Autowired
    private SysMenuService sysMenuService;




    @Override
    public ResponseResult<MyPageData<CreditOrderListVo>> list(CreditOrderListDto dto, MyPageParam pageParam) {
        TokenData tokenData = TokenData.takeFromRequest();
        if (tokenData != null && tokenData.getRoleIds() != null) {
            LambdaQueryWrapper<SysRole> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(SysRole::getRoleId, tokenData.getRoleIds().split(","));
            List<SysRole> sysRoles = sysRoleMapper.selectList(queryWrapper);
            // 定义需要检查的角色名称关键字列表
            List<String> roleKeywords = Arrays.asList("门店组长", "门店销售", "门店主管", "城市代理");
            for (SysRole sysRole : sysRoles) {
                String roleName = sysRole.getRoleName();
                if (StringUtils.isNotEmpty(roleName)) {
                    for (String keyword : roleKeywords) {
                        if (roleName.contains(keyword) && !tokenData.getIsAdmin()) {
                            dto.setSysUserId(tokenData.getUserId());
                            dto.setSysRoleName(roleName);
                        }
                    }
                }
            }
        }
        if (pageParam != null){
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        }
        if (dto != null && StringUtils.isNotEmpty(dto.getTimeEnd())){
            dto.setTimeEnd(dto.getTimeEnd() + " 23:59:59");
        }
        List<CreditOrderListVo> list = creditOrderMapper.creditOrderList(dto);

        Collection<SysMenu> allMenuList = sysMenuService.getMenuListByRoleIds(tokenData.getRoleIds());

        if (isDesensitizationPermissionExistUtils.isDesensitizationPermissionExist(allMenuList,"creditOrder")) {
            // 包含权限，执行脱敏逻辑
            for (CreditOrderListVo creditOrderListVo : list) {
                creditOrderListVo.setMobile(DesensitizedUtil.mobilePhone(creditOrderListVo.getMobile()));
            }
        }

        return ResponseResult.success(MyPageUtil.makeResponseData(list, CreditOrderListVo.class));
    }


    @Override
    public ResponseResult<UserDetailVo> listDetail(CreditOrderListDetailDto dto) {
        TokenData tokenData = TokenData.takeFromRequest();
        UserDetailVo userDetailVo = creditOrderMapper.userDetailVo(dto);
        // TODO 查询通道授信记录 指资方授信暂不处理
        List<PassageCreditRecordVo> psc = new ArrayList<>();
        // 查询平台授信记录 初筛授信
        List<PlatformCreditRecordVo> pcr = creditOrderMapper.platformCreditRecord(dto);
        // TODO 查询通信录记录 暂无
        List<ContactsVo> c = new ArrayList<>();
        userDetailVo.setCreditRecordList(psc);
        userDetailVo.setPlatformCreditRecordList(pcr);
        userDetailVo.setContactsList(c);
        String userId = dto.getUserId();
        //查询面核资料记录
        List<StaffRecordVo> staffRecordVos = creditOrderMapper.queryStaffAuditRecord(userId);
        for (StaffRecordVo staffRecordVo : staffRecordVos) {
            userDetailVo.setCreditAmount(staffRecordVo.getCreditAmount());
            userDetailVo.setRefuseReason(staffRecordVo.getRefuseReason());
            userDetailVo.setAuditTime(staffRecordVo.getAuditTime());
            userDetailVo.setAuditStatus(staffRecordVo.getAuditStatus());
            userDetailVo.setUserNameStaff(staffRecordVo.getUserName());
            userDetailVo.setMobileStaff(staffRecordVo.getMobile());
        }

        //查询面核资料
        UserStaffVo userStaffVo = creditOrderMapper.queryUserStaff(userId);
        StaffAuditRecordVo result;
        if (userStaffVo != null && userStaffVo.getStaffId() != null){
            result = creditOrderMapper.staffAuditRecord(userStaffVo.getStaffId());
            if (result != null){
                List<String> reportImages = convertStringToList(result.getReportImages());
                result.setReportImagesList(reportImages);
                List<String> wechatImages = convertStringToList(result.getWechatImages());
                result.setWechatImagesList(wechatImages);
                List<String> sesameImages = convertStringToList(result.getSesameImages());
                result.setSesameImagesList(sesameImages);
            }
        }else {
            result = null;
        }
        userDetailVo.setStaffAuditRecordVo(result);
        Collection<SysMenu> allMenuList = sysMenuService.getMenuListByRoleIds(tokenData.getRoleIds());
        //判断是否脱敏 手机号
        if (isDesensitizationPermissionExistUtils.isDesensitizationPermissionExist(allMenuList,"creditOrder")) {
            userDetailVo.setMobile(DesensitizedUtil.mobilePhone(userDetailVo.getMobile()));
        }
        return ResponseResult.success(userDetailVo);
    }

    public static List<String> convertStringToList(String input) {
        List<String> result = new ArrayList<>();
        if (input == null || input.isEmpty()) {
            return result;
        }
        // 去除首尾的引号和方括号
        input = input.replace("\"", "").replace("[", "").replace("]", "");
        // 使用逗号分割字符串
        String[] parts = input.split(",");
        for (String part : parts) {
            part = part.trim();
            if (!part.isEmpty()) {
                // 替换指定的前缀
                part = part.replace("https://web.jianxinrongzi.com/api/file/download?filename=",
                        "https://12qyc-1328155466.cos.ap-guangzhou.myqcloud.com/image/");
                result.add(part);
            }
        }
        return result;
    }

    @Override
    public ResponseResult<MyPageData<DisburseOrderResultListVo>> disburseOrderList(DisburseOrderRequestListDto dto , MyPageParam pageParam) {
        TokenData tokenData = TokenData.takeFromRequest();
        if (tokenData != null && tokenData.getRoleIds() != null) {
            LambdaQueryWrapper<SysRole> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(SysRole::getRoleId, tokenData.getRoleIds().split(","));
            List<SysRole> sysRoles = sysRoleMapper.selectList(queryWrapper);
            // 定义需要检查的角色名称关键字列表
            List<String> roleKeywords = Arrays.asList("门店组长", "门店销售", "门店主管", "城市代理");
            for (SysRole sysRole : sysRoles) {
                String roleName = sysRole.getRoleName();
                if (StringUtils.isNotEmpty(roleName)) {
                    for (String keyword : roleKeywords) {
                        if (roleName.contains(keyword) && !tokenData.getIsAdmin()) {
                            dto.setSysUserId(tokenData.getUserId());
                            dto.setSysRoleName(roleName);
                        }
                    }
                }
            }
        }
        if (pageParam != null){
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        }
        if (dto != null && StringUtils.isNotEmpty(dto.getTimeEnd())){
            dto.setTimeEnd(dto.getTimeEnd() + " 23:59:59");
        }
        List<DisburseOrderResultListVo> list = creditOrderMapper.disburseOrderList(dto);

        Collection<SysMenu> allMenuList = sysMenuService.getMenuListByRoleIds(tokenData.getRoleIds());
        //判断是否脱敏 手机号
        if (isDesensitizationPermissionExistUtils.isDesensitizationPermissionExist(allMenuList,"standingOrder")) {
            for (DisburseOrderResultListVo disburseOrderResultListVo : list) {
                disburseOrderResultListVo.setMobile(DesensitizedUtil.mobilePhone(disburseOrderResultListVo.getMobile()));
            }
        }
        return ResponseResult.success(MyPageUtil.makeResponseData(list, DisburseOrderResultListVo.class));
    }

    @Override
    public ResponseResult<DisburseOrderListDetailVo> disburseListDetail(DisburseOrderListDetailDto dto) {
        //获取用户id
        Long userId = creditOrderMapper.getUserIdByDisburseId(dto.getDisburseId());
        dto.setUserId(userId.toString());
        DisburseOrderListDetailVo disburseOrderListDetailVo = creditOrderMapper.disburseListDetail(dto);
        // 查询平台授信记录 初筛授信
        List<DisbursePlatformCreditRecordVo> pcr = creditOrderMapper.disbursePlatformCreditRecord(dto);
        disburseOrderListDetailVo.setPlatformCreditRecordList(pcr);
//        List<HistoricalRepaymentVo> hr = creditOrderMapper.historicalRepayment(dto);
//        disburseOrderListDetailVo.setHistoricalRepaymentList(hr);
        return ResponseResult.success(disburseOrderListDetailVo);
    }

    @Override
    public ResponseResult<MyPageData<UserListVo>> userList(UserListDto dto, MyPageParam pageParam) {
        TokenData tokenData = TokenData.takeFromRequest();
        if (pageParam != null){
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        }
        if (dto != null && StringUtils.isNotEmpty(dto.getTimeEnd())){
            dto.setTimeEnd(dto.getTimeEnd() + " 23:59:59");
        }
        List<UserListVo> list = creditOrderMapper.userList(dto);

        Collection<SysMenu> allMenuList = sysMenuService.getMenuListByRoleIds(tokenData.getRoleIds());

        if (isDesensitizationPermissionExistUtils.isDesensitizationPermissionExist(allMenuList,"customerInformation")) {
            for (UserListVo userListVo : list) {
                userListVo.setMobile(DesensitizedUtil.mobilePhone(userListVo.getMobile()));
            }
        }
        return ResponseResult.success(MyPageUtil.makeResponseData(list, UserListVo.class));
    }

    @Override
    public ResponseResult<UserListDetailVo> userDetail(Long userId) {
        TokenData tokenData = TokenData.takeFromRequest();
        UserListDetailVo vo = userDataMapper.selectDetail(userId);

        Collection<SysMenu> allMenuList = sysMenuService.getMenuListByRoleIds(tokenData.getRoleIds());

        if (isDesensitizationPermissionExistUtils.isDesensitizationPermissionExist(allMenuList,"customerInformation")) {
            vo.setMobile(DesensitizedUtil.mobilePhone(vo.getMobile()));
        }
        return ResponseResult.success(vo);
    }

    @Transactional
    @Override
    public ResponseResult<Void> deleteUser(Long userId) {
        try {
            // 判断用户是否在借款中
            Integer count = userDataMapper.selectCount(userId);
            if (count > 0) {
                return ResponseResult.error(ErrorCodeEnum.FAIL, "用户在借款中，无法删除");
            }
            // 删除用户记录
            userDataMapper.deleteUser(userId);
            return ResponseResult.success();
        } catch (Exception e) {
            // 处理异常
            return ResponseResult.error(ErrorCodeEnum.FAIL, "删除用户时出现异常：" + e.getMessage());
        }
    }


}
