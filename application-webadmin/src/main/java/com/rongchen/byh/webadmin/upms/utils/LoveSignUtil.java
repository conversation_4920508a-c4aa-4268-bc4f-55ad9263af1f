package com.rongchen.byh.webadmin.upms.utils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.DigestUtils;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.Signature;
import java.security.interfaces.RSAPrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;

public class LoveSignUtil {
    /**
     * 生成签名
     * @param appId      接入者APPID
     * @param privateKey 用户私钥
     * @param dataString 请求参数JSON字符串
     * @param timestamp  时间
     *
     * @return 签名字符串
     * @throws Exception 相关异常，接入者可自行捕捉
     */
    public static String getSign(String appId, String privateKey, String dataString, String timestamp) throws Exception {
        Signature signature = Signature.getInstance("SHA1withRSA");
        byte[] privateKeyBytes = Base64.getDecoder().decode(privateKey);
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        RSAPrivateKey rsaPrivateKey = (RSAPrivateKey) keyFactory.generatePrivate(pkcs8EncodedKeySpec);
        signature.initSign(rsaPrivateKey);

        String updateString = dataString + DigestUtils.md5DigestAsHex(dataString.getBytes(StandardCharsets.UTF_8)) +
                appId + timestamp;
        signature.update(updateString.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(signature.sign()).replaceAll("\r\n", "");
    }

    /**
     * 拼接请求文本参数
     * @param name 参数键
     * @param value 参数值
     * @param boundary 边界值
     * @return 拼接完成的字符串
     */
    public static String addTextValue(String name, String value, String boundary) {
        return "--" + boundary + "\r\n" +
                "Content-Disposition: form-data; name=\"" + name + "\"\r\n" +
                "Content-Type: text/plain; charset=UTF-8\r\n" +
                "Content-Transfer-Encoding: 8bit\r\n\r\n" +
                value + "\r\n";
    }

    /**
     * 写入文件参数
     * @param os 输出流
     * @param boundary 边界值
     * @param paramName 文件参数名
     * @param filepathArr 文件路径集合
     *
     * @throws IOException 相关异常 接入者可自行捕捉
     */
    public static void writeFile(OutputStream os, String boundary, String paramName, String... filepathArr) throws IOException {
        for (String filepath : filepathArr) {
            File file = new File(filepath);
            if (!file.isFile()) {
                continue;
            }
            String filename = file.getName();
            // 拼接请求头
            String byteString = "\r\n--" + boundary + "\r\n" +
                    "Content-Disposition: form-data; name=\"" + paramName + "\"; " +
                    "filename=\"" + filename + "\"\r\n" +
                    "Content-Type: application/octet-stream; charset=UTF-8\r\n\r\n";
            // 获取文件流
            InputStream inputStream = new FileInputStream(file);
            os.write(byteString.getBytes(StandardCharsets.UTF_8));
            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) != -1) {
                os.write(buffer, 0, length);
            }
            inputStream.close();
        }
        os.write(("\r\n--" + boundary + "--\r\n").getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 将base64字符串转为文件写入本地
     * @param base64String base64字符串
     * @param filepath 本地路径
     *
     */
    public static void writeToLocal(String base64String, String filepath) {
        if (StringUtils.isBlank(filepath)) {
            return;
        }
        File file = new File(filepath);
        if (file.exists()) {
            file.delete();
        }

        try (FileOutputStream fos = new FileOutputStream(file);
             BufferedOutputStream bos = new BufferedOutputStream(fos)) {
            bos.write(Base64.getDecoder().decode(base64String));
            fos.flush();
            bos.flush();
        } catch (IOException e) {
            throw new RuntimeException("文件写出失败");
        }
    }
}
