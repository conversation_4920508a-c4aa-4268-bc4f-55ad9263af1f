package com.rongchen.byh.webadmin.upms.service.order;


import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.webadmin.upms.dto.order.CreditOrderListDetailDto;
import com.rongchen.byh.webadmin.upms.dto.order.CreditOrderListDto;
import com.rongchen.byh.webadmin.upms.dto.order.DisburseOrderListDetailDto;
import com.rongchen.byh.webadmin.upms.dto.order.DisburseOrderRequestListDto;
import com.rongchen.byh.webadmin.upms.dto.user.UserListDto;
import com.rongchen.byh.webadmin.upms.vo.order.CreditOrderListVo;
import com.rongchen.byh.webadmin.upms.vo.order.DisburseOrderListDetailVo;
import com.rongchen.byh.webadmin.upms.vo.order.DisburseOrderResultListVo;
import com.rongchen.byh.webadmin.upms.vo.order.UserDetailVo;
import com.rongchen.byh.webadmin.upms.vo.user.UserListDetailVo;
import com.rongchen.byh.webadmin.upms.vo.user.UserListVo;

/**
 * <AUTHOR>
 */
public interface CreditOrderService {

    /**
     * 授信订单列表
     * @date 2024/12/6 15:37
     *
     * @param dto
     * @param pageParam
     * @return com.rongchen.byh.common.core.object.ResponseResult<com.rongchen.byh.common.core.object.MyPageData<com.rongchen.byh.common.core.object.LoginUserInfo>>
     */
    ResponseResult<MyPageData<CreditOrderListVo>> list(CreditOrderListDto dto, MyPageParam pageParam);

    /**
     * 受新订单列表 详情 用户信息
     * @date 2024/12/6 16:32
     *
     * @param dto
     * @return com.rongchen.byh.common.core.object.ResponseResult<com.rongchen.byh.common.core.object.MyPageData<com.rongchen.byh.webadmin.upms.vo.order.UserDetailVo>>
     */
    ResponseResult<UserDetailVo> listDetail(CreditOrderListDetailDto dto);


    /**
     * 支用订单列表
     * @date 2024/12/6 16:32
     *
     * @param dto
     * @return com.rongchen.byh.common.core.object.ResponseResult<com.rongchen.byh.common.core.object.MyPageData<com.rongchen.byh.webadmin.upms.vo.order.UserDetailVo>>
     */
    ResponseResult<MyPageData<DisburseOrderResultListVo>> disburseOrderList(DisburseOrderRequestListDto dto, MyPageParam pageParam);

    /**
     * 支用订单列表 详情 用户信息
     * @date 2024/12/6 16:32
     *
     * @param dto
     * @return com.rongchen.byh.common.core.object.ResponseResult<com.rongchen.byh.common.core.object.MyPageData<com.rongchen.byh.webadmin.upms.vo.order.UserDetailVo>>
     */
    ResponseResult<DisburseOrderListDetailVo> disburseListDetail(DisburseOrderListDetailDto dto);

    /**
     * 用户列表
     * @date 2024/12/6 16:32
     *
     * @param dto
     * @return com.rongchen.byh.common.core.object.ResponseResult<com.rongchen.byh.common.core.object.MyPageData<com.rongchen.byh.webadmin.upms.vo.order.UserDetailVo>>
     */
    ResponseResult<MyPageData<UserListVo>> userList(UserListDto dto, MyPageParam pageParam);

    /**
     * 用户详情
     * @param userId
     * @return
     */
    ResponseResult<UserListDetailVo> userDetail(Long userId);

    ResponseResult<Void> deleteUser(Long userId);
}
