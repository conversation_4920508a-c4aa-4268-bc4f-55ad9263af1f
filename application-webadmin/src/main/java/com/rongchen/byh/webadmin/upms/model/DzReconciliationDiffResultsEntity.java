package com.rongchen.byh.webadmin.upms.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**

 * 项目名称：byh_java
 * 文件名称: DzReconciliationDiffResultsEntity
 * 创建时间: 2025-05-26 12:13
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.model
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */

/**
 * 对账差异结果表 - 增强版 (包含虚拟字段和优化索引)
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "`dz_reconciliation_diff_results`")
public class DzReconciliationDiffResultsEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 批次ID
     */
    @TableField(value = "`batch_id`")
    private String batchId;

    /**
     * 使用的规则ID
     */
    @TableField(value = "`rule_id_used`")
    private String ruleIdUsed;

    /**
     * 使用的规则版本
     */
    @TableField(value = "`rule_version_used`")
    private Integer ruleVersionUsed;

    /**
     * 差异类型 :
     BALANCED=账已对平
     MISSING_OUR_SIDE=我方无，资方有 (资方多)
     MISSING_PARTNER_SIDE=我方有，资方无 (我方多)
     MISMATCHED_FIELDS=双方共有，但字段不一致
     DUPLICATE_OUR_SIDE=我方数据重复 (根据匹配键判断)
     DUPLICATE_PARTNER_SIDE=资方数据重复 (根据匹配键判断)
     */
    @TableField(value = "`difference_type`")
    private String differenceType;

    /**
     * 主要匹配键的JSON表示
     */
    @TableField(value = "`primary_matching_key_json`")
    private String primaryMatchingKeyJson;

    /**
     * 我方记录快照 (JSON)
     */
    @TableField(value = "`our_record_snapshot`")
    private String ourRecordSnapshot;

    /**
     * 资方记录快照 (JSON)
     */
    @TableField(value = "`partner_record_snapshot`")
    private String partnerRecordSnapshot;

    /**
     * 字段差异详情列表 (JSON)
     */
    @TableField(value = "`field_differences_json`")
    private String fieldDifferencesJson;

    /**
     * 确认状态 (UNCONFIRMED, CONFIRMED_MANUAL, CONFIRMED_AUTO, PROCESSING)
     */
    @TableField(value = "`confirmation_status`")
    private String confirmationStatus;

    /**
     * 确认人
     */
    @TableField(value = "`confirmed_by`")
    private String confirmedBy;

    /**
     * 确认时间戳
     */
    @TableField(value = "`confirmation_timestamp`")
    private Date confirmationTimestamp;

    /**
     * 备注或处理意见
     */
    @TableField(value = "`comments`")
    private String comments;

    /**
     * 创建时间戳
     */
    @TableField(value = "`created_timestamp`")
    private Date createdTimestamp;

    /**
     * 创建时间
     */
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 我方记录来源渠道
     */
    @TableField(value = "`our_source_channel`")
    private String ourSourceChannel;

    /**
     * 资方记录来源渠道
     */
    @TableField(value = "`partner_source_channel`")
    private String partnerSourceChannel;

    /**
     * 我方原始记录ID
     */
    @TableField(value = "`our_original_record_id`")
    private String ourOriginalRecordId;

    /**
     * 资方原始记录ID
     */
    @TableField(value = "`partner_original_record_id`")
    private String partnerOriginalRecordId;

    /**
     * 我方记录中的资方借款订单号
     */
    @TableField(value = "`our_partner_loan_order_no`")
    private String ourPartnerLoanOrderNo;

    /**
     * 资方记录中的资方借款订单号
     */
    @TableField(value = "`partner_partner_loan_order_no`")
    private String partnerPartnerLoanOrderNo;

    /**
     * 我方记录中的借款本金金额
     */
    @TableField(value = "`our_principal_amount`")
    private BigDecimal ourPrincipalAmount;

    /**
     * 资方记录中的借款本金金额
     */
    @TableField(value = "`partner_principal_amount`")
    private BigDecimal partnerPrincipalAmount;

    /**
     * 我方记录中的放款成功日期
     */
    @TableField(value = "`our_funding_date`")
    private Date ourFundingDate;

    /**
     * 资方记录中的放款成功日期
     */
    @TableField(value = "`partner_funding_date`")
    private Date partnerFundingDate;

    /**
     * 我方记录中的贷款机构代码
     */
    @TableField(value = "`our_funding_provider_code`")
    private String ourFundingProviderCode;

    /**
     * 资方记录中的贷款机构代码
     */
    @TableField(value = "`partner_funding_provider_code`")
    private String partnerFundingProviderCode;

    /**
     * 我方记录中的资方还款流水号
     */
    @TableField(value = "`our_partner_repayment_id`")
    private String ourPartnerRepaymentId;

    /**
     * 资方记录中的资方还款流水号
     */
    @TableField(value = "`partner_partner_repayment_id`")
    private String partnerPartnerRepaymentId;

    /**
     * 我方记录中的本次实还总金额
     */
    @TableField(value = "`our_total_repaid_amount`")
    private BigDecimal ourTotalRepaidAmount;

    /**
     * 资方记录中的本次实还总金额
     */
    @TableField(value = "`partner_total_repaid_amount`")
    private BigDecimal partnerTotalRepaidAmount;

    /**
     * 我方记录中的还款成功日期时间
     */
    @TableField(value = "`our_repayment_effective_date`")
    private Date ourRepaymentEffectiveDate;

    /**
     * 资方记录中的还款成功日期时间
     */
    @TableField(value = "`partner_repayment_effective_date`")
    private Date partnerRepaymentEffectiveDate;

    /**
     * 我方记录中的还款类型代码
     */
    @TableField(value = "`our_repayment_type_code`")
    private String ourRepaymentTypeCode;

    /**
     * 资方记录中的还款类型代码
     */
    @TableField(value = "`partner_repayment_type_code`")
    private String partnerRepaymentTypeCode;

    /**
     * 我方记录中的资方授信申请/记录ID
     */
    @TableField(value = "`our_partner_credit_no`")
    private String ourPartnerCreditNo;

    /**
     * 资方记录中的资方授信申请/记录ID
     */
    @TableField(value = "`partner_partner_credit_no`")
    private String partnerPartnerCreditNo;

    /**
     * 我方记录中的授信额度
     */
    @TableField(value = "`our_credit_amount`")
    private BigDecimal ourCreditAmount;

    /**
     * 资方记录中的授信额度
     */
    @TableField(value = "`partner_credit_amount`")
    private BigDecimal partnerCreditAmount;

    /**
     * 我方记录中的授信日期
     */
    @TableField(value = "`our_credit_date`")
    private Date ourCreditDate;

    /**
     * 资方记录中的授信日期
     */
    @TableField(value = "`partner_credit_date`")
    private Date partnerCreditDate;

    /**
     * 我方记录中的授信申请/审批状态
     */
    @TableField(value = "`our_credit_status`")
    private String ourCreditStatus;

    /**
     * 资方记录中的授信申请/审批状态
     */
    @TableField(value = "`partner_credit_status`")
    private String partnerCreditStatus;

    /**
     * 我方记录中的对账金额
     */
    @TableField(value = "`our_reconciliation_amount`")
    private BigDecimal ourReconciliationAmount;

    /**
     * 资方记录中的对账金额
     */
    @TableField(value = "`partner_reconciliation_amount`")
    private BigDecimal partnerReconciliationAmount;
}