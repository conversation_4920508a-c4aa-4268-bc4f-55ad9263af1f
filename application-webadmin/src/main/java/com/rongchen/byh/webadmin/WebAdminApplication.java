package com.rongchen.byh.webadmin;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 应用服务启动类。
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@EnableAsync
@SpringBootApplication
@ComponentScan("com.rongchen.byh")
@MapperScan("com.rongchen.byh.webadmin.upms.dao")
public class WebAdminApplication {

	public static void main(String[] args) {
		SpringApplication.run(WebAdminApplication.class, args);
	}
}
