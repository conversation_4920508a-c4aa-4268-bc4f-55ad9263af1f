package com.rongchen.byh.webadmin.reconciliation.rules;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rongchen.byh.webadmin.reconciliation.rules.dsl.CustomComparisonFunctionRegistry;
import com.rongchen.byh.webadmin.reconciliation.strategy.ComparisonStrategy;
import com.rongchen.byh.webadmin.reconciliation.strategy.JsonDslComparisonStrategy;
import com.rongchen.byh.webadmin.upms.model.DzReconciliationRulesEntity;
import java.io.IOException;
import java.lang.reflect.Constructor;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 对账规则策略工厂。
 * <p>
 * 负责根据对账规则定义 ({@link DzReconciliationRulesEntity}) 创建相应的
 * {@link ComparisonStrategy} 实例。
 * <p>
 * 支持两种规则类型：
 * <ul>
 * <li>JSON_DSL: 通过 {@link JsonDslComparisonStrategy} 执行。</li>
 * <li>JAVA_CLASS: 通过反射实例化指定的Java类来执行。</li>
 * </ul>
 */
@Component
public class RuleStrategyFactory {

    private static final Logger logger = LoggerFactory.getLogger(RuleStrategyFactory.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    private final CustomComparisonFunctionRegistry customComparisonFunctionRegistry;
    private final ApplicationContext applicationContext; // 用于实例化Spring管理的Java类规则
    private final List<String> allowedJavaClassPackages; // 安全控制：允许实例化的Java类包名前缀列表

    @Autowired
    public RuleStrategyFactory(CustomComparisonFunctionRegistry customComparisonFunctionRegistry,
            ApplicationContext applicationContext,
            @Value("${reconciliation.allowed-java-rule-packages:com.rongchen.byh.webadmin.reconciliation.rules.custom}") String allowedPackagesStr) {
        this.customComparisonFunctionRegistry = customComparisonFunctionRegistry;
        this.applicationContext = applicationContext;

        if (StringUtils.hasText(allowedPackagesStr)) {
            this.allowedJavaClassPackages = Arrays.asList(allowedPackagesStr.split(","));
            this.allowedJavaClassPackages.replaceAll(String::trim); // Trim whitespace
        } else {
            this.allowedJavaClassPackages = Collections.emptyList();
        }
        logger.info("RuleStrategyFactory初始化，允许的Java规则包: {}", allowedJavaClassPackages);
    }

    /**
     * 根据规则定义创建比较策略实例。
     *
     * @param ruleEntity 对账规则实体，从数据库加载。
     * @return 对应的 {@link ComparisonStrategy} 实例。
     * @throws Exception 如果创建策略失败。
     */
    public ComparisonStrategy createStrategy(DzReconciliationRulesEntity ruleEntity) throws Exception {
        if (ruleEntity == null) {
            throw new IllegalArgumentException("规则实体不能为空");
        }

        String ruleType = ruleEntity.getRuleType();
        String ruleContentJson = ruleEntity.getRuleContentJson();

        if (ruleType == null || ruleContentJson == null) {
            throw new IllegalArgumentException("规则类型和规则内容不能为空。RuleId: " + ruleEntity.getRuleId());
        }

        logger.debug("为规则ID '{}' (版本 {}) 创建策略，类型: {}", ruleEntity.getRuleId(), ruleEntity.getVersion(), ruleType);

        JsonNode rootNode;
        try {
            rootNode = objectMapper.readTree(ruleContentJson);
        } catch (IOException e) {
            logger.error("解析规则内容JSON失败，RuleId: {} : {}", ruleEntity.getRuleId(), e.getMessage());
            throw new RuntimeException("规则内容JSON格式错误", e);
        }

        switch (ruleType.toUpperCase()) {
            case "JSON_DSL":
                JsonNode comparisonFieldsNode = rootNode.path("comparisonFields");
                if (comparisonFieldsNode.isMissingNode() || !comparisonFieldsNode.isArray()) {
                    throw new IllegalArgumentException(
                            "JSON_DSL规则缺少或无效的 'comparisonFields' 数组。RuleId: " + ruleEntity.getRuleId());
                }
                return new JsonDslComparisonStrategy(comparisonFieldsNode, customComparisonFunctionRegistry);

            case "JAVA_CLASS":
                JsonNode comparisonStrategyNode = rootNode.path("comparisonStrategy");
                String className = comparisonStrategyNode.path("className").asText();
                if (className == null || className.trim().isEmpty()) {
                    throw new IllegalArgumentException("JAVA_CLASS规则缺少 'className'。RuleId: " + ruleEntity.getRuleId());
                }

                // 安全检查：确保类在允许的包内
                if (!isClassAllowed(className)) {
                    logger.error("尝试实例化不允许的Java规则类: {}。RuleId: {}", className, ruleEntity.getRuleId());
                    throw new SecurityException("Java规则类 '" + className + "' 不在允许实例化的包列表中。");
                }

                Map<String, Object> params = null;
                if (comparisonStrategyNode.has("parameters")) {
                    params = objectMapper.convertValue(comparisonStrategyNode.path("parameters"), Map.class);
                }

                return instantiateJavaClassStrategy(className, params);

            default:
                logger.error("不支持的规则类型: {}。RuleId: {}", ruleType, ruleEntity.getRuleId());
                throw new IllegalArgumentException("不支持的规则类型: " + ruleType);
        }
    }

    private boolean isClassAllowed(String className) {
        if (allowedJavaClassPackages == null || allowedJavaClassPackages.isEmpty()) {
            return false; // 如果没有配置允许的包，则默认不允许任何自定义Java类
        }
        for (String allowedPackage : allowedJavaClassPackages) {
            if (className.startsWith(allowedPackage + ".")) {
                return true;
            }
        }
        return false;
    }

    @SuppressWarnings("unchecked")
    private ComparisonStrategy instantiateJavaClassStrategy(String className, Map<String, Object> params)
            throws Exception {
        try {
            Class<?> strategyClass = Class.forName(className);
            if (!ComparisonStrategy.class.isAssignableFrom(strategyClass)) {
                throw new IllegalArgumentException("类 " + className + " 没有实现ComparisonStrategy接口");
            }

            // 尝试从Spring上下文中获取Bean实例 (如果它是一个Spring管理的Bean)
            try {
                ComparisonStrategy strategyBean = (ComparisonStrategy) applicationContext.getBean(strategyClass);
                // TODO: 如果Java类策略需要参数，这里需要一种方式将params传递给Bean实例，
                // 可能通过setter方法，或者策略本身设计为从配置中读取。或者，如果参数是每次调用都不同，
                // 则不应在实例化时注入，而是通过 ComparisonStrategy.compare() 方法的 strategyParameters 参数传递。
                // 当前JsonDslComparisonStrategy的compare方法中的strategyParameters主要用于此目的。
                // 如果Java类策略也希望每次调用时接收参数，则无需在此处处理params。
                logger.info("找到并使用Spring管理的Java规则Bean: {}", className);
                return strategyBean;
            } catch (Exception e) {
                // 不是Spring Bean，或者获取失败，尝试通过反射直接实例化
                logger.info("Java规则类 {} 不是Spring Bean，尝试通过反射实例化。", className);
            }

            // 尝试通过反射实例化 (假设它有一个无参构造函数，或者一个接受Map参数的构造函数)
            Constructor<?> constructor;
            try {
                // 优先尝试有Map参数的构造函数，如果Java类策略需要初始化参数
                constructor = strategyClass.getDeclaredConstructor(Map.class);
                return (ComparisonStrategy) constructor.newInstance(params);
            } catch (NoSuchMethodException nsme) {
                // 尝试无参构造函数
                constructor = strategyClass.getDeclaredConstructor();
                ComparisonStrategy instance = (ComparisonStrategy) constructor.newInstance();
                // 如果需要参数，并且类有setter方法，可以在这里尝试设置，但这会使工厂更复杂
                // 更好的方式是让Java类策略的compare方法直接使用传入的 strategyParameters
                return instance;
            }
        } catch (ClassNotFoundException e) {
            logger.error("Java规则类未找到: {}", className, e);
            throw new RuntimeException("Java规则类未找到: " + className, e);
        } catch (ReflectiveOperationException e) {
            logger.error("实例化Java规则类 {} 失败: {}", className, e.getMessage(), e);
            throw new RuntimeException("实例化Java规则类失败: " + className, e);
        }
    }
}